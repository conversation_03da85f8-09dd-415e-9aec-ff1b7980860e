# Project Brief: Redfyn - Cryptocurrency Tracking Platform

## Project Overview

Redfyn is a comprehensive platform for cryptocurrency investors and traders that provides real-time token data across multiple blockchain networks. The system enables users to select, track, and analyze tokens of interest while maintaining state across different views and sessions. It also provides DeFi liquidity pool analytics to help users identify opportunities across various protocols.

## Core Requirements

### 1. Data Aggregation & Display

- [x] Fetch real-time token data from CoinGecko API
- [x] Support for multiple networks (Ethereum, Solana, Base, Binance Smart Chain, etc.)
- [x] Implement Universal/Global view combining data from all networks
- [x] Display token highlights (trending, gainers, losers, new)
- [x] Create comprehensive Market view with detailed metrics
- [x] Implement Token Radar for filtered discovery
- [ ] Fetch and normalize liquidity pool data from multiple DeFi protocols
- [ ] Display standardized pool metrics (TVL, APY, volume, fees)

### 2. Token Selection System

- [x] Create a unified token selection mechanism
- [x] Allow users to select tokens from any view (Highlights, Market, Radar)
- [x] Persist selected tokens in localStorage between sessions
- [x] Implement visual indicators for selected tokens
- [x] Create a View Selected view for focused analysis
- [x] Support Trade view with selected tokens
- [ ] Extend selection to track liquidity pools of interest

### 3. User Interface & Experience

- [x] Implement responsive design for all viewports
- [x] Create intuitive navigation between different views
- [x] Ensure consistent token selection UX throughout the app
- [x] Provide appropriate loading states and error handling
- [x] Implement sorting and filtering in list views
- [ ] Design liquidity pool explorer interface
- [ ] Create pool comparison and analysis views

### 4. Performance Optimization

- [x] Implement backend caching with Redis
- [x] Optimize API calls to prevent rate limiting
- [x] Implement pagination for large data sets
- [x] Optimize token selection performance
- [ ] Implement efficient analytics calculations for pool metrics
- [ ] Optimize rendering for pool visualization components

### 5. DeFi Liquidity Pool Analytics

- [ ] Create adapters for various DeFi protocols (Uniswap, SushiSwap, Curve, etc.)
- [ ] Implement standardized data model for different pool types
- [ ] Develop risk assessment algorithms (impermanent loss calculation, etc.)
- [ ] Create pool performance tracking and historical analysis
- [ ] Implement position simulation tools
- [ ] Provide pool composition and token analysis features

## Project Goals

### Short-term Goals (MVP)

1. **Functioning Token Selection**: 
   - Users can select tokens from any view
   - Selections are remembered between sessions
   - Selected tokens available in Trade view

2. **Multi-Network Support**:
   - Support for Ethereum, Solana, Base, and Binance Smart Chain
   - Universal view that aggregates across networks
   - Consistent handling of network-specific tokens

3. **Basic Liquidity Pool Tracking**:
   - Initial support for major DeFi protocols
   - Standardized view of key pool metrics
   - Basic filtering and sorting of pool data

### Medium-term Goals (Next Phase)

1. **Enhanced Data Visualization**:
   - Advanced charting for token performance
   - Visual comparison between selected tokens
   - Pool performance visualization and historical charts

2. **Expanded Protocol Support**:
   - Additional blockchain networks
   - More DeFi protocols and pool types
   - Cross-protocol analytics and comparisons

3. **Advanced Filtering & Search**:
   - More granular filtering options
   - Saved filter presets
   - Advanced search functionality

### Long-term Goals (Future Vision)

1. **Trading Integration**:
   - Direct swap/trade functionality
   - Wallet connection
   - Position management

2. **Personalized Insights**:
   - Custom alerts and notifications
   - Portfolio tracking and analytics
   - Performance predictions
   
3. **Comprehensive DeFi Dashboard**:
   - Pool creation and management tools
   - Yield farming strategy optimization
   - Risk-adjusted return analysis
   - Cross-protocol position management

## Technical Specifications

### Frontend

- React-based single-page application
- State management using hooks and localStorage
- Real-time data updates
- Responsive design for all device sizes
- Performance-optimized rendering
- Chart.js for data visualization

### Backend

- Node.js/Express API layer
- TypeScript for type safety
- Redis caching for performance optimization
- Protocol adapters for different DeFi platforms
- Strategy pattern for analytics calculations

### External Dependencies

- CoinGecko API for token data
- DeFi protocol APIs/subgraphs
- Blockchain RPC providers for on-chain data

## Project Timeline

### Phase 1: Token Selection & Basic Views
- [x] Implement token selection mechanism
- [x] Create highlights, market, and radar views
- [x] Establish multi-network support
- [x] Implement basic trade view

### Phase 2: DeFi Liquidity Pools
- [ ] Create protocol adapter framework
- [ ] Implement data normalization layer
- [ ] Develop initial pool explorer UI
- [ ] Add basic analytics features

### Phase 3: Advanced Features
- [ ] Enhance visualization and analytics
- [ ] Add additional protocols and networks
- [ ] Implement simulation tools
- [ ] Advanced risk assessment features

## Success Criteria

1. Users can successfully select and track tokens across sessions
2. Platform supports multiple networks with consistent UX
3. Liquidity pool data from different protocols is normalized and accessible
4. System maintains performance with large datasets
5. Analytics provide accurate and valuable insights for liquidity providers 