# Tech Context

## Multi-Service Architecture

RedFyn follows a microservices architecture with four main services:

### Frontend Service (spot_frontend/)
- **Port**: 4001
- **Technology**: React + TypeScript + Vite
- **Purpose**: User interface for trading, wallet management, and DeFi interactions

### Backend Services

#### 1. Spot Backend Service (backend/spot_backend/)
- **Port**: 5001
- **Technology**: Node.js + Express + TypeScript
- **Purpose**: Core trading API, wallet balance management, token metadata

#### 2. Liquidity Pool Service (backend/liquidity_pool/)
- **Port**: 3047
- **Technology**: Node.js + Express + TypeScript
- **Purpose**: Multi-chain DEX aggregation, liquidity pool management, price quotes

#### 3. Solana Service (backend/solana/)
- **Port**: 6000 (configured) / 6001 (actual)
- **Technology**: Node.js + Express + TypeScript
- **Purpose**: Specialized Solana blockchain interactions, Pump.fun integration

## Technologies Used

### Frontend
- **React**: Main frontend framework (JavaScript library)
- **TypeScript**: Type-safe JavaScript for better development experience
- **Vite**: Fast build tool and development server
- **TailwindCSS**: Utility-first CSS framework for styling
- **React Router**: For navigation and routing
- **LocalStorage API**: For client-side data persistence
- **Fetch API**: For making HTTP requests to the backend services
- **Chart.js**: For data visualization and liquidity pool analytics
- **Privy SDK**: For multi-chain wallet connection and management
- **React Headless UI**: For accessible UI components
- **React Icons**: For icon components
- **Web3/Ethers.js**: For blockchain interactions on EVM chains
- **@solana/web3.js**: For Solana blockchain interactions

### Backend Services
- **Node.js**: Runtime environment for all backend services
- **Express**: Web application framework
- **TypeScript**: Programming language (type-safe JavaScript)
- **Redis**: In-memory data store for caching (optional)
- **Axios**: HTTP client for external API requests
- **Web3.js/ethers.js**: For blockchain interaction and liquidity pool data fetching
- **Nodemon**: Development tool for auto-restarting services
- **ts-node**: TypeScript execution environment for development

### Deployment & Infrastructure
- **Docker**: Containerization platform
- **Docker Compose**: Tool for defining and running multi-container Docker applications
- **Nginx**: Web server and reverse proxy (running on host)
- **Certbot / Let's Encrypt**: For managing SSL certificates

### Data Sources
- **CoinGecko API**: Primary source for cryptocurrency data
  - Pro API with higher rate limits
  - Free API as fallback
- **DeFi Protocol APIs/SDKs/Subgraphs**:
  - Uniswap Alpha Router (ETH V2/V3)
  - Uniswap V2 ABI (for BSC)
  - SushiSwap API
  - Curve Finance API
  - PancakeSwap API
  - Pump.fun API/RPC (Solana)
  - DexScreener API (for graduated PumpFun tokens)
- **DEX Aggregators**:
  - 0x API for EVM chains
  - Jupiter API for Solana
- **Blockchain RPC Providers**:
  - Infura/Alchemy for Ethereum
  - QuickNode for Solana
  - Other network providers as needed

### Development Tools
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **Nodemon**: Auto-restart server during development
- **Vite**: Frontend build tool and development server
- **TypeScript Compiler**: For backend TS compilation

## Development Setup

### Environment Requirements
- Node.js (v18+)
- npm (v8+)
- Redis server (optional, for caching)

### Quick Start
```bash
# Install dependencies for all services
npm install

# Start all services concurrently
npm run dev
```

This will start:
- Frontend on http://localhost:4001
- Spot Backend on http://localhost:5001
- Liquidity Pool Service on http://localhost:3047
- Solana Service on http://localhost:6000

### Individual Service Development
```bash
# Frontend only
npm run dev:frontend

# Individual backend services
npm run dev:spot-backend
npm run dev:liquidity-pool
npm run dev:solana
```

### Environment Variables

#### Spot Backend (.env in backend/spot_backend/)
```
PORT=5001
ALCHEMY_API_KEY_ETH=your_alchemy_key
BSCSCAN_API_KEY=your_bscscan_key
ETHERSCAN_API_KEY=your_etherscan_key
SOLANA_RPC_URL=your_solana_rpc
REDIS_URL=redis://localhost:6379
```

#### Liquidity Pool Service (.env in backend/liquidity_pool/)
```
PORT=3047
NODE_ENV=development
REDIS_URL=redis://localhost:6379
ETH_RPC_URL=https://mainnet.infura.io/v3/your_key
BSC_RPC_URL=https://bsc-dataseed.binance.org/
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
```

#### Solana Service (.env in backend/solana/)
```
PORT=6000
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_DEVNET_RPC_URL=https://api.devnet.solana.com
```

#### Frontend (.env in spot_frontend/)
```
VITE_PRIVY_APP_ID=your_privy_app_id
VITE_API_URL=http://localhost:5001/api
VITE_LIQUIDITY_API_URL=http://localhost:3047/api
VITE_SOLANA_API_URL=http://localhost:6000/api
```

### Project Structure
```
redfyn-spot/
├── spot_frontend/            # React frontend application (Port: 4001)
│   ├── src/
│   │   ├── components/       # Reusable UI components
│   │   │   ├── Trading/      # Trading panel components
│   │   │   ├── Wallet/       # Wallet connection components
│   │   │   └── UI/           # Generic UI elements
│   │   ├── hooks/            # Custom React hooks
│   │   ├── pages/            # Application pages/routes
│   │   ├── services/         # API services and external integrations
│   │   ├── store/            # State management
│   │   ├── utils/            # Helper functions and utilities
│   │   └── App.tsx           # Main application component
│   ├── public/               # Static assets
│   ├── .env                  # Environment variables
│   └── package.json          # Dependencies and scripts
├── backend/                  # Backend services directory
│   ├── spot_backend/         # Core trading backend (Port: 5001)
│   │   ├── src/
│   │   │   ├── controllers/  # API endpoint controllers
│   │   │   ├── routes/       # API route definitions
│   │   │   ├── services/     # Business logic and external integrations
│   │   │   ├── utils/        # Helper functions and utilities
│   │   │   ├── middleware/   # Express middleware
│   │   │   ├── config/       # Configuration files
│   │   │   └── index.ts      # Application entry point
│   │   ├── dist/             # Compiled TypeScript
│   │   ├── .env              # Environment variables
│   │   └── tsconfig.json     # TypeScript configuration
│   ├── liquidity_pool/       # Liquidity pool service (Port: 3047)
│   │   ├── src/
│   │   │   ├── api/          # API endpoints for liquidity pools
│   │   │   ├── services/     # DEX integration services
│   │   │   ├── protocols/    # Protocol-specific implementations
│   │   │   ├── utils/        # Helper functions and utilities
│   │   │   ├── config/       # Configuration files
│   │   │   └── index.ts      # Application entry point
│   │   ├── docs/             # API documentation
│   │   ├── tests/            # Test suites
│   │   └── config/           # Configuration for different networks
│   └── solana/               # Solana blockchain service (Port: 6000)
│       ├── src/
│       │   ├── controllers/  # Solana-specific controllers
│       │   ├── services/     # Solana blockchain services
│       │   ├── utils/        # Solana utilities
│       │   └── index.ts      # Application entry point
│       ├── idl/              # Anchor IDL files
│       └── test/             # Solana-specific tests
├── scripts/                  # Utility scripts
│   ├── start-services.sh     # Service startup script
│   └── check-port.sh         # Port checking utility
├── memory-bank/              # Project documentation and context
├── nginx/                    # Nginx configuration for production
├── docker-compose.yml        # Development Docker setup
├── docker-compose.prod.yml   # Production Docker setup
├── package.json              # Root package.json with unified scripts
└── README.md                 # Project documentation
```

## Production Deployment Setup

### Infrastructure
- Server running a Linux distribution (e.g., Ubuntu)
- Docker and Docker Compose installed
- Nginx installed and running on the host system
- Ports 80 and 443 open on the server firewall
- DNS configured for `redfyn.crypfi.io` pointing to the server IP

### Configuration Files
- **`docker-compose.prod.yml`**: Defines the frontend, backend, liquidity pool, and redis services for production.
- **`nginx/conf/redfyn.conf`**: Nginx virtual host configuration file used by the host Nginx installation.
- **`deploy.sh`**: Script to automate deployment, SSL certificate handling, Nginx configuration, and container startup.
- **`DEPLOYMENT.md`**: Documentation detailing the deployment process.
- **Dockerfiles**: Located in `spot_frontend/`, `spot_backend/`, and `liquidity_pool_v1/` for building production images.

### Key Environment Variables (Production)
- **Frontend**: Needs `VITE_API_URL=https://redfyn.crypfi.io/api`, `VITE_LP_API_URL=https://redfyn.crypfi.io/lp`, etc. (Managed via `.env` files, ideally use secrets management in a real production scenario).
- **Backend**: `NODE_ENV=production`, `PORT=5000`, `REDIS_URL=redis://redis:6379`.
- **Liquidity Pool**: `NODE_ENV=production`, `PORT=3000`, `REDIS_URL=redis://redis:6379`, `REDIS_HOST=redis`, `REDIS_PORT=6379`.

### Deployment Process
1. Ensure DNS is pointed correctly.
2. Obtain SSL certificates using Certbot (`sudo certbot certonly --webroot -w /var/www/html -d redfyn.crypfi.io`).
3. Run the `deploy.sh` script. This script:
   - Copies the Nginx config (`nginx/conf/redfyn.conf`) to the host Nginx setup (`/etc/nginx/sites-available/`).
   - Enables the site and restarts the host Nginx service.
   - Uses `docker-compose -f docker-compose.prod.yml up -d` to build and start the application containers.
   - Sets up a cron job for automatic SSL certificate renewal.

## Technical Constraints

### API Rate Limits
- **CoinGecko Free Tier**: 10-30 calls/minute
- **CoinGecko Pro Tier**: ~500 calls/minute
- **DeFi Protocol Subgraphs**: Varying limits (typically 100-1000 requests/day for free tiers)
- **0x API**: Rate limits based on API key tier
- **Jupiter API**: Rate limits for quote requests
- **Strategy**: Aggressive caching to stay within limits

### Performance Considerations
- **Data Size**: Large datasets with thousands of tokens and pools
- **Real-time Requirements**: Market data should be reasonably fresh
- **Computational Complexity**: Some pool analytics (impermanent loss, APY projections) require complex calculations
- **React Rendering**: Components like TradingPanel need optimization to prevent infinite update loops
- **Solution**: Time-based caching with periodic background refreshes, using React optimization patterns

### Cross-Browser Compatibility
- Support modern browsers (Chrome, Firefox, Safari, Edge)
- No IE11 support required

### Security Considerations
- API keys stored as environment variables, not in code
- No sensitive user data stored (localStorage only contains token IDs)
- Rate limiting to prevent abuse
- Wallet connection through trusted providers (Privy)
- No private key storage in application

## Dependencies

### Frontend Key Dependencies
```json
{
  "dependencies": {
    "react": "^18.x",
    "react-dom": "^18.x",
    "react-router-dom": "^6.x",
    "react-icons": "^4.x",
    "tailwindcss": "^3.x",
    "chart.js": "^4.x",
    "react-chartjs-2": "^5.x",
    "date-fns": "^2.x",
    "@privy-io/react-auth": "^1.x",
    "@headlessui/react": "^1.x",
    "ethers": "^5.x",
    "@solana/web3.js": "^1.x"
  },
  "devDependencies": {
    "vite": "^4.x",
    "@vitejs/plugin-react": "^3.x",
    "eslint": "^8.x",
    "prettier": "^2.x"
  }
}
```

### Backend Key Dependencies
```json
{
  "dependencies": {
    "express": "^4.x",
    "typescript": "^4.x",
    "axios": "^1.x",
    "redis": "^4.x",
    "winston": "^3.x", // For logging
    "web3": "^1.x", // For blockchain interaction
    "ethers": "^5.x", // Alternative for blockchain interaction
    "graphql": "^16.x", // For querying protocol subgraphs
    "graphql-request": "^5.x" // For making GraphQL requests
  },
  "devDependencies": {
    "nodemon": "^2.x",
    "ts-node": "^10.x",
    "@types/express": "^4.x",
    "eslint": "^8.x"
  }
}
```

## API Integrations

### CoinGecko API
Primary external data source with endpoints for:
- Trending coins
- Market data
- Price changes
- New listings
- Token metadata

### DeFi Protocol APIs
- **Uniswap (Ethereum)**: V2/V3 via Alpha Router SDK.
- **Uniswap (BSC)**: V2 via direct contract interaction (`ethers.js`).
- **SushiSwap**: Pool information across multiple chains (Ethereum, Polygon)
- **PancakeSwap**: BSC-based pools
- **Raydium**: Solana-based pools supporting concentrated liquidity
- **Meteora**: Solana-based pools with dynamic fees

### Pump.fun Integration (Solana)
- Interacts directly with Solana RPC and Pump.fun program.
- Handles bonding curve mechanics for active tokens.
- Fetches DEX data (e.g., DexScreener) for graduated tokens.
- Requires Solana RPC endpoint (potentially Chainstack).

### Privy Wallet SDK
Multi-chain wallet provider with support for:
- Multiple wallet connections (MetaMask, Phantom, Embedded wallets)
- Authentication and login
- Connected wallet management
- Chain-specific wallet operations
- Transaction signing

### DEX Aggregator APIs
- **0x API**: Quote aggregation across multiple EVM DEXes
- **Jupiter API**: Quote aggregation across Solana DEXes
- Both provide:
  - Price quotes for token swaps
  - Best route discovery
  - Price impact calculations
  - Gas estimates

### Integration Architecture
```