# System Patterns

## System Architecture

The Redfyn platform follows a client-server architecture with a React frontend and Node.js/Express backend:

```
┌─────────────────┐         ┌─────────────────┐         ┌─────────────────┐
│                 │         │                 │         │                 │
│  React Frontend │ ───────▶│ Express Backend │ ───────▶│  External APIs  │
│                 │         │                 │         │                 │
└─────────────────┘         └─────────────────┘         └─────────────────┘
        ▲                            │                          │
        │                            │                          │
        └────────────────────────────┴──────────────────────────┘
```

## Key Technical Decisions

1. **Data-Driven UI**: Components respond to centralized data stores rather than managing state individually.
2. **Caching Strategy**: Backend caches API responses for token data, highlights, radar data, and liquidity pools to minimize external API calls.
3. **Network Abstraction**: Single API endpoints to serve both network-specific and universal data.
4. **Token Selection Persistence**: localStorage used for token selection state management.
5. **Protocol Adapter Pattern**: Standardized interface for different DeFi protocols to normalize liquidity pool data.
6. **Wallet Integration**: Privy SDK integration for multi-chain wallet support (Ethereum and Solana).
7. **Component Memoization**: Performance optimization using React.memo and useCallback for high-frequency render components.
8. **Refs for Initialization**: Using useRef to track component initialization state to prevent infinite update loops.

## Design Patterns

### Token Selection System

The token selection system follows a custom hook pattern with React Context for global state management:

```mermaid
flowchart TD
    A[useTokenSelection Hook] -->|Provides functions| B[Token Selection Context]
    B -->|Consumed by| C[HighlightsTable]
    B -->|Consumed by| D[Market]
    B -->|Consumed by| E[Radar]
    B -->|Consumed by| F[Trade View]
    G[localStorage] <-->|Persist/Read| A
```

### Wallet Integration System

The wallet integration follows a memoization pattern with React for performance optimization:

```mermaid
flowchart TD
    A[Privy SDK] -->|Authentication| B[useWallets Hook]
    B -->|Connected Wallets| C[MemoizedWalletSelector]
    B -->|Authentication Status| D[TradingPanel]
    C -->|Selected Wallet| D
    D -->|Token Balance Requests| E[Wallet API]
    D -->|Quote Requests| F[DEX API]
    F -->|Best Price Route| D
```

### API Service Pattern

Data fetching follows a layered approach:

```mermaid
flowchart TD
    A[Frontend Component] -->|API Request| B[API Util]
    B -->|HTTP Request| C[Backend Controller]
    C -->|Business Logic| D[Service]
    D -->|Cached?| E{Redis Cache}
    E -->|Yes| D
    E -->|No| F[External API Call]
    F -->|Data| D
    D -->|Save| E
    D -->|Response| C
    C -->|Response| B
    B -->|Data| A
```

### Trading System Pattern

The trading system follows an aggregator pattern with support for bundled transactions:

```mermaid
flowchart TD
    A[TradingPanel] -->|Token & Amount| B[Quote Service]
    B -->|Request Quotes| C[Multiple DEXes]
    C -->|All Quotes| B
    B -->|Best Quote| A
    A -->|Selected Quote| D[Transaction Service]
    E[Wallet SDK] -->|Signing| D
    D -->|Submit Transaction| F[Blockchain]
    
    G[BundledSwapExample] -->|Multiple Trades| H[useBundledTransactions]
    H -->|Step 1| I[DEX1 Service]
    H -->|Step 2| J[DEX2 Service]
    I -->|Protocol-Specific| K[Ethereum, BSC, etc]
    J -->|Protocol-Specific| L[Solana, etc]
```

### Bundled Transactions Pattern

The bundled transactions system follows a coordinator pattern to sequence and monitor multi-step transactions across different protocols:

```mermaid
flowchart TD
    A[useBundledTransactions Hook] -->|Coordinates| B[Transaction Steps]
    B -->|Step Definition| C[Step Config]
    C -->|Contains| D[Protocol]
    C -->|Contains| E[Params]
    C -->|Contains| F[Dependencies]
    
    A -->|Executes| G[Step Executor]
    G -->|Routes to| H[Protocol Router]
    H -->|Ethereum| I[Uniswap/SushiSwap Service]
    H -->|Solana| J[PumpFun Service]
    
    A -->|Tracks| K[Transaction State]
    K -->|Updates| L[Step Status]
    K -->|Tracks| M[Results]
    
    N[Error Handler] -->|Manages| O[Retry Logic]
    N -->|Provides| P[Fallback Options]
    
    A -->|Uses| N
```

### Liquidity Pool Integration

The liquidity pool system uses adapter and strategy patterns to normalize data from multiple different DeFi protocols:

```mermaid
flowchart TD
    A[UI Components] -->|Request| B[Pool Controller]
    B -->|Process| C[Pool Service]
    C -->|Cached?| D{Redis Cache}
    D -->|Yes| C
    D -->|No| E[Protocol Factory/Router]
    E -->|Create/Route| F[Specific Protocol Adapter/Service]
    F -->|Fetch/Calculate| G[External DeFi APIs/RPC/SDK]
    G -->|Raw Data/Route| F
    F -->|Normalize/Result| C
    C -->|Cache| D
    C -->|Response| B
    B -->|Data| A
    
    H[Analytics Service] -->|Calculate| I[Strategy Pattern]
    I -->|APY Calculation| J[APY Strategy]
    I -->|IL Calculation| K[IL Strategy]
    I -->|Protocol-specific| L[Protocol Strategy]
    
    C -->|Pool Data| H
    
    subgraph "Supported Protocols/Services"
    P1[Uniswap Service (Alpha Router: ETH V2/V3)] --- P1b[Uniswap V2 Service (BSC)]
    P1b --- P2[PancakeSwap Service (BSC V2)]
    P2 --- P3[SushiSwap Service (ETH)]
    P3 --- P4[Raydium Service (SOL)]
    P4 --- P5[Meteora Service (SOL)]
    P5 --- P6[PumpFun Service (SOL - Bonding Curve/Graduated)]
    end
    
    F -->|Protocol-specific| P1
    F -->|Protocol-specific| P1b
    F -->|Protocol-specific| P2
    F -->|Protocol-specific| P3
    F -->|Protocol-specific| P4
    F -->|Protocol-specific| P5
    F -->|Protocol-specific| P6
```

## Component Relationships

### Token View Flow

```mermaid
flowchart TD
    A[Home Page] -->|Contains| B[HighlightsTable]
    A -->|Contains| C[Market]
    A -->|Contains| D[Radar]
    
    B -->|Token Selection| E[useTokenSelection]
    C -->|Token Selection| E
    D -->|Token Selection| E
    
    E -->|Selected Tokens| F[Trade View]
    F -->|Shows Charts| G[Token Details]
    F -->|Trading Interface| H[TradingPanel]
    
    H -->|Wallet Selection| I[MemoizedWalletSelector]
    H -->|Token Selection| J[BalanceDropdown]
    H -->|Quote Fetching| K[DEX APIs]
    H -->|Wallet Balances| L[Wallet API]
```

### Liquidity Pool Flow

```mermaid
flowchart TD
    A[Home Page] -->|Contains| B[Pools Overview]
    B -->|Shows| C[Pool List]
    C -->|Pool Selection| D[usePoolSelection]
    D -->|Selected Pool| E[Pool Detail View]
    E -->|Shows| F[Pool Analytics]
    E -->|Shows| G[Historical Performance]
    E -->|Shows| H[Position Simulator]
```

## Data Models

### Token Data Model

```typescript
interface Token {
  id: string;
  name: string;
  symbol: string;
  current_price: number;
  price_change_percentage_24h: number;
  market_cap: number;
  total_volume: number;
  network?: string;
  image?: string;
}
```

### Wallet Data Model

```typescript
interface Wallet {
  address: string;
  walletClientType: string; // "embedded", "injected", "phantom", etc.
  connected: boolean;
  connector?: {
    name: string;
  };
  embedded?: boolean;
}

interface WalletToken {
  name: string;
  symbol: string;
  chain: string; // "Ethereum", "BSC", "Solana", etc.
  icon: string;
  balance: string;
  decimals: number;
  tokenAddress: string;
  walletAddress: string;
  walletType: string;
}
```

### Quote Data Model

```typescript
interface Quote {
  dexId: string;
  chainId: number | string;
  tokenIn: {
    address: string;
    symbol: string;
    name: string;
    decimals: number;
  };
  tokenOut: {
    address: string;
    symbol: string;
    name: string;
    decimals: number;
  };
  amountIn: string;
  amountOut: string;
  executionPrice: string;
  priceImpact: number;
  platformFee?: number;
  routerAddress: string;
  path?: string[];
}

interface DEXQuote {
  success: boolean;
  data?: {
    bestQuote: Quote;
    allQuotes: Quote[];
    bestDex: string;
  };
  message?: string;
  code?: string;
}
```

### Liquidity Pool Data Model

```typescript
interface LiquidityPool {
  id: string;                    // Unique identifier (protocol+address)
  name: string;                  // Display name
  protocol: string;              // Uniswap, SushiSwap, etc.
  network: string;               // Ethereum, BSC, etc.
  tokens: {                      // Tokens in the pool
    address: string;
    symbol: string;
    weight?: number;             // Optional weight for weighted pools
    reserve: number;             // Current reserve amount
  }[];
  tvl: number;                   // Total Value Locked in USD
  apy: {                         // Annual Percentage Yield
    total: number;
    fee: number;                 // Component from fees
    rewards?: number;            // Component from incentives
  };
  volume24h: number;             // 24-hour trading volume
  fee: number;                   // Fee percentage
  createdAt: string;             // Creation timestamp
}
```

### PumpFun Data Models (Illustrative)

```typescript
// Represents data fetched from the Pump.fun bonding curve account
interface PumpFunBondingCurveData {
  virtualTokenReserves: bigint;
  virtualSolReserves: bigint;
  realTokenReserves: bigint;
  realSolReserves: bigint;
  tokenTotalSupply: bigint;
  isGraduated: boolean;
}

// Represents the result of a PumpFun quote
interface PumpFunQuoteResult {
  inAmount: number;
  outAmount: number;
  price: number;
  priceImpact: number;
  minOutAmount: number;
  source: string; // e.g., 'pumpfun-bonding', 'pumpfun-graduated'
  tokenStatus?: string; // 'active', 'graduated'
  tokenDecimals?: number;
  platformFee?: SushiSwapFeeData; // Reusing FeeData structure
}
```

## Architectural Patterns

### Adapter Pattern for DeFi Protocols

The system uses adapters/services to normalize data and interactions for different DeFi protocols:

```typescript
// Base adapter/service concept (interfaces might vary)
interface BasePoolService {
  getQuote(params: any): Promise<any>;
  prepareSwap(params: any): Promise<any>;
  // Other common methods like getPools, getTokens might exist
}

// Implementation examples
class UniswapService implements BasePoolService {
  // Uniswap Alpha Router implementation (ETH V2/V3)
}

class UniswapV2BSCService implements BasePoolService {
  // Uniswap V2 implementation specific to BSC
}

class PancakeSwapAdapter implements BasePoolService {
  // PancakeSwap-specific implementation (likely similar to UniswapV2BSCService)
}

class RaydiumAdapter implements BasePoolService {
  // Raydium-specific implementation
}

class MeteoraAdapter implements BasePoolService {
  // Meteora-specific implementation
}

class PumpFunService implements BasePoolService {
  // PumpFun specific implementation (bonding curve, graduated token handling)
}
```

### Strategy Pattern for Analytics

Different calculation strategies for different pool types:

```typescript
// Strategy interface
interface APYCalculationStrategy {
  calculate(pool: LiquidityPool): APYBreakdown;
}

// Concrete strategies
class ConstantProductStrategy implements APYCalculationStrategy {
  // For Uniswap V2-style pools
}

class ConcentratedLiquidityStrategy implements APYCalculationStrategy {
  // For Uniswap V3-style pools
}

class StablecoinPoolStrategy implements APYCalculationStrategy {
  // For Curve-style pools
}
```

### Memoization Pattern for UI Components

Using React.memo to optimize component rendering:

```typescript
// Create a memoized version of a component
const MemoizedComponent = memo(({ prop1, prop2, onSomething }) => {
  // Component implementation
  return (
    <div>
      {/* Component JSX */}
    </div>
  );
});

// Using stable callbacks with memoized components
const ParentComponent = () => {
  // Create stable callback with useCallback
  const handleSomething = useCallback((value) => {
    // Handler implementation
  }, [/* dependencies */]);
  
  return (
    <MemoizedComponent
      prop1={value1}
      prop2={value2}
      onSomething={handleSomething}
    />
  );
};
```

### Ref Pattern for Initialization State

Using refs to track initialization state and prevent infinite loops:

```typescript
const Component = () => {
  // State values
  const [state, setState] = useState(initialValue);
  
  // Ref to track initialization
  const isInitializedRef = useRef(false);
  
  // Effect that should only run once
  useEffect(() => {
    // Skip if already initialized
    if (isInitializedRef.current) {
      return;
    }
    
    // Mark as initialized
    isInitializedRef.current = true;
    
    // Run initialization logic
    // ...
    
    // Optional: Use setTimeout to break render cycles
    const timeoutId = setTimeout(() => {
      setState(newValue);
    }, 0);
    
    // Cleanup
    return () => clearTimeout(timeoutId);
  }, [/* dependencies */]);
  
  // Rest of component
};
```

## Error Handling Strategy

1. **Backend Errors**: Structured error responses with status codes and error messages.
2. **Frontend Error Boundaries**: React error boundaries to prevent entire UI crashes.
3. **API Error Fallbacks**: Fallback to cached data when external APIs fail.
4. **Network Resilience**: Retry logic with exponential backoff for transient failures.
5. **Wallet Connection Errors**: Graceful handling with clear user feedback and recovery options.
6. **DEX Quote Failures**: Fallback to alternative DEXes when primary DEX fails to provide quotes.

## Cache Invalidation Strategy

1. **Time-Based Invalidation**: Cache entries expire after configurable TTL.
2. **Background Refresh**: Scheduled jobs refresh cache before expiration.
3. **On-Demand Refresh**: Critical paths can force-refresh cache.
4. **Partial Updates**: Update specific cache keys without invalidating all data.

## Performance Optimization Strategies

1. **Component Memoization**: Using React.memo and useCallback to prevent unnecessary re-renders.
2. **Refs for State Tracking**: Using useRef to store values that shouldn't trigger re-renders.
3. **Debounced API Calls**: Implementing debounce for high-frequency input changes.
4. **Timeout for State Updates**: Using setTimeout to break render cycles and prevent maximum update depth errors.
5. **Proper Effect Dependencies**: Carefully managing useEffect dependencies to prevent infinite loops.
6. **Aggressive Caching**: Implementing multi-level caching (memory, localStorage, backend Redis).
7. **Lazy Loading**: Only loading components and data when needed.
8. **Bundle Optimization**: Code splitting and tree shaking to reduce initial load time.

### Production Deployment Architecture

The production environment utilizes Docker Compose to orchestrate the frontend, backend, liquidity pool, and Redis services. Nginx, running directly on the host system, acts as a reverse proxy, handling SSL termination (via Let's Encrypt/Certbot) and routing requests to the appropriate Docker containers based on URL paths.

```mermaid
flowchart TD
    subgraph Host Server
        Nginx[Host Nginx (Ports 80, 443)]
    end

    subgraph Docker Network (app-network)
        Frontend[Frontend Container (Port 4000)]
        Backend[Backend Container (Port 5000)]
        LP[Liquidity Pool Container (Port 3000)]
        Redis[Redis Container (Port 6379)]
    end

    User[User Browser] -->|HTTPS: redfyn.crypfi.io| Nginx
    Nginx -->|/| Frontend
    Nginx -->|/api/| Backend
    Nginx -->|/lp/| LP
    Nginx -->|/liquidity-api/| LP

    Backend -->|redis://redis:6379| Redis
    LP -->|redis://redis:6379| Redis
    Frontend -->|Calls /api/ & /lp/ & /liquidity-api/ via Nginx| Nginx

    Certbot[Certbot (Host)] <--> Nginx
```

- **SSL Termination**: Nginx handles SSL certificates obtained via Certbot.
- **Routing**: Nginx routes requests based on path:
    - `/` -> Frontend (Port 4000)
    - `/api/*` -> Backend (Port 5000)
    - `/lp/*` -> Liquidity Pool (Port 3000)
    - `/liquidity-api/*` -> Rewritten to `/api/*` and routed to Liquidity Pool (Port 3000)
- **Service Discovery**: Containers within the `app-network` Docker network communicate using service names (e.g., `redis`).
- **Persistence**: Redis data is persisted using a named Docker volume (`redis_data`).
