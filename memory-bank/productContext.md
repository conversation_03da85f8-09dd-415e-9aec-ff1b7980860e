# Product Context

## Why Redfyn Exists

Redfyn was created to provide a comprehensive platform for cryptocurrency investors and traders to monitor, track, and trade tokens across multiple blockchain networks. The crypto landscape is fragmented, with tokens spread across different chains and exchanges, making it difficult for users to maintain situational awareness.

Redfyn solves this by offering:

1. A unified dashboard for tracking tokens across all major blockchains
2. Tools for identifying trading opportunities
3. Personalized watchlists for monitoring selected tokens
4. Liquidity pool exploration and analysis for DeFi investors

## Problems It Solves

### For Token Investors & Traders
1. **Information Overload**: Reduces noise by allowing users to focus on tokens they care about
2. **Network Fragmentation**: Provides a universal view across chains (Ethereum, Solana, Binance, etc.)
3. **Decision Fatigue**: Highlights important market movements and metrics
4. **Context Switching**: Eliminates need to use multiple tools and platforms

### For DeFi Liquidity Providers
1. **Protocol Fragmentation**: Standardizes data across DeFi protocols like Uniswap, SushiSwap, and Curve
2. **Risk Assessment**: Analyzes impermanent loss risk and APY sustainability
3. **Opportunity Discovery**: Identifies high-performing pools with favorable risk-reward ratios
4. **Position Management**: Tracks performance of liquidity positions across protocols

## How It Should Work

### Core Experience Flow

1. **Home Page**: Users see highlights, market overview, and token radar
   - Highlights: Recent market movers and important events
   - Market: Comprehensive token table with filterable data
   - Radar: Visual representation of token performance

2. **Token Selection**: Users can select tokens of interest from any view
   - Selections persist across sessions
   - Selected tokens become available in Trade view
   - Selection is accessible from any main view component

3. **Trade View**: Shows detailed information on selected tokens
   - Price charts with time range selection
   - Key metrics and comparisons
   - Trading links to relevant exchanges

4. **Liquidity Pool Explorer**: Navigate and analyze liquidity pools
   - Pool discovery based on protocol, network, or token
   - Detailed metrics including TVL, APY, volume, and fee analysis
   - Historical performance tracking
   - Risk assessment and comparison tools

### Key Functionality

#### Token Selection
- Users can select tokens by clicking a star icon
- Selection is consistent across all components
- Selected tokens are remembered between sessions

#### Universal View
- "Universal" network selection aggregates data across all chains
- Network filter isolates tokens on specific blockchains
- Consistent sorting and filtering applies across networks

#### Token Radar
- Visual discovery tool for identifying opportunities
- Filters by timeframe, network, and metrics
- Interactive exploration of new and emerging tokens

#### Liquidity Pools
- Exploration of DeFi liquidity pools across protocols
- Standardized metrics for comparing pools (APY, volume, TVL)
- Pool composition and token analysis
- Risk assessment for impermanent loss and other factors
- Position simulation tool to project earnings and risks

## User Experience Goals

### Simplicity
- Clean, uncluttered interface despite complex data
- Progressive disclosure of information
- Consistent interaction patterns across all components

### Personalization
- Token selection creates personalized view of the market
- Remembers user preferences between sessions
- Customizable filters and views

### Performance
- Fast loading times for critical data
- Smooth interactions and transitions
- Efficient data fetching and caching

### Reliability
- Graceful handling of API rate limits and errors
- Fallback content when data is unavailable
- Consistent data quality across sources

### Accessibility
- Support for keyboard navigation
- Proper contrast and readability
- Screen reader compatibility

## User Personas

### Alex - Active Trader
- Trades multiple times per day
- Monitors dozens of tokens
- Needs quick access to key metrics
- Values rapid updates and comprehensive data

### Morgan - Strategic Investor
- Makes weekly investment decisions
- Focuses on fundamental value
- Prefers depth of information over breadth
- Values historical context and trends

### Jordan - DeFi Liquidity Provider
- Provides liquidity in pools across protocols
- Needs to compare pools for optimal returns
- Monitors risk factors like impermanent loss
- Values detailed analytics and risk assessment

### Taylor - Multi-Chain Explorer
- Invests across different blockchains
- Needs unified view of positions
- Values cross-chain analytics and comparisons
- Interested in network-specific trends

## Key Metrics

### Success Indicators
- Daily active users
- Session duration
- Number of tokens selected
- Pools analyzed per session
- User retention rate
- Error rates and API success rates

### User Satisfaction Metrics
- Time to first selection
- Navigation path efficiency
- Feature discovery rate
- Error recovery time

## Future Directions

### Integration Possibilities
- Direct exchange and swap integration
- Wallet connection for portfolio tracking
- Alerts and notifications for price movements
- Advanced analytics for liquidity providers
- Social features for sharing insights

### Platform Expansion
- Mobile applications
- Browser extensions
- API access for developers
- Customizable dashboards
- Advanced charting tools 