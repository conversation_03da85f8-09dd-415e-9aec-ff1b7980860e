#!/bin/bash

# Exit on error
set -e

# Color variables
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}===== Redfyn Deployment Script =====${NC}"
echo -e "${GREEN}This script will deploy your application with SSL support${NC}"

# Check if docker and docker-compose are installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker is not installed. Please install Docker first.${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Docker Compose is not installed. Please install Docker Compose first.${NC}"
    exit 1
fi

# Check if domain is set correctly
DOMAIN="redfyn.crypfi.io"
echo -e "${YELLOW}Using domain: ${DOMAIN}${NC}"

# Check if .env files exist
if [ ! -f "./spot_frontend/.env" ]; then
    echo -e "${YELLOW}Warning: spot_frontend/.env file not found. Creating a template...${NC}"
    echo "# Frontend environment variables" > ./spot_frontend/.env
    echo "VITE_API_URL=https://${DOMAIN}/api" >> ./spot_frontend/.env
    echo "VITE_LP_API_URL=https://${DOMAIN}/lp" >> ./spot_frontend/.env
    echo -e "${YELLOW}Please edit spot_frontend/.env with your environment variables${NC}"
fi

if [ ! -f "./spot_backend/.env" ]; then
    echo -e "${YELLOW}Warning: spot_backend/.env file not found. Creating a template...${NC}"
    echo "# Backend environment variables" > ./spot_backend/.env
    echo "NODE_ENV=production" >> ./spot_backend/.env
    echo "PORT=5000" >> ./spot_backend/.env
    echo "REDIS_URL=redis://redis:6379" >> ./spot_backend/.env
    echo -e "${YELLOW}Please edit spot_backend/.env with your environment variables${NC}"
fi

if [ ! -f "./liquidity_pool_v1/.env" ]; then
    echo -e "${YELLOW}Warning: liquidity_pool_v1/.env file not found. Creating a template...${NC}"
    echo "# Liquidity Pool environment variables" > ./liquidity_pool_v1/.env
    echo "NODE_ENV=production" >> ./liquidity_pool_v1/.env
    echo "PORT=3000" >> ./liquidity_pool_v1/.env
    echo "REDIS_URL=redis://redis:6379" >> ./liquidity_pool_v1/.env
    echo -e "${YELLOW}Please edit liquidity_pool_v1/.env with your environment variables${NC}"
fi

# Check if we have SSL certificates already
if [ ! -f "/etc/letsencrypt/live/${DOMAIN}/fullchain.pem" ] || [ ! -f "/etc/letsencrypt/live/${DOMAIN}/privkey.pem" ]; then
    echo -e "${YELLOW}SSL certificates not found. Please obtain SSL certificates for ${DOMAIN} using certbot:${NC}"
    echo -e "${YELLOW}sudo certbot certonly --webroot -w /var/www/html -d ${DOMAIN}${NC}"
    echo -e "${YELLOW}Then run this script again.${NC}"
    exit 1
fi

# Copy SSL certificates to nginx folder
echo -e "${GREEN}Copying SSL certificates...${NC}"
mkdir -p ./nginx/ssl
cp /etc/letsencrypt/live/${DOMAIN}/fullchain.pem ./nginx/ssl/
cp /etc/letsencrypt/live/${DOMAIN}/privkey.pem ./nginx/ssl/

# Set correct permissions
chmod 644 ./nginx/ssl/fullchain.pem
chmod 644 ./nginx/ssl/privkey.pem

# Deploy with SSL
echo -e "${GREEN}Deploying application with SSL...${NC}"

# Configure Nginx
echo -e "${GREEN}Setting up Nginx configuration...${NC}"

# Create a symbolic link to your Nginx config or copy it to sites-available
if [ -d "/etc/nginx/sites-available" ]; then
    # Debian/Ubuntu style Nginx configuration
    echo -e "${YELLOW}Copying Nginx configuration to /etc/nginx/sites-available/${DOMAIN}${NC}"
    sudo cp ./nginx/conf/redfyn.conf /etc/nginx/sites-available/${DOMAIN}
    
    # Enable the site
    if [ ! -f "/etc/nginx/sites-enabled/${DOMAIN}" ]; then
        echo -e "${YELLOW}Enabling the site in Nginx${NC}"
        sudo ln -s /etc/nginx/sites-available/${DOMAIN} /etc/nginx/sites-enabled/
    fi
else
    # Direct conf.d inclusion (CentOS/RHEL/other)
    echo -e "${YELLOW}Copying Nginx configuration to /etc/nginx/conf.d/${DOMAIN}.conf${NC}"
    sudo cp ./nginx/conf/redfyn.conf /etc/nginx/conf.d/${DOMAIN}.conf
fi

# Restart Nginx
echo -e "${GREEN}Restarting Nginx...${NC}"
sudo systemctl restart nginx || sudo service nginx restart

# Start the application containers
echo -e "${GREEN}Starting application containers...${NC}"
docker-compose -f docker-compose.prod.yml up -d

# Setup auto-renewal for SSL certificates if not already set
if ! crontab -l | grep -q "certbot renew"; then
    echo -e "${GREEN}Setting up SSL auto-renewal...${NC}"
    (crontab -l 2>/dev/null; echo "0 0 * * * certbot renew --quiet --post-hook \"cp /etc/letsencrypt/live/${DOMAIN}/fullchain.pem /www/wwwroot/redfyn/nginx/ssl/ && cp /etc/letsencrypt/live/${DOMAIN}/privkey.pem /www/wwwroot/redfyn/nginx/ssl/ && systemctl restart nginx\"") | crontab -
fi

echo -e "${GREEN}===== Deployment Complete =====${NC}"
echo -e "${GREEN}Your application is now running at https://${DOMAIN}${NC}" 