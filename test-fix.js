// Simple test to verify the pulse localStorage fix
console.log('🔧 Testing Pulse localStorage Fix...\n');

// Test the API endpoint first
fetch('/api/home/<USER>')
  .then(response => response.json())
  .then(data => {
    console.log('✅ API Response received');
    console.log('📊 Token data:', {
      id: data.data.id,
      name: data.data.name,
      symbol: data.data.symbol,
      price: data.data.price
    });
    
    // Test the normalization logic
    const address = 'So11111111111111111111111111111111111111112';
    const tokenData = data.data;
    
    const normalizedToken = {
      id: address, // Use address as primary identifier
      name: tokenData.name || 'Unknown Token',
      address: address,
      symbol: tokenData.symbol || 'UNKNOWN',
      network: 'solana',
      bonding_percent: tokenData.bonding_percent || 0,
      volume: tokenData.volume || 0,
      price: tokenData.price || 0,
      market_cap: tokenData.market_cap || 0,
      liquidity: tokenData.liquidity || 0,
      supply: tokenData.supply || 0,
      exchange_name: tokenData.exchange_name || 'Unknown',
      exchange_logo: tokenData.exchange_logo || '',
      pool_address: tokenData.pool_address || '',
      price_change_24h: tokenData.price_change_24h || 0,
      imageUrl: tokenData.imageUrl || tokenData.logo || '',
    };
    
    console.log('✅ Normalized token:', normalizedToken);
    
    // Test localStorage operations
    const existingTokens = JSON.parse(localStorage.getItem('pulseTokens') || '[]');
    console.log('📊 Existing tokens:', existingTokens.length);
    
    // Test duplicate check logic
    const isAlreadyPresent = existingTokens.some(t =>
      t.id === normalizedToken.id ||
      t.address === normalizedToken.address ||
      (t.address && normalizedToken.address && t.address.toLowerCase() === normalizedToken.address.toLowerCase())
    );
    
    console.log('🔍 Token already present:', isAlreadyPresent);
    
    if (!isAlreadyPresent) {
      const updatedTokens = [...existingTokens, normalizedToken];
      localStorage.setItem('pulseTokens', JSON.stringify(updatedTokens));
      console.log('✅ Token added to localStorage');
      console.log('📊 New token count:', updatedTokens.length);
    } else {
      console.log('ℹ️ Token already exists, not adding duplicate');
    }
    
    // Set as active token
    localStorage.setItem('activePulseToken', JSON.stringify(normalizedToken));
    console.log('✅ Set as active token');
    
    // Final verification
    const finalTokens = JSON.parse(localStorage.getItem('pulseTokens') || '[]');
    const finalActiveToken = JSON.parse(localStorage.getItem('activePulseToken') || '{}');
    
    console.log('\n🎯 Final Results:');
    console.log('📊 pulseTokens count:', finalTokens.length);
    console.log('📊 activePulseToken:', finalActiveToken.symbol || 'none');
    console.log('✅ Fix verification complete!');
  })
  .catch(error => {
    console.error('❌ Test failed:', error);
  });
