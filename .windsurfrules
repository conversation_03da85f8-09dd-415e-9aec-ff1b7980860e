# Windsurf Rules for the Redfyn Project
## General Guidelines
1.  Do not create mock data unless specifically requested and necessary for isolated testing. Prefer using real API endpoints or documented structures where possible.
2.  Before adding import statements, always verify the path relative to the current file and the overall project structure (`spot_frontend/` or `spot_backend/` or `solana/`). Ensure imports do not reference files outside the intended project scope (e.g., importing from the backend into the frontend or vice-versa unless explicitly part of the design). 

