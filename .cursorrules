# Cursor Rules for the Redfyn Project

## Code Organization and Structure
1. Frontend code is in `spot_frontend/`
2. Backend API code is in `spot_backend/`
3. Liquidity pool service is in `liquidity_pool_v1/`

## Wallet Implementation Patterns
1. The frontend uses Privy for wallet management
2. Only display smart wallets in the UI, not embedded wallets
3. All transactions should support Account Abstraction (ERC-4337)
4. Always use the smart wallet client for transactions when available

## Smart Wallet Implementation
1. Smart wallets are created via `SmartWalletsProvider` in `main.tsx`
2. Smart wallet operations are managed by `useSmartWallet` hook
3. Smart wallet utilities are in `smartWalletUtils.ts`
4. Bundled transactions use `useBundledTransactions` hook

## Important Implementation Details
1. Filters ensure only smart wallets are displayed in the UI
2. For wallet connection, always use Privy's login function
3. ERC-4337 bundler URL is configured in `main.tsx`
4. Transaction handling supports multiple DEXes (PancakeSwap is prioritized)
5. Both BSC and Solana chains are supported

## Port Configuration
1. Frontend service runs on port 4001
2. Backend API service runs on port 5001
3. Liquidity pool service runs on port 3047
4. Always check if a port is already in use before starting a service
5. If a service is already running on the specified port, do not start another instance

## Abbreviations
- l-p: liquidity pool
- l-p-s: liquidity pool service

## Testing Guidelines
1. Test smart wallet operations on BSC testnet first
2. Ensure proper error handling for wallet operations
3. Verify trading functions with small amounts
4. Test ApproveAndSwap bundled transactions specifically
