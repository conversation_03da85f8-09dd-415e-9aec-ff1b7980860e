# Trading Panel Service

A Node.js TypeScript service for handling trading panel functionality in the RedFyn Spot platform.

## Structure

```
trading_panel/
├── src/
│   ├── controllers/     # Request handlers
│   ├── services/        # Business logic
│   ├── routes/          # API routes
│   ├── types/           # TypeScript interfaces
│   └── index.ts         # Entry point
├── package.json
├── tsconfig.json
└── README.md
```

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

3. Run in development:
   ```bash
   npm run dev
   ```

4. Build for production:
   ```bash
   npm run build
   npm start
   ```

## API Endpoints

- `GET /health` - Health check
- `GET /config` - Get trading panel configuration
