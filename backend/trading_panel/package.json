{"name": "trading_panel", "version": "1.0.0", "description": "Trading Panel Service for RedFyn Spot", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "watch": "nodemon --exec ts-node src/index.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "ws": "^8.14.2", "http": "^0.0.1-security"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/node": "^20.5.0", "@types/ws": "^8.5.5", "typescript": "^5.1.6", "ts-node": "^10.9.1", "nodemon": "^3.0.1"}}