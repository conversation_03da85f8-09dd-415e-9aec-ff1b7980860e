import { Request, Response } from 'express';
import { TradingPanelService } from '../services/tradingPanelService';
import { WebSocketServerService } from '../services/webSocketServer';

const tradingPanelService = new TradingPanelService();
let wsServer: WebSocketServerService | null = null;

// Set WebSocket server reference
export const setWebSocketServer = (server: WebSocketServerService): void => {
  wsServer = server;
};

export const getTradingPanelConfig = async (req: Request, res: Response) => {
  try {
    const config = await tradingPanelService.getConfig();
    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to get trading panel config'
    });
  }
};

// Get WebSocket server status
export const getWebSocketStatus = async (req: Request, res: Response) => {
  try {
    if (!wsServer) {
      return res.status(503).json({
        success: false,
        error: 'WebSocket server not initialized'
      });
    }

    const status = wsServer.getStatus();
    res.json({
      success: true,
      data: {
        ...status,
        endpoint: 'ws://localhost:5003'
      }
    });
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Health check with WebSocket status
export const healthCheck = async (req: Request, res: Response) => {
  try {
    const wsStatus = wsServer ? wsServer.getStatus() : null;

    res.json({
      success: true,
      data: {
        service: 'trading_panel',
        status: 'OK',
        timestamp: new Date().toISOString(),
        websocket: wsStatus ? {
          clients: wsStatus.clientCount,
          mobulaConnected: wsStatus.mobulaConnected,
          currentPool: wsStatus.currentPool
        } : 'Not initialized'
      }
    });
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message });
  }
};
