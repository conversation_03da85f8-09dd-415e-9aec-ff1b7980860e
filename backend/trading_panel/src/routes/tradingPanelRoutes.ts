import { Router } from 'express';
import {
  getTradingPanelConfig,
  getWebSocketStatus,
  healthCheck,
  getMemoryStats,
  forceCleanup
} from '../controllers/tradingPanelController';

const router = Router();

// Trading panel routes
router.get('/config', getTradingPanelConfig);
router.get('/websocket/status', getWebSocketStatus);
router.get('/health', healthCheck);

// New monitoring and management routes
router.get('/memory-stats', getMemoryStats);
router.post('/force-cleanup', forceCleanup);

export default router;
