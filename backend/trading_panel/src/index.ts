import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { WebSocketServerService } from './services/webSocketServer';
import { setWebSocketServer } from './controllers/tradingPanelController';
import tradingPanelRoutes from './routes/tradingPanelRoutes';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5003;

// Middleware
app.use(cors({
  origin: ['http://localhost:4001', 'http://localhost:3000'], // Frontend URLs
  credentials: true
}));
app.use(express.json());

// Routes
app.use('/api/trading-panel', tradingPanelRoutes);

// Create HTTP server
const server = createServer(app);

// Initialize WebSocket server
const wsServer = new WebSocketServerService(server);
setWebSocketServer(wsServer);

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🔄 SIGTERM received, shutting down gracefully...');
  wsServer.shutdown();
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🔄 SIGINT received, shutting down gracefully...');
  wsServer.shutdown();
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 Trading Panel service running on port ${PORT}`);
  console.log(`📡 WebSocket server available at ws://localhost:${PORT}`);
  console.log(`🌐 HTTP API available at http://localhost:${PORT}/api/trading-panel`);
});
