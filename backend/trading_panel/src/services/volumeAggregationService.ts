import { FormattedTrade } from '../types/index.js';

interface VolumeMetrics {
  volume24h: number;
  volume_5min: number;
  volume_1h: number;
  volume_6h: number;
  buyers_24h: number;
  sellers_24h: number;
  buy_volume_24h: number;
  sell_volume_24h: number;
  buy_volume_5min: number;
  sell_volume_5min: number;
  buy_volume_1h: number;
  sell_volume_1h: number;
  buy_volume_6h: number;
  sell_volume_6h: number;
  net_volume_5min: number;
  net_volume_1h: number;
  net_volume_6h: number;
  net_volume_24h: number;
}

interface TimeframeTrades {
  trades: FormattedTrade[];
  lastCleanup: number;
  memoryUsage: number; // Track memory usage per pool
}

interface PoolMemoryStats {
  poolAddress: string;
  tradeCount: number;
  memoryUsage: number;
  lastCleanup: number;
}

class VolumeAggregationService {
  private tradeHistory: Map<string, TimeframeTrades> = new Map();
  private readonly CLEANUP_INTERVAL = 2 * 60 * 1000; // 2 minutes (more frequent)
  private readonly MAX_HISTORY_AGE = 24 * 60 * 60 * 1000; // 24 hours
  private readonly MAX_TRADES_PER_POOL = 1000; // Critical: Limit trades per pool
  private readonly MAX_TOTAL_MEMORY_MB = 100; // Maximum total memory usage in MB
  private readonly EMERGENCY_CLEANUP_THRESHOLD = 150; // Emergency cleanup at 150MB
  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor() {
    // Start cleanup interval with proper timer management
    this.startCleanupTimer();
    console.log(`📊 VolumeAggregationService initialized with memory limits:
      - Max trades per pool: ${this.MAX_TRADES_PER_POOL}
      - Max total memory: ${this.MAX_TOTAL_MEMORY_MB}MB
      - Cleanup interval: ${this.CLEANUP_INTERVAL / 1000}s`);
  }

  /**
   * Start the cleanup timer with proper management
   */
  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.cleanupTimer = setInterval(() => this.cleanupOldTrades(), this.CLEANUP_INTERVAL);
  }

  /**
   * Add a new trade and calculate volume metrics with memory management
   */
  public addTrade(poolAddress: string, trade: FormattedTrade): VolumeMetrics {
    if (!this.tradeHistory.has(poolAddress)) {
      this.tradeHistory.set(poolAddress, {
        trades: [],
        lastCleanup: Date.now(),
        memoryUsage: 0
      });
    }

    const poolData = this.tradeHistory.get(poolAddress)!;

    // Add the new trade
    poolData.trades.push(trade);

    // Critical: Enforce per-pool trade limit to prevent memory leaks
    if (poolData.trades.length > this.MAX_TRADES_PER_POOL) {
      // Keep only the most recent trades (sorted by timestamp)
      poolData.trades.sort((a, b) => b.timestamp - a.timestamp);
      poolData.trades = poolData.trades.slice(0, this.MAX_TRADES_PER_POOL);
      console.log(`⚠️ Pool ${poolAddress} exceeded trade limit, trimmed to ${this.MAX_TRADES_PER_POOL} trades`);
    } else {
      // Sort trades by timestamp (newest first) - only if not already trimmed
      poolData.trades.sort((a, b) => b.timestamp - a.timestamp);
    }

    // Update memory usage estimate
    poolData.memoryUsage = this.estimateMemoryUsage(poolData.trades);

    // Check for emergency cleanup if total memory usage is too high
    this.checkEmergencyCleanup();

    // Calculate and return volume metrics
    return this.calculateVolumeMetrics(poolData.trades);
  }

  /**
   * Get current volume metrics for a pool
   */
  public getVolumeMetrics(poolAddress: string): VolumeMetrics | null {
    const poolData = this.tradeHistory.get(poolAddress);
    if (!poolData || poolData.trades.length === 0) {
      return null;
    }

    return this.calculateVolumeMetrics(poolData.trades);
  }

  /**
   * Calculate volume metrics from trade history
   */
  private calculateVolumeMetrics(trades: FormattedTrade[]): VolumeMetrics {
    const now = Date.now();
    const timeframes = {
      '5min': 5 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000
    };

    // Initialize metrics
    const metrics: VolumeMetrics = {
      volume24h: 0,
      volume_5min: 0,
      volume_1h: 0,
      volume_6h: 0,
      buyers_24h: 0,
      sellers_24h: 0,
      buy_volume_24h: 0,
      sell_volume_24h: 0,
      buy_volume_5min: 0,
      sell_volume_5min: 0,
      buy_volume_1h: 0,
      sell_volume_1h: 0,
      buy_volume_6h: 0,
      sell_volume_6h: 0,
      net_volume_5min: 0,
      net_volume_1h: 0,
      net_volume_6h: 0,
      net_volume_24h: 0
    };

    // Separate volumes by timeframe and type
    const volumes = {
      '5min': { buy: 0, sell: 0, buyCount: 0, sellCount: 0 },
      '1h': { buy: 0, sell: 0, buyCount: 0, sellCount: 0 },
      '6h': { buy: 0, sell: 0, buyCount: 0, sellCount: 0 },
      '24h': { buy: 0, sell: 0, buyCount: 0, sellCount: 0 }
    };

    // Process each trade
    for (const trade of trades) {
      const tradeAge = now - trade.timestamp;
      const tradeVolume = trade.tokenAmountUsd || 0;
      const isBuy = trade.type === 'buy';

      // Check each timeframe
      for (const [timeframe, duration] of Object.entries(timeframes)) {
        if (tradeAge <= duration) {
          const volumeData = volumes[timeframe as keyof typeof volumes];
          
          if (isBuy) {
            volumeData.buy += tradeVolume;
            volumeData.buyCount++;
          } else {
            volumeData.sell += tradeVolume;
            volumeData.sellCount++;
          }
        }
      }
    }

    // Calculate final metrics
    metrics.volume_5min = volumes['5min'].buy + volumes['5min'].sell;
    metrics.volume_1h = volumes['1h'].buy + volumes['1h'].sell;
    metrics.volume_6h = volumes['6h'].buy + volumes['6h'].sell;
    metrics.volume24h = volumes['24h'].buy + volumes['24h'].sell;

    metrics.buyers_24h = volumes['24h'].buyCount;
    metrics.sellers_24h = volumes['24h'].sellCount;

    // Separate buy and sell volumes
    metrics.buy_volume_5min = volumes['5min'].buy;
    metrics.sell_volume_5min = volumes['5min'].sell;
    metrics.buy_volume_1h = volumes['1h'].buy;
    metrics.sell_volume_1h = volumes['1h'].sell;
    metrics.buy_volume_6h = volumes['6h'].buy;
    metrics.sell_volume_6h = volumes['6h'].sell;
    metrics.buy_volume_24h = volumes['24h'].buy;
    metrics.sell_volume_24h = volumes['24h'].sell;

    metrics.net_volume_5min = volumes['5min'].buy - volumes['5min'].sell;
    metrics.net_volume_1h = volumes['1h'].buy - volumes['1h'].sell;
    metrics.net_volume_6h = volumes['6h'].buy - volumes['6h'].sell;
    metrics.net_volume_24h = volumes['24h'].buy - volumes['24h'].sell;

    return metrics;
  }

  /**
   * Estimate memory usage for a trade array (rough calculation)
   */
  private estimateMemoryUsage(trades: FormattedTrade[]): number {
    // Rough estimate: ~1KB per trade object (including all properties)
    return trades.length * 1024; // bytes
  }

  /**
   * Check if emergency cleanup is needed based on total memory usage
   */
  private checkEmergencyCleanup(): void {
    const totalMemoryUsage = this.getTotalMemoryUsage();
    const totalMemoryMB = totalMemoryUsage / (1024 * 1024);

    if (totalMemoryMB > this.EMERGENCY_CLEANUP_THRESHOLD) {
      console.warn(`🚨 Emergency cleanup triggered! Total memory: ${totalMemoryMB.toFixed(2)}MB`);
      this.performEmergencyCleanup();
    } else if (totalMemoryMB > this.MAX_TOTAL_MEMORY_MB) {
      console.warn(`⚠️ Memory usage high: ${totalMemoryMB.toFixed(2)}MB, performing aggressive cleanup`);
      this.performAggressiveCleanup();
    }
  }

  /**
   * Get total memory usage across all pools
   */
  private getTotalMemoryUsage(): number {
    let totalUsage = 0;
    for (const poolData of this.tradeHistory.values()) {
      totalUsage += poolData.memoryUsage;
    }
    return totalUsage;
  }

  /**
   * Perform emergency cleanup - drastically reduce trade history
   */
  private performEmergencyCleanup(): void {
    const emergencyLimit = Math.floor(this.MAX_TRADES_PER_POOL / 4); // Keep only 25%
    let cleanedPools = 0;

    for (const [, poolData] of this.tradeHistory.entries()) {
      if (poolData.trades.length > emergencyLimit) {
        poolData.trades = poolData.trades.slice(0, emergencyLimit);
        poolData.memoryUsage = this.estimateMemoryUsage(poolData.trades);
        cleanedPools++;
      }
    }

    console.log(`🚨 Emergency cleanup completed: ${cleanedPools} pools cleaned, limit set to ${emergencyLimit} trades per pool`);
  }

  /**
   * Perform aggressive cleanup - reduce trade history more than normal
   */
  private performAggressiveCleanup(): void {
    const aggressiveLimit = Math.floor(this.MAX_TRADES_PER_POOL / 2); // Keep only 50%
    let cleanedPools = 0;

    for (const [, poolData] of this.tradeHistory.entries()) {
      if (poolData.trades.length > aggressiveLimit) {
        poolData.trades = poolData.trades.slice(0, aggressiveLimit);
        poolData.memoryUsage = this.estimateMemoryUsage(poolData.trades);
        cleanedPools++;
      }
    }

    console.log(`⚠️ Aggressive cleanup completed: ${cleanedPools} pools cleaned, limit set to ${aggressiveLimit} trades per pool`);
  }

  /**
   * Enhanced cleanup with memory management and monitoring
   */
  private cleanupOldTrades(): void {
    const now = Date.now();
    let totalCleaned = 0;
    let poolsRemoved = 0;

    console.log(`🧹 Starting cleanup cycle for ${this.tradeHistory.size} pools`);

    for (const [poolAddress, poolData] of this.tradeHistory.entries()) {
      const initialCount = poolData.trades.length;

      // Remove trades older than 24 hours
      poolData.trades = poolData.trades.filter(
        trade => (now - trade.timestamp) <= this.MAX_HISTORY_AGE
      );

      // Additional cleanup: enforce per-pool limits even during regular cleanup
      if (poolData.trades.length > this.MAX_TRADES_PER_POOL) {
        poolData.trades.sort((a, b) => b.timestamp - a.timestamp);
        poolData.trades = poolData.trades.slice(0, this.MAX_TRADES_PER_POOL);
      }

      const cleanedCount = initialCount - poolData.trades.length;
      totalCleaned += cleanedCount;

      // Remove empty pools
      if (poolData.trades.length === 0) {
        this.tradeHistory.delete(poolAddress);
        poolsRemoved++;
      } else {
        poolData.lastCleanup = now;
        poolData.memoryUsage = this.estimateMemoryUsage(poolData.trades);
      }
    }

    const totalMemoryMB = this.getTotalMemoryUsage() / (1024 * 1024);
    console.log(`✅ Cleanup completed: ${totalCleaned} trades removed, ${poolsRemoved} empty pools removed, total memory: ${totalMemoryMB.toFixed(2)}MB`);
  }

  /**
   * Get trade history for debugging
   */
  public getTradeHistory(poolAddress: string): FormattedTrade[] {
    const poolData = this.tradeHistory.get(poolAddress);
    return poolData ? [...poolData.trades] : [];
  }

  /**
   * Get memory statistics for monitoring
   */
  public getMemoryStats(): PoolMemoryStats[] {
    const stats: PoolMemoryStats[] = [];

    for (const [poolAddress, poolData] of this.tradeHistory.entries()) {
      stats.push({
        poolAddress,
        tradeCount: poolData.trades.length,
        memoryUsage: poolData.memoryUsage,
        lastCleanup: poolData.lastCleanup
      });
    }

    return stats.sort((a, b) => b.memoryUsage - a.memoryUsage); // Sort by memory usage desc
  }

  /**
   * Get total service statistics
   */
  public getServiceStats(): {
    totalPools: number;
    totalTrades: number;
    totalMemoryUsage: number;
    totalMemoryMB: number;
    averageTradesPerPool: number;
    maxTradesPerPool: number;
    lastCleanup: number;
  } {
    let totalTrades = 0;
    let maxTrades = 0;
    let lastCleanup = 0;

    for (const poolData of this.tradeHistory.values()) {
      totalTrades += poolData.trades.length;
      maxTrades = Math.max(maxTrades, poolData.trades.length);
      lastCleanup = Math.max(lastCleanup, poolData.lastCleanup);
    }

    const totalMemoryUsage = this.getTotalMemoryUsage();

    return {
      totalPools: this.tradeHistory.size,
      totalTrades,
      totalMemoryUsage,
      totalMemoryMB: totalMemoryUsage / (1024 * 1024),
      averageTradesPerPool: this.tradeHistory.size > 0 ? totalTrades / this.tradeHistory.size : 0,
      maxTradesPerPool: maxTrades,
      lastCleanup
    };
  }

  /**
   * Force cleanup for a specific pool
   */
  public forceCleanupPool(poolAddress: string): boolean {
    const poolData = this.tradeHistory.get(poolAddress);
    if (!poolData) {
      return false;
    }

    const initialCount = poolData.trades.length;
    const now = Date.now();

    // Remove old trades
    poolData.trades = poolData.trades.filter(
      trade => (now - trade.timestamp) <= this.MAX_HISTORY_AGE
    );

    // Enforce limits
    if (poolData.trades.length > this.MAX_TRADES_PER_POOL) {
      poolData.trades.sort((a, b) => b.timestamp - a.timestamp);
      poolData.trades = poolData.trades.slice(0, this.MAX_TRADES_PER_POOL);
    }

    poolData.memoryUsage = this.estimateMemoryUsage(poolData.trades);
    poolData.lastCleanup = now;

    const cleanedCount = initialCount - poolData.trades.length;
    console.log(`🧹 Force cleanup for pool ${poolAddress}: ${cleanedCount} trades removed`);

    return cleanedCount > 0;
  }

  /**
   * Clear all trade history (for testing) with proper cleanup
   */
  public clearHistory(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.tradeHistory.clear();
    console.log('🗑️ All trade history cleared');
  }

  /**
   * Shutdown the service properly
   */
  public shutdown(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.tradeHistory.clear();
    console.log('🔌 VolumeAggregationService shut down');
  }
}

// Export singleton instance
export const volumeAggregationService = new VolumeAggregationService();
