import { Trade, FormattedTrade } from '../types/index.js';

interface VolumeMetrics {
  volume24h: number;
  volume_5min: number;
  volume_1h: number;
  volume_6h: number;
  buyers_24h: number;
  sellers_24h: number;
  buy_volume_24h: number;
  sell_volume_24h: number;
  buy_volume_5min: number;
  sell_volume_5min: number;
  buy_volume_1h: number;
  sell_volume_1h: number;
  buy_volume_6h: number;
  sell_volume_6h: number;
  net_volume_5min: number;
  net_volume_1h: number;
  net_volume_6h: number;
  net_volume_24h: number;
}

interface TimeframeTrades {
  trades: FormattedTrade[];
  lastCleanup: number;
}

class VolumeAggregationService {
  private tradeHistory: Map<string, TimeframeTrades> = new Map();
  private readonly CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_HISTORY_AGE = 24 * 60 * 60 * 1000; // 24 hours

  constructor() {
    // Start cleanup interval
    setInterval(() => this.cleanupOldTrades(), this.CLEANUP_INTERVAL);
  }

  /**
   * Add a new trade and calculate volume metrics
   */
  public addTrade(poolAddress: string, trade: FormattedTrade): VolumeMetrics {
    if (!this.tradeHistory.has(poolAddress)) {
      this.tradeHistory.set(poolAddress, {
        trades: [],
        lastCleanup: Date.now()
      });
    }

    const poolData = this.tradeHistory.get(poolAddress)!;
    
    // Add the new trade
    poolData.trades.push(trade);
    
    // Sort trades by timestamp (newest first)
    poolData.trades.sort((a, b) => b.timestamp - a.timestamp);
    
    // Calculate and return volume metrics
    return this.calculateVolumeMetrics(poolData.trades);
  }

  /**
   * Get current volume metrics for a pool
   */
  public getVolumeMetrics(poolAddress: string): VolumeMetrics | null {
    const poolData = this.tradeHistory.get(poolAddress);
    if (!poolData || poolData.trades.length === 0) {
      return null;
    }

    return this.calculateVolumeMetrics(poolData.trades);
  }

  /**
   * Calculate volume metrics from trade history
   */
  private calculateVolumeMetrics(trades: FormattedTrade[]): VolumeMetrics {
    const now = Date.now();
    const timeframes = {
      '5min': 5 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000
    };

    // Initialize metrics
    const metrics: VolumeMetrics = {
      volume24h: 0,
      volume_5min: 0,
      volume_1h: 0,
      volume_6h: 0,
      buyers_24h: 0,
      sellers_24h: 0,
      buy_volume_24h: 0,
      sell_volume_24h: 0,
      buy_volume_5min: 0,
      sell_volume_5min: 0,
      buy_volume_1h: 0,
      sell_volume_1h: 0,
      buy_volume_6h: 0,
      sell_volume_6h: 0,
      net_volume_5min: 0,
      net_volume_1h: 0,
      net_volume_6h: 0,
      net_volume_24h: 0
    };

    // Separate volumes by timeframe and type
    const volumes = {
      '5min': { buy: 0, sell: 0, buyCount: 0, sellCount: 0 },
      '1h': { buy: 0, sell: 0, buyCount: 0, sellCount: 0 },
      '6h': { buy: 0, sell: 0, buyCount: 0, sellCount: 0 },
      '24h': { buy: 0, sell: 0, buyCount: 0, sellCount: 0 }
    };

    // Process each trade
    for (const trade of trades) {
      const tradeAge = now - trade.timestamp;
      const tradeVolume = trade.tokenAmountUsd || 0;
      const isBuy = trade.type === 'buy';

      // Check each timeframe
      for (const [timeframe, duration] of Object.entries(timeframes)) {
        if (tradeAge <= duration) {
          const volumeData = volumes[timeframe as keyof typeof volumes];
          
          if (isBuy) {
            volumeData.buy += tradeVolume;
            volumeData.buyCount++;
          } else {
            volumeData.sell += tradeVolume;
            volumeData.sellCount++;
          }
        }
      }
    }

    // Calculate final metrics
    metrics.volume_5min = volumes['5min'].buy + volumes['5min'].sell;
    metrics.volume_1h = volumes['1h'].buy + volumes['1h'].sell;
    metrics.volume_6h = volumes['6h'].buy + volumes['6h'].sell;
    metrics.volume24h = volumes['24h'].buy + volumes['24h'].sell;

    metrics.buyers_24h = volumes['24h'].buyCount;
    metrics.sellers_24h = volumes['24h'].sellCount;

    // Separate buy and sell volumes
    metrics.buy_volume_5min = volumes['5min'].buy;
    metrics.sell_volume_5min = volumes['5min'].sell;
    metrics.buy_volume_1h = volumes['1h'].buy;
    metrics.sell_volume_1h = volumes['1h'].sell;
    metrics.buy_volume_6h = volumes['6h'].buy;
    metrics.sell_volume_6h = volumes['6h'].sell;
    metrics.buy_volume_24h = volumes['24h'].buy;
    metrics.sell_volume_24h = volumes['24h'].sell;

    metrics.net_volume_5min = volumes['5min'].buy - volumes['5min'].sell;
    metrics.net_volume_1h = volumes['1h'].buy - volumes['1h'].sell;
    metrics.net_volume_6h = volumes['6h'].buy - volumes['6h'].sell;
    metrics.net_volume_24h = volumes['24h'].buy - volumes['24h'].sell;

    return metrics;
  }

  /**
   * Clean up old trades to prevent memory leaks
   */
  private cleanupOldTrades(): void {
    const now = Date.now();
    
    for (const [poolAddress, poolData] of this.tradeHistory.entries()) {
      // Remove trades older than 24 hours
      poolData.trades = poolData.trades.filter(
        trade => (now - trade.timestamp) <= this.MAX_HISTORY_AGE
      );
      
      // Remove empty pools
      if (poolData.trades.length === 0) {
        this.tradeHistory.delete(poolAddress);
      } else {
        poolData.lastCleanup = now;
      }
    }
  }

  /**
   * Get trade history for debugging
   */
  public getTradeHistory(poolAddress: string): FormattedTrade[] {
    const poolData = this.tradeHistory.get(poolAddress);
    return poolData ? [...poolData.trades] : [];
  }

  /**
   * Clear all trade history (for testing)
   */
  public clearHistory(): void {
    this.tradeHistory.clear();
  }
}

// Export singleton instance
export const volumeAggregationService = new VolumeAggregationService();
