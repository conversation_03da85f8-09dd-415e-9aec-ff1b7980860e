#!/usr/bin/env node

/**
 * Test script to verify memory leak fixes and multi-pool support
 */

import { volumeAggregationService } from './dist/services/volumeAggregationService.js';

console.log('🧪 Testing Memory Leak Fixes and Multi-Pool Support...\n');

// Test 1: Volume Aggregation Service Memory Management
async function testVolumeAggregationMemory() {
  console.log('1️⃣ Testing Volume Aggregation Memory Management...');
  
  try {
    // Clear any existing data
    volumeAggregationService.clearHistory();
    
    // Create mock trades for multiple pools
    const pools = ['pool1', 'pool2', 'pool3'];
    const tradesPerPool = 1200; // Exceeds the 1000 limit
    
    console.log(`   📊 Adding ${tradesPerPool} trades to ${pools.length} pools...`);
    
    for (const poolAddress of pools) {
      for (let i = 0; i < tradesPerPool; i++) {
        const mockTrade = {
          id: `trade_${poolAddress}_${i}`,
          timestamp: Date.now() - (i * 1000), // Spread trades over time
          type: i % 2 === 0 ? 'buy' : 'sell',
          amount: '100',
          usdAmount: '50',
          mc: '1000000',
          price: '0.5',
          trader: 'test_trader',
          age: '1s',
          txHash: `hash_${i}`,
          marketCap: 1000000,
          tokenAmount: 100,
          tokenAmountUsd: 50,
          actualTokenAmount: 200
        };
        
        volumeAggregationService.addTrade(poolAddress, mockTrade);
      }
    }
    
    // Check memory stats
    const stats = volumeAggregationService.getServiceStats();
    const memoryStats = volumeAggregationService.getMemoryStats();
    
    console.log('   📈 Memory Statistics:');
    console.log(`      - Total pools: ${stats.totalPools}`);
    console.log(`      - Total trades: ${stats.totalTrades}`);
    console.log(`      - Memory usage: ${stats.totalMemoryMB.toFixed(2)}MB`);
    console.log(`      - Average trades per pool: ${stats.averageTradesPerPool.toFixed(0)}`);
    console.log(`      - Max trades per pool: ${stats.maxTradesPerPool}`);
    
    // Verify memory limits are enforced
    let allPoolsWithinLimit = true;
    for (const poolStat of memoryStats) {
      if (poolStat.tradeCount > 1000) {
        allPoolsWithinLimit = false;
        console.log(`   ❌ Pool ${poolStat.poolAddress} has ${poolStat.tradeCount} trades (exceeds limit)`);
      }
    }
    
    if (allPoolsWithinLimit) {
      console.log('   ✅ All pools within trade limit (≤1000 trades)');
    }
    
    // Test force cleanup
    console.log('   🧹 Testing force cleanup...');
    const cleanupResult = volumeAggregationService.forceCleanupPool('pool1');
    console.log(`   🧹 Force cleanup result: ${cleanupResult}`);
    
    return allPoolsWithinLimit;
    
  } catch (error) {
    console.error('   ❌ Volume aggregation test failed:', error.message);
    return false;
  }
}

// Test 2: Memory Cleanup Cycles
async function testMemoryCleanupCycles() {
  console.log('\n2️⃣ Testing Memory Cleanup Cycles...');
  
  try {
    // Add old trades that should be cleaned up
    const oldTimestamp = Date.now() - (25 * 60 * 60 * 1000); // 25 hours ago
    
    for (let i = 0; i < 100; i++) {
      const mockTrade = {
        id: `old_trade_${i}`,
        timestamp: oldTimestamp,
        type: 'buy',
        amount: '100',
        usdAmount: '50',
        mc: '1000000',
        price: '0.5',
        trader: 'test_trader',
        age: '25h',
        txHash: `old_hash_${i}`,
        marketCap: 1000000,
        tokenAmount: 100,
        tokenAmountUsd: 50,
        actualTokenAmount: 200
      };
      
      volumeAggregationService.addTrade('test_cleanup_pool', mockTrade);
    }
    
    const beforeStats = volumeAggregationService.getServiceStats();
    console.log(`   📊 Before cleanup: ${beforeStats.totalTrades} trades`);
    
    // Force cleanup to simulate the cleanup cycle
    volumeAggregationService.forceCleanupPool('test_cleanup_pool');
    
    const afterStats = volumeAggregationService.getServiceStats();
    console.log(`   📊 After cleanup: ${afterStats.totalTrades} trades`);
    
    const tradesRemoved = beforeStats.totalTrades - afterStats.totalTrades;
    console.log(`   🗑️ Trades removed: ${tradesRemoved}`);
    
    return tradesRemoved > 0;
    
  } catch (error) {
    console.error('   ❌ Cleanup cycle test failed:', error.message);
    return false;
  }
}

// Test 3: Service Statistics
async function testServiceStatistics() {
  console.log('\n3️⃣ Testing Service Statistics...');
  
  try {
    const stats = volumeAggregationService.getServiceStats();
    const memoryStats = volumeAggregationService.getMemoryStats();
    
    console.log('   📈 Service Statistics:');
    console.log(`      - Total pools: ${stats.totalPools}`);
    console.log(`      - Total trades: ${stats.totalTrades}`);
    console.log(`      - Memory usage: ${stats.totalMemoryMB.toFixed(2)}MB`);
    console.log(`      - Average trades per pool: ${stats.averageTradesPerPool.toFixed(0)}`);
    
    console.log('   📊 Top 3 pools by memory usage:');
    memoryStats.slice(0, 3).forEach((pool, index) => {
      console.log(`      ${index + 1}. ${pool.poolAddress}: ${pool.tradeCount} trades, ${(pool.memoryUsage / 1024).toFixed(1)}KB`);
    });
    
    return stats.totalPools > 0 && memoryStats.length > 0;
    
  } catch (error) {
    console.error('   ❌ Statistics test failed:', error.message);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🎯 Running Memory Management Tests...\n');
  
  const results = {
    memoryManagement: await testVolumeAggregationMemory(),
    cleanupCycles: await testMemoryCleanupCycles(),
    statistics: await testServiceStatistics()
  };
  
  console.log('\n📋 Test Results Summary:');
  console.log('========================');
  
  let allPassed = true;
  for (const [test, passed] of Object.entries(results)) {
    const status = passed ? '✅ PASSED' : '❌ FAILED';
    console.log(`${test}: ${status}`);
    if (!passed) allPassed = false;
  }
  
  console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  // Cleanup
  volumeAggregationService.shutdown();
  
  process.exit(allPassed ? 0 : 1);
}

// Run tests
runAllTests().catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});
