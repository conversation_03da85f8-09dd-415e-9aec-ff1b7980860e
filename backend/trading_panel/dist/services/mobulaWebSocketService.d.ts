import { FormattedTrade } from '../types';
export declare class MobulaWebSocketService {
    private ws;
    private isConnected;
    private reconnectAttempts;
    private maxReconnectAttempts;
    private reconnectDelay;
    private currentPoolAddress;
    private processedTradeIds;
    private onTradeCallback;
    private onErrorCallback;
    private onConnectionCallback;
    private readonly SOL_ADDRESS;
    private readonly MOBULA_API_KEY;
    private readonly MOBULA_WS_URL;
    constructor();
    onTrade(callback: (trade: FormattedTrade) => void): void;
    onError(callback: (error: string) => void): void;
    onConnection(callback: (connected: boolean) => void): void;
    connect(poolAddress: string): Promise<void>;
    private subscribe;
    private handleMessage;
    private fetchHistoricalTrades;
    private scheduleReconnect;
    disconnect(): void;
    getConnectionStatus(): {
        connected: boolean;
        poolAddress: string | null;
    };
    private getTradeTimestamp;
    private getTokenPair;
    private generateTradeId;
    private formatTrade;
    private formatTokenAmount;
    private formatTradeValue;
    private formatMarketCap;
    private formatAge;
    private formatTraderHash;
    private getFullHash;
}
//# sourceMappingURL=mobulaWebSocketService.d.ts.map