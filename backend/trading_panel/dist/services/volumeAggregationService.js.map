{"version": 3, "file": "volumeAggregationService.js", "sourceRoot": "", "sources": ["../../src/services/volumeAggregationService.ts"], "names": [], "mappings": ";;;AAoCA,MAAM,wBAAwB;IAS5B;QARQ,iBAAY,GAAiC,IAAI,GAAG,EAAE,CAAC;QAC9C,qBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,4BAA4B;QAC9D,oBAAe,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;QAClD,wBAAmB,GAAG,IAAI,CAAC,CAAC,kCAAkC;QAC9D,wBAAmB,GAAG,GAAG,CAAC,CAAC,mCAAmC;QAC9D,gCAA2B,GAAG,GAAG,CAAC,CAAC,6BAA6B;QACzE,iBAAY,GAA0B,IAAI,CAAC;QAGjD,sDAAsD;QACtD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC;+BACe,IAAI,CAAC,mBAAmB;4BAC3B,IAAI,CAAC,mBAAmB;4BACxB,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACxF,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,WAAmB,EAAE,KAAqB;QACxD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE;gBACjC,MAAM,EAAE,EAAE;gBACV,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;gBACvB,WAAW,EAAE,CAAC;aACf,CAAC,CAAC;QACL,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAE,CAAC;QAErD,oBAAoB;QACpB,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE5B,iEAAiE;QACjE,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACtD,yDAAyD;YACzD,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;YAC1D,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACrE,OAAO,CAAC,GAAG,CAAC,WAAW,WAAW,qCAAqC,IAAI,CAAC,mBAAmB,SAAS,CAAC,CAAC;QAC5G,CAAC;aAAM,CAAC;YACN,wEAAwE;YACxE,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;QAC5D,CAAC;QAED,+BAA+B;QAC/B,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEjE,gEAAgE;QAChE,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,sCAAsC;QACtC,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,WAAmB;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,MAAwB;QACrD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG;YACjB,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;YACrB,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;YACpB,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;YACxB,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;SAC3B,CAAC;QAEF,qBAAqB;QACrB,MAAM,OAAO,GAAkB;YAC7B,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,CAAC;YACd,cAAc,EAAE,CAAC;YACjB,eAAe,EAAE,CAAC;YAClB,eAAe,EAAE,CAAC;YAClB,gBAAgB,EAAE,CAAC;YACnB,aAAa,EAAE,CAAC;YAChB,cAAc,EAAE,CAAC;YACjB,aAAa,EAAE,CAAC;YAChB,cAAc,EAAE,CAAC;YACjB,eAAe,EAAE,CAAC;YAClB,aAAa,EAAE,CAAC;YAChB,aAAa,EAAE,CAAC;YAChB,cAAc,EAAE,CAAC;SAClB,CAAC;QAEF,yCAAyC;QACzC,MAAM,OAAO,GAAG;YACd,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE;YACtD,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE;YACpD,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE;YACpD,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE;SACtD,CAAC;QAEF,qBAAqB;QACrB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC;YACvC,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,IAAI,CAAC,CAAC;YAC9C,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC;YAEnC,uBAAuB;YACvB,KAAK,MAAM,CAAC,SAAS,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC/D,IAAI,QAAQ,IAAI,QAAQ,EAAE,CAAC;oBACzB,MAAM,UAAU,GAAG,OAAO,CAAC,SAAiC,CAAC,CAAC;oBAE9D,IAAI,KAAK,EAAE,CAAC;wBACV,UAAU,CAAC,GAAG,IAAI,WAAW,CAAC;wBAC9B,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACxB,CAAC;yBAAM,CAAC;wBACN,UAAU,CAAC,IAAI,IAAI,WAAW,CAAC;wBAC/B,UAAU,CAAC,SAAS,EAAE,CAAC;oBACzB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,0BAA0B;QAC1B,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;QACjE,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QAC3D,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QAC3D,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;QAE7D,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;QAC7C,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;QAE/C,gCAAgC;QAChC,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;QAC9C,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;QAChD,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;QAC1C,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QAC5C,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;QAC1C,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QAC5C,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;QAC5C,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;QAE9C,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;QACrE,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QAC/D,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QAC/D,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;QAElE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,MAAwB;QAClD,mEAAmE;QACnE,OAAO,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,QAAQ;IACvC,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACpD,MAAM,aAAa,GAAG,gBAAgB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAEvD,IAAI,aAAa,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACrD,OAAO,CAAC,IAAI,CAAC,iDAAiD,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC5F,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACjC,CAAC;aAAM,IAAI,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,yBAAyB,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,mCAAmC,CAAC,CAAC;YACnG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YAClD,UAAU,IAAI,QAAQ,CAAC,WAAW,CAAC;QACrC,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB;QACjF,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,CAAC,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,cAAc,EAAE,CAAC;gBAC5C,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;gBAC3D,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACjE,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,mCAAmC,YAAY,gCAAgC,cAAc,kBAAkB,CAAC,CAAC;IAC/H,CAAC;IAED;;OAEG;IACK,wBAAwB;QAC9B,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB;QAClF,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,CAAC,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,eAAe,EAAE,CAAC;gBAC7C,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;gBAC5D,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACjE,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,oCAAoC,YAAY,gCAAgC,eAAe,kBAAkB,CAAC,CAAC;IACjI,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,OAAO,CAAC,GAAG,CAAC,iCAAiC,IAAI,CAAC,YAAY,CAAC,IAAI,QAAQ,CAAC,CAAC;QAE7E,KAAK,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;YAClE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;YAE5C,oCAAoC;YACpC,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CACtC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,eAAe,CACzD,CAAC;YAEF,0EAA0E;YAC1E,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACtD,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;gBAC1D,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACvE,CAAC;YAED,MAAM,YAAY,GAAG,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;YAC3D,YAAY,IAAI,YAAY,CAAC;YAE7B,qBAAqB;YACrB,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBACtC,YAAY,EAAE,CAAC;YACjB,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,WAAW,GAAG,GAAG,CAAC;gBAC3B,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QACjE,OAAO,CAAC,GAAG,CAAC,wBAAwB,YAAY,oBAAoB,YAAY,uCAAuC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACvJ,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,WAAmB;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACpD,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,cAAc;QACnB,MAAM,KAAK,GAAsB,EAAE,CAAC;QAEpC,KAAK,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;YAClE,KAAK,CAAC,IAAI,CAAC;gBACT,WAAW;gBACX,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM;gBAClC,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,WAAW,EAAE,QAAQ,CAAC,WAAW;aAClC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,4BAA4B;IAC1F,CAAC;IAED;;OAEG;IACI,eAAe;QASpB,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YAClD,WAAW,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;YACtC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACxD,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEpD,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;YAClC,WAAW;YACX,gBAAgB;YAChB,aAAa,EAAE,gBAAgB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;YAC/C,oBAAoB,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3F,gBAAgB,EAAE,SAAS;YAC3B,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,WAAmB;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;QAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,oBAAoB;QACpB,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CACtC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,eAAe,CACzD,CAAC;QAEF,iBAAiB;QACjB,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACtD,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC;YAC1D,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACvE,CAAC;QAED,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACjE,QAAQ,CAAC,WAAW,GAAG,GAAG,CAAC;QAE3B,MAAM,YAAY,GAAG,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,6BAA6B,WAAW,KAAK,YAAY,iBAAiB,CAAC,CAAC;QAExF,OAAO,YAAY,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC3B,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC3B,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACvD,CAAC;CACF;AAED,4BAA4B;AACf,QAAA,wBAAwB,GAAG,IAAI,wBAAwB,EAAE,CAAC"}