"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketServerService = void 0;
const ws_1 = __importStar(require("ws"));
const mobulaWebSocketService_1 = require("./mobulaWebSocketService");
class WebSocketServerService {
    constructor(server) {
        this.clients = new Map(); // Track client subscriptions
        this.poolSubscriptions = new Map(); // Multi-pool support
        this.MAX_POOLS_PER_SERVICE = 50; // Limit concurrent pools
        this.POOL_CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
        this.POOL_IDLE_TIMEOUT = 10 * 60 * 1000; // 10 minutes
        this.cleanupTimer = null;
        this.wss = new ws_1.WebSocketServer({ server });
        this.setupWebSocketServer();
        this.startPoolCleanup();
        console.log('🔧 Multi-Pool WebSocket Server initialized');
        console.log(`📊 Configuration:
      - Max pools per service: ${this.MAX_POOLS_PER_SERVICE}
      - Pool cleanup interval: ${this.POOL_CLEANUP_INTERVAL / 1000}s
      - Pool idle timeout: ${this.POOL_IDLE_TIMEOUT / 1000}s`);
    }
    /**
     * Start pool cleanup timer
     */
    startPoolCleanup() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
        this.cleanupTimer = setInterval(() => this.cleanupIdlePools(), this.POOL_CLEANUP_INTERVAL);
    }
    /**
     * Setup Mobula callbacks for a specific pool
     */
    setupMobulaCallbacks(poolAddress, mobulaService) {
        // Handle new trades from Mobula for this specific pool
        mobulaService.onTrade((trade) => {
            console.log(`📊 [POOL ${poolAddress}] Broadcasting trade to clients:`, {
                tradeId: trade.id,
                hasPairData: !!trade.pairData,
                pairDataLiquidity: trade.pairData?.liquidity,
                liquidityType: typeof trade.pairData?.liquidity,
                pairDataKeys: trade.pairData ? Object.keys(trade.pairData) : []
            });
            const message = {
                type: 'trade',
                data: trade
            };
            // Broadcast only to clients subscribed to this pool
            this.broadcastToPoolClients(poolAddress, message);
        });
        // Handle Mobula connection errors for this pool
        mobulaService.onError((error) => {
            console.error(`❌ [POOL ${poolAddress}] Mobula error:`, error);
            this.broadcastToPoolClients(poolAddress, {
                type: 'error',
                error: error
            });
        });
        // Handle Mobula connection status for this pool
        mobulaService.onConnection((connected) => {
            console.log(`🔌 [POOL ${poolAddress}] Mobula connection status: ${connected ? 'Connected' : 'Disconnected'}`);
            this.broadcastToPoolClients(poolAddress, {
                type: connected ? 'connected' : 'disconnected',
                data: { connected, poolAddress }
            });
        });
    }
    setupWebSocketServer() {
        this.wss.on('connection', (ws) => {
            console.log('👤 New client connected');
            // Send initial status (no pool subscribed yet)
            ws.send(JSON.stringify({
                type: 'connected',
                data: {
                    connected: false,
                    message: 'Connected to WebSocket server. Send subscribe message to start receiving trades.',
                    availablePools: this.poolSubscriptions.size,
                    maxPools: this.MAX_POOLS_PER_SERVICE
                }
            }));
            // Handle client messages
            ws.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    this.handleClientMessage(ws, message);
                }
                catch (error) {
                    console.error('❌ Error parsing client message:', error);
                    ws.send(JSON.stringify({
                        type: 'error',
                        error: 'Invalid message format'
                    }));
                }
            });
            // Handle client disconnect
            ws.on('close', () => {
                console.log('👤 Client disconnected');
                this.handleClientDisconnect(ws);
            });
            // Handle client errors
            ws.on('error', (error) => {
                console.error('❌ Client WebSocket error:', error);
                this.handleClientDisconnect(ws);
            });
        });
    }
    /**
     * Handle client disconnect with proper cleanup
     */
    handleClientDisconnect(ws) {
        const clientSub = this.clients.get(ws);
        if (clientSub) {
            console.log(`👤 Client disconnected from pool: ${clientSub.poolAddress}`);
            this.unsubscribeClientFromPool(ws, clientSub.poolAddress);
        }
        this.clients.delete(ws);
    }
    handleClientMessage(ws, message) {
        console.log('📨 Received client message:', message);
        switch (message.action) {
            case 'subscribe':
                this.handleSubscribe(ws, message.poolAddress);
                break;
            case 'unsubscribe':
                this.handleUnsubscribe(ws);
                break;
            default:
                ws.send(JSON.stringify({
                    type: 'error',
                    error: 'Unknown action'
                }));
        }
    }
    async handleSubscribe(ws, poolAddress) {
        try {
            console.log(`📡 Client subscribing to pool: ${poolAddress}`);
            // Check if client is already subscribed to a different pool
            const existingSubscription = this.clients.get(ws);
            if (existingSubscription && existingSubscription.poolAddress !== poolAddress) {
                console.log(`🔄 Client switching from pool ${existingSubscription.poolAddress} to ${poolAddress}`);
                this.unsubscribeClientFromPool(ws, existingSubscription.poolAddress);
            }
            // Check pool limits
            if (this.poolSubscriptions.size >= this.MAX_POOLS_PER_SERVICE && !this.poolSubscriptions.has(poolAddress)) {
                throw new Error(`Maximum number of pools (${this.MAX_POOLS_PER_SERVICE}) reached`);
            }
            // Get or create pool subscription
            let poolSub = this.poolSubscriptions.get(poolAddress);
            if (!poolSub) {
                console.log(`🆕 Creating new pool subscription for: ${poolAddress}`);
                poolSub = await this.createPoolSubscription(poolAddress);
                this.poolSubscriptions.set(poolAddress, poolSub);
            }
            // Add client to pool
            poolSub.clients.add(ws);
            poolSub.lastActivity = Date.now();
            // Update client subscription tracking
            this.clients.set(ws, {
                client: ws,
                poolAddress,
                subscribedAt: Date.now()
            });
            console.log(`✅ Client subscribed to pool ${poolAddress} (${poolSub.clients.size} clients total)`);
            // Confirm subscription to client
            ws.send(JSON.stringify({
                type: 'connected',
                data: {
                    connected: true,
                    poolAddress: poolAddress,
                    message: 'Successfully subscribed to trade feed',
                    clientsInPool: poolSub.clients.size,
                    totalPools: this.poolSubscriptions.size
                }
            }));
        }
        catch (error) {
            console.error('❌ Error subscribing to pool:', error);
            ws.send(JSON.stringify({
                type: 'error',
                error: `Failed to subscribe: ${error.message}`
            }));
        }
    }
    /**
     * Create a new pool subscription with Mobula service
     */
    async createPoolSubscription(poolAddress) {
        const mobulaService = new mobulaWebSocketService_1.MobulaWebSocketService();
        // Setup callbacks for this pool
        this.setupMobulaCallbacks(poolAddress, mobulaService);
        // Connect to Mobula for this pool
        await mobulaService.connect(poolAddress);
        return {
            poolAddress,
            clients: new Set(),
            mobulaService,
            createdAt: Date.now(),
            lastActivity: Date.now()
        };
    }
    /**
     * Unsubscribe client from a specific pool
     */
    unsubscribeClientFromPool(ws, poolAddress) {
        const poolSub = this.poolSubscriptions.get(poolAddress);
        if (poolSub) {
            poolSub.clients.delete(ws);
            console.log(`📡 Client unsubscribed from pool ${poolAddress} (${poolSub.clients.size} clients remaining)`);
            // If no clients left in this pool, clean it up
            if (poolSub.clients.size === 0) {
                console.log(`🗑️ No clients left in pool ${poolAddress}, cleaning up`);
                poolSub.mobulaService.disconnect();
                this.poolSubscriptions.delete(poolAddress);
            }
        }
    }
    handleUnsubscribe(ws) {
        const clientSub = this.clients.get(ws);
        if (!clientSub) {
            ws.send(JSON.stringify({
                type: 'error',
                error: 'Client is not subscribed to any pool'
            }));
            return;
        }
        console.log(`📡 Client unsubscribing from pool: ${clientSub.poolAddress}`);
        this.unsubscribeClientFromPool(ws, clientSub.poolAddress);
        this.clients.delete(ws);
        ws.send(JSON.stringify({
            type: 'disconnected',
            data: {
                connected: false,
                poolAddress: clientSub.poolAddress,
                message: 'Unsubscribed from trade feed'
            }
        }));
    }
    /**
     * Broadcast message to clients subscribed to a specific pool
     */
    broadcastToPoolClients(poolAddress, message) {
        const poolSub = this.poolSubscriptions.get(poolAddress);
        if (!poolSub) {
            console.warn(`⚠️ Attempted to broadcast to non-existent pool: ${poolAddress}`);
            return;
        }
        try {
            // Validate message structure
            if (!message || typeof message !== 'object') {
                console.error(`❌ [POOL ${poolAddress}] Invalid message structure:`, message);
                return;
            }
            if (!message.type || !['trade', 'error', 'connected', 'disconnected'].includes(message.type)) {
                console.error(`❌ [POOL ${poolAddress}] Invalid message type:`, message.type);
                return;
            }
            // Safe JSON stringify with circular reference handling
            const seenObjects = new WeakSet();
            const messageStr = JSON.stringify(message, (_key, value) => {
                // Handle potential circular references
                if (typeof value === 'object' && value !== null) {
                    if (seenObjects.has(value)) {
                        return '[Circular Reference]';
                    }
                    seenObjects.add(value);
                }
                return value;
            });
            let successCount = 0;
            const clientsToRemove = [];
            // Send to all clients in this pool
            poolSub.clients.forEach((client) => {
                if (client.readyState === ws_1.default.OPEN) {
                    try {
                        client.send(messageStr);
                        successCount++;
                    }
                    catch (error) {
                        console.error(`❌ Error sending message to client in pool ${poolAddress}:`, error);
                        clientsToRemove.push(client);
                    }
                }
                else {
                    // Mark closed clients for removal
                    clientsToRemove.push(client);
                }
            });
            // Clean up closed clients
            clientsToRemove.forEach(client => {
                poolSub.clients.delete(client);
                this.clients.delete(client);
            });
            // Update pool activity
            poolSub.lastActivity = Date.now();
            console.log(`📡 [POOL ${poolAddress}] Broadcasted ${message.type} to ${successCount}/${poolSub.clients.size} clients`);
        }
        catch (error) {
            console.error(`❌ [POOL ${poolAddress}] Error serializing message for broadcast:`, error);
            console.error('❌ Problematic message:', message);
        }
    }
    /**
     * Clean up idle pools that have no activity
     */
    cleanupIdlePools() {
        const now = Date.now();
        const poolsToRemove = [];
        console.log(`🧹 Starting pool cleanup cycle (${this.poolSubscriptions.size} pools)`);
        for (const [poolAddress, poolSub] of this.poolSubscriptions.entries()) {
            const idleTime = now - poolSub.lastActivity;
            // Remove pools with no clients or that have been idle too long
            if (poolSub.clients.size === 0 || idleTime > this.POOL_IDLE_TIMEOUT) {
                console.log(`🗑️ Removing idle pool ${poolAddress} (clients: ${poolSub.clients.size}, idle: ${Math.round(idleTime / 1000)}s)`);
                poolSub.mobulaService.disconnect();
                poolsToRemove.push(poolAddress);
            }
        }
        // Remove idle pools
        poolsToRemove.forEach(poolAddress => {
            this.poolSubscriptions.delete(poolAddress);
        });
        if (poolsToRemove.length > 0) {
            console.log(`✅ Pool cleanup completed: ${poolsToRemove.length} pools removed, ${this.poolSubscriptions.size} pools remaining`);
        }
    }
    /**
     * Get comprehensive server status
     */
    getStatus() {
        const now = Date.now();
        const pools = Array.from(this.poolSubscriptions.entries()).map(([poolAddress, poolSub]) => ({
            poolAddress,
            clientCount: poolSub.clients.size,
            connected: poolSub.mobulaService.getConnectionStatus().connected,
            createdAt: poolSub.createdAt,
            lastActivity: poolSub.lastActivity,
            idleTime: now - poolSub.lastActivity
        }));
        return {
            clientCount: this.clients.size,
            poolCount: this.poolSubscriptions.size,
            pools,
            maxPools: this.MAX_POOLS_PER_SERVICE,
            memoryUsage: {
                totalClients: this.clients.size,
                totalPools: this.poolSubscriptions.size
            }
        };
    }
    /**
     * Get pool-specific statistics
     */
    getPoolStats(poolAddress) {
        const poolSub = this.poolSubscriptions.get(poolAddress);
        if (!poolSub) {
            return { exists: false };
        }
        const now = Date.now();
        return {
            exists: true,
            clientCount: poolSub.clients.size,
            connected: poolSub.mobulaService.getConnectionStatus().connected,
            createdAt: poolSub.createdAt,
            lastActivity: poolSub.lastActivity,
            idleTime: now - poolSub.lastActivity
        };
    }
    /**
     * Force cleanup of a specific pool
     */
    forceCleanupPool(poolAddress) {
        const poolSub = this.poolSubscriptions.get(poolAddress);
        if (!poolSub) {
            return false;
        }
        console.log(`🗑️ Force cleanup of pool: ${poolAddress}`);
        // Notify clients
        this.broadcastToPoolClients(poolAddress, {
            type: 'disconnected',
            data: {
                connected: false,
                poolAddress,
                message: 'Pool forcibly cleaned up by server'
            }
        });
        // Disconnect Mobula service
        poolSub.mobulaService.disconnect();
        // Remove client subscriptions
        poolSub.clients.forEach(client => {
            this.clients.delete(client);
        });
        // Remove pool
        this.poolSubscriptions.delete(poolAddress);
        return true;
    }
    /**
     * Shutdown the server with proper cleanup
     */
    shutdown() {
        console.log('🔌 Shutting down Multi-Pool WebSocket server...');
        // Stop cleanup timer
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
        // Disconnect all Mobula services
        for (const [poolAddress, poolSub] of this.poolSubscriptions.entries()) {
            console.log(`📡 Disconnecting from pool: ${poolAddress}`);
            poolSub.mobulaService.disconnect();
        }
        // Close all client connections
        this.clients.forEach((clientSub) => {
            if (clientSub.client.readyState === ws_1.default.OPEN) {
                clientSub.client.close(1000, 'Server shutdown');
            }
        });
        // Clear all data structures
        this.clients.clear();
        this.poolSubscriptions.clear();
        // Close the WebSocket server
        this.wss.close(() => {
            console.log('✅ Multi-Pool WebSocket server shut down');
        });
    }
}
exports.WebSocketServerService = WebSocketServerService;
//# sourceMappingURL=webSocketServer.js.map