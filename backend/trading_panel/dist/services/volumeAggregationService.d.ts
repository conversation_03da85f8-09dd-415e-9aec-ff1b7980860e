import { FormattedTrade } from '../types/index.js';
interface VolumeMetrics {
    volume24h: number;
    volume_5min: number;
    volume_1h: number;
    volume_6h: number;
    buyers_24h: number;
    sellers_24h: number;
    buy_volume_24h: number;
    sell_volume_24h: number;
    buy_volume_5min: number;
    sell_volume_5min: number;
    buy_volume_1h: number;
    sell_volume_1h: number;
    buy_volume_6h: number;
    sell_volume_6h: number;
    net_volume_5min: number;
    net_volume_1h: number;
    net_volume_6h: number;
    net_volume_24h: number;
}
interface PoolMemoryStats {
    poolAddress: string;
    tradeCount: number;
    memoryUsage: number;
    lastCleanup: number;
}
declare class VolumeAggregationService {
    private tradeHistory;
    private readonly CLEANUP_INTERVAL;
    private readonly MAX_HISTORY_AGE;
    private readonly MAX_TRADES_PER_POOL;
    private readonly MAX_TOTAL_MEMORY_MB;
    private readonly EMERGENCY_CLEANUP_THRESHOLD;
    private cleanupTimer;
    constructor();
    /**
     * Start the cleanup timer with proper management
     */
    private startCleanupTimer;
    /**
     * Add a new trade and calculate volume metrics with memory management
     */
    addTrade(poolAddress: string, trade: FormattedTrade): VolumeMetrics;
    /**
     * Get current volume metrics for a pool
     */
    getVolumeMetrics(poolAddress: string): VolumeMetrics | null;
    /**
     * Calculate volume metrics from trade history
     */
    private calculateVolumeMetrics;
    /**
     * Estimate memory usage for a trade array (rough calculation)
     */
    private estimateMemoryUsage;
    /**
     * Check if emergency cleanup is needed based on total memory usage
     */
    private checkEmergencyCleanup;
    /**
     * Get total memory usage across all pools
     */
    private getTotalMemoryUsage;
    /**
     * Perform emergency cleanup - drastically reduce trade history
     */
    private performEmergencyCleanup;
    /**
     * Perform aggressive cleanup - reduce trade history more than normal
     */
    private performAggressiveCleanup;
    /**
     * Enhanced cleanup with memory management and monitoring
     */
    private cleanupOldTrades;
    /**
     * Get trade history for debugging
     */
    getTradeHistory(poolAddress: string): FormattedTrade[];
    /**
     * Get memory statistics for monitoring
     */
    getMemoryStats(): PoolMemoryStats[];
    /**
     * Get total service statistics
     */
    getServiceStats(): {
        totalPools: number;
        totalTrades: number;
        totalMemoryUsage: number;
        totalMemoryMB: number;
        averageTradesPerPool: number;
        maxTradesPerPool: number;
        lastCleanup: number;
    };
    /**
     * Force cleanup for a specific pool
     */
    forceCleanupPool(poolAddress: string): boolean;
    /**
     * Clear all trade history (for testing) with proper cleanup
     */
    clearHistory(): void;
    /**
     * Shutdown the service properly
     */
    shutdown(): void;
}
export declare const volumeAggregationService: VolumeAggregationService;
export {};
//# sourceMappingURL=volumeAggregationService.d.ts.map