"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const http_1 = require("http");
const webSocketServer_1 = require("./services/webSocketServer");
const tradingPanelController_1 = require("./controllers/tradingPanelController");
const tradingPanelRoutes_1 = __importDefault(require("./routes/tradingPanelRoutes"));
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 5003;
// Middleware
app.use((0, cors_1.default)({
    origin: ['http://localhost:4001', 'http://localhost:3000'], // Frontend URLs
    credentials: true
}));
app.use(express_1.default.json());
// Routes
app.use('/api/trading-panel', tradingPanelRoutes_1.default);
// Create HTTP server
const server = (0, http_1.createServer)(app);
// Initialize WebSocket server
const wsServer = new webSocketServer_1.WebSocketServerService(server);
(0, tradingPanelController_1.setWebSocketServer)(wsServer);
// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🔄 SIGTERM received, shutting down gracefully...');
    wsServer.shutdown();
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    console.log('🔄 SIGINT received, shutting down gracefully...');
    wsServer.shutdown();
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});
// Start server
server.listen(PORT, () => {
    console.log(`🚀 Trading Panel service running on port ${PORT}`);
    console.log(`📡 WebSocket server available at ws://localhost:${PORT}`);
    console.log(`🌐 HTTP API available at http://localhost:${PORT}/api/trading-panel`);
});
//# sourceMappingURL=index.js.map