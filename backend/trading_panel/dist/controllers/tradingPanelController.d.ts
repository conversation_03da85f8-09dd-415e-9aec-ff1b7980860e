import { Request, Response } from 'express';
import { WebSocketServerService } from '../services/webSocketServer';
export declare const setWebSocketServer: (server: WebSocketServerService) => void;
export declare const getTradingPanelConfig: (req: Request, res: Response) => Promise<void>;
export declare const getWebSocketStatus: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const healthCheck: (req: Request, res: Response) => Promise<void>;
export declare const getMemoryStats: (req: Request, res: Response) => Promise<void>;
export declare const forceCleanup: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=tradingPanelController.d.ts.map