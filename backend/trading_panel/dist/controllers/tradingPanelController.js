"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.forceCleanup = exports.getMemoryStats = exports.healthCheck = exports.getWebSocketStatus = exports.getTradingPanelConfig = exports.setWebSocketServer = void 0;
const tradingPanelService_1 = require("../services/tradingPanelService");
const volumeAggregationService_1 = require("../services/volumeAggregationService");
const tradingPanelService = new tradingPanelService_1.TradingPanelService();
let wsServer = null;
// Set WebSocket server reference
const setWebSocketServer = (server) => {
    wsServer = server;
};
exports.setWebSocketServer = setWebSocketServer;
const getTradingPanelConfig = async (req, res) => {
    try {
        const config = await tradingPanelService.getConfig();
        res.json({
            success: true,
            data: config
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: 'Failed to get trading panel config'
        });
    }
};
exports.getTradingPanelConfig = getTradingPanelConfig;
// Get WebSocket server status
const getWebSocketStatus = async (req, res) => {
    try {
        if (!wsServer) {
            return res.status(503).json({
                success: false,
                error: 'WebSocket server not initialized'
            });
        }
        const status = wsServer.getStatus();
        res.json({
            success: true,
            data: {
                ...status,
                endpoint: 'ws://localhost:5003'
            }
        });
    }
    catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
};
exports.getWebSocketStatus = getWebSocketStatus;
// Enhanced health check with multi-pool WebSocket status and memory stats
const healthCheck = async (req, res) => {
    try {
        const wsStatus = wsServer ? wsServer.getStatus() : null;
        const volumeStats = volumeAggregationService_1.volumeAggregationService.getServiceStats();
        res.json({
            success: true,
            data: {
                service: 'trading_panel',
                status: 'OK',
                timestamp: new Date().toISOString(),
                websocket: wsStatus ? {
                    totalClients: wsStatus.clientCount,
                    activePools: wsStatus.poolCount,
                    maxPools: wsStatus.maxPools,
                    pools: wsStatus.pools.map(pool => ({
                        poolAddress: pool.poolAddress,
                        clients: pool.clientCount,
                        connected: pool.connected,
                        idleTime: Math.round(pool.idleTime / 1000) + 's'
                    }))
                } : 'Not initialized',
                volumeAggregation: {
                    totalPools: volumeStats.totalPools,
                    totalTrades: volumeStats.totalTrades,
                    memoryUsage: `${volumeStats.totalMemoryMB.toFixed(2)}MB`,
                    averageTradesPerPool: Math.round(volumeStats.averageTradesPerPool),
                    maxTradesPerPool: volumeStats.maxTradesPerPool
                }
            }
        });
    }
    catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
};
exports.healthCheck = healthCheck;
// Get detailed memory statistics
const getMemoryStats = async (req, res) => {
    try {
        const volumeStats = volumeAggregationService_1.volumeAggregationService.getServiceStats();
        const memoryStats = volumeAggregationService_1.volumeAggregationService.getMemoryStats();
        const wsStatus = wsServer ? wsServer.getStatus() : null;
        res.json({
            success: true,
            data: {
                volumeAggregation: volumeStats,
                poolMemoryBreakdown: memoryStats,
                websocketPools: wsStatus?.pools || [],
                systemMemory: {
                    nodeMemory: process.memoryUsage(),
                    uptime: process.uptime()
                }
            }
        });
    }
    catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
};
exports.getMemoryStats = getMemoryStats;
// Force cleanup of volume aggregation service
const forceCleanup = async (req, res) => {
    try {
        const { poolAddress } = req.body;
        let volumeCleanup = false;
        let wsCleanup = false;
        if (poolAddress) {
            // Clean specific pool
            volumeCleanup = volumeAggregationService_1.volumeAggregationService.forceCleanupPool(poolAddress);
            wsCleanup = wsServer ? wsServer.forceCleanupPool(poolAddress) : false;
        }
        else {
            // Clean all pools - not implemented for safety
            res.status(400).json({
                success: false,
                error: 'Pool address required for cleanup. Use /force-cleanup-all for complete cleanup.'
            });
            return;
        }
        res.json({
            success: true,
            data: {
                poolAddress,
                volumeAggregationCleaned: volumeCleanup,
                websocketPoolCleaned: wsCleanup,
                message: `Cleanup completed for pool: ${poolAddress}`
            }
        });
    }
    catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
};
exports.forceCleanup = forceCleanup;
//# sourceMappingURL=tradingPanelController.js.map