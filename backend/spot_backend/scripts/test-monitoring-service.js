#!/usr/bin/env node

/**
 * Test script for Limit Order Monitoring Service
 * 
 * This script tests the monitoring service functionality by:
 * 1. Checking service health
 * 2. Viewing active subscriptions
 * 3. Testing manual refresh
 * 4. Monitoring service metrics
 */

import fetch from 'node-fetch';
import { setTimeout } from 'timers/promises';

const BASE_URL = process.env.SPOT_BACKEND_URL || 'http://localhost:5001';
const TEST_DURATION = 60000; // 1 minute test

class MonitoringServiceTester {
  constructor() {
    this.baseUrl = BASE_URL;
    this.testResults = {
      healthCheck: null,
      subscriptions: null,
      refresh: null,
      monitoring: []
    };
  }

  /**
   * Run all tests
   */
  async runTests() {
    console.log('🧪 Starting Limit Order Monitoring Service Tests...\n');
    console.log(`📡 Testing against: ${this.baseUrl}\n`);

    try {
      // Test 1: Health Check
      await this.testHealthCheck();
      
      // Test 2: View Subscriptions
      await this.testSubscriptions();
      
      // Test 3: Manual Refresh
      await this.testManualRefresh();
      
      // Test 4: Monitor Service for 1 minute
      await this.monitorService();
      
      // Print results
      this.printResults();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  /**
   * Test health check endpoint
   */
  async testHealthCheck() {
    console.log('🔍 Testing health check endpoint...');
    
    try {
      const response = await fetch(`${this.baseUrl}/api/limit-orders/monitoring/health`);
      const data = await response.json();
      
      this.testResults.healthCheck = {
        success: response.ok,
        status: response.status,
        data: data
      };
      
      if (response.ok) {
        console.log('✅ Health check passed');
        console.log(`   Connection: ${data.connected ? '🟢 Connected' : '🔴 Disconnected'}`);
        console.log(`   Active Subscriptions: ${data.activeSubscriptions}`);
        console.log(`   Total Orders: ${data.totalOrders}`);
        console.log(`   Connection State: ${data.connectionState}`);
        console.log(`   Has API Key: ${data.config?.hasApiKey ? '✅' : '❌'}`);
      } else {
        console.log('❌ Health check failed');
        console.log(`   Status: ${response.status}`);
        console.log(`   Error: ${data.error || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.log('❌ Health check request failed:', error.message);
      this.testResults.healthCheck = {
        success: false,
        error: error.message
      };
    }
    
    console.log('');
  }

  /**
   * Test subscriptions endpoint
   */
  async testSubscriptions() {
    console.log('📊 Testing subscriptions endpoint...');
    
    try {
      const response = await fetch(`${this.baseUrl}/api/limit-orders/monitoring/subscriptions`);
      const data = await response.json();
      
      this.testResults.subscriptions = {
        success: response.ok,
        status: response.status,
        data: data
      };
      
      if (response.ok && data.success) {
        console.log('✅ Subscriptions endpoint working');
        console.log(`   Total Subscriptions: ${data.count}`);
        
        if (data.data && data.data.length > 0) {
          console.log('   Active Subscriptions:');
          data.data.forEach((sub, index) => {
            console.log(`     ${index + 1}. ${sub.key}`);
            console.log(`        Orders: ${sub.orderCount}`);
            console.log(`        Last Price: ${sub.lastPrice || 'N/A'}`);
            console.log(`        Last Update: ${sub.lastUpdate ? new Date(sub.lastUpdate).toISOString() : 'N/A'}`);
          });
        } else {
          console.log('   No active subscriptions found');
        }
      } else {
        console.log('❌ Subscriptions endpoint failed');
        console.log(`   Status: ${response.status}`);
        console.log(`   Error: ${data.error || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.log('❌ Subscriptions request failed:', error.message);
      this.testResults.subscriptions = {
        success: false,
        error: error.message
      };
    }
    
    console.log('');
  }

  /**
   * Test manual refresh endpoint
   */
  async testManualRefresh() {
    console.log('🔄 Testing manual refresh endpoint...');
    
    try {
      const response = await fetch(`${this.baseUrl}/api/limit-orders/monitoring/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      const data = await response.json();
      
      this.testResults.refresh = {
        success: response.ok,
        status: response.status,
        data: data
      };
      
      if (response.ok && data.success) {
        console.log('✅ Manual refresh successful');
        console.log(`   Message: ${data.message}`);
      } else {
        console.log('❌ Manual refresh failed');
        console.log(`   Status: ${response.status}`);
        console.log(`   Error: ${data.error || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.log('❌ Manual refresh request failed:', error.message);
      this.testResults.refresh = {
        success: false,
        error: error.message
      };
    }
    
    console.log('');
  }

  /**
   * Monitor service for specified duration
   */
  async monitorService() {
    console.log(`⏱️ Monitoring service for ${TEST_DURATION / 1000} seconds...`);
    console.log('   (Checking health every 10 seconds)\n');
    
    const startTime = Date.now();
    const interval = 10000; // 10 seconds
    let checkCount = 0;
    
    while (Date.now() - startTime < TEST_DURATION) {
      checkCount++;
      console.log(`📊 Health Check #${checkCount} (${new Date().toISOString()})`);
      
      try {
        const response = await fetch(`${this.baseUrl}/api/limit-orders/monitoring/health`);
        const data = await response.json();
        
        const healthData = {
          timestamp: Date.now(),
          connected: data.connected,
          activeSubscriptions: data.activeSubscriptions,
          totalOrders: data.totalOrders,
          lastUpdate: data.lastUpdate,
          connectionState: data.connectionState,
          executedOrders: data.stats?.executedOrders || 0,
          failedExecutions: data.stats?.failedExecutions || 0
        };
        
        this.testResults.monitoring.push(healthData);
        
        console.log(`   🔗 Connected: ${data.connected ? '✅' : '❌'}`);
        console.log(`   📡 Subscriptions: ${data.activeSubscriptions}`);
        console.log(`   📋 Orders: ${data.totalOrders}`);
        console.log(`   ⚡ State: ${data.connectionState}`);
        console.log(`   ✅ Executed: ${data.stats?.executedOrders || 0}`);
        console.log(`   ❌ Failed: ${data.stats?.failedExecutions || 0}`);
        
        if (data.lastUpdate) {
          const lastUpdateAge = Date.now() - data.lastUpdate;
          console.log(`   🕐 Last Update: ${Math.round(lastUpdateAge / 1000)}s ago`);
        }
        
      } catch (error) {
        console.log(`   ❌ Health check failed: ${error.message}`);
        this.testResults.monitoring.push({
          timestamp: Date.now(),
          error: error.message
        });
      }
      
      console.log('');
      
      // Wait for next check
      if (Date.now() - startTime < TEST_DURATION) {
        await setTimeout(interval);
      }
    }
    
    console.log('✅ Monitoring period completed\n');
  }

  /**
   * Print test results summary
   */
  printResults() {
    console.log('📋 TEST RESULTS SUMMARY');
    console.log('========================\n');
    
    // Health Check Results
    console.log('🔍 Health Check:');
    if (this.testResults.healthCheck?.success) {
      console.log('   ✅ PASSED');
    } else {
      console.log('   ❌ FAILED');
      if (this.testResults.healthCheck?.error) {
        console.log(`   Error: ${this.testResults.healthCheck.error}`);
      }
    }
    
    // Subscriptions Results
    console.log('\n📊 Subscriptions:');
    if (this.testResults.subscriptions?.success) {
      console.log('   ✅ PASSED');
      const count = this.testResults.subscriptions.data?.count || 0;
      console.log(`   Active Subscriptions: ${count}`);
    } else {
      console.log('   ❌ FAILED');
      if (this.testResults.subscriptions?.error) {
        console.log(`   Error: ${this.testResults.subscriptions.error}`);
      }
    }
    
    // Manual Refresh Results
    console.log('\n🔄 Manual Refresh:');
    if (this.testResults.refresh?.success) {
      console.log('   ✅ PASSED');
    } else {
      console.log('   ❌ FAILED');
      if (this.testResults.refresh?.error) {
        console.log(`   Error: ${this.testResults.refresh.error}`);
      }
    }
    
    // Monitoring Results
    console.log('\n⏱️ Service Monitoring:');
    const monitoringData = this.testResults.monitoring.filter(m => !m.error);
    if (monitoringData.length > 0) {
      console.log(`   ✅ Completed ${monitoringData.length} health checks`);
      
      const connectionUptime = monitoringData.filter(m => m.connected).length;
      const uptimePercentage = (connectionUptime / monitoringData.length * 100).toFixed(1);
      console.log(`   📊 Connection Uptime: ${uptimePercentage}%`);
      
      const totalExecuted = Math.max(...monitoringData.map(m => m.executedOrders || 0));
      const totalFailed = Math.max(...monitoringData.map(m => m.failedExecutions || 0));
      console.log(`   ✅ Orders Executed: ${totalExecuted}`);
      console.log(`   ❌ Execution Failures: ${totalFailed}`);
    } else {
      console.log('   ❌ No successful monitoring data collected');
    }
    
    console.log('\n🎯 OVERALL RESULT:');
    const allTestsPassed = this.testResults.healthCheck?.success && 
                          this.testResults.subscriptions?.success && 
                          this.testResults.refresh?.success &&
                          monitoringData.length > 0;
    
    if (allTestsPassed) {
      console.log('   ✅ ALL TESTS PASSED - Monitoring service is working correctly!');
    } else {
      console.log('   ❌ SOME TESTS FAILED - Check the service configuration and logs');
    }
    
    console.log('\n📝 Next Steps:');
    console.log('   1. Check the service logs for detailed information');
    console.log('   2. Verify MOBULA_API_KEY is set correctly');
    console.log('   3. Ensure database connection is working');
    console.log('   4. Create test limit orders to verify execution');
  }
}

// Run tests if script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new MonitoringServiceTester();
  tester.runTests().catch(error => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  });
}

export { MonitoringServiceTester };
