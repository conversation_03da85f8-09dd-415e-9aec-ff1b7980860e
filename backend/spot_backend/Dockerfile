# Use an official Node.js runtime as a parent image
FROM node:18-alpine

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock.json (or yarn.lock)
COPY package*.json ./
# If using yarn, uncomment the next line and comment out the npm install line
# COPY yarn.lock ./

# Install dependencies
# Use --omit=dev if you don't need devDependencies in the final image
RUN npm install 
# If using yarn, uncomment the next line and comment out the npm install line
# RUN yarn install --production

# Copy the rest of the application code
COPY . .

# If your backend needs a build step (e.g., for TypeScript)
# Replace 'build' with your actual build script if different
# Ensure your tsconfig.json outputs to a directory like 'dist'
RUN npm run build

# Expose the port the app runs on (as defined in docker-compose.yml environment)
EXPOSE 5000

# Define the command to run the application
# The actual entry point is index.js in the dist folder
CMD ["node", "dist/index.js"]