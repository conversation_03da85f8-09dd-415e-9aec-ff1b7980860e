# WebSocket-Based Pulse Data System Implementation

## Overview

This implementation replaces the REST API-based pulse data fetching with a WebSocket-based system that provides real-time data while maintaining backward compatibility with the existing frontend code.

## Architecture

### Backend Components

1. **MobulaWebSocketService** (`src/services/mobulaWebSocketService.ts`)
   - Manages WebSocket connection to Mobula API
   - Implements automatic connection/disconnection based on user activity
   - Caches real-time pulse data
   - Handles reconnection logic with exponential backoff

2. **UserActivityService** (`src/services/userActivityService.ts`)
   - Tracks user activity on pulse pages
   - Manages user sessions and heartbeats
   - Triggers WebSocket connections when users are active

3. **ActivityController** (`src/controllers/activityController.ts`)
   - Provides API endpoints for user activity tracking
   - Returns WebSocket status and activity statistics

4. **Updated PulseService** (`src/services/pulseService.ts`)
   - Modified to use cached WebSocket data first
   - Falls back to REST API if WebSocket data is unavailable
   - Maintains same interface for backward compatibility

### Frontend Components

1. **ActivityService** (`src/services/activityService.ts`)
   - Tracks user activity on pulse pages
   - Sends heartbeats to maintain active status
   - Handles cleanup on page unload

2. **WebSocketStatus Component** (`src/components/WebSocketStatus.tsx`)
   - Debug component to monitor WebSocket status
   - Shows connection status, active users, and activity stats

## Configuration

### Environment Variables

Add to your `.env` file:

```env
# Mobula API Configuration
MOBULA_API_KEY=your_mobula_api_key
MOBULA_WSS_URL=wss://api-prod.mobula.io
```

### API Endpoints

New endpoints added:

- `POST /api/activity/pulse/register` - Register user on pulse page
- `POST /api/activity/pulse/unregister` - Unregister user from pulse page
- `POST /api/activity/heartbeat` - Send user activity heartbeat
- `GET /api/activity/stats` - Get activity statistics
- `GET /api/activity/websocket-status` - Get WebSocket connection status

## Data Flow

1. **User enters pulse page** → Frontend registers activity → Backend connects to WebSocket
2. **WebSocket receives data** → Backend caches data → Frontend requests data → Backend serves cached data
3. **User leaves pulse page** → Frontend unregisters activity → Backend disconnects after 60s if no active users
4. **WebSocket unavailable** → Backend falls back to REST API → Same data structure maintained

## Connection Management

### Auto-Connect/Disconnect Logic

- **Connect**: When first user registers pulse activity
- **Disconnect**: 60 seconds after last user unregisters
- **Reconnect**: Automatic with exponential backoff (max 5 attempts)

### User Activity Tracking

- **Registration**: When user enters pulse page
- **Heartbeat**: Every 30 seconds while active
- **Cleanup**: On page unload, visibility change, or manual unregister

## Error Handling

### WebSocket Errors

- Connection failures trigger automatic reconnection
- Exponential backoff prevents spam reconnections
- Falls back to REST API if WebSocket unavailable

### Data Fallback

1. **Fresh WebSocket data** (preferred)
2. **Stale WebSocket data** (if REST API fails)
3. **REST API data** (fallback)
4. **Empty data structure** (last resort)

## Monitoring and Debugging

### WebSocket Status Component

The `WebSocketStatus` component provides real-time monitoring:

- Connection status (Connected/Disconnected)
- Number of active users
- Last data update timestamp
- Activity statistics
- List of active pulse users

### Logging

Comprehensive logging at different levels:

- **Info**: Connection events, user activity, data updates
- **Debug**: Message details, heartbeats
- **Warn**: Stale data, connection issues
- **Error**: Connection failures, parsing errors

## Performance Considerations

### Caching Strategy

- **Cache Duration**: 5 minutes for fresh data
- **Stale Data**: Available as fallback for up to cache expiry
- **Memory Usage**: Single cached object per service instance

### Connection Efficiency

- **Single Connection**: Shared among all active users
- **Lazy Connection**: Only connects when users are active
- **Auto Cleanup**: Disconnects when no users present

## Testing

### Manual Testing

1. **Open pulse page** → Check WebSocket connects
2. **Multiple users** → Verify single connection shared
3. **Leave page** → Confirm disconnect after 60s
4. **Network issues** → Test fallback to REST API
5. **Reconnection** → Verify automatic reconnection works

### Debug Information

Use the WebSocket Status component to monitor:
- Real-time connection status
- User activity tracking
- Data freshness
- Error states

## Deployment Notes

### Environment Setup

1. Ensure `MOBULA_WSS_URL` is set to `wss://api-prod.mobula.io`
2. Configure `MOBULA_API_KEY` with valid Mobula API key
3. Verify WebSocket port accessibility in production

### Backward Compatibility

- Existing frontend code works without changes
- Same API endpoint (`/home/<USER>
- Same response format preserved
- Optional user ID parameter added for activity tracking

## Security Considerations

### API Key Protection

- API key stored securely in environment variables
- Not exposed to frontend
- Used only in backend WebSocket connections

### User Activity

- User IDs are temporary session identifiers
- No sensitive user data stored
- Activity data cleaned up automatically

## Future Enhancements

### Potential Improvements

1. **Redis Integration**: Store cached data in Redis for multi-instance deployments
2. **Rate Limiting**: Implement rate limiting for activity endpoints
3. **User Authentication**: Integrate with actual user authentication system
4. **Metrics Collection**: Add detailed metrics for monitoring
5. **Health Checks**: Implement health check endpoints for WebSocket status

### Scalability

- Current implementation supports single backend instance
- For multiple instances, consider Redis for shared cache
- Load balancer should support WebSocket connections

## Troubleshooting

### Common Issues

1. **WebSocket not connecting**
   - Check `MOBULA_WSS_URL` environment variable
   - Verify API key is valid
   - Check network connectivity

2. **Data not updating**
   - Verify users are registered on pulse page
   - Check WebSocket connection status
   - Review backend logs for errors

3. **Frequent disconnections**
   - Check user activity tracking
   - Verify heartbeat functionality
   - Review reconnection logic

### Debug Steps

1. Check WebSocket Status component
2. Review backend logs for connection events
3. Monitor network tab for API calls
4. Verify environment variables are set correctly
