/**
 * Test script for Privy API integration
 * Run with: node test-privy-api.js
 */

const axios = require('axios');
require('dotenv').config();

const BACKEND_URL = 'http://localhost:5001';

// Test user ID - replace with actual user ID from your Privy dashboard
// Use the clean format without 'did:privy:' prefix
const TEST_USER_ID = 'cm9qjpbkz014mjs0n71tbywf4'; // Replace with real user ID

async function testPrivyAPI() {
  console.log('🧪 Testing Privy Wallet ID API Integration\n');

  // Test 1: Check environment variables
  console.log('1️⃣ Checking environment variables...');
  const privyAppId = process.env.PRIVY_APP_ID;
  const privyAppSecret = process.env.PRIVY_APP_SECRET;

  if (!privyAppId) {
    console.error('❌ PRIVY_APP_ID not found in environment');
    return;
  }
  if (!privyAppSecret || privyAppSecret === 'your_privy_app_secret_here') {
    console.error('❌ PRIVY_APP_SECRET not configured properly');
    console.log('   Please set your actual Privy app secret in .env file');
    return;
  }
  console.log('✅ Environment variables configured\n');

  // Test 2: Check backend server
  console.log('2️⃣ Checking backend server...');
  try {
    const healthCheck = await axios.get(`${BACKEND_URL}/health`);
    console.log('✅ Backend server is running');
  } catch (error) {
    console.error('❌ Backend server not accessible');
    console.log('   Make sure to run: npm run dev in backend/spot_backend');
    return;
  }
  console.log('');

  // Test 3: Test Privy API endpoint
  console.log('3️⃣ Testing Privy API endpoint...');
  
  if (TEST_USER_ID === 'cm9qjpbkz014mjs0n71tbywf4') {
    console.log('⚠️  Using example user ID');
    console.log('   To test with real data, replace TEST_USER_ID with actual user ID');
    console.log('   Use the clean format without "did:privy:" prefix');
    console.log('   Example: "cm9qjpbkz014mjs0n71tbywf4"\n');
  }

  try {
    const response = await axios.post(
      `${BACKEND_URL}/api/wallet/privy-solana-info`,
      { userId: TEST_USER_ID },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000
      }
    );

    if (response.data.success) {
      console.log('✅ API call successful!');
      console.log('📋 Response data:');
      console.log(`   Wallet ID: ${response.data.data.walletId}`);
      console.log(`   Address: ${response.data.data.address}`);
      console.log(`   Total Solana Wallets: ${response.data.data.totalSolanaWallets}`);
    } else {
      console.log('⚠️  API call returned error:');
      console.log(`   ${response.data.error}`);
    }
  } catch (error) {
    if (error.response) {
      console.log('⚠️  API returned error:');
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Error: ${error.response.data.error || error.response.data.message}`);
      
      if (error.response.status === 401) {
        console.log('   💡 This likely means your PRIVY_APP_SECRET is incorrect');
      } else if (error.response.status === 404) {
        console.log('   💡 This likely means the user ID doesn\'t exist in Privy');
      }
    } else {
      console.error('❌ Network error:', error.message);
    }
  }
  console.log('');

  // Test 4: Test with invalid user ID
  console.log('4️⃣ Testing error handling...');
  try {
    const response = await axios.post(
      `${BACKEND_URL}/api/wallet/privy-solana-info`,
      { userId: 'invalid-user-id' },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 5000
      }
    );
    console.log('⚠️  Expected error but got success - this might be unexpected');
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('✅ Error handling working correctly');
      console.log(`   Expected 400 error: ${error.response.data.error}`);
    } else {
      console.log('⚠️  Unexpected error response');
    }
  }
  console.log('');

  // Test 5: Test with 'did:privy:' prefix (should be handled correctly)
  console.log('5️⃣ Testing with did:privy: prefix...');
  try {
    const response = await axios.post(
      `${BACKEND_URL}/api/wallet/privy-solana-info`,
      { userId: `did:privy:${TEST_USER_ID}` }, // Add prefix
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 5000
      }
    );

    if (response.data.success) {
      console.log('✅ Prefix handling working correctly');
      console.log(`   Wallet ID: ${response.data.data.walletId}`);
    } else {
      console.log('⚠️  API returned error with prefix:');
      console.log(`   ${response.data.error}`);
    }
  } catch (error) {
    if (error.response) {
      console.log('⚠️  API error with prefix:');
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Error: ${error.response.data.error}`);
    } else {
      console.error('❌ Network error with prefix:', error.message);
    }
  }
  console.log('');

  console.log('🎉 Test completed!');
  console.log('');
  console.log('📝 Next steps:');
  console.log('1. Replace TEST_USER_ID with a real user ID from Privy dashboard');
  console.log('2. Test the frontend integration in TradingPanel');
  console.log('3. Check browser console for wallet ID logs');
  console.log('4. Verify cache functionality in localStorage');
  console.log('5. Test the optimized flow: Login → Preload → Swap → Logout');
}

// Run the test
testPrivyAPI().catch(console.error);
