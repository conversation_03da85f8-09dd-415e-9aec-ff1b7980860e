const { io } = require('socket.io-client');

// Test frontend WebSocket connection to backend
const testFrontendWebSocket = () => {
  console.log('🔌 Testing Frontend WebSocket Connection to Backend');
  console.log('📡 Connecting to: http://localhost:5001');
  
  const socket = io('http://localhost:5001', {
    transports: ['websocket', 'polling'],
    timeout: 15000,
    reconnection: false,
    forceNew: true,
    upgrade: true,
    rememberUpgrade: false
  });

  let messageCount = 0;
  let hasReceivedPulseData = false;

  const timeout = setTimeout(() => {
    console.log('⏰ Test timeout reached (30 seconds)');
    socket.disconnect();
  }, 30000);

  socket.on('connect', () => {
    console.log('✅ Connected to backend WebSocket');
    console.log('🔗 Socket ID:', socket.id);
    
    // Register as a user and join pulse room
    const testUserId = `test_user_${Date.now()}`;
    const testSessionId = `test_session_${Date.now()}`;
    
    console.log('📝 Registering user and joining pulse room...');
    socket.emit('register', {
      userId: testUserId,
      sessionId: testSessionId
    });
    
    // Wait a bit then join pulse room
    setTimeout(() => {
      console.log('🏠 Joining pulse room...');
      socket.emit('join-pulse', {
        userId: testUserId,
        sessionId: testSessionId
      });
    }, 1000);
  });

  socket.on('connection-status', (status) => {
    console.log('📊 Connection status received:', status);
  });

  socket.on('pulse-data', (update) => {
    messageCount++;
    hasReceivedPulseData = true;
    
    console.log(`📥 Pulse data received (message ${messageCount}):`, {
      source: update.source,
      timestamp: new Date(update.timestamp).toLocaleTimeString(),
      hasData: !!update.data,
      dataKeys: update.data ? Object.keys(update.data) : [],
      newCount: update.data?.new?.length || 0,
      bondingCount: update.data?.bonding?.length || 0,
      bondedCount: update.data?.bonded?.length || 0
    });
    
    // Log first pulse data in detail
    if (messageCount === 1) {
      console.log('📋 First pulse data details:', JSON.stringify(update, null, 2));
    }
  });

  socket.on('room-stats', (stats) => {
    console.log('📈 Room stats received:', stats);
  });

  socket.on('disconnect', (reason) => {
    clearTimeout(timeout);
    console.log('🔌 Disconnected from backend WebSocket:', reason);
    console.log('📊 Test Summary:');
    console.log(`   - Messages received: ${messageCount}`);
    console.log(`   - Pulse data received: ${hasReceivedPulseData ? 'Yes' : 'No'}`);
    
    if (!hasReceivedPulseData) {
      console.log('\n❌ No pulse data received. Possible issues:');
      console.log('   - Backend Mobula WebSocket not receiving data');
      console.log('   - Backend cache is empty or stale');
      console.log('   - Frontend WebSocket service not broadcasting');
      console.log('   - Client not properly registered in pulse room');
    }
  });

  socket.on('connect_error', (error) => {
    console.error('❌ Connection error:', error.message);
  });

  socket.on('error', (error) => {
    console.error('❌ Socket error:', error);
  });

  // Manually disconnect after test
  setTimeout(() => {
    console.log('🔚 Manually disconnecting...');
    socket.disconnect();
  }, 25000);
};

testFrontendWebSocket();
