# Pulse Trade Page Implementation

## Overview

This implementation provides a comprehensive trade data system for the pulse trade page that:

1. **Fetches initial trade data** from Mobula API using REST endpoints
2. **Streams real-time updates** via WebSocket connections
3. **Manages subscriptions** per pool address with proper cleanup
4. **Handles unsubscription** when users leave the page

## Architecture

### Backend Components

#### 1. **MobulaTradeWebSocketService** (`src/services/mobulaTradeWebSocketService.ts`)
- Manages WebSocket connection to Mobula trade feed API
- Handles subscription/unsubscription for specific pool addresses
- Implements automatic reconnection with exponential backoff
- Maintains trade history cache (last 100 trades per pool)
- Emits events for real-time trade data

**Key Features:**
- Pool-specific subscriptions with client tracking
- Automatic cleanup when no clients remain
- Fallback WebSocket URL support
- Heartbeat mechanism to keep connection alive

#### 2. **TradeService** (`src/services/tradeService.ts`)
- Fetches initial trade data from Mobula REST API
- Provides pagination support for historical trades
- Validates Solana pool addresses
- Formats trade data for frontend consumption
- Health monitoring for API availability

**API Endpoints Used:**
```
GET https://api.mobula.io/api/1/market/trades/pair
Parameters:
- address: pool_address (from frontend localStorage)
- blockchain: solana
- limit: 20 (configurable)
- sortOrder: desc
```

#### 3. **FrontendWebSocketService** (Extended)
- Added trade subscription/unsubscription handlers
- Manages trade room clients per pool address
- Broadcasts real-time trade data to subscribed clients
- Handles client disconnection cleanup
- Integrates with existing pulse functionality

#### 4. **Trade API Routes** (`src/routes/tradeRoutes.ts`)
- `/api/trade/initial/:poolAddress` - Get initial trade data
- `/api/trade/paginated/:poolAddress` - Get paginated trade data
- `/api/trade/validate/:poolAddress` - Validate pool address
- `/api/trade/health` - Service health check
- `/api/trade/websocket/status` - WebSocket connection status
- `/api/trade/frontend/stats` - Frontend subscription statistics

## Data Flow

### Initial Load Sequence

1. **Frontend** gets `activePulseToken` from localStorage
2. **API Request** to `/api/trade/initial/{poolAddress}?limit=20`
3. **Backend** fetches data from Mobula API
4. **Response** returns formatted trade data
5. **WebSocket** subscription for real-time updates

### Real-time Updates

1. **WebSocket Subscription** to Mobula trade feed:
   ```json
   {
     "type": "pair",
     "authorization": "YOUR-API-KEY",
     "payload": {
       "address": "pool_address",
       "blockchain": "solana"
     }
   }
   ```

2. **Real-time Data** flows: Mobula → Backend → Frontend
3. **Frontend** receives updates via `trade-data` event

### Unsubscription

When user leaves the page:
```json
{
  "type": "unsubscribe",
  "payload": {}
}
```

## WebSocket Events

### Frontend → Backend

#### Subscribe to Trade Data
```javascript
socket.emit('subscribe-trade', {
  poolAddress: 'pool_address_from_localStorage',
  userId: 'user_id',
  sessionId: 'session_id'
});
```

#### Unsubscribe from Trade Data
```javascript
socket.emit('unsubscribe-trade', {
  poolAddress: 'pool_address_from_localStorage',
  userId: 'user_id',
  sessionId: 'session_id'
});
```

### Backend → Frontend

#### Trade Data Updates
```javascript
socket.on('trade-data', (data) => {
  // data.poolAddress - the pool address
  // data.tradeData - latest trade information
  // data.tradeHistory - array of recent trades (last 20)
  // data.timestamp - update timestamp
});
```

## API Response Formats

### Initial Trade Data
```json
{
  "success": true,
  "data": {
    "poolAddress": "********************************************",
    "trades": [
      {
        "id": "trade_id",
        "timestamp": 1703123456,
        "type": "buy",
        "amount": 1000.50,
        "price": 0.000123,
        "valueUsd": 123.45,
        "tokenAmount": 1000000,
        "tokenAmountUsd": 123.45,
        "pair": "pool_address",
        "blockchain": "solana",
        "dex": "raydium",
        "wallet": "wallet_address",
        "txHash": "transaction_hash",
        "displayAmount": "1.00K",
        "displayPrice": "0.000123",
        "displayValueUsd": "$123.45",
        "timeAgo": "2m ago"
      }
    ],
    "count": 20,
    "timestamp": 1703123456789
  }
}
```

## Configuration

### Environment Variables
```env
MOBULA_API_KEY=your_mobula_api_key
MOBULA_WSS_URL=wss://api-prod.mobula.io  # Optional, defaults to wss://api.mobula.io
```

### Rate Limiting
- Trade API endpoints: 100 requests per minute per IP
- WebSocket connections: Standard Socket.IO limits

## Error Handling

### API Errors
- Invalid pool address format
- Missing API key
- Mobula API unavailable
- Rate limiting exceeded

### WebSocket Errors
- Connection failures with automatic retry
- Subscription errors with graceful fallback
- Client disconnection cleanup

## Testing

Run the test suite:
```bash
node test-trade-functionality.js
```

Tests include:
- API endpoint functionality
- Pool address validation
- WebSocket connection and subscription
- Real-time data flow
- Proper unsubscription

## Integration Notes

### Frontend Integration
1. Get pool address from `localStorage.getItem('activePulseToken')`
2. Call initial trade API on page load
3. Establish WebSocket connection and subscribe
4. Handle real-time updates
5. Unsubscribe when leaving page

### Separation from Pulse Data
- Trade WebSocket service is completely separate from pulse WebSocket
- Different subscription mechanisms and data formats
- Independent connection management and cleanup
- No interference with existing pulse functionality

## Performance Considerations

- Trade history limited to 100 trades per pool in memory
- Automatic cleanup of inactive subscriptions
- Connection pooling for WebSocket connections
- Efficient data formatting and caching

## Security

- API key validation for Mobula requests
- Rate limiting on all endpoints
- Input validation for pool addresses
- Proper error handling without data leakage
