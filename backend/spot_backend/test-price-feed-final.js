const WebSocket = require('ws');

console.log('🧪 Testing Mobula Price Feed (Enterprise)');
console.log('==========================================');

// Use the API key from your .env file
const API_KEY = 'fffa68cd-6bde-4ac5-909d-eb627d8baca0';

console.log(`🔑 API Key: ${API_KEY.substring(0, 8)}...${API_KEY.substring(API_KEY.length - 4)}`);
console.log('🎯 Testing Price Feed endpoint: wss://production-feed.mobula.io');
console.log('⏱️ Test duration: 30 seconds');
console.log('');

const testPriceFeed = () => {
  return new Promise((resolve) => {
    const ws = new WebSocket('wss://production-feed.mobula.io');
    let messageCount = 0;
    let priceUpdates = 0;
    let connectionTime = null;
    
    // Test timeout
    const timeout = setTimeout(() => {
      console.log('⏰ Test timeout reached');
      ws.close();
    }, 30000);
    
    ws.on('open', () => {
      connectionTime = Date.now();
      console.log('✅ Price Feed connected successfully!');
      
      // Subscribe to SOL and USDC for testing
      const subscription = {
        type: "feed",
        authorization: API_KEY,
        kind: "tokens",
        tokens: [
          {
            blockchain: "solana",
            address: "So11111111111111111111111111111111111111112" // SOL
          },
          {
            blockchain: "solana", 
            address: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v" // USDC
          }
        ]
      };
      
      console.log('📤 Sending Price Feed subscription...');
      console.log('   Tokens: SOL, USDC');
      console.log('   Format: Enterprise Price Feed');
      
      ws.send(JSON.stringify(subscription));
    });
    
    ws.on('message', (data) => {
      messageCount++;
      
      try {
        const message = JSON.parse(data.toString());
        
        console.log(`📥 Message ${messageCount}:`, {
          type: typeof message,
          hasPrice: message.price !== undefined,
          hasSymbol: message.baseSymbol !== undefined,
          hasTimestamp: message.timestamp !== undefined,
          time: new Date().toISOString()
        });
        
        // Check if it's price data
        if (message.price && message.baseSymbol && message.timestamp) {
          priceUpdates++;
          console.log(`💰 ${message.baseSymbol} Price: $${message.price}`);
          console.log(`📊 Market Depth: Up $${message.marketDepthUSDUp?.toLocaleString()}, Down $${message.marketDepthUSDDown?.toLocaleString()}`);
          console.log(`📈 Volume 24h: $${message.volume24h?.toLocaleString()}`);
          console.log(`⏰ Timestamp: ${new Date(message.timestamp).toISOString()}`);
          console.log('');
        } else if (message.type || message.status) {
          console.log('ℹ️ Control message:', {
            type: message.type,
            status: message.status,
            message: message.message || 'N/A'
          });
        } else {
          console.log('❓ Unknown message format:', message);
        }
        
        // Show first message in full for debugging
        if (messageCount === 1) {
          console.log('📋 Full first message:');
          console.log(JSON.stringify(message, null, 2));
          console.log('');
        }
        
      } catch (error) {
        console.error('❌ Error parsing message:', error.message);
        console.log('📄 Raw message:', data.toString().substring(0, 200) + '...');
      }
    });
    
    ws.on('error', (error) => {
      console.error('❌ WebSocket error:', error.message);
      
      if (error.message.includes('401') || error.message.includes('403')) {
        console.log('🔐 Authentication issue - check if API key has Enterprise access');
      } else if (error.message.includes('404')) {
        console.log('🔍 Endpoint not found - Price Feed might not be available');
      } else {
        console.log('🌐 Network or connection issue');
      }
    });
    
    ws.on('close', (code, reason) => {
      clearTimeout(timeout);
      const duration = connectionTime ? (Date.now() - connectionTime) / 1000 : 0;
      
      console.log('🔌 Connection closed');
      console.log(`   Code: ${code}`);
      console.log(`   Reason: ${reason.toString() || 'Normal closure'}`);
      console.log(`   Duration: ${duration.toFixed(1)}s`);
      console.log('');
      
      console.log('📊 TEST RESULTS:');
      console.log('================');
      console.log(`✉️ Total messages: ${messageCount}`);
      console.log(`💰 Price updates: ${priceUpdates}`);
      console.log(`⏱️ Connection time: ${duration.toFixed(1)}s`);
      
      if (priceUpdates > 0) {
        console.log('');
        console.log('🎉 SUCCESS! Price Feed is working correctly');
        console.log('✅ Your service can now use Price Feed for:');
        console.log('   • Unlimited asset monitoring');
        console.log('   • Real-time price updates');
        console.log('   • Superior pricing accuracy');
        console.log('   • Better limit order execution');
      } else if (messageCount > 0) {
        console.log('');
        console.log('⚠️ PARTIAL SUCCESS: Connected but no price data');
        console.log('💡 Possible reasons:');
        console.log('   • Subscription format might need adjustment');
        console.log('   • Tokens might not be available in Price Feed');
        console.log('   • API key might need Enterprise permissions');
      } else {
        console.log('');
        console.log('❌ FAILED: No data received');
        console.log('💡 Possible issues:');
        console.log('   • API key might not have Enterprise access');
        console.log('   • Price Feed endpoint might be different');
        console.log('   • Network connectivity issues');
        console.log('   • Authentication problems');
      }
      
      resolve({
        success: priceUpdates > 0,
        messages: messageCount,
        priceUpdates: priceUpdates,
        duration: duration
      });
    });
  });
};

// Run the test
testPriceFeed().then((result) => {
  console.log('');
  console.log('🏁 Test completed');
  
  if (result.success) {
    console.log('✅ Price Feed is ready for production use!');
  } else {
    console.log('❌ Price Feed needs investigation');
  }
}).catch((error) => {
  console.error('💥 Test failed with error:', error);
});
