const { createClient } = require('@supabase/supabase-js');

// Test Supabase connection via HTTP endpoint
async function testSupabaseConnection() {
  try {
    console.log('🔍 Testing Supabase connection...');
    
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('❌ Missing Supabase environment variables');
      return false;
    }
    
    console.log('URL:', supabaseUrl);
    console.log('Service Key:', supabaseServiceKey ? 'Set' : 'Not set');
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Test basic connection
    console.log('\n🔍 Testing database connection...');
    const { error, count } = await supabase
      .from('limit_orders')
      .select('*', { count: 'exact', head: true });

    if (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }

    console.log('✅ Database connection successful');
    console.log(`📊 Found ${count || 0} limit orders in the database`);
    
    // Test creating a sample limit order
    console.log('\n🔍 Testing limit order creation...');
    const testOrder = {
      user_id: 'test_user_' + Date.now(),
      token_address: 'So11111111111111111111111111111111111111112',
      token_symbol: 'SOL',
      token_name: 'Solana',
      order_type: 'buy',
      target_price: 150.00,
      amount: 1.0,
      status: 'pending'
    };
    
    const { data: insertData, error: insertError } = await supabase
      .from('limit_orders')
      .insert([testOrder])
      .select();

    if (insertError) {
      console.error('❌ Insert test failed:', insertError.message);
      return false;
    }

    console.log('✅ Insert test successful');
    console.log('📝 Created test order:', insertData[0]);
    
    // Clean up test data
    if (insertData && insertData[0]) {
      const { error: deleteError } = await supabase
        .from('limit_orders')
        .delete()
        .eq('id', insertData[0].id);
        
      if (deleteError) {
        console.warn('⚠️ Failed to clean up test data:', deleteError.message);
      } else {
        console.log('🧹 Test data cleaned up successfully');
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ Supabase test failed:', error.message);
    return false;
  }
}

// Test via HTTP request
async function testViaHTTP() {
  try {
    console.log('\n🌐 Testing via HTTP request...');
    
    const response = await fetch('http://localhost:5001/api/limit-orders/stats?user_id=test_user');
    
    if (!response.ok) {
      console.error('❌ HTTP request failed:', response.status, response.statusText);
      return false;
    }
    
    const data = await response.json();
    console.log('✅ HTTP request successful');
    console.log('📊 Response:', data);
    
    return true;
  } catch (error) {
    console.error('❌ HTTP test failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🧪 Starting Supabase tests...\n');
  
  const directTest = await testSupabaseConnection();
  console.log('\n' + '='.repeat(50));
  
  const httpTest = await testViaHTTP();
  
  console.log('\n' + '='.repeat(50));
  console.log('📋 Test Results:');
  console.log(`Direct Supabase: ${directTest ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`HTTP Endpoint: ${httpTest ? '✅ PASS' : '❌ FAIL'}`);
  
  if (directTest && httpTest) {
    console.log('\n🎉 All tests passed! Supabase integration is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the logs above for details.');
  }
}

runTests().catch(console.error);
