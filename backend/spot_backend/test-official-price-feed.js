const WebSocket = require('ws');

console.log('🧪 Testing Official Mobula Price Feed (Enterprise)');
console.log('==================================================');

// Use your API key
const API_KEY = 'fffa68cd-6bde-4ac5-909d-eb627d8baca0';

console.log(`🔑 API Key: ${API_KEY.substring(0, 8)}...${API_KEY.substring(API_KEY.length - 4)}`);
console.log('🎯 Testing endpoint: wss://api.mobula.io');
console.log('💎 Format: Official Price Feed V2 (Enterprise)');
console.log('📊 Features: Volume & market depth weighted pricing');
console.log('⏱️ Test duration: 30 seconds');
console.log('');

const testOfficialPriceFeed = () => {
  return new Promise((resolve) => {
    const ws = new WebSocket('wss://api.mobula.io');
    let messageCount = 0;
    let priceUpdates = 0;
    let connectionTime = null;
    
    // Test timeout
    const timeout = setTimeout(() => {
      console.log('⏰ Test timeout reached');
      ws.close();
    }, 30000);
    
    ws.on('open', () => {
      connectionTime = Date.now();
      console.log('✅ Connected to wss://api.mobula.io successfully!');
      
      // Official Price Feed V2 subscription format
      const subscription = {
        type: "feed",
        authorization: API_KEY,
        kind: "address",
        tokens: [
          {
            blockchain: "solana",
            address: "So11111111111111111111111111111111111111112" // SOL
          },
          {
            blockchain: "solana", 
            address: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v" // USDC
          }
        ]
        // quote defaults to USD
      };
      
      console.log('📤 Sending Official Price Feed subscription...');
      console.log('   Format: Price Feed V2 (Enterprise)');
      console.log('   Type: "feed"');
      console.log('   Kind: "address"');
      console.log('   Tokens: SOL, USDC');
      console.log('   Quote: USD (default)');
      console.log('');
      console.log('📋 Subscription message:');
      console.log(JSON.stringify(subscription, null, 2));
      console.log('');
      
      ws.send(JSON.stringify(subscription));
    });
    
    ws.on('message', (data) => {
      messageCount++;
      
      try {
        const message = JSON.parse(data.toString());
        
        console.log(`📥 Message ${messageCount}:`, {
          type: typeof message,
          hasPrice: message.price !== undefined,
          hasSymbol: message.baseSymbol !== undefined,
          hasTimestamp: message.timestamp !== undefined,
          hasExtra: message.extra !== undefined,
          time: new Date().toISOString()
        });
        
        // Check if it's official Price Feed data
        if (message.price && message.baseSymbol && message.timestamp && message.quoteSymbol) {
          priceUpdates++;
          console.log(`💎 ${message.baseSymbol}/${message.quoteSymbol} Price: $${message.price}`);
          console.log(`📊 Market Depth: Up $${message.marketDepthUSDUp?.toLocaleString()}, Down $${message.marketDepthUSDDown?.toLocaleString()}`);
          console.log(`📈 Volume 24h: $${message.volume24h?.toLocaleString()}`);
          console.log(`⏰ Timestamp: ${new Date(message.timestamp).toISOString()}`);
          
          if (message.extra) {
            console.log(`🔍 Extra data:`, {
              priceUSD: message.extra.priceUSD,
              quoteAssetPrice: message.extra.quoteAssetPrice,
              blockNumber: message.extra.blockNumber
            });
          }
          console.log('');
        } 
        // Handle control messages
        else if (message.type || message.status || message.success || message.error) {
          console.log('ℹ️ Control message:', {
            type: message.type,
            status: message.status,
            success: message.success,
            error: message.error,
            message: message.message || 'N/A'
          });
          console.log('');
        }
        // Handle unknown format
        else {
          console.log('❓ Unknown message format:');
          console.log('   Keys:', Object.keys(message));
          console.log('   Sample:', JSON.stringify(message).substring(0, 200) + '...');
          console.log('');
        }
        
        // Show first message in full for debugging
        if (messageCount === 1) {
          console.log('📋 Full first message:');
          console.log(JSON.stringify(message, null, 2));
          console.log('');
        }
        
      } catch (error) {
        console.error('❌ Error parsing message:', error.message);
        console.log('📄 Raw message:', data.toString().substring(0, 300) + '...');
        console.log('');
      }
    });
    
    ws.on('error', (error) => {
      console.error('❌ WebSocket error:', error.message);
      
      if (error.message.includes('401') || error.message.includes('403')) {
        console.log('🔐 Authentication issue:');
        console.log('   • API key might not have Enterprise access');
        console.log('   • Price Feed requires Enterprise plan');
      } else if (error.message.includes('404')) {
        console.log('🔍 Endpoint not found');
      } else if (error.message.includes('429')) {
        console.log('⏱️ Rate limited - too many requests');
      } else {
        console.log('🌐 Network or connection issue');
      }
      console.log('');
    });
    
    ws.on('close', (code, reason) => {
      clearTimeout(timeout);
      const duration = connectionTime ? (Date.now() - connectionTime) / 1000 : 0;
      
      console.log('🔌 Connection closed');
      console.log(`   Code: ${code}`);
      console.log(`   Reason: ${reason.toString() || 'Normal closure'}`);
      console.log(`   Duration: ${duration.toFixed(1)}s`);
      console.log('');
      
      console.log('📊 TEST RESULTS:');
      console.log('================');
      console.log(`✉️ Total messages: ${messageCount}`);
      console.log(`💎 Price updates: ${priceUpdates}`);
      console.log(`⏱️ Connection time: ${duration.toFixed(1)}s`);
      console.log(`🎯 Endpoint: wss://api.mobula.io`);
      console.log(`📋 Format: Official Price Feed V2`);
      
      if (priceUpdates > 0) {
        console.log('');
        console.log('🎉 SUCCESS! Official Price Feed is working!');
        console.log('✅ Your Enterprise plan has access to:');
        console.log('   • Volume & market depth weighted pricing');
        console.log('   • Real-time price updates');
        console.log('   • Unlimited asset monitoring');
        console.log('   • Superior pricing accuracy');
        console.log('   • Enterprise-grade reliability');
        console.log('');
        console.log('🚀 Ready for production limit order monitoring!');
      } else if (messageCount > 0) {
        console.log('');
        console.log('⚠️ PARTIAL SUCCESS: Connected but no price data');
        console.log('💡 Possible reasons:');
        console.log('   • Subscription format might need adjustment');
        console.log('   • Tokens might not be available');
        console.log('   • API key might need Enterprise permissions');
      } else {
        console.log('');
        console.log('❌ FAILED: No data received');
        console.log('💡 Possible issues:');
        console.log('   • API key might not have Enterprise access');
        console.log('   • Price Feed requires Enterprise plan');
        console.log('   • Network connectivity issues');
        console.log('   • Authentication problems');
      }
      
      resolve({
        success: priceUpdates > 0,
        messages: messageCount,
        priceUpdates: priceUpdates,
        duration: duration,
        endpoint: 'wss://api.mobula.io',
        format: 'Official Price Feed V2'
      });
    });
  });
};

// Run the test
console.log('🚀 Starting Official Price Feed test...');
testOfficialPriceFeed().then((result) => {
  console.log('');
  console.log('🏁 Test completed');
  
  if (result.success) {
    console.log('✅ Official Price Feed is ready for production!');
    console.log('💡 Your limit order service will have superior performance');
  } else {
    console.log('❌ Official Price Feed needs investigation');
    console.log('💡 Check Enterprise plan access and API key permissions');
  }
}).catch((error) => {
  console.error('💥 Test failed with error:', error);
});
