{"root": ["./src/index-minimal.ts", "./src/index-simple.ts", "./src/index.ts", "./src/config/index.ts", "./src/config/supabase.ts", "./src/config/websocketConfig.ts", "./src/controllers/activityController.ts", "./src/controllers/homeController.ts", "./src/controllers/limitOrderController.ts", "./src/controllers/privyController.ts", "./src/controllers/walletController.ts", "./src/middleware/errorHandler.ts", "./src/middleware/rateLimiter.ts", "./src/routes/activityRoutes.ts", "./src/routes/homeRoutes.ts", "./src/routes/limitOrderRoutes.ts", "./src/routes/walletRoutes.ts", "./src/scripts/clearCache.ts", "./src/scripts/testNetworkEndpoints.ts", "./src/services/coinService.ts", "./src/services/dexService.ts", "./src/services/frontendWebSocketService.ts", "./src/services/limitOrderMonitoringService.ts", "./src/services/limitOrderService.ts", "./src/services/mobulaWebSocketService.ts", "./src/services/networkHighlightsService.ts", "./src/services/performanceMonitorService.ts", "./src/services/pulseService.ts", "./src/services/requestDeduplicationService.ts", "./src/services/tokenRadarService.ts", "./src/services/tokenRegistry.ts", "./src/services/userActivityService.ts", "./src/services/walletService.ts", "./src/services/workerThreadService.ts", "./src/services/workers/taskWorker.ts", "./src/test/checkLimitOrderConfig.ts", "./src/test/testLimitOrderMonitoring.ts", "./src/utils/cacheInitializer.ts", "./src/utils/cacheScheduler.ts", "./src/utils/formatter.ts", "./src/utils/limitOrderValidation.ts", "./src/utils/logger.ts", "./src/utils/marketData.ts", "./src/utils/marketTrends.ts", "./src/utils/redis.ts"], "errors": true, "version": "5.8.2"}