#!/usr/bin/env node

/**
 * Test script to verify the critical fixes for system stability
 */

import { workerThreadService } from './dist/services/workerThreadService.js';
import { frontendWebSocketService } from './dist/services/frontendWebSocketService.js';
import { performanceMonitorService } from './dist/services/performanceMonitorService.js';

console.log('🧪 Testing Critical System Stability Fixes...\n');

async function testWorkerThreadService() {
  console.log('1️⃣ Testing WorkerThreadService fixes...');
  
  try {
    // Initialize worker service
    await workerThreadService.initialize();
    console.log('   ✅ Worker service initialized successfully');
    
    // Test stats
    const stats = workerThreadService.getStats();
    console.log('   📊 Initial stats:', stats);
    
    // Test task execution (should not cause recursion)
    const testTask = {
      id: 'test_task_1',
      type: 'CACHE_REFRESH',
      data: { cacheType: 'test', params: {} },
      priority: 'HIGH'
    };
    
    console.log('   🚀 Executing test task...');
    const result = await workerThreadService.executeTask(testTask);
    console.log('   ✅ Task executed successfully:', result.success);
    
    // Test multiple tasks to verify no recursion issues
    const tasks = [];
    for (let i = 0; i < 5; i++) {
      tasks.push(workerThreadService.executeTask({
        id: `test_task_${i + 2}`,
        type: 'DATA_PROCESSING',
        data: { type: 'SORT_TOKENS', data: { tokens: [], sortBy: 'name' } },
        priority: 'MEDIUM'
      }));
    }
    
    console.log('   🔄 Executing multiple tasks...');
    const results = await Promise.all(tasks);
    console.log('   ✅ All tasks completed successfully');
    
    // Test shutdown
    await workerThreadService.shutdown();
    console.log('   ✅ Worker service shutdown successfully\n');
    
  } catch (error) {
    console.error('   ❌ WorkerThreadService test failed:', error.message);
    return false;
  }
  
  return true;
}

async function testFrontendWebSocketService() {
  console.log('2️⃣ Testing FrontendWebSocketService timer fixes...');
  
  try {
    // Test stats (service should be properly initialized)
    const stats = frontendWebSocketService.getStats();
    console.log('   📊 WebSocket service stats:', {
      totalClients: stats.totalClients,
      isInitialized: stats.isInitialized
    });
    
    // Test shutdown (should clean up all timers)
    frontendWebSocketService.shutdown();
    console.log('   ✅ WebSocket service shutdown with timer cleanup\n');
    
  } catch (error) {
    console.error('   ❌ FrontendWebSocketService test failed:', error.message);
    return false;
  }
  
  return true;
}

async function testPerformanceMonitorService() {
  console.log('3️⃣ Testing PerformanceMonitorService timer fixes...');
  
  try {
    // Test stats
    const stats = performanceMonitorService.getStats();
    console.log('   📊 Performance service stats available');
    
    // Test stop (should clean up all timers)
    performanceMonitorService.stop();
    console.log('   ✅ Performance service stopped with timer cleanup\n');
    
  } catch (error) {
    console.error('   ❌ PerformanceMonitorService test failed:', error.message);
    return false;
  }
  
  return true;
}

async function testMemoryLeakPrevention() {
  console.log('4️⃣ Testing memory leak prevention...');
  
  try {
    // Check initial memory usage
    const initialMemory = process.memoryUsage();
    console.log('   📊 Initial memory usage:', Math.round(initialMemory.heapUsed / 1024 / 1024), 'MB');
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
      console.log('   🗑️ Forced garbage collection');
    }
    
    // Check active handles (should be minimal)
    const activeHandles = process._getActiveHandles().length;
    console.log('   🔗 Active handles:', activeHandles);
    
    if (activeHandles < 10) {
      console.log('   ✅ Active handles count is healthy\n');
    } else {
      console.log('   ⚠️ High number of active handles detected\n');
    }
    
  } catch (error) {
    console.error('   ❌ Memory leak test failed:', error.message);
    return false;
  }
  
  return true;
}

async function runAllTests() {
  console.log('🎯 Running comprehensive system stability tests...\n');
  
  const results = {
    workerService: await testWorkerThreadService(),
    webSocketService: await testFrontendWebSocketService(),
    performanceService: await testPerformanceMonitorService(),
    memoryLeaks: await testMemoryLeakPrevention()
  };
  
  console.log('📋 Test Results Summary:');
  console.log('========================');
  
  let allPassed = true;
  for (const [test, passed] of Object.entries(results)) {
    const status = passed ? '✅ PASSED' : '❌ FAILED';
    console.log(`${test}: ${status}`);
    if (!passed) allPassed = false;
  }
  
  console.log('========================');
  
  if (allPassed) {
    console.log('🎉 ALL CRITICAL FIXES VERIFIED SUCCESSFULLY!');
    console.log('💡 The system should now be stable and VSCode crashes should be eliminated.');
  } else {
    console.log('⚠️ Some tests failed. Please review the issues above.');
  }
  
  process.exit(allPassed ? 0 : 1);
}

// Run tests
runAllTests().catch(error => {
  console.error('💥 Test execution failed:', error);
  process.exit(1);
});
