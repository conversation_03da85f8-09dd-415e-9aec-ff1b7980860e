#!/usr/bin/env node

/**
 * Comprehensive Test Suite for All Implemented Services
 * Tests Worker Thread Service, Limit Order Monitoring, and Performance Monitoring
 */

import axios from 'axios';
import { spawn } from 'child_process';
import { setTimeout } from 'timers/promises';

const BASE_URL = 'http://localhost:5001';
const TEST_TIMEOUT = 30000; // 30 seconds

class ServiceTester {
  constructor() {
    this.results = {
      serverStart: false,
      basicHealth: false,
      performanceMonitoring: false,
      workerThreads: false,
      limitOrderMonitoring: false,
      limitOrderHealth: false,
      limitOrderRefresh: false,
      limitOrderStatus: false,
      websocketHealth: false,
      errors: []
    };
  }

  async log(message, type = 'INFO') {
    const timestamp = new Date().toISOString();
    const prefix = type === 'ERROR' ? '❌' : type === 'SUCCESS' ? '✅' : '📋';
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async testEndpoint(name, url, expectedStatus = 200) {
    try {
      await this.log(`Testing ${name}: ${url}`);
      const response = await axios.get(url, { timeout: 5000 });
      
      if (response.status === expectedStatus) {
        await this.log(`${name} - SUCCESS (${response.status})`, 'SUCCESS');
        return { success: true, data: response.data, status: response.status };
      } else {
        await this.log(`${name} - UNEXPECTED STATUS: ${response.status}`, 'ERROR');
        return { success: false, error: `Unexpected status: ${response.status}` };
      }
    } catch (error) {
      await this.log(`${name} - ERROR: ${error.message}`, 'ERROR');
      this.results.errors.push(`${name}: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  async testPostEndpoint(name, url, data = {}) {
    try {
      await this.log(`Testing ${name}: POST ${url}`);
      const response = await axios.post(url, data, { timeout: 5000 });
      
      if (response.status >= 200 && response.status < 300) {
        await this.log(`${name} - SUCCESS (${response.status})`, 'SUCCESS');
        return { success: true, data: response.data, status: response.status };
      } else {
        await this.log(`${name} - UNEXPECTED STATUS: ${response.status}`, 'ERROR');
        return { success: false, error: `Unexpected status: ${response.status}` };
      }
    } catch (error) {
      await this.log(`${name} - ERROR: ${error.message}`, 'ERROR');
      this.results.errors.push(`${name}: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  async waitForServer(maxAttempts = 30) {
    await this.log('Waiting for server to start...');
    
    for (let i = 0; i < maxAttempts; i++) {
      try {
        const response = await axios.get(`${BASE_URL}/health`, { timeout: 2000 });
        if (response.status === 200) {
          await this.log('Server is ready!', 'SUCCESS');
          this.results.serverStart = true;
          return true;
        }
      } catch (error) {
        // Server not ready yet
        await setTimeout(1000);
      }
    }
    
    await this.log('Server failed to start within timeout', 'ERROR');
    return false;
  }

  async runAllTests() {
    await this.log('🧪 Starting Comprehensive Service Tests', 'INFO');
    await this.log('=' * 60);

    // Test 1: Basic Health Check
    await this.log('\n📋 Test 1: Basic Health Check');
    const healthResult = await this.testEndpoint('Basic Health', `${BASE_URL}/health`);
    this.results.basicHealth = healthResult.success;

    if (healthResult.success) {
      await this.log(`Health Response: ${JSON.stringify(healthResult.data, null, 2)}`);
    }

    // Test 2: Performance Monitoring
    await this.log('\n📋 Test 2: Performance Monitoring Service');
    const perfResult = await this.testEndpoint('Performance Monitoring', `${BASE_URL}/api/performance`);
    this.results.performanceMonitoring = perfResult.success;

    if (perfResult.success) {
      const data = perfResult.data.data;
      await this.log(`Performance Data Available:`);
      await this.log(`  - Workers: ${data.workers ? 'Available' : 'Missing'}`);
      await this.log(`  - Real-time Metrics: ${data.realTime ? 'Available' : 'Missing'}`);
      await this.log(`  - Memory Usage: ${data.memory ? data.memory.heapUsed + 'MB' : 'Missing'}`);
      
      this.results.workerThreads = !!(data.workers && data.workers.activeWorkers !== undefined);
    }

    // Test 3: WebSocket Health
    await this.log('\n📋 Test 3: WebSocket Health Check');
    const wsResult = await this.testEndpoint('WebSocket Health', `${BASE_URL}/api/websocket/health`);
    this.results.websocketHealth = wsResult.success;

    // Test 4: Limit Order Monitoring Health
    await this.log('\n📋 Test 4: Limit Order Monitoring Health');
    const loHealthResult = await this.testEndpoint('Limit Order Health', `${BASE_URL}/api/limit-orders/monitoring/health`);
    this.results.limitOrderHealth = loHealthResult.success;

    if (loHealthResult.success) {
      const data = loHealthResult.data;
      await this.log(`Limit Order Monitoring:`);
      await this.log(`  - Connected: ${data.connected}`);
      await this.log(`  - Connection State: ${data.connectionState}`);
      await this.log(`  - Active Subscriptions: ${data.activeSubscriptions}`);
      await this.log(`  - Total Orders: ${data.totalOrders}`);
    }

    // Test 5: Limit Order Status
    await this.log('\n📋 Test 5: Limit Order Monitoring Status');
    const loStatusResult = await this.testEndpoint('Limit Order Status', `${BASE_URL}/api/limit-orders/monitoring/status`);
    this.results.limitOrderStatus = loStatusResult.success;

    // Test 6: Limit Order Refresh
    await this.log('\n📋 Test 6: Limit Order Monitoring Refresh');
    const loRefreshResult = await this.testPostEndpoint('Limit Order Refresh', `${BASE_URL}/api/limit-orders/monitoring/refresh`);
    this.results.limitOrderRefresh = loRefreshResult.success;

    this.results.limitOrderMonitoring = this.results.limitOrderHealth && this.results.limitOrderStatus;

    // Generate Test Report
    await this.generateReport();
  }

  async generateReport() {
    await this.log('\n' + '=' * 60);
    await this.log('🎯 COMPREHENSIVE TEST RESULTS', 'INFO');
    await this.log('=' * 60);

    const tests = [
      { name: 'Server Startup', result: this.results.serverStart },
      { name: 'Basic Health Check', result: this.results.basicHealth },
      { name: 'Performance Monitoring', result: this.results.performanceMonitoring },
      { name: 'Worker Thread Service', result: this.results.workerThreads },
      { name: 'WebSocket Health', result: this.results.websocketHealth },
      { name: 'Limit Order Health', result: this.results.limitOrderHealth },
      { name: 'Limit Order Status', result: this.results.limitOrderStatus },
      { name: 'Limit Order Refresh', result: this.results.limitOrderRefresh },
      { name: 'Overall Limit Order Monitoring', result: this.results.limitOrderMonitoring }
    ];

    let passedTests = 0;
    let totalTests = tests.length;

    for (const test of tests) {
      const status = test.result ? '✅ PASS' : '❌ FAIL';
      await this.log(`${status} - ${test.name}`);
      if (test.result) passedTests++;
    }

    await this.log('\n📊 SUMMARY:');
    await this.log(`Tests Passed: ${passedTests}/${totalTests}`);
    await this.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

    if (this.results.errors.length > 0) {
      await this.log('\n❌ ERRORS ENCOUNTERED:');
      for (const error of this.results.errors) {
        await this.log(`  - ${error}`);
      }
    }

    // Service-specific analysis
    await this.log('\n🔍 SERVICE ANALYSIS:');
    
    if (this.results.workerThreads) {
      await this.log('✅ Worker Thread Service: ENABLED and FUNCTIONAL');
    } else {
      await this.log('❌ Worker Thread Service: NOT FUNCTIONAL');
    }

    if (this.results.limitOrderMonitoring) {
      await this.log('✅ Limit Order Monitoring: ENABLED and FUNCTIONAL');
    } else {
      await this.log('❌ Limit Order Monitoring: NOT FUNCTIONAL');
    }

    if (this.results.performanceMonitoring) {
      await this.log('✅ Performance Monitoring: ENABLED and FUNCTIONAL');
    } else {
      await this.log('❌ Performance Monitoring: NOT FUNCTIONAL');
    }

    const overallSuccess = passedTests >= 7; // At least 7/9 tests should pass
    await this.log(`\n🎯 OVERALL RESULT: ${overallSuccess ? '✅ SUCCESS' : '❌ NEEDS ATTENTION'}`);

    return {
      success: overallSuccess,
      passedTests,
      totalTests,
      results: this.results
    };
  }
}

// Run the tests
async function main() {
  const tester = new ServiceTester();
  
  // Wait for server to be ready
  const serverReady = await tester.waitForServer();
  
  if (!serverReady) {
    await tester.log('❌ Server is not running. Please start the server first:', 'ERROR');
    await tester.log('   cd backend/spot_backend && npm run dev');
    process.exit(1);
  }

  // Run all tests
  const results = await tester.runAllTests();
  
  // Exit with appropriate code
  process.exit(results.success ? 0 : 1);
}

// Handle script execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  });
}

export { ServiceTester };
