// Simple test to verify services are properly enabled
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🧪 Testing Service Configurations...\n');

// Test 1: Environment Variables
console.log('📋 Environment Variables:');
console.log(`  MOBULA_API_KEY: ${process.env.MOBULA_API_KEY ? '✅ Set' : '❌ Not set'}`);
console.log(`  SUPABASE_URL: ${process.env.SUPABASE_URL ? '✅ Set' : '❌ Not set'}`);
console.log(`  SUPABASE_SERVICE_ROLE_KEY: ${process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ Set' : '❌ Not set'}`);
console.log(`  REDIS_URL: ${process.env.REDIS_URL ? '✅ Set' : '❌ Not set'}`);

// Test 2: Service Imports
console.log('\n📋 Service Import Test:');
try {
  const { workerThreadService } = await import('./dist/services/workerThreadService.js');
  console.log('  Worker Thread Service: ✅ Imported');
} catch (error) {
  console.log('  Worker Thread Service: ❌ Import failed -', error.message);
}

try {
  const { limitOrderMonitoringService } = await import('./dist/services/limitOrderMonitoringService.js');
  console.log('  Limit Order Monitoring Service: ✅ Imported');
} catch (error) {
  console.log('  Limit Order Monitoring Service: ❌ Import failed -', error.message);
}

try {
  const { performanceMonitorService } = await import('./dist/services/performanceMonitorService.js');
  console.log('  Performance Monitor Service: ✅ Imported');
} catch (error) {
  console.log('  Performance Monitor Service: ❌ Import failed -', error.message);
}

// Test 3: Basic Service Functionality
console.log('\n📋 Service Functionality Test:');
try {
  const { workerThreadService } = await import('./dist/services/workerThreadService.js');
  const stats = workerThreadService.getStats();
  console.log('  Worker Thread Service Stats: ✅ Available');
  console.log('    - Active Workers:', stats.activeWorkers);
  console.log('    - Queue Size:', stats.queueSize);
} catch (error) {
  console.log('  Worker Thread Service Stats: ❌ Failed -', error.message);
}

try {
  const { limitOrderMonitoringService } = await import('./dist/services/limitOrderMonitoringService.js');
  const status = limitOrderMonitoringService.getStatus();
  console.log('  Limit Order Monitoring Status: ✅ Available');
  console.log('    - Connection State:', status.connectionState);
  console.log('    - Active Subscriptions:', status.activeSubscriptions);
} catch (error) {
  console.log('  Limit Order Monitoring Status: ❌ Failed -', error.message);
}

try {
  const { performanceMonitorService } = await import('./dist/services/performanceMonitorService.js');
  const metrics = performanceMonitorService.getRealTimeMetrics();
  console.log('  Performance Monitor Metrics: ✅ Available');
  console.log('    - Memory Usage:', Math.round(process.memoryUsage().heapUsed / 1024 / 1024), 'MB');
} catch (error) {
  console.log('  Performance Monitor Metrics: ❌ Failed -', error.message);
}

console.log('\n🎯 Service Configuration Test Complete');
