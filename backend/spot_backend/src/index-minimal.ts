import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createServer } from 'http';

// Load environment variables
dotenv.config();

console.log('🔧 Starting Spot Backend Server...');
console.log('📍 Environment:', process.env.NODE_ENV || 'development');
console.log('📍 Port from env:', process.env.PORT);

// Initialize express app
const app = express();
console.log('✅ Express app initialized');

// Middleware
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// Basic routes
app.get('/', (req, res) => {
  res.json({ 
    message: 'Welcome to the Spot Trading API',
    status: 'running',
    timestamp: new Date().toISOString()
  });
});

app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    service: 'spot-backend',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: {
      heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
      rss: Math.round(process.memoryUsage().rss / 1024 / 1024)
    }
  });
});

app.get('/api/performance', (req, res) => {
  res.json({
    status: 'success',
    timestamp: Date.now(),
    data: {
      memory: {
        heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024),
        rss: Math.round(process.memoryUsage().rss / 1024 / 1024)
      },
      uptime: Math.round(process.uptime())
    }
  });
});

app.get('/api/websocket/health', (req, res) => {
  res.status(503).json({
    ready: false,
    status: 'disabled',
    message: 'WebSocket service temporarily disabled',
    timestamp: Date.now()
  });
});

app.get('/api/limit-orders/monitoring/health', (req, res) => {
  res.status(503).json({
    connected: false,
    activeSubscriptions: 0,
    totalOrders: 0,
    lastUpdate: 0,
    connectionState: 'disabled',
    config: { wsUrl: '', hasApiKey: false, maxReconnectAttempts: 0 },
    stats: { executedOrders: 0, failedExecutions: 0 },
    message: 'Monitoring service temporarily disabled',
    timestamp: Date.now()
  });
});

app.get('/api/limit-orders/monitoring/subscriptions', (req, res) => {
  res.json({
    success: false,
    data: [],
    count: 0,
    message: 'Monitoring service temporarily disabled',
    timestamp: Date.now()
  });
});

app.post('/api/limit-orders/monitoring/refresh', (req, res) => {
  res.json({
    success: false,
    message: 'Monitoring service temporarily disabled',
    timestamp: Date.now()
  });
});

// Basic limit orders endpoints
app.get('/api/limit-orders', (req, res) => {
  res.json({
    success: true,
    data: [],
    message: 'Limit orders service running (basic mode)',
    timestamp: Date.now()
  });
});

app.post('/api/limit-orders', (req, res) => {
  res.status(503).json({
    success: false,
    message: 'Limit orders creation temporarily disabled',
    timestamp: Date.now()
  });
});

// Basic home endpoints
app.get('/api/home/<USER>', (req, res) => {
  res.json({
    success: true,
    data: {
      new: [],
      bonding: [],
      bonded: []
    },
    message: 'Pulse service running (basic mode)',
    timestamp: Date.now()
  });
});

// Basic wallet endpoints
app.get('/api/wallet/balances', (req, res) => {
  res.json({
    success: true,
    data: {
      BTC: 0,
      ETH: 0,
      SOL: 0
    },
    message: 'Wallet service running (basic mode)',
    timestamp: Date.now()
  });
});

// Basic activity endpoints
app.get('/api/activity', (req, res) => {
  res.json({
    success: true,
    data: [],
    message: 'Activity service running (basic mode)',
    timestamp: Date.now()
  });
});

// Error handling
app.use((req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.method} ${req.path} not found`,
    timestamp: Date.now()
  });
});

app.use((error: any, req: any, res: any, next: any) => {
  console.error('Error:', error);
  res.status(500).json({
    error: 'Internal Server Error',
    message: error.message || 'Something went wrong',
    timestamp: Date.now()
  });
});

// Create HTTP server
const httpServer = createServer(app);

// Start the server
const PORT = process.env.PORT || 5001;
console.log(`🔧 Attempting to start server on port ${PORT}...`);

const server = httpServer.listen(Number(PORT), '0.0.0.0', () => {
  console.log(`🚀 Spot Backend Server running on port ${PORT}`);
  console.log(`📍 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 API Base: http://localhost:${PORT}/api`);
  console.log(`✅ Server initialization completed`);
});

server.on('error', (error: any) => {
  console.error('❌ Server error:', error);
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ Port ${PORT} is already in use`);
  }
});

server.on('listening', () => {
  console.log(`✅ Server is listening on port ${PORT}`);
});

// Graceful shutdown
const shutdown = () => {
  console.log('🛑 Shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
};

process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);

export default app;
