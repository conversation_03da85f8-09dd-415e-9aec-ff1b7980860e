import { parentPort, workerData } from 'worker_threads';
import axios from 'axios';

class TaskWorker {
  constructor(workerId) {
    this.workerId = workerId;
    this.setupMessageHandler();
  }

  setupMessageHandler() {
    if (!parentPort) {
      throw new Error('Worker must be run in worker thread');
    }

    parentPort.on('message', async (task) => {
      const startTime = Date.now();
      let result;

      try {
        const data = await this.executeTask(task);
        result = {
          id: task.id,
          success: true,
          data,
          executionTime: Date.now() - startTime
        };
      } catch (error) {
        result = {
          id: task.id,
          success: false,
          error: error.message,
          executionTime: Date.now() - startTime
        };
      }

      parentPort.postMessage(result);
    });
  }

  async executeTask(task) {
    switch (task.type) {
      case 'CACHE_REFRESH':
        return this.executeCacheRefresh(task.data);
      case 'API_CALL':
        return this.executeApiCall(task.data);
      case 'DATA_PROCESSING':
        return this.executeDataProcessing(task.data);
      case 'WEBSOCKET_PROCESSING':
        return this.executeWebSocketProcessing(task.data);
      default:
        throw new Error(`Unknown task type: ${task.type}`);
    }
  }

  async executeCacheRefresh(data) {
    const { cacheType, params } = data;

    switch (cacheType) {
      case 'connect-coins':
        return this.refreshConnectCoinsCache(params);
      case 'network-highlights':
        return this.refreshNetworkHighlightsCache(params);
      case 'token-radar':
        return this.refreshTokenRadarCache(params);
      default:
        // Simple cache refresh simulation for unknown types
        await new Promise(resolve => setTimeout(resolve, 100));
        return { success: true, type: cacheType };
    }
  }

  async executeApiCall(config) {
    const { url, method = 'GET', headers = {}, params, data, timeout = 25000 } = config;

    const response = await axios({
      url,
      method,
      headers,
      params,
      data,
      timeout
    });

    return response.data;
  }

  async executeDataProcessing(config) {
    const { type, data } = config;

    switch (type) {
      case 'SORT_TOKENS':
        return this.sortTokens(data.tokens, data.sortBy);
      case 'FILTER_TOKENS':
        return this.filterTokens(data.tokens, data.filters);
      case 'TRANSFORM_DATA':
        return this.transformData(data.input, data.transformType);
      case 'AGGREGATE_DATA':
        return this.aggregateData(data.datasets, data.aggregationType);
      default:
        // Simple data processing for unknown types
        return { processed: true, type: type };
    }
  }

  async executeWebSocketProcessing(config) {
    const { type, data } = config;

    switch (type) {
      case 'PROCESS_PULSE_DATA':
        return this.processPulseData(data);
      case 'VALIDATE_WEBSOCKET_MESSAGE':
        return this.validateWebSocketMessage(data);
      default:
        throw new Error(`Unknown WebSocket processing type: ${type}`);
    }
  }

  // Cache refresh implementations
  async refreshConnectCoinsCache(params) {
    // Simulate cache refresh
    await new Promise(resolve => setTimeout(resolve, 200));
    return { 
      success: true, 
      type: 'connect-coins',
      refreshed: Date.now(),
      params 
    };
  }

  async refreshNetworkHighlightsCache(params) {
    // Simulate cache refresh
    await new Promise(resolve => setTimeout(resolve, 150));
    return { 
      success: true, 
      type: 'network-highlights',
      refreshed: Date.now(),
      params 
    };
  }

  async refreshTokenRadarCache(params) {
    // Simulate cache refresh
    await new Promise(resolve => setTimeout(resolve, 100));
    return { 
      success: true, 
      type: 'token-radar',
      refreshed: Date.now(),
      params 
    };
  }

  // Data processing implementations
  sortTokens(tokens, sortBy) {
    if (!Array.isArray(tokens)) return tokens;
    
    return tokens.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return (a.name || '').localeCompare(b.name || '');
        case 'price':
          return (b.price || 0) - (a.price || 0);
        case 'volume':
          return (b.volume || 0) - (a.volume || 0);
        case 'marketCap':
          return (b.marketCap || 0) - (a.marketCap || 0);
        default:
          return 0;
      }
    });
  }

  filterTokens(tokens, filters) {
    if (!Array.isArray(tokens)) return tokens;
    
    return tokens.filter(token => {
      if (filters.minPrice && (token.price || 0) < filters.minPrice) return false;
      if (filters.maxPrice && (token.price || 0) > filters.maxPrice) return false;
      if (filters.minVolume && (token.volume || 0) < filters.minVolume) return false;
      if (filters.network && token.network !== filters.network) return false;
      if (filters.search && !(token.name || '').toLowerCase().includes(filters.search.toLowerCase())) return false;
      return true;
    });
  }

  transformData(input, transformType) {
    switch (transformType) {
      case 'NORMALIZE_PRICES':
        return this.normalizePrices(input);
      case 'CALCULATE_PERCENTAGES':
        return this.calculatePercentages(input);
      case 'FORMAT_NUMBERS':
        return this.formatNumbers(input);
      default:
        return input;
    }
  }

  aggregateData(datasets, aggregationType) {
    switch (aggregationType) {
      case 'MERGE_NETWORKS':
        return this.mergeNetworkData(datasets);
      case 'COMBINE_TIMEFRAMES':
        return this.combineTimeframeData(datasets);
      default:
        return datasets;
    }
  }

  // WebSocket processing implementations
  processPulseData(data) {
    const { rawData, blockchain } = data;
    
    // Process pulse data categories
    const processed = {
      new: this.processTokenCategory(rawData.new || [], 'new'),
      bonding: this.processTokenCategory(rawData.bonding || [], 'bonding'),
      bonded: this.processTokenCategory(rawData.bonded || [], 'bonded')
    };

    return {
      ...processed,
      lastUpdated: Date.now(),
      blockchain
    };
  }

  validateWebSocketMessage(data) {
    const { message, schema } = data;
    
    // Basic validation
    if (!message || typeof message !== 'object') {
      return { valid: false, error: 'Invalid message format' };
    }

    return { valid: true, message };
  }

  // Helper methods
  processTokenCategory(tokens, category) {
    return tokens.map(token => ({
      ...token,
      category,
      processedAt: Date.now()
    }));
  }

  normalizePrices(data) {
    if (!Array.isArray(data)) return data;
    
    const prices = data.map(item => item.price || 0).filter(p => p > 0);
    const max = Math.max(...prices);
    const min = Math.min(...prices);
    const range = max - min;
    
    return data.map(item => ({
      ...item,
      normalizedPrice: range > 0 ? ((item.price || 0) - min) / range : 0
    }));
  }

  calculatePercentages(data) {
    if (!Array.isArray(data)) return data;
    
    const total = data.reduce((sum, item) => sum + (item.value || 0), 0);
    
    return data.map(item => ({
      ...item,
      percentage: total > 0 ? ((item.value || 0) / total) * 100 : 0
    }));
  }

  formatNumbers(data) {
    if (!Array.isArray(data)) return data;
    
    return data.map(item => ({
      ...item,
      formattedPrice: this.formatCurrency(item.price),
      formattedVolume: this.formatLargeNumber(item.volume),
      formattedMarketCap: this.formatLargeNumber(item.marketCap)
    }));
  }

  formatCurrency(value) {
    if (typeof value !== 'number') return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(value);
  }

  formatLargeNumber(value) {
    if (typeof value !== 'number') return '0';
    
    if (value >= 1e9) return (value / 1e9).toFixed(2) + 'B';
    if (value >= 1e6) return (value / 1e6).toFixed(2) + 'M';
    if (value >= 1e3) return (value / 1e3).toFixed(2) + 'K';
    return value.toFixed(2);
  }

  mergeNetworkData(datasets) {
    const merged = {};
    
    datasets.forEach(dataset => {
      if (dataset && typeof dataset === 'object') {
        Object.assign(merged, dataset);
      }
    });
    
    return merged;
  }

  combineTimeframeData(datasets) {
    return datasets.reduce((combined, dataset) => {
      if (Array.isArray(dataset)) {
        return combined.concat(dataset);
      }
      return combined;
    }, []);
  }
}

// Initialize worker
if (workerData?.workerId) {
  new TaskWorker(workerData.workerId);
}
