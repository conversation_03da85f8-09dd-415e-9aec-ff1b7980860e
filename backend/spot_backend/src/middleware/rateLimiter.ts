import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger.js';
import { getFromCache, setToCache } from '../utils/redis.js';

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  keyGenerator?: (req: Request) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  onLimitReached?: (req: Request, res: Response) => void;
}

interface RateLimitInfo {
  count: number;
  resetTime: number;
  firstRequest: number;
}

class RateLimiter {
  private config: RateLimitConfig;
  private prefix: string;

  constructor(config: RateLimitConfig, prefix: string = 'rate_limit') {
    this.config = {
      keyGenerator: (req) => req.ip || 'unknown',
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      ...config
    };
    this.prefix = prefix;
  }

  /**
   * Generate cache key for rate limiting
   */
  private generateKey(req: Request): string {
    const identifier = this.config.keyGenerator!(req);
    return `${this.prefix}:${identifier}`;
  }

  /**
   * Get current rate limit info
   */
  private async getRateLimitInfo(key: string): Promise<RateLimitInfo> {
    const cached = await getFromCache(key);
    const now = Date.now();

    if (!cached) {
      return {
        count: 0,
        resetTime: now + this.config.windowMs,
        firstRequest: now
      };
    }

    // Reset if window has expired
    if (now >= cached.resetTime) {
      return {
        count: 0,
        resetTime: now + this.config.windowMs,
        firstRequest: now
      };
    }

    return cached;
  }

  /**
   * Update rate limit info
   */
  private async updateRateLimitInfo(key: string, info: RateLimitInfo): Promise<void> {
    const ttl = Math.ceil((info.resetTime - Date.now()) / 1000);
    await setToCache(key, info, Math.max(ttl, 1));
  }

  /**
   * Create middleware function
   */
  middleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const key = this.generateKey(req);
        const rateLimitInfo = await this.getRateLimitInfo(key);

        // Check if limit is exceeded
        if (rateLimitInfo.count >= this.config.maxRequests) {
          const resetTime = new Date(rateLimitInfo.resetTime);
          
          // Set rate limit headers
          res.set({
            'X-RateLimit-Limit': this.config.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': resetTime.toISOString(),
            'Retry-After': Math.ceil((rateLimitInfo.resetTime - Date.now()) / 1000).toString()
          });

          logger.warn(`Rate limit exceeded for ${this.generateKey(req)}`);

          if (this.config.onLimitReached) {
            this.config.onLimitReached(req, res);
          } else {
            res.status(429).json({
              error: 'Too Many Requests',
              message: `Rate limit exceeded. Try again after ${resetTime.toISOString()}`,
              retryAfter: Math.ceil((rateLimitInfo.resetTime - Date.now()) / 1000)
            });
          }
          return;
        }

        // Increment counter
        rateLimitInfo.count++;
        await this.updateRateLimitInfo(key, rateLimitInfo);

        // Set rate limit headers
        res.set({
          'X-RateLimit-Limit': this.config.maxRequests.toString(),
          'X-RateLimit-Remaining': (this.config.maxRequests - rateLimitInfo.count).toString(),
          'X-RateLimit-Reset': new Date(rateLimitInfo.resetTime).toISOString()
        });

        // Handle response to potentially skip counting
        const originalSend = res.send;
        res.send = function(body) {
          const shouldSkip = 
            (res.statusCode < 400 && rateLimiter.config.skipSuccessfulRequests) ||
            (res.statusCode >= 400 && rateLimiter.config.skipFailedRequests);

          if (shouldSkip) {
            // Decrement counter if we should skip this request
            rateLimitInfo.count--;
            rateLimiter.updateRateLimitInfo(key, rateLimitInfo).catch(err => {
              logger.error('Failed to update rate limit after skip:', err);
            });
          }

          return originalSend.call(this, body);
        };

        const rateLimiter = this;
        next();
      } catch (error) {
        logger.error('Rate limiter error:', error);
        next(); // Continue on error to avoid blocking requests
      }
    };
  }
}

// Predefined rate limiters for different use cases
export const createApiRateLimiter = (maxRequests: number = 100, windowMs: number = 60000) => {
  return new RateLimiter({
    windowMs,
    maxRequests,
    keyGenerator: (req) => `${req.ip}:${req.path}`,
    onLimitReached: (req, res) => {
      logger.warn(`API rate limit exceeded for ${req.ip} on ${req.path}`);
    }
  }, 'api_rate_limit');
};

export const createUserRateLimiter = (maxRequests: number = 1000, windowMs: number = 3600000) => {
  return new RateLimiter({
    windowMs,
    maxRequests,
    keyGenerator: (req) => {
      // Try to get user ID from various sources
      const userId = req.headers['x-user-id'] || 
                    req.body?.userId || 
                    req.query?.userId || 
                    req.ip;
      return `user:${userId}`;
    }
  }, 'user_rate_limit');
};

export const createWebSocketRateLimiter = (maxRequests: number = 50, windowMs: number = 60000) => {
  return new RateLimiter({
    windowMs,
    maxRequests,
    keyGenerator: (req) => `ws:${req.ip}`,
    skipSuccessfulRequests: true // Only count failed WebSocket attempts
  }, 'ws_rate_limit');
};

// Circuit breaker pattern for external API calls
export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  
  constructor(
    private failureThreshold: number = 5,
    private resetTimeout: number = 60000,
    private monitorWindow: number = 300000
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.resetTimeout) {
        this.state = 'HALF_OPEN';
        logger.info('Circuit breaker moving to HALF_OPEN state');
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await operation();
      
      if (this.state === 'HALF_OPEN') {
        this.reset();
      }
      
      return result;
    } catch (error) {
      this.recordFailure();
      throw error;
    }
  }

  private recordFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
      logger.warn(`Circuit breaker opened after ${this.failures} failures`);
    }
  }

  private reset(): void {
    this.failures = 0;
    this.state = 'CLOSED';
    logger.info('Circuit breaker reset to CLOSED state');
  }

  getState(): { state: string; failures: number; lastFailureTime: number } {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime
    };
  }
}

export { RateLimiter };
export default RateLimiter;
