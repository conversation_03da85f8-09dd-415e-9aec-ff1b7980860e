import { Request, Response } from 'express';
import { limitOrderService } from '../services/limitOrderService.js';
import { logger } from '../utils/logger.js';
import { CreateLimitOrderData, LimitOrderFilters } from '../config/supabase.js';

/**
 * Create a new limit order
 * POST /api/limit-orders
 */
export async function createLimitOrder(req: Request, res: Response): Promise<void> {
  try {
    const {
      user_id,
      token_address,
      token_name,
      token_symbol,
      token_image,
      pool_address,
      dex_type,
      direction,
      amount,
      target_price,
      current_price,
      current_market_cap,
      target_market_cap,
      slippage,
      wallet_address,
      wallet_id,
      expires_at
    } = req.body;

    // Validate required fields
    if (!user_id || !token_address || !token_name || !token_symbol || 
        !pool_address || !dex_type || !direction || !amount || 
        !target_price || !current_price || !wallet_address || !wallet_id) {
      res.status(400).json({
        success: false,
        error: 'Missing required fields',
        required: [
          'user_id', 'token_address', 'token_name', 'token_symbol',
          'pool_address', 'dex_type', 'direction', 'amount',
          'target_price', 'current_price', 'wallet_address', 'wallet_id'
        ]
      });
      return;
    }

    const orderData: CreateLimitOrderData = {
      user_id,
      token_address,
      token_name,
      token_symbol,
      token_image,
      pool_address,
      dex_type,
      direction,
      amount: parseFloat(amount),
      target_price: parseFloat(target_price),
      current_price: parseFloat(current_price),
      current_market_cap: current_market_cap ? parseFloat(current_market_cap) : undefined,
      target_market_cap: target_market_cap ? parseFloat(target_market_cap) : undefined,
      slippage: slippage ? parseFloat(slippage) : 0.01, // Default 1% slippage
      wallet_address,
      wallet_id,
      expires_at: expires_at || undefined
    };

    const result = await limitOrderService.createLimitOrder(orderData);

    if (result.success) {
      res.status(201).json({
        success: true,
        data: result.data,
        message: 'Limit order created successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('Error in createLimitOrder controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

/**
 * Get limit orders for a user
 * GET /api/limit-orders
 */
export async function getLimitOrders(req: Request, res: Response): Promise<void> {
  try {
    const { user_id } = req.query;

    if (!user_id || typeof user_id !== 'string') {
      res.status(400).json({
        success: false,
        error: 'user_id query parameter is required'
      });
      return;
    }

    // Parse filters from query parameters
    const filters: LimitOrderFilters = {
      status: req.query.status as any,
      token_address: req.query.token_address as string,
      direction: req.query.direction as any,
      dex_type: req.query.dex_type as string,
      limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
      offset: req.query.offset ? parseInt(req.query.offset as string) : undefined,
      order_by: req.query.order_by as any || 'created_at',
      order_direction: req.query.order_direction as any || 'desc'
    };

    // Validate numeric parameters
    if (filters.limit && (isNaN(filters.limit) || filters.limit < 1 || filters.limit > 100)) {
      res.status(400).json({
        success: false,
        error: 'limit must be a number between 1 and 100'
      });
      return;
    }

    if (filters.offset && (isNaN(filters.offset) || filters.offset < 0)) {
      res.status(400).json({
        success: false,
        error: 'offset must be a non-negative number'
      });
      return;
    }

    const result = await limitOrderService.getLimitOrders(user_id, filters);

    if (result.success) {
      res.json({
        success: true,
        data: result.data,
        count: result.count,
        filters: filters
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('Error in getLimitOrders controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

/**
 * Get a specific limit order by ID
 * GET /api/limit-orders/:id
 */
export async function getLimitOrderById(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const { user_id } = req.query;

    if (!user_id || typeof user_id !== 'string') {
      res.status(400).json({
        success: false,
        error: 'user_id query parameter is required'
      });
      return;
    }

    if (!id) {
      res.status(400).json({
        success: false,
        error: 'Order ID is required'
      });
      return;
    }

    const result = await limitOrderService.getLimitOrderById(user_id, id);

    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      const statusCode = result.error === 'Order not found' ? 404 : 400;
      res.status(statusCode).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('Error in getLimitOrderById controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

/**
 * Update a limit order (mainly for cancellation)
 * PUT /api/limit-orders/:id
 */
export async function updateLimitOrder(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const { user_id, status, error_message } = req.body;

    if (!user_id) {
      res.status(400).json({
        success: false,
        error: 'user_id is required'
      });
      return;
    }

    if (!id) {
      res.status(400).json({
        success: false,
        error: 'Order ID is required'
      });
      return;
    }

    // Only allow status updates for now (mainly cancellation)
    const updateData: any = {};
    
    if (status) {
      if (!['pending', 'cancelled'].includes(status)) {
        res.status(400).json({
          success: false,
          error: 'Only pending and cancelled status updates are allowed'
        });
        return;
      }
      updateData.status = status;
    }

    if (error_message) {
      updateData.error_message = error_message;
    }

    if (Object.keys(updateData).length === 0) {
      res.status(400).json({
        success: false,
        error: 'No valid update fields provided'
      });
      return;
    }

    const result = await limitOrderService.updateLimitOrder(user_id, id, updateData);

    if (result.success) {
      res.json({
        success: true,
        data: result.data,
        message: 'Order updated successfully'
      });
    } else {
      const statusCode = result.error === 'Order not found' ? 404 : 400;
      res.status(statusCode).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('Error in updateLimitOrder controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

/**
 * Cancel a limit order
 * DELETE /api/limit-orders/:id/cancel
 */
export async function cancelLimitOrder(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const { user_id } = req.query;

    if (!user_id || typeof user_id !== 'string') {
      res.status(400).json({
        success: false,
        error: 'user_id query parameter is required'
      });
      return;
    }

    if (!id) {
      res.status(400).json({
        success: false,
        error: 'Order ID is required'
      });
      return;
    }

    const result = await limitOrderService.cancelLimitOrder(user_id, id);

    if (result.success) {
      res.json({
        success: true,
        data: result.data,
        message: 'Order cancelled successfully'
      });
    } else {
      const statusCode = result.error === 'Order not found' ? 404 : 400;
      res.status(statusCode).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('Error in cancelLimitOrder controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

/**
 * Permanently delete a limit order (only cancelled/expired orders)
 * DELETE /api/limit-orders/:id
 */
export async function deleteLimitOrder(req: Request, res: Response): Promise<void> {
  try {
    const { id } = req.params;
    const { user_id } = req.query;

    if (!user_id || typeof user_id !== 'string') {
      res.status(400).json({
        success: false,
        error: 'user_id query parameter is required'
      });
      return;
    }

    if (!id) {
      res.status(400).json({
        success: false,
        error: 'Order ID is required'
      });
      return;
    }

    // First, check if the order exists and can be deleted
    const orderResult = await limitOrderService.getLimitOrderById(user_id, id);

    if (!orderResult.success) {
      const statusCode = orderResult.error === 'Order not found' ? 404 : 400;
      res.status(statusCode).json({
        success: false,
        error: orderResult.error
      });
      return;
    }

    const order = orderResult.data!;

    // Only allow deletion of cancelled or expired orders
    if (order.status !== 'cancelled' && order.status !== 'expired') {
      res.status(400).json({
        success: false,
        error: 'Only cancelled or expired orders can be deleted. Please cancel the order first.'
      });
      return;
    }

    // Proceed with deletion
    const result = await limitOrderService.deleteLimitOrder(user_id, id);

    if (result.success) {
      res.json({
        success: true,
        message: 'Order deleted successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }

  } catch (error) {
    logger.error('Error in deleteLimitOrder controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}

/**
 * Get order statistics for a user
 * GET /api/limit-orders/stats
 */
export async function getLimitOrderStats(req: Request, res: Response): Promise<void> {
  try {
    const { user_id } = req.query;

    if (!user_id || typeof user_id !== 'string') {
      res.status(400).json({
        success: false,
        error: 'user_id query parameter is required'
      });
      return;
    }

    // Get counts for each status
    const [pending, executed, cancelled, expired] = await Promise.all([
      limitOrderService.getLimitOrders(user_id, { status: 'pending' }),
      limitOrderService.getLimitOrders(user_id, { status: 'executed' }),
      limitOrderService.getLimitOrders(user_id, { status: 'cancelled' }),
      limitOrderService.getLimitOrders(user_id, { status: 'expired' })
    ]);

    const stats = {
      pending: pending.success ? pending.count || 0 : 0,
      executed: executed.success ? executed.count || 0 : 0,
      cancelled: cancelled.success ? cancelled.count || 0 : 0,
      expired: expired.success ? expired.count || 0 : 0,
      total: 0
    };

    stats.total = stats.pending + stats.executed + stats.cancelled + stats.expired;

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    logger.error('Error in getLimitOrderStats controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}
