import { logger } from './logger.js';
import { refreshConnectCoinsCache, refreshNetworkHighlightsCache, refreshTokenRadarCache } from '../controllers/homeController.js';

/**
 * Initialize all caches on server startup (non-blocking)
 * This handles the initial loading of data for all cache endpoints
 */
export async function initializeCaches(): Promise<void> {
  // Start all cache operations in parallel without blocking
  const cacheOperations = [
    initializeConnectCoinsCache(),
    initializeNetworkHighlightsCache(),
    initializeTokenRadarCache()
  ];

  // Don't await - let them run in background
  Promise.allSettled(cacheOperations).then(results => {
    // Cache initialization completed silently
  }).catch(error => {
    logger.error('Cache initialization error:', error);
  });
}

/**
 * Initialize connect coins cache (non-blocking)
 */
async function initializeConnectCoinsCache(): Promise<void> {
  try {
    await refreshConnectCoinsCache();
  } catch (error) {
    logger.error('Failed to initialize connect coins cache:', error);
  }
}
  
/**
 * Initialize network highlights cache (non-blocking)
 */
async function initializeNetworkHighlightsCache(): Promise<void> {
  try {
    await refreshNetworkHighlightsCache();
  } catch (error) {
    logger.error('Failed to initialize network highlights cache:', error);
  }
}
  
/**
 * Initialize token radar cache (non-blocking, optimized)
 */
async function initializeTokenRadarCache(): Promise<void> {
  try {
    const networks = ['universal', 'solana', 'binance-smart-chain'];
    const timeframes = ['1m', '5m', '1h', '4h', '24h'];

    // Prioritize most important caches first
    const priorityOperations = [
      () => refreshTokenRadarCache(100), // Default universal
      () => refreshTokenRadarCache(100, 'solana'), // Solana default
      () => refreshTokenRadarCache(100, 'binance-smart-chain') // BSC default
    ];

    // Execute priority operations first
    await Promise.allSettled(priorityOperations.map(op => op()));

    // Then cache other combinations in background
    const backgroundOperations: Promise<any>[] = [];

    for (const network of networks) {
      if (network === 'universal') continue;

      for (const timeframe of timeframes) {
        backgroundOperations.push(
          refreshTokenRadarCache(50, network, timeframe).catch(error => {
            // Silent error handling
          })
        );
      }
    }

    // Universal with different timeframes
    for (const timeframe of timeframes) {
      if (timeframe === '24h') continue;
      backgroundOperations.push(
        refreshTokenRadarCache(50, undefined, timeframe).catch(error => {
          // Silent error handling
        })
      );
    }

    // Execute background operations without blocking
    Promise.allSettled(backgroundOperations).then(() => {
      // Background initialization completed silently
    });

  } catch (error) {
    logger.error('Failed to initialize token radar cache:', error);
  }
}