const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:4000';
const TEST_POOL_ADDRESS = '3Sh7S9XwatY5aUCuufpzGrjT4PiqzMS6psuBpDcXKXXe'; // Example Solana pool address

async function testTradeEndpoints() {
  console.log('🧪 Testing Trade API Endpoints...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing trade service health...');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/api/trade/health`);
      console.log(`   ✅ Health check: ${healthResponse.data.status} - ${healthResponse.data.message}`);
    } catch (error) {
      console.log(`   ❌ Health check failed: ${error.message}`);
    }

    // Test 2: Pool address validation
    console.log('\n2. Testing pool address validation...');
    try {
      const validationResponse = await axios.get(`${BASE_URL}/api/trade/validate/${TEST_POOL_ADDRESS}`);
      console.log(`   ✅ Validation: ${validationResponse.data.data.isValid ? 'Valid' : 'Invalid'} pool address`);
    } catch (error) {
      console.log(`   ❌ Validation failed: ${error.message}`);
    }

    // Test 3: Initial trade data
    console.log('\n3. Testing initial trade data fetch...');
    try {
      const tradeResponse = await axios.get(`${BASE_URL}/api/trade/initial/${TEST_POOL_ADDRESS}?limit=5`);
      const trades = tradeResponse.data.data.trades;
      console.log(`   ✅ Fetched ${trades.length} initial trades`);
      if (trades.length > 0) {
        console.log(`   📊 Latest trade: ${trades[0].type} ${trades[0].displayAmount} for ${trades[0].displayValueUsd}`);
      }
    } catch (error) {
      console.log(`   ❌ Initial trade data failed: ${error.message}`);
    }

    // Test 4: Paginated trade data
    console.log('\n4. Testing paginated trade data...');
    try {
      const paginatedResponse = await axios.get(`${BASE_URL}/api/trade/paginated/${TEST_POOL_ADDRESS}?limit=3&page=1`);
      const result = paginatedResponse.data.data;
      console.log(`   ✅ Fetched ${result.trades.length} trades (page 1)`);
      if (result.pagination) {
        console.log(`   📄 Pagination: ${result.pagination.page}/${Math.ceil(result.pagination.total / result.pagination.limit)} pages`);
      }
    } catch (error) {
      console.log(`   ❌ Paginated trade data failed: ${error.message}`);
    }

    // Test 5: WebSocket status
    console.log('\n5. Testing trade WebSocket status...');
    try {
      const wsStatusResponse = await axios.get(`${BASE_URL}/api/trade/websocket/status`);
      const status = wsStatusResponse.data.data;
      console.log(`   ✅ WebSocket status: ${status.connected ? 'Connected' : 'Disconnected'}`);
      console.log(`   📡 Active subscriptions: ${status.subscriptions}`);
    } catch (error) {
      console.log(`   ❌ WebSocket status failed: ${error.message}`);
    }

    // Test 6: Frontend trade stats
    console.log('\n6. Testing frontend trade statistics...');
    try {
      const statsResponse = await axios.get(`${BASE_URL}/api/trade/frontend/stats`);
      const stats = statsResponse.data.data;
      console.log(`   ✅ Trade room clients: ${stats.tradeRoomClients}`);
      console.log(`   📊 Active subscriptions: ${stats.activeTradeSubscriptions}`);
    } catch (error) {
      console.log(`   ❌ Frontend stats failed: ${error.message}`);
    }

    console.log('\n✅ Trade API testing completed!');

  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
  }
}

// Test WebSocket functionality
async function testWebSocketConnection() {
  console.log('\n🔌 Testing WebSocket Connection...\n');

  try {
    const io = require('socket.io-client');
    const socket = io('http://localhost:4000', {
      transports: ['websocket'],
      timeout: 5000
    });

    socket.on('connect', () => {
      console.log('✅ WebSocket connected successfully');

      // Register client
      socket.emit('register', {
        userId: 'test-user-123',
        sessionId: 'test-session-456'
      });

      // Subscribe to trade data
      setTimeout(() => {
        console.log('📡 Subscribing to trade data...');
        socket.emit('subscribe-trade', {
          poolAddress: TEST_POOL_ADDRESS,
          userId: 'test-user-123',
          sessionId: 'test-session-456'
        });
      }, 1000);

      // Listen for trade data
      socket.on('trade-data', (data) => {
        console.log(`📊 Received trade data for pool: ${data.poolAddress}`);
        console.log(`   Latest trade: ${data.tradeData?.type || 'N/A'} ${data.tradeData?.displayAmount || 'N/A'}`);
        console.log(`   History count: ${data.tradeHistory?.length || 0} trades`);
      });

      // Unsubscribe after 5 seconds
      setTimeout(() => {
        console.log('🚪 Unsubscribing from trade data...');
        socket.emit('unsubscribe-trade', {
          poolAddress: TEST_POOL_ADDRESS,
          userId: 'test-user-123',
          sessionId: 'test-session-456'
        });

        setTimeout(() => {
          socket.disconnect();
          console.log('✅ WebSocket test completed');
        }, 1000);
      }, 5000);
    });

    socket.on('connect_error', (error) => {
      console.log('❌ WebSocket connection failed:', error.message);
    });

    socket.on('error', (error) => {
      console.log('❌ WebSocket error:', error);
    });

  } catch (error) {
    console.error('❌ WebSocket test failed:', error.message);
  }
}

// Run tests
async function runAllTests() {
  console.log('🚀 Starting Trade Functionality Tests\n');
  console.log('=' .repeat(50));

  await testTradeEndpoints();
  
  console.log('\n' + '=' .repeat(50));
  
  await testWebSocketConnection();

  console.log('\n🎉 All tests completed!');
}

// Check if server is running first
async function checkServerStatus() {
  try {
    const response = await axios.get(`${BASE_URL}/api/home/<USER>
    console.log('✅ Server is running, starting tests...\n');
    return true;
  } catch (error) {
    console.log('❌ Server is not running. Please start the backend server first.');
    console.log('   Run: npm start in the backend/spot_backend directory\n');
    return false;
  }
}

// Main execution
checkServerStatus().then(isRunning => {
  if (isRunning) {
    runAllTests();
  }
});
