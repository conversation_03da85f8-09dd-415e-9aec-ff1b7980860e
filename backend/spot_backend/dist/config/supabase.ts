import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { logger } from '../utils/logger.js';

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Use service role for backend operations

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase configuration. Please set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables.');
}

// Create Supabase client with service role key for backend operations
export const supabase: SupabaseClient = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Database types for TypeScript
export interface LimitOrder {
  id: string;
  user_id: string;
  token_address: string;
  token_name: string;
  token_symbol: string;
  token_image?: string;
  pool_address: string;
  dex_type: string;
  direction: 'buy' | 'sell';
  amount: number;
  target_price: number;
  current_price: number;
  current_market_cap?: number;
  target_market_cap?: number;
  slippage: number;
  wallet_address: string;
  wallet_id: string;
  status: 'pending' | 'executed' | 'cancelled' | 'expired';
  expires_at?: string;
  created_at: string;
  updated_at: string;
  executed_at?: string;
  execution_tx_hash?: string;
  error_message?: string;
}

export interface CreateLimitOrderData {
  user_id: string;
  token_address: string;
  token_name: string;
  token_symbol: string;
  token_image?: string;
  pool_address: string;
  dex_type: string;
  direction: 'buy' | 'sell';
  amount: number;
  target_price: number;
  current_price: number;
  current_market_cap?: number;
  target_market_cap?: number;
  slippage: number;
  wallet_address: string;
  wallet_id: string;
  expires_at?: string;
}

export interface UpdateLimitOrderData {
  status?: 'pending' | 'executed' | 'cancelled' | 'expired';
  executed_at?: string;
  execution_tx_hash?: string;
  error_message?: string;
}

export interface LimitOrderFilters {
  status?: 'pending' | 'executed' | 'cancelled' | 'expired';
  token_address?: string;
  direction?: 'buy' | 'sell';
  dex_type?: string;
  limit?: number;
  offset?: number;
  order_by?: 'created_at' | 'updated_at' | 'target_price';
  order_direction?: 'asc' | 'desc';
}

// Test database connection
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    const { error, count } = await supabase
      .from('limit_orders')
      .select('*', { count: 'exact', head: true });

    if (error) {
      logger.error('Database connection test failed:', error);
      return false;
    }

    logger.info('Database connection test successful');
    logger.info(`Found ${count || 0} limit orders in the database`);
    return true;
  } catch (error) {
    logger.error('Database connection test error:', error);
    return false;
  }
}

// Helper function to set user context for RLS
export function setUserContext(userId: string) {
  return supabase.rpc('set_config', {
    setting_name: 'app.current_user_id',
    setting_value: userId,
    is_local: true
  });
}

export default supabase;
