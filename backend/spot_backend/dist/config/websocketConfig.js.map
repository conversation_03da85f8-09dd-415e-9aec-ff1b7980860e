{"version": 3, "file": "websocketConfig.js", "sourceRoot": "", "sources": ["../../src/config/websocketConfig.ts"], "names": [], "mappings": "AAAA,OAAO,MAAM,MAAM,QAAQ,CAAC;AAE5B,6BAA6B;AAC7B,MAAM,CAAC,MAAM,EAAE,CAAC;AAqDhB,MAAM,CAAC,MAAM,eAAe,GAAoB;IAC9C,oBAAoB;IACpB,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,EAAE;IACxC,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,0BAA0B;IAEtE,2BAA2B;IAC3B,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,MAAM,CAAC;IACrE,oBAAoB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,GAAG,CAAC;IACzE,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,OAAO,CAAC;IACtE,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,OAAO,CAAC;IAEtE,6EAA6E;IAC7E,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,OAAO,CAAC,EAAE,gCAAgC;IACjG,kBAAkB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,OAAO,CAAC,EAAE,2BAA2B;IAEtG,mCAAmC;IACnC,aAAa,EAAE;QACb,GAAG,EAAE;YACH,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,OAAO,CAAC,EAAE,4BAA4B;YAChG,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,OAAO,CAAC,EAAE,aAAa;YAC1F,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,QAAQ,CAAC,CAAC,yBAAyB;SACzG;QACD,OAAO,EAAE;YACP,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,OAAO,CAAC,EAAE,8BAA8B;YACtG,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,IAAI,QAAQ,CAAC,EAAE,YAAY;YAC9F,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,IAAI,QAAQ,CAAC,CAAC,0BAA0B;SAC9G;QACD,MAAM,EAAE;YACN,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,QAAQ,CAAC,EAAE,4BAA4B;YACpG,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,QAAQ,CAAC,EAAE,aAAa;YAC9F,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,SAAS,CAAC,CAAC,0BAA0B;SAC9G;KACF;IAED,8BAA8B;IAC9B,kBAAkB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,QAAQ,CAAC,EAAE,YAAY;IACxF,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,OAAO,CAAC,EAAE,aAAa;IAEjF,wBAAwB;IACxB,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,MAAM;IAEzC,+BAA+B;IAC/B,mBAAmB,EAAE;QACnB,IAAI,EAAE,OAAO;QACb,OAAO,EAAE;YACP,SAAS,EAAE,CAAC,SAAS,CAAC;YACtB,WAAW,EAAE,CAAC,eAAe,CAAC;SAC/B;KACF;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,uBAAuB;IACrC,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;IACrE,CAAC;IAED,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;QAClC,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;IACrE,CAAC;IAED,IAAI,eAAe,CAAC,iBAAiB,GAAG,IAAI,EAAE,CAAC;QAC7C,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;IAC5D,CAAC;IAED,IAAI,eAAe,CAAC,oBAAoB,GAAG,CAAC,EAAE,CAAC;QAC7C,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CAAC,+CAA+C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACtF,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,2BAA2B;IACzC,uBAAuB,EAAE,CAAC;IAC1B,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,wBAAwB,CACtC,SAAoB,EACpB,WAAsB;IAEtB,IAAI,SAAS,EAAE,CAAC;QACd,eAAe,CAAC,mBAAmB,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;IACpE,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QAChB,eAAe,CAAC,mBAAmB,CAAC,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;IACxE,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa;IAQ3B,OAAO;QACL,MAAM,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;QACrF,YAAY,EAAE,eAAe,CAAC,YAAY;QAC1C,SAAS,EAAE,eAAe,CAAC,mBAAmB,CAAC,OAAO,CAAC,SAAS;QAChE,WAAW,EAAE,eAAe,CAAC,mBAAmB,CAAC,OAAO,CAAC,WAAW;QACpE,iBAAiB,EAAE,eAAe,CAAC,iBAAiB;QACpD,oBAAoB,EAAE,eAAe,CAAC,oBAAoB;KAC3D,CAAC;AACJ,CAAC"}