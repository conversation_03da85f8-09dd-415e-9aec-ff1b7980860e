import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export interface WebSocketConfig {
  // API Configuration
  apiKey: string;
  websocketUrl: string;
  
  // Connection Configuration
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeatInterval: number;
  connectionTimeout: number;
  
  // Cache Configuration
  cacheExpiryMs: number;
  staleDataThreshold: number;

  // Category-specific cache settings
  categoryCache: {
    new: {
      expiryMs: number;
      staleThreshold: number;
      fallbackExpiryMs: number;
    };
    bonding: {
      expiryMs: number;
      staleThreshold: number;
      fallbackExpiryMs: number;
    };
    bonded: {
      expiryMs: number;
      staleThreshold: number;
      fallbackExpiryMs: number;
    };
  };
  
  // User Activity Configuration
  userSessionTimeout: number;
  disconnectDelay: number;
  
  // Logging Configuration
  logLevel: string;
  
  // Default subscription payload
  defaultSubscription: {
    type: string;
    payload: {
      factories: string[];
      blockchains: string[];
    };
  };
}

export const websocketConfig: WebSocketConfig = {
  // API Configuration
  apiKey: process.env.MOBULA_API_KEY || '',
  websocketUrl: process.env.MOBULA_WSS_URL || 'wss://api-prod.mobula.io',
  
  // Connection Configuration
  reconnectInterval: parseInt(process.env.RECONNECT_INTERVAL || '5000'),
  maxReconnectAttempts: parseInt(process.env.MAX_RECONNECT_ATTEMPTS || '5'),
  heartbeatInterval: parseInt(process.env.HEARTBEAT_INTERVAL || '30000'),
  connectionTimeout: parseInt(process.env.CONNECTION_TIMEOUT || '10000'),
  
  // Cache Configuration - Different expiry times for different data categories
  cacheExpiryMs: parseInt(process.env.CACHE_EXPIRY_MS || '30000'), // 30 seconds for real-time data
  staleDataThreshold: parseInt(process.env.STALE_DATA_THRESHOLD || '60000'), // 1 minute stale threshold

  // Category-specific cache settings
  categoryCache: {
    new: {
      expiryMs: parseInt(process.env.NEW_TOKENS_CACHE_EXPIRY || '15000'), // 15 seconds - most dynamic
      staleThreshold: parseInt(process.env.NEW_TOKENS_STALE_THRESHOLD || '30000'), // 30 seconds
      fallbackExpiryMs: parseInt(process.env.NEW_TOKENS_FALLBACK_EXPIRY || '300000') // 5 minutes for fallback
    },
    bonding: {
      expiryMs: parseInt(process.env.BONDING_TOKENS_CACHE_EXPIRY || '60000'), // 1 minute - moderate changes
      staleThreshold: parseInt(process.env.BONDING_TOKENS_STALE_THRESHOLD || '120000'), // 2 minutes
      fallbackExpiryMs: parseInt(process.env.BONDING_TOKENS_FALLBACK_EXPIRY || '600000') // 10 minutes for fallback
    },
    bonded: {
      expiryMs: parseInt(process.env.BONDED_TOKENS_CACHE_EXPIRY || '300000'), // 5 minutes - least dynamic
      staleThreshold: parseInt(process.env.BONDED_TOKENS_STALE_THRESHOLD || '600000'), // 10 minutes
      fallbackExpiryMs: parseInt(process.env.BONDED_TOKENS_FALLBACK_EXPIRY || '1800000') // 30 minutes for fallback
    }
  },

  // User Activity Configuration
  userSessionTimeout: parseInt(process.env.USER_SESSION_TIMEOUT || '300000'), // 5 minutes
  disconnectDelay: parseInt(process.env.DISCONNECT_DELAY || '60000'), // 60 seconds
  
  // Logging Configuration
  logLevel: process.env.LOG_LEVEL || 'info',
  
  // Default subscription payload
  defaultSubscription: {
    type: 'pulse',
    payload: {
      factories: ['pumpfun'],
      blockchains: ['solana:solana']
    }
  }
};

/**
 * Validate required configuration
 */
export function validateWebSocketConfig(): boolean {
  const errors: string[] = [];
  
  if (!websocketConfig.apiKey) {
    errors.push('MOBULA_API_KEY is required in environment variables');
  }
  
  if (!websocketConfig.websocketUrl) {
    errors.push('MOBULA_WSS_URL is required in environment variables');
  }
  
  if (websocketConfig.reconnectInterval < 1000) {
    errors.push('RECONNECT_INTERVAL must be at least 1000ms');
  }
  
  if (websocketConfig.maxReconnectAttempts < 1) {
    errors.push('MAX_RECONNECT_ATTEMPTS must be at least 1');
  }
  
  if (errors.length > 0) {
    throw new Error(`WebSocket configuration validation failed:\n${errors.join('\n')}`);
  }
  
  return true;
}

/**
 * Get configuration with validation
 */
export function getValidatedWebSocketConfig(): WebSocketConfig {
  validateWebSocketConfig();
  return websocketConfig;
}

/**
 * Update subscription configuration at runtime
 */
export function updateSubscriptionConfig(
  factories?: string[], 
  blockchains?: string[]
): void {
  if (factories) {
    websocketConfig.defaultSubscription.payload.factories = factories;
  }
  
  if (blockchains) {
    websocketConfig.defaultSubscription.payload.blockchains = blockchains;
  }
}

/**
 * Get connection status information
 */
export function getConfigInfo(): {
  apiKey: string;
  websocketUrl: string;
  factories: string[];
  blockchains: string[];
  reconnectInterval: number;
  maxReconnectAttempts: number;
} {
  return {
    apiKey: websocketConfig.apiKey ? '***' + websocketConfig.apiKey.slice(-4) : 'Not set',
    websocketUrl: websocketConfig.websocketUrl,
    factories: websocketConfig.defaultSubscription.payload.factories,
    blockchains: websocketConfig.defaultSubscription.payload.blockchains,
    reconnectInterval: websocketConfig.reconnectInterval,
    maxReconnectAttempts: websocketConfig.maxReconnectAttempts
  };
}
