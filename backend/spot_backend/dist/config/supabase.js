import { createClient } from '@supabase/supabase-js';
import { logger } from '../utils/logger.js';
// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Use service role for backend operations
if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase configuration. Please set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables.');
}
// Create Supabase client with service role key for backend operations
export const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
        autoRefreshToken: false,
        persistSession: false
    }
});
// Test database connection
export async function testDatabaseConnection() {
    try {
        const { error, count } = await supabase
            .from('limit_orders')
            .select('*', { count: 'exact', head: true });
        if (error) {
            logger.error('Database connection test failed:', error);
            return false;
        }
        logger.info('Database connection test successful');
        logger.info(`Found ${count || 0} limit orders in the database`);
        return true;
    }
    catch (error) {
        logger.error('Database connection test error:', error);
        return false;
    }
}
// Helper function to set user context for RLS
export function setUserContext(userId) {
    return supabase.rpc('set_config', {
        setting_name: 'app.current_user_id',
        setting_value: userId,
        is_local: true
    });
}
export default supabase;
//# sourceMappingURL=supabase.js.map