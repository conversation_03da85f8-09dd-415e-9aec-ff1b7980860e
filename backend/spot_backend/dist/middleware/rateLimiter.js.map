{"version": 3, "file": "rateLimiter.js", "sourceRoot": "", "sources": ["../../src/middleware/rateLimiter.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAiB7D,MAAM,WAAW;IACP,MAAM,CAAkB;IACxB,MAAM,CAAS;IAEvB,YAAY,MAAuB,EAAE,SAAiB,YAAY;QAChE,IAAI,CAAC,MAAM,GAAG;YACZ,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,SAAS;YAC1C,sBAAsB,EAAE,KAAK;YAC7B,kBAAkB,EAAE,KAAK;YACzB,GAAG,MAAM;SACV,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,GAAY;QAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,YAAa,CAAC,GAAG,CAAC,CAAC;QAClD,OAAO,GAAG,IAAI,CAAC,MAAM,IAAI,UAAU,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,GAAW;QACxC,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC;QACvC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;gBACL,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACrC,YAAY,EAAE,GAAG;aAClB,CAAC;QACJ,CAAC;QAED,8BAA8B;QAC9B,IAAI,GAAG,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YAC5B,OAAO;gBACL,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACrC,YAAY,EAAE,GAAG;aAClB,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,GAAW,EAAE,IAAmB;QAChE,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;QAC5D,MAAM,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;YAC/D,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAEvD,6BAA6B;gBAC7B,IAAI,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;oBACnD,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;oBAEpD,yBAAyB;oBACzB,GAAG,CAAC,GAAG,CAAC;wBACN,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE;wBACvD,uBAAuB,EAAE,GAAG;wBAC5B,mBAAmB,EAAE,SAAS,CAAC,WAAW,EAAE;wBAC5C,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE;qBACnF,CAAC,CAAC;oBAEH,MAAM,CAAC,IAAI,CAAC,2BAA2B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAEhE,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;wBAC/B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;oBACvC,CAAC;yBAAM,CAAC;wBACN,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;4BACnB,KAAK,EAAE,mBAAmB;4BAC1B,OAAO,EAAE,wCAAwC,SAAS,CAAC,WAAW,EAAE,EAAE;4BAC1E,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;yBACrE,CAAC,CAAC;oBACL,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,oBAAoB;gBACpB,aAAa,CAAC,KAAK,EAAE,CAAC;gBACtB,MAAM,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;gBAEnD,yBAAyB;gBACzB,GAAG,CAAC,GAAG,CAAC;oBACN,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE;oBACvD,uBAAuB,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;oBACnF,mBAAmB,EAAE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;iBACrE,CAAC,CAAC;gBAEH,+CAA+C;gBAC/C,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;gBAC9B,GAAG,CAAC,IAAI,GAAG,UAAS,IAAI;oBACtB,MAAM,UAAU,GACd,CAAC,GAAG,CAAC,UAAU,GAAG,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,sBAAsB,CAAC;wBACnE,CAAC,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;oBAEnE,IAAI,UAAU,EAAE,CAAC;wBACf,mDAAmD;wBACnD,aAAa,CAAC,KAAK,EAAE,CAAC;wBACtB,WAAW,CAAC,mBAAmB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BAC9D,MAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,GAAG,CAAC,CAAC;wBAC/D,CAAC,CAAC,CAAC;oBACL,CAAC;oBAED,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACvC,CAAC,CAAC;gBAEF,MAAM,WAAW,GAAG,IAAI,CAAC;gBACzB,IAAI,EAAE,CAAC;YACT,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;gBAC3C,IAAI,EAAE,CAAC,CAAC,+CAA+C;YACzD,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;CACF;AAED,mDAAmD;AACnD,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,cAAsB,GAAG,EAAE,WAAmB,KAAK,EAAE,EAAE;IAC1F,OAAO,IAAI,WAAW,CAAC;QACrB,QAAQ;QACR,WAAW;QACX,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,IAAI,EAAE;QAC9C,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAC3B,MAAM,CAAC,IAAI,CAAC,+BAA+B,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;QACtE,CAAC;KACF,EAAE,gBAAgB,CAAC,CAAC;AACvB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,cAAsB,IAAI,EAAE,WAAmB,OAAO,EAAE,EAAE;IAC9F,OAAO,IAAI,WAAW,CAAC;QACrB,QAAQ;QACR,WAAW;QACX,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE;YACpB,0CAA0C;YAC1C,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC;gBACzB,GAAG,CAAC,IAAI,EAAE,MAAM;gBAChB,GAAG,CAAC,KAAK,EAAE,MAAM;gBACjB,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,QAAQ,MAAM,EAAE,CAAC;QAC1B,CAAC;KACF,EAAE,iBAAiB,CAAC,CAAC;AACxB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,0BAA0B,GAAG,CAAC,cAAsB,EAAE,EAAE,WAAmB,KAAK,EAAE,EAAE;IAC/F,OAAO,IAAI,WAAW,CAAC;QACrB,QAAQ;QACR,WAAW;QACX,YAAY,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE;QACrC,sBAAsB,EAAE,IAAI,CAAC,uCAAuC;KACrE,EAAE,eAAe,CAAC,CAAC;AACtB,CAAC,CAAC;AAEF,iDAAiD;AACjD,MAAM,OAAO,cAAc;IAMf;IACA;IACA;IAPF,QAAQ,GAAG,CAAC,CAAC;IACb,eAAe,GAAG,CAAC,CAAC;IACpB,KAAK,GAAoC,QAAQ,CAAC;IAE1D,YACU,mBAA2B,CAAC,EAC5B,eAAuB,KAAK,EAC5B,gBAAwB,MAAM;QAF9B,qBAAgB,GAAhB,gBAAgB,CAAY;QAC5B,iBAAY,GAAZ,YAAY,CAAgB;QAC5B,kBAAa,GAAb,aAAa,CAAiB;IACrC,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAI,SAA2B;QAC1C,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;YAC1B,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC1D,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;gBACzB,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;YAC3D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YAEjC,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;gBAC/B,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3C,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,gCAAgC,IAAI,CAAC,QAAQ,WAAW,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAEO,KAAK;QACX,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;QACtB,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;IACvD,CAAC;IAED,QAAQ;QACN,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC;IACJ,CAAC;CACF;AAED,OAAO,EAAE,WAAW,EAAE,CAAC;AACvB,eAAe,WAAW,CAAC"}