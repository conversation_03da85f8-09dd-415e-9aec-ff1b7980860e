import { createClient } from 'redis';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Redis client
const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
const redisClient = createClient({ url: redisUrl });

// Cache key prefixes to clear
const CACHE_KEYS = [
  'network:trends',
  'network:trends:binance-smart-chain',
  'network:trends:solana',
  'network:trends:universal',
  'connect:coins'
];

async function clearCache() {
  console.log('Connecting to Redis...');
  await redisClient.connect();
  
  console.log('Connected to Redis. Clearing cache keys...');
  
  // Clear each key individually
  for (const key of CACHE_KEYS) {
    const result = await redisClient.del(key);
    console.log(`Cleared ${key}: ${result === 1 ? 'Success' : 'Key not found'}`);
  }
  
  // Also search for any keys with network:trends pattern
  const keys = await redisClient.keys('network:trends*');
  console.log(`Found additional network trend keys: ${keys.length}`);
  
  if (keys.length > 0) {
    for (const key of keys) {
      const result = await redisClient.del(key);
      console.log(`Cleared ${key}: ${result === 1 ? 'Success' : 'Failed'}`);
    }
  }
  
  console.log('Cache clearing completed');
  await redisClient.disconnect();
  process.exit(0);
}

// Run the function
clearCache().catch(error => {
  console.error('Error clearing cache:', error);
  process.exit(1);
});
