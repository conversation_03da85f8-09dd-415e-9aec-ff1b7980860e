import axios from 'axios';
import fs from 'fs';
import path from 'path';

// Base URL for the API
const BASE_URL = 'http://localhost:5001';
const NETWORKS = ['universal', 'binance-smart-chain', 'solana'];

// Function to test network highlights endpoint
async function testNetworkHighlights() {
  console.log('Testing network highlights endpoints with force refresh...\n');
  
  const results: Record<string, any> = {};
  
  for (const network of NETWORKS) {
    console.log(`Testing network: ${network}`);
    
    try {
      // Force refresh to bypass cache
      const response = await axios.get(`${BASE_URL}/api/home/<USER>
        params: {
          network,
          forceRefresh: 'true'
        },
        timeout: 60000 // Even longer timeout for API calls (60 seconds)
      });
      
      const data = response.data;
      
      // Extract essential information for logging
      const result = {
        status: response.status,
        source: data.source,
        trending: Array.isArray(data.data?.trending) ? data.data.trending.length : 0,
        gainers: Array.isArray(data.data?.gainers) ? data.data.gainers.length : 0,
        losers: Array.isArray(data.data?.losers) ? data.data.losers.length : 0,
        new: Array.isArray(data.data?.new) ? data.data.new.length : 0,
        isEmpty: isDataEmpty(data.data),
        firstItem: Array.isArray(data.data?.trending) && data.data.trending.length > 0 
          ? {
              name: data.data.trending[0].name,
              symbol: data.data.trending[0].symbol,
              network: data.data.trending[0].network
            } 
          : null
      };
      
      results[network] = result;
      
      console.log(`  Status: ${result.status}`);
      console.log(`  Source: ${result.source}`);
      console.log(`  Data counts: trending=${result.trending}, gainers=${result.gainers}, losers=${result.losers}, new=${result.new}`);
      console.log(`  Empty: ${result.isEmpty ? 'Yes' : 'No'}`);
      
      if (result.firstItem) {
        console.log(`  Sample: ${result.firstItem.name} (${result.firstItem.symbol}) on ${result.firstItem.network}`);
      }
      
      // Save raw data to file for inspection
      await saveResponseToFile(network, data);
      
    } catch (error: any) {
      console.error(`  Error testing ${network}:`, error.message);
      results[network] = { error: error.message };
    }
    
    console.log('');
  }
  
  console.log('Summary:');
  for (const network of NETWORKS) {
    const result = results[network];
    if (result.error) {
      console.log(`  ❌ ${network}: Error - ${result.error}`);
    } else if (result.isEmpty) {
      console.log(`  ❌ ${network}: Empty data`);
    } else {
      console.log(`  ✅ ${network}: ${result.trending}/${result.gainers}/${result.losers}/${result.new} items`);
    }
  }
}

// Helper to save response to file
async function saveResponseToFile(network: string, data: any) {
  const outputDir = path.join(__dirname, '../../temp');
  
  // Create temp directory if it doesn't exist
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  const filePath = path.join(outputDir, `${network}-highlights.json`);
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  console.log(`  Response saved to: ${filePath}`);
}

// Helper to check if data is empty
function isDataEmpty(data: any): boolean {
  if (!data) return true;
  
  const categories = ['trending', 'gainers', 'losers', 'new'];
  
  let totalItems = 0;
  for (const category of categories) {
    if (Array.isArray(data[category])) {
      totalItems += data[category].length;
    }
  }
  
  return totalItems === 0;
}

// Run the tests
testNetworkHighlights().catch(error => {
  console.error('Error running tests:', error);
  process.exit(1);
});
