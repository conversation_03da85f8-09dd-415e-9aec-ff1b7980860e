-- Supabase SQL schema for the application

-- Create enum types for limit orders
CREATE TYPE order_direction AS ENUM ('buy', 'sell');
CREATE TYPE order_status AS ENUM ('pending', 'executed', 'cancelled', 'expired');

-- Create limit_orders table
CREATE TABLE limit_orders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    token_address VARCHAR(255) NOT NULL,
    token_name VARCHAR(255) NOT NULL,
    token_symbol VARCHAR(50) NOT NULL,
    token_image TEXT, -- URL to token image/logo
    pool_address VARCHAR(255) NOT NULL,
    dex_type VARCHAR(50) NOT NULL,
    direction order_direction NOT NULL,
    amount DECIMAL(20, 8) NOT NULL,
    target_price DECIMAL(20, 8) NOT NULL,
    current_price DECIMAL(20, 8) NOT NULL,
    slippage DECIMAL(5, 4) NOT NULL DEFAULT 0.01,
    wallet_address VARCHAR(255) NOT NULL,
    wallet_id VARCHAR(255) NOT NULL,
    status order_status DEFAULT 'pending',
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    executed_at TIMESTAMP WITH TIME ZONE,
    execution_tx_hash VARCHAR(255),
    error_message TEXT,
    
    -- Constraints
    CONSTRAINT positive_amount CHECK (amount > 0),
    CONSTRAINT positive_target_price CHECK (target_price > 0),
    CONSTRAINT positive_current_price CHECK (current_price > 0),
    CONSTRAINT valid_slippage CHECK (slippage >= 0 AND slippage <= 1),
    CONSTRAINT valid_dex_type CHECK (dex_type IN ('pumpfun', 'pumpswap', 'launchlab')),
    CONSTRAINT execution_consistency CHECK (
        (status = 'executed' AND executed_at IS NOT NULL) OR 
        (status != 'executed' AND executed_at IS NULL)
    )
);

-- Create indexes for performance
CREATE INDEX idx_limit_orders_user_id ON limit_orders(user_id);
CREATE INDEX idx_limit_orders_status ON limit_orders(status);
CREATE INDEX idx_limit_orders_token_address ON limit_orders(token_address);
CREATE INDEX idx_limit_orders_wallet_address ON limit_orders(wallet_address);
CREATE INDEX idx_limit_orders_created_at ON limit_orders(created_at DESC);
CREATE INDEX idx_limit_orders_expires_at ON limit_orders(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_limit_orders_user_status ON limit_orders(user_id, status);
CREATE INDEX idx_limit_orders_token_status ON limit_orders(token_address, status);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_limit_orders_updated_at 
    BEFORE UPDATE ON limit_orders 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically expire orders
CREATE OR REPLACE FUNCTION expire_old_orders()
RETURNS void AS $$
BEGIN
    UPDATE limit_orders 
    SET status = 'expired', updated_at = NOW()
    WHERE status = 'pending' 
    AND expires_at IS NOT NULL 
    AND expires_at < NOW();
END;
$$ language 'plpgsql';

-- Create view for active orders (pending and not expired)
CREATE VIEW active_limit_orders AS
SELECT * FROM limit_orders
WHERE status = 'pending' 
AND (expires_at IS NULL OR expires_at > NOW());

-- Row Level Security (RLS) policies
ALTER TABLE limit_orders ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own orders
CREATE POLICY "Users can view own orders" ON limit_orders
    FOR SELECT USING (user_id = current_setting('app.current_user_id', true));

-- Policy: Users can only insert their own orders
CREATE POLICY "Users can insert own orders" ON limit_orders
    FOR INSERT WITH CHECK (user_id = current_setting('app.current_user_id', true));

-- Policy: Users can only update their own orders
CREATE POLICY "Users can update own orders" ON limit_orders
    FOR UPDATE USING (user_id = current_setting('app.current_user_id', true));

-- Policy: Users can only delete their own orders
CREATE POLICY "Users can delete own orders" ON limit_orders
    FOR DELETE USING (user_id = current_setting('app.current_user_id', true));
