# Limit Orders Database System

This directory contains the database schema and configuration for the limit orders system using Supabase.

## Overview

The limit orders system provides a comprehensive backend for storing and managing pending limit orders with the following features:

- **Secure Storage**: Uses Supabase with Row Level Security (RLS)
- **Real-time Updates**: Built-in real-time capabilities
- **Automatic Expiration**: Orders can be set to expire automatically
- **Comprehensive Validation**: Multiple layers of data validation
- **Performance Optimized**: Proper indexing and query optimization

## Database Schema

### Tables

#### `limit_orders`
Main table for storing limit orders with the following structure:

```sql
CREATE TABLE limit_orders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    token_address VARCHAR(255) NOT NULL,
    token_name VARCHAR(255) NOT NULL,
    token_symbol VARCHAR(50) NOT NULL,
    pool_address VARCHAR(255) NOT NULL,
    dex_type VARCHAR(50) NOT NULL,
    direction order_direction NOT NULL,
    amount DECIMAL(20, 8) NOT NULL,
    target_price DECIMAL(20, 8) NOT NULL,
    current_price DECIMAL(20, 8) NOT NULL,
    slippage DECIMAL(5, 4) NOT NULL DEFAULT 0.01,
    wallet_address VARCHAR(255) NOT NULL,
    wallet_id VARCHAR(255) NOT NULL,
    status order_status DEFAULT 'pending',
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    executed_at TIMESTAMP WITH TIME ZONE,
    execution_tx_hash VARCHAR(255),
    error_message TEXT
);
```

### Enums

#### `order_direction`
```sql
CREATE TYPE order_direction AS ENUM ('buy', 'sell');
```

#### `order_status`
```sql
CREATE TYPE order_status AS ENUM ('pending', 'executed', 'cancelled', 'expired');
```

### Views

#### `active_limit_orders`
View for active orders (pending and not expired):
```sql
CREATE VIEW active_limit_orders AS
SELECT * FROM limit_orders
WHERE status = 'pending' 
AND (expires_at IS NULL OR expires_at > NOW());
```

## Setup Instructions

### 1. Supabase Project Setup

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Get your project URL and service role key
3. Add them to your `.env` file:
   ```
   SUPABASE_URL=your_supabase_project_url
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ```

### 2. Database Schema Installation

1. Open the Supabase SQL Editor
2. Copy and paste the contents of `supabase.sql`
3. Execute the SQL to create all tables, indexes, and functions

### 3. Row Level Security (RLS)

The system uses RLS to ensure users can only access their own orders. The policies are automatically created by the SQL script.

## API Endpoints

### Create Limit Order
```http
POST /api/limit-orders
Content-Type: application/json

{
  "user_id": "user123",
  "token_address": "So11111111111111111111111111111111111111112",
  "token_name": "Solana",
  "token_symbol": "SOL",
  "pool_address": "pool_address_here",
  "dex_type": "pumpfun",
  "direction": "buy",
  "amount": 1.5,
  "target_price": 100.50,
  "current_price": 105.00,
  "slippage": 0.01,
  "wallet_address": "wallet_address_here",
  "wallet_id": "wallet_id_here",
  "expires_at": "2024-12-31T23:59:59Z"
}
```

### Get User's Orders
```http
GET /api/limit-orders?user_id=user123&status=pending&limit=20&offset=0
```

### Get Specific Order
```http
GET /api/limit-orders/order_id?user_id=user123
```

### Cancel Order
```http
DELETE /api/limit-orders/order_id?user_id=user123
```

### Update Order
```http
PUT /api/limit-orders/order_id
Content-Type: application/json

{
  "user_id": "user123",
  "status": "cancelled"
}
```

### Get Order Statistics
```http
GET /api/limit-orders/stats?user_id=user123
```

## Service Layer

### LimitOrderService

The `LimitOrderService` class provides all CRUD operations:

- `createLimitOrder(orderData)` - Create new order
- `getLimitOrders(userId, filters)` - Get orders with filtering
- `getLimitOrderById(userId, orderId)` - Get specific order
- `updateLimitOrder(userId, orderId, updateData)` - Update order
- `cancelLimitOrder(userId, orderId)` - Cancel order
- `deleteLimitOrder(userId, orderId)` - Delete order
- `getPendingOrders()` - Get all pending orders (for monitoring)
- `markOrderExecuted(orderId, txHash)` - Mark as executed
- `markOrderFailed(orderId, errorMessage)` - Mark as failed
- `expireOldOrders()` - Expire old orders

## Validation

### Data Validation

The system includes comprehensive validation:

- **Required Fields**: All mandatory fields are validated
- **Data Types**: Numeric and string type validation
- **Address Format**: Solana address format validation
- **Business Logic**: Price logic validation for buy/sell orders
- **Limits**: User order limits and rate limiting

### Balance Validation

Integration points for balance validation:
- SOL balance for buy orders
- Token balance for sell orders
- Reserve amount for transaction fees

## Performance Considerations

### Indexes

The following indexes are created for optimal performance:

- `idx_limit_orders_user_id` - User-based queries
- `idx_limit_orders_status` - Status filtering
- `idx_limit_orders_token_address` - Token-based queries
- `idx_limit_orders_created_at` - Time-based ordering
- `idx_limit_orders_user_status` - Combined user/status queries

### Query Optimization

- Use the `active_limit_orders` view for monitoring services
- Implement pagination for large result sets
- Use appropriate filters to reduce query scope

## Security

### Row Level Security (RLS)

- Users can only access their own orders
- Service role key bypasses RLS for system operations
- Automatic user context setting for all operations

### Data Protection

- Sensitive data is properly typed and validated
- SQL injection protection through parameterized queries
- Rate limiting on all endpoints

## Monitoring and Maintenance

### Automatic Cleanup

- `expire_old_orders()` function automatically expires old orders
- Should be called periodically (recommended: every 5 minutes)
- Automatic `updated_at` timestamp updates

### Health Checks

- Database connection testing on startup
- Service health monitoring endpoints
- Performance metrics collection

## Future Extensions

This system is designed to be extended with:

1. **Price Monitoring Service** - Monitor token prices and trigger orders
2. **Execution Service** - Execute orders when conditions are met
3. **Notification System** - Alert users when orders are executed
4. **Advanced Order Types** - Stop-loss, take-profit, trailing stops
5. **Order Book Integration** - Real-time order book updates

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check Supabase URL and service key
   - Verify network connectivity
   - Check Supabase project status

2. **RLS Policy Errors**
   - Ensure user context is set properly
   - Verify user_id matches the authenticated user
   - Check RLS policies are enabled

3. **Validation Errors**
   - Review validation error messages
   - Check data types and formats
   - Verify required fields are provided

### Debugging

Enable debug logging by setting `LOG_LEVEL=debug` in your environment variables.
