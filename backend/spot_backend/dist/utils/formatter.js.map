{"version": 3, "file": "formatter.js", "sourceRoot": "", "sources": ["../../src/utils/formatter.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH;;;;;;GAMG;AACH,MAAM,UAAU,mBAAmB,CAAC,KAAY,EAAE,KAAc;IAC9D,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;IAE5B,qCAAqC;IACrC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;YACrE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED,mCAAmC;IACnC,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAEnD,2BAA2B;IAC3B,IAAI,KAAK,IAAI,WAAW,CAAC,MAAM,GAAG,KAAK,EAAE,CAAC;QACxC,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACrC,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,YAAY,CAAC,KAAa,EAAE,WAAmB,CAAC;IAC9D,IAAI,KAAK,CAAC,KAAK,CAAC;QAAE,OAAO,GAAG,CAAC;IAE7B,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACjC,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,cAAc,CAAC,KAAa,EAAE,SAAiB,GAAG,EAAE,WAAmB,CAAC;IACtF,IAAI,KAAK,CAAC,KAAK,CAAC;QAAE,OAAO,GAAG,MAAM,GAAG,CAAC;IAEtC,2DAA2D;IAC3D,MAAM,SAAS,GAAG,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE;QAC9C,qBAAqB,EAAE,QAAQ;QAC/B,qBAAqB,EAAE,QAAQ;KAChC,CAAC,CAAC;IAEH,OAAO,GAAG,MAAM,GAAG,SAAS,EAAE,CAAC;AACjC,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,cAAc,CAAC,GAAW,EAAE,YAAoB,EAAE;IAChE,IAAI,CAAC,GAAG;QAAE,OAAO,EAAE,CAAC;IACpB,IAAI,GAAG,CAAC,MAAM,IAAI,SAAS;QAAE,OAAO,GAAG,CAAC;IAExC,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,KAAK,CAAC;AACzC,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,iBAAiB,CAAC,KAAa,EAAE,WAAmB,CAAC;IACnE,IAAI,KAAK,CAAC,KAAK,CAAC;QAAE,OAAO,GAAG,CAAC;IAE7B,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC;QACjB,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;SAAM,IAAI,KAAK,GAAG,OAAO,EAAE,CAAC;QAC3B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;IAChD,CAAC;SAAM,IAAI,KAAK,GAAG,UAAU,EAAE,CAAC;QAC9B,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;IACnD,CAAC;SAAM,IAAI,KAAK,GAAG,aAAa,EAAE,CAAC;QACjC,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;IACtD,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,KAAK,GAAG,aAAa,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;IACzD,CAAC;AACH,CAAC"}