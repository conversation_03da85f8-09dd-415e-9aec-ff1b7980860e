/**
 * Utility functions for formatting and manipulating data
 */
/**
 * Deduplicate an array of objects based on the symbol property
 * Useful for removing duplicate tokens when merging from multiple sources
 * @param items Array of objects with symbol property
 * @param limit Optional limit for the number of items to return
 * @returns Deduplicated array
 */
export function deduplicateBySymbol(items, limit) {
    const uniqueMap = new Map();
    // Use symbol property to deduplicate
    for (const item of items) {
        if (item && item.symbol && !uniqueMap.has(item.symbol.toLowerCase())) {
            uniqueMap.set(item.symbol.toLowerCase(), item);
        }
    }
    // Convert map values back to array
    const uniqueItems = Array.from(uniqueMap.values());
    // Apply limit if specified
    if (limit && uniqueItems.length > limit) {
        return uniqueItems.slice(0, limit);
    }
    return uniqueItems;
}
/**
 * Format a number to a specified number of decimal places
 * @param value Number to format
 * @param decimals Number of decimal places
 * @returns Formatted number as string
 */
export function formatNumber(value, decimals = 2) {
    if (isNaN(value))
        return '0';
    return value.toFixed(decimals);
}
/**
 * Format currency value with symbol and thousands separators
 * @param value Number to format
 * @param symbol Currency symbol (default $)
 * @param decimals Number of decimal places
 * @returns Formatted currency string
 */
export function formatCurrency(value, symbol = '$', decimals = 2) {
    if (isNaN(value))
        return `${symbol}0`;
    // Format with thousands separator and fixed decimal places
    const formatted = value.toLocaleString('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
    return `${symbol}${formatted}`;
}
/**
 * Truncate a string to the specified length and add ellipsis if truncated
 * @param str String to truncate
 * @param maxLength Maximum length
 * @returns Truncated string
 */
export function truncateString(str, maxLength = 20) {
    if (!str)
        return '';
    if (str.length <= maxLength)
        return str;
    return str.slice(0, maxLength) + '...';
}
/**
 * Format a large number with abbreviations (K, M, B, T)
 * @param value Number to format
 * @param decimals Number of decimal places
 * @returns Formatted number string with abbreviation
 */
export function formatLargeNumber(value, decimals = 2) {
    if (isNaN(value))
        return '0';
    if (value < 1000) {
        return value.toFixed(decimals);
    }
    else if (value < 1000000) {
        return (value / 1000).toFixed(decimals) + 'K';
    }
    else if (value < 1000000000) {
        return (value / 1000000).toFixed(decimals) + 'M';
    }
    else if (value < 1000000000000) {
        return (value / 1000000000).toFixed(decimals) + 'B';
    }
    else {
        return (value / 1000000000000).toFixed(decimals) + 'T';
    }
}
//# sourceMappingURL=formatter.js.map