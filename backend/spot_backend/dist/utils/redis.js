import { createClient } from 'redis';
import { logger } from './logger.js';
// Create Redis client
const redisClient = createClient({
    url: process.env.REDIS_URL || 'redis://localhost:6379'
});
// Handle Redis connection events
redisClient.on('connect', () => {
    logger.info('Redis connected');
});
redisClient.on('error', (err) => {
    logger.error('Redis connection error:', err);
});
// Initialize Redis connection
export const initRedis = async () => {
    try {
        await redisClient.connect();
    }
    catch (error) {
        logger.error('Failed to connect to Redis:', error);
    }
};
// Enhanced cache with metrics and compression
let cacheHits = 0;
let cacheMisses = 0;
let cacheErrors = 0;
export const getFromCache = async (key) => {
    try {
        const start = Date.now();
        const data = await redisClient.get(key);
        if (data) {
            cacheHits++;
            const parseTime = Date.now();
            const result = JSON.parse(data);
            // Log slow parse operations
            if (Date.now() - parseTime > 10) {
                logger.warn(`Slow JSON parse for key ${key}: ${Date.now() - parseTime}ms`);
            }
            return result;
        }
        else {
            cacheMisses++;
            return null;
        }
    }
    catch (error) {
        cacheErrors++;
        logger.error(`Error getting ${key} from Redis:`, error);
        return null;
    }
};
// Enhanced cache set with compression and metrics
export const setToCache = async (key, value, expiry) => {
    try {
        const start = Date.now();
        const stringValue = JSON.stringify(value);
        // Log large cache entries
        if (stringValue.length > 100000) { // 100KB
            logger.warn(`Large cache entry for key ${key}: ${(stringValue.length / 1024).toFixed(2)}KB`);
        }
        if (expiry) {
            await redisClient.setEx(key, expiry, stringValue);
        }
        else {
            await redisClient.set(key, stringValue);
        }
        // Log slow cache operations
        const duration = Date.now() - start;
        if (duration > 50) {
            logger.warn(`Slow cache set for key ${key}: ${duration}ms`);
        }
    }
    catch (error) {
        cacheErrors++;
        logger.error(`Error setting ${key} in Redis:`, error);
    }
};
// Remove a specific key from Redis
export const removeFromCache = async (key) => {
    try {
        await redisClient.del(key);
        logger.info(`Successfully removed key ${key} from Redis cache`);
    }
    catch (error) {
        logger.error(`Error removing ${key} from Redis:`, error);
    }
};
// Cache statistics and health monitoring
export const getCacheStats = () => ({
    hits: cacheHits,
    misses: cacheMisses,
    errors: cacheErrors,
    hitRate: cacheHits / (cacheHits + cacheMisses) || 0,
    totalOperations: cacheHits + cacheMisses + cacheErrors
});
// Cache warming utilities
export const warmCache = async (keys) => {
    const results = await Promise.allSettled(keys.map(async ({ key, fetcher, ttl }) => {
        try {
            const cached = await getFromCache(key);
            if (!cached) {
                const data = await fetcher();
                await setToCache(key, data, ttl);
                logger.info(`Cache warmed for key: ${key}`);
            }
        }
        catch (error) {
            logger.error(`Failed to warm cache for key ${key}:`, error);
        }
    }));
    const successful = results.filter(r => r.status === 'fulfilled').length;
    logger.info(`Cache warming completed: ${successful}/${keys.length} successful`);
};
// Batch operations for better performance
export const getBatchFromCache = async (keys) => {
    try {
        const pipeline = redisClient.multi();
        keys.forEach(key => pipeline.get(key));
        const results = await pipeline.exec();
        const batchResult = {};
        results?.forEach((result, index) => {
            const [error, data] = result;
            if (!error && data) {
                try {
                    batchResult[keys[index]] = JSON.parse(data);
                    cacheHits++;
                }
                catch (parseError) {
                    logger.error(`JSON parse error for key ${keys[index]}:`, parseError);
                    cacheErrors++;
                }
            }
            else {
                cacheMisses++;
            }
        });
        return batchResult;
    }
    catch (error) {
        logger.error('Batch cache get error:', error);
        cacheErrors++;
        return {};
    }
};
export default redisClient;
//# sourceMappingURL=redis.js.map