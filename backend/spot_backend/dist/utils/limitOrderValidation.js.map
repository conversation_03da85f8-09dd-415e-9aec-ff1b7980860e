{"version": 3, "file": "limitOrderValidation.js", "sourceRoot": "", "sources": ["../../src/utils/limitOrderValidation.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAYrC;;GAEG;AACH,MAAM,UAAU,sBAAsB,CAAC,SAA+B;IACpE,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;IAE9B,4BAA4B;IAC5B,MAAM,cAAc,GAAmC;QACrD,SAAS,EAAE,eAAe,EAAE,YAAY,EAAE,cAAc;QACxD,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ;QACjD,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,WAAW;KAC/D,CAAC;IAEF,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;QACnC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED,0BAA0B;IAC1B,IAAI,SAAS,CAAC,OAAO,IAAI,OAAO,SAAS,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;QAC/D,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;IAC1C,CAAC;IAED,IAAI,SAAS,CAAC,UAAU,IAAI,OAAO,SAAS,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;QACrE,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI,SAAS,CAAC,YAAY,IAAI,OAAO,SAAS,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;QACzE,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;IAC/C,CAAC;IAED,qBAAqB;IACrB,IAAI,SAAS,CAAC,aAAa,EAAE,CAAC;QAC5B,MAAM,iBAAiB,GAAG,qBAAqB,CAAC,SAAS,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;QAC1F,IAAI,iBAAiB;YAAE,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACxD,CAAC;IAED,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;QAC3B,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,SAAS,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QACvF,IAAI,gBAAgB;YAAE,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACtD,CAAC;IAED,IAAI,SAAS,CAAC,cAAc,EAAE,CAAC;QAC7B,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,SAAS,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;QAC7F,IAAI,kBAAkB;YAAE,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC1D,CAAC;IAED,qBAAqB;IACrB,IAAI,SAAS,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QACnC,IAAI,OAAO,SAAS,CAAC,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YACpE,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC/C,CAAC;aAAM,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC/C,CAAC;aAAM,IAAI,SAAS,CAAC,MAAM,GAAG,OAAO,EAAE,CAAC;YACtC,QAAQ,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,IAAI,SAAS,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;QACzC,IAAI,OAAO,SAAS,CAAC,YAAY,KAAK,QAAQ,IAAI,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC;YAChF,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACrD,CAAC;aAAM,IAAI,SAAS,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,IAAI,SAAS,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;QAC1C,IAAI,OAAO,SAAS,CAAC,aAAa,KAAK,QAAQ,IAAI,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC;YAClF,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;aAAM,IAAI,SAAS,CAAC,aAAa,IAAI,CAAC,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;QACrC,IAAI,OAAO,SAAS,CAAC,QAAQ,KAAK,QAAQ,IAAI,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxE,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC;aAAM,IAAI,SAAS,CAAC,QAAQ,GAAG,CAAC,IAAI,SAAS,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QAC/D,CAAC;aAAM,IAAI,SAAS,CAAC,QAAQ,GAAG,GAAG,EAAE,CAAC;YACpC,QAAQ,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED,kBAAkB;IAClB,IAAI,SAAS,CAAC,SAAS,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC;QAC1E,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;IAC1D,CAAC;IAED,IAAI,SAAS,CAAC,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC7F,MAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;IACvE,CAAC;IAED,4BAA4B;IAC5B,IAAI,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,aAAa,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;QAC7E,MAAM,eAAe,GAAG,kBAAkB,CACxC,SAAS,CAAC,SAAS,EACnB,SAAS,CAAC,YAAY,EACtB,SAAS,CAAC,aAAa,CACxB,CAAC;QACF,IAAI,eAAe,CAAC,KAAK,EAAE,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,eAAe,CAAC,OAAO,EAAE,CAAC;YAC5B,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED,wBAAwB;IACxB,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;QACzB,MAAM,eAAe,GAAG,kBAAkB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACjE,IAAI,eAAe;YAAE,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACpD,CAAC;IAED,OAAO;QACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;QAC5B,MAAM;QACN,QAAQ;KACT,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,qBAAqB,CAAC,OAAe,EAAE,SAAiB;IAC/D,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAChC,OAAO,GAAG,SAAS,mBAAmB,CAAC;IACzC,CAAC;IAED,kCAAkC;IAClC,MAAM,WAAW,GAAG,+BAA+B,CAAC;IACpD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;QAC/B,OAAO,GAAG,SAAS,2DAA2D,CAAC;IACjF,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CACzB,SAAyB,EACzB,WAAmB,EACnB,YAAoB;IAEpB,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC;IAE5E,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;QACxB,wFAAwF;QACxF,IAAI,WAAW,GAAG,YAAY,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,CAAC,CAAC,WAAW,GAAG,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,GAAG,CAAC;YACrE,IAAI,QAAQ,GAAG,EAAE,EAAE,CAAC;gBAClB,OAAO;oBACL,KAAK,EAAE,6BAA6B,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,oEAAoE;iBAC5H,CAAC;YACJ,CAAC;iBAAM,IAAI,QAAQ,GAAG,EAAE,EAAE,CAAC;gBACzB,OAAO;oBACL,OAAO,EAAE,6BAA6B,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,iEAAiE;iBAC3H,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;SAAM,CAAC;QACN,0FAA0F;QAC1F,IAAI,WAAW,GAAG,YAAY,EAAE,CAAC;YAC/B,MAAM,QAAQ,GAAG,CAAC,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,YAAY,CAAC,GAAG,GAAG,CAAC;YACrE,IAAI,QAAQ,GAAG,EAAE,EAAE,CAAC;gBAClB,OAAO;oBACL,KAAK,EAAE,8BAA8B,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,mEAAmE;iBAC5H,CAAC;YACJ,CAAC;iBAAM,IAAI,QAAQ,GAAG,EAAE,EAAE,CAAC;gBACzB,OAAO;oBACL,OAAO,EAAE,8BAA8B,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,gEAAgE;iBAC3H,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,sCAAsC;IACtC,IAAI,eAAe,GAAG,EAAE,EAAE,CAAC,CAAC,6BAA6B;QACvD,OAAO;YACL,KAAK,EAAE,mGAAmG;SAC3G,CAAC;IACJ,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CAAC,SAAiB;IAC3C,IAAI,CAAC;QACH,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,IAAI,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;YACpC,OAAO,0CAA0C,CAAC;QACpD,CAAC;QAED,IAAI,cAAc,IAAI,GAAG,EAAE,CAAC;YAC1B,OAAO,kCAAkC,CAAC;QAC5C,CAAC;QAED,kEAAkE;QAClE,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC3E,IAAI,cAAc,GAAG,cAAc,EAAE,CAAC;YACpC,OAAO,qDAAqD,CAAC;QAC/D,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,0CAA0C,CAAC;IACpD,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB,CACxC,SAAyB,EACzB,MAAc,EACd,YAAoB,EACpB,aAAqB;IAErB,qCAAqC;IACrC,kEAAkE;IAElE,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;IAE9B,mBAAmB;IACnB,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;IAC/C,CAAC;IAED,0CAA0C;IAC1C,wCAAwC;IACxC,MAAM,CAAC,IAAI,CAAC,sCAAsC,SAAS,aAAa,MAAM,SAAS,CAAC,CAAC;IAEzF,OAAO;QACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;QAC5B,MAAM;QACN,QAAQ;KACT,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CAAC,MAAc,EAAE,kBAA0B;IAC5E,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;IAE9B,MAAM,kBAAkB,GAAG,EAAE,CAAC,CAAC,kCAAkC;IACjE,MAAM,iBAAiB,GAAG,EAAE,CAAC,CAAC,oBAAoB;IAElD,IAAI,kBAAkB,IAAI,kBAAkB,EAAE,CAAC;QAC7C,MAAM,CAAC,IAAI,CAAC,qCAAqC,kBAAkB,gDAAgD,CAAC,CAAC;IACvH,CAAC;SAAM,IAAI,kBAAkB,IAAI,iBAAiB,EAAE,CAAC;QACnD,QAAQ,CAAC,IAAI,CAAC,YAAY,kBAAkB,0DAA0D,CAAC,CAAC;IAC1G,CAAC;IAED,OAAO;QACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;QAC5B,MAAM;QACN,QAAQ;KACT,CAAC;AACJ,CAAC"}