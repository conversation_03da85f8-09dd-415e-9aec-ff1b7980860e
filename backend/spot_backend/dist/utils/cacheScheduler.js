import cron from 'node-cron';
import { logger } from './logger.js';
import { refreshConnectCoinsCache, refreshNetworkHighlightsCache, refreshTokenRadarCache } from '../controllers/homeController.js';
import { performanceMonitorService } from '../services/performanceMonitorService.js';
/**
 * Schedule all cache refresh jobs with worker thread optimization
 * @param schedule Cron schedule expression (default: every 5 minutes)
 */
export function scheduleCacheRefresh(schedule = '*/5 * * * *') {
    logger.info(`Setting up scheduled cache refresh with schedule: ${schedule}`);
    // Schedule the refresh job
    cron.schedule(schedule, async () => {
        const startTime = Date.now();
        logger.info('Running scheduled refresh of home page data');
        try {
            // Execute cache refreshes in parallel (worker threads temporarily disabled)
            const refreshPromises = [
                // Connect coins cache (high priority - critical for UI)
                refreshConnectCoinsCache(),
                // Network highlights cache (medium priority)
                refreshNetworkHighlightsCache(),
                // Token radar cache (low priority - can be done in background)
                executeTokenRadarRefresh()
            ];
            // Wait for all cache refreshes to complete
            const results = await Promise.allSettled(refreshPromises);
            // Log results
            results.forEach((result, index) => {
                const cacheTypes = ['connect-coins', 'network-highlights', 'token-radar'];
                if (result.status === 'fulfilled') {
                    logger.info(`✅ ${cacheTypes[index]} cache refresh completed`);
                }
                else {
                    logger.error(`❌ ${cacheTypes[index]} cache refresh failed:`, result.reason);
                }
            });
            const totalTime = Date.now() - startTime;
            performanceMonitorService.recordMetric('cache_refresh_duration', totalTime, { type: 'scheduled' });
            logger.info(`Scheduled cache refresh completed in ${totalTime}ms`);
        }
        catch (error) {
            logger.error('Failed to execute scheduled cache refresh:', error);
            performanceMonitorService.recordMetric('cache_refresh_errors', 1, { type: 'scheduled' });
        }
    });
    logger.info('Cache refresh scheduler initialized with worker thread support');
}
/**
 * Execute cache refresh with fallback to main thread if worker fails
 * (Currently disabled - worker threads temporarily unavailable)
 */
// async function executeWithFallback(cacheType: string, fallbackFn: () => Promise<any>): Promise<any> {
//   try {
//     // Try to execute in worker thread first
//     const result = await workerThreadService.executeCacheRefresh(cacheType, {});
//     if (result.success) {
//       return result.data;
//     } else {
//       logger.warn(`Worker thread failed for ${cacheType}, falling back to main thread`);
//       return await fallbackFn();
//     }
//   } catch (error) {
//     logger.warn(`Worker thread error for ${cacheType}, falling back to main thread:`, error);
//     return await fallbackFn();
//   }
// }
/**
 * Execute token radar refresh in background
 */
async function executeTokenRadarRefresh() {
    try {
        const networks = ['universal', 'solana', 'binance-smart-chain'];
        const timeframes = ['1m', '5m', '1h', '4h', '24h'];
        // Execute in batches to avoid overwhelming the system
        const batches = [
            // Batch 1: Default radar
            [{ limit: 100, network: undefined, timeframe: '24h' }],
            // Batch 2: Network-specific radars
            networks.filter(n => n !== 'universal').map(network => ({
                limit: 100,
                network,
                timeframe: '24h'
            })),
            // Batch 3: Important timeframe combinations (background priority)
            ['solana', 'binance-smart-chain'].flatMap(network => timeframes.map(timeframe => ({
                limit: 50,
                network,
                timeframe
            }))),
            // Batch 4: Universal timeframes (lowest priority)
            timeframes.filter(t => t !== '24h').map(timeframe => ({
                limit: 50,
                network: undefined,
                timeframe
            }))
        ];
        // Execute batches sequentially to manage load
        for (const batch of batches) {
            const batchPromises = batch.map(async (config) => {
                try {
                    await refreshTokenRadarCache(config.limit, config.network, config.timeframe);
                }
                catch (error) {
                    logger.error(`Failed to refresh token radar cache for ${config.network || 'universal'}/${config.timeframe}:`, error);
                }
            });
            await Promise.allSettled(batchPromises);
        }
        logger.info('Token radar cache refresh completed for all networks and timeframes');
    }
    catch (error) {
        logger.error('Failed to perform token radar refresh:', error);
        throw error;
    }
}
//# sourceMappingURL=cacheScheduler.js.map