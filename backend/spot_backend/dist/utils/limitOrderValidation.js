import { logger } from './logger.js';
/**
 * Validate limit order data comprehensively
 */
export function validateLimitOrderData(orderData) {
    const errors = [];
    const warnings = [];
    // Required field validation
    const requiredFields = [
        'user_id', 'token_address', 'token_name', 'token_symbol',
        'pool_address', 'dex_type', 'direction', 'amount',
        'target_price', 'current_price', 'wallet_address', 'wallet_id'
    ];
    for (const field of requiredFields) {
        if (!orderData[field]) {
            errors.push(`Missing required field: ${field}`);
        }
    }
    // String field validation
    if (orderData.user_id && typeof orderData.user_id !== 'string') {
        errors.push('user_id must be a string');
    }
    if (orderData.token_name && typeof orderData.token_name !== 'string') {
        errors.push('token_name must be a string');
    }
    if (orderData.token_symbol && typeof orderData.token_symbol !== 'string') {
        errors.push('token_symbol must be a string');
    }
    // Address validation
    if (orderData.token_address) {
        const tokenAddressError = validateSolanaAddress(orderData.token_address, 'token_address');
        if (tokenAddressError)
            errors.push(tokenAddressError);
    }
    if (orderData.pool_address) {
        const poolAddressError = validateSolanaAddress(orderData.pool_address, 'pool_address');
        if (poolAddressError)
            errors.push(poolAddressError);
    }
    if (orderData.wallet_address) {
        const walletAddressError = validateSolanaAddress(orderData.wallet_address, 'wallet_address');
        if (walletAddressError)
            errors.push(walletAddressError);
    }
    // Numeric validation
    if (orderData.amount !== undefined) {
        if (typeof orderData.amount !== 'number' || isNaN(orderData.amount)) {
            errors.push('amount must be a valid number');
        }
        else if (orderData.amount <= 0) {
            errors.push('amount must be greater than 0');
        }
        else if (orderData.amount > 1000000) {
            warnings.push('amount is very large, please verify');
        }
    }
    if (orderData.target_price !== undefined) {
        if (typeof orderData.target_price !== 'number' || isNaN(orderData.target_price)) {
            errors.push('target_price must be a valid number');
        }
        else if (orderData.target_price <= 0) {
            errors.push('target_price must be greater than 0');
        }
    }
    if (orderData.current_price !== undefined) {
        if (typeof orderData.current_price !== 'number' || isNaN(orderData.current_price)) {
            errors.push('current_price must be a valid number');
        }
        else if (orderData.current_price <= 0) {
            errors.push('current_price must be greater than 0');
        }
    }
    if (orderData.slippage !== undefined) {
        if (typeof orderData.slippage !== 'number' || isNaN(orderData.slippage)) {
            errors.push('slippage must be a valid number');
        }
        else if (orderData.slippage < 0 || orderData.slippage > 1) {
            errors.push('slippage must be between 0 and 1 (0% to 100%)');
        }
        else if (orderData.slippage > 0.5) {
            warnings.push('slippage is very high (>50%), please verify');
        }
    }
    // Enum validation
    if (orderData.direction && !['buy', 'sell'].includes(orderData.direction)) {
        errors.push('direction must be either "buy" or "sell"');
    }
    if (orderData.dex_type && !['pumpfun', 'pumpswap', 'launchlab'].includes(orderData.dex_type)) {
        errors.push('dex_type must be one of: pumpfun, pumpswap, launchlab');
    }
    // Business logic validation
    if (orderData.target_price && orderData.current_price && orderData.direction) {
        const priceValidation = validatePriceLogic(orderData.direction, orderData.target_price, orderData.current_price);
        if (priceValidation.error) {
            errors.push(priceValidation.error);
        }
        if (priceValidation.warning) {
            warnings.push(priceValidation.warning);
        }
    }
    // Expiration validation
    if (orderData.expires_at) {
        const expirationError = validateExpiration(orderData.expires_at);
        if (expirationError)
            errors.push(expirationError);
    }
    return {
        isValid: errors.length === 0,
        errors,
        warnings
    };
}
/**
 * Validate Solana address format
 */
function validateSolanaAddress(address, fieldName) {
    if (typeof address !== 'string') {
        return `${fieldName} must be a string`;
    }
    // Basic Solana address validation
    const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
    if (!base58Regex.test(address)) {
        return `${fieldName} must be a valid Solana address (32-44 base58 characters)`;
    }
    return null;
}
/**
 * Validate price logic for buy/sell orders
 */
function validatePriceLogic(direction, targetPrice, currentPrice) {
    const priceDifference = Math.abs(targetPrice - currentPrice) / currentPrice;
    if (direction === 'buy') {
        // Buy orders should typically have target price <= current price (buy when price drops)
        if (targetPrice > currentPrice) {
            const increase = ((targetPrice - currentPrice) / currentPrice) * 100;
            if (increase > 50) {
                return {
                    error: `Buy order target price is ${increase.toFixed(1)}% higher than current price. This seems unusual for a limit order.`
                };
            }
            else if (increase > 10) {
                return {
                    warning: `Buy order target price is ${increase.toFixed(1)}% higher than current price. Please verify this is intentional.`
                };
            }
        }
    }
    else {
        // Sell orders should typically have target price >= current price (sell when price rises)
        if (targetPrice < currentPrice) {
            const decrease = ((currentPrice - targetPrice) / currentPrice) * 100;
            if (decrease > 50) {
                return {
                    error: `Sell order target price is ${decrease.toFixed(1)}% lower than current price. This seems unusual for a limit order.`
                };
            }
            else if (decrease > 10) {
                return {
                    warning: `Sell order target price is ${decrease.toFixed(1)}% lower than current price. Please verify this is intentional.`
                };
            }
        }
    }
    // Check for extreme price differences
    if (priceDifference > 10) { // More than 1000% difference
        return {
            error: `Target price differs from current price by more than 1000%. Please verify the prices are correct.`
        };
    }
    return {};
}
/**
 * Validate expiration timestamp
 */
function validateExpiration(expiresAt) {
    try {
        const expirationDate = new Date(expiresAt);
        const now = new Date();
        if (isNaN(expirationDate.getTime())) {
            return 'expires_at must be a valid ISO timestamp';
        }
        if (expirationDate <= now) {
            return 'expires_at must be in the future';
        }
        // Check if expiration is too far in the future (more than 1 year)
        const oneYearFromNow = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
        if (expirationDate > oneYearFromNow) {
            return 'expires_at cannot be more than 1 year in the future';
        }
        return null;
    }
    catch (error) {
        return 'expires_at must be a valid ISO timestamp';
    }
}
/**
 * Validate order amount against user balance (to be implemented with balance service)
 */
export async function validateOrderBalance(direction, amount, tokenAddress, walletAddress) {
    // Placeholder for balance validation
    // This would integrate with the existing balance checking service
    const errors = [];
    const warnings = [];
    // Basic validation
    if (amount <= 0) {
        errors.push('Amount must be greater than 0');
    }
    // TODO: Implement actual balance checking
    // For now, just return basic validation
    logger.info(`Balance validation placeholder for ${direction} order of ${amount} tokens`);
    return {
        isValid: errors.length === 0,
        errors,
        warnings
    };
}
/**
 * Validate order limits per user (rate limiting)
 */
export function validateOrderLimits(userId, existingOrderCount) {
    const errors = [];
    const warnings = [];
    const MAX_PENDING_ORDERS = 50; // Maximum pending orders per user
    const WARNING_THRESHOLD = 30; // Warning threshold
    if (existingOrderCount >= MAX_PENDING_ORDERS) {
        errors.push(`Maximum number of pending orders (${MAX_PENDING_ORDERS}) reached. Please cancel some existing orders.`);
    }
    else if (existingOrderCount >= WARNING_THRESHOLD) {
        warnings.push(`You have ${existingOrderCount} pending orders. Consider managing your existing orders.`);
    }
    return {
        isValid: errors.length === 0,
        errors,
        warnings
    };
}
//# sourceMappingURL=limitOrderValidation.js.map