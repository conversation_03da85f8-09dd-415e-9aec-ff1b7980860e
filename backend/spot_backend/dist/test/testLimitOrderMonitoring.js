import { limitOrderMonitoringService } from '../services/limitOrderMonitoringService.js';
import { logger } from '../utils/logger.js';
/**
 * Test script for Limit Order Monitoring Service
 * Tests WebSocket connection, fallback URL switching, and API key integration
 */
async function testLimitOrderMonitoring() {
    logger.info('🧪 Starting Limit Order Monitoring Service Test...');
    try {
        // Test 1: Check initial configuration
        logger.info('📋 Test 1: Checking initial configuration...');
        const status = limitOrderMonitoringService.getStatus();
        logger.info('Initial status:', {
            connected: status.connected,
            connectionState: status.connectionState,
            config: status.config
        });
        // Verify API key is loaded
        if (!status.config.hasApiKey) {
            logger.error('❌ MOBULA_API_KEY not found in environment variables');
            return;
        }
        logger.info('✅ API key is configured');
        // Verify WebSocket URLs are configured
        logger.info('WebSocket URLs:', {
            primary: status.config.primaryWsUrl,
            fallback: status.config.fallbackWsUrl,
            current: status.config.currentWsUrl
        });
        // Test 2: Initialize the service
        logger.info('📋 Test 2: Initializing monitoring service...');
        await limitOrderMonitoringService.initialize();
        // Wait a few seconds for connection
        await new Promise(resolve => setTimeout(resolve, 5000));
        // Check connection status
        const statusAfterInit = limitOrderMonitoringService.getStatus();
        logger.info('Status after initialization:', {
            connected: statusAfterInit.connected,
            connectionState: statusAfterInit.connectionState,
            activeSubscriptions: statusAfterInit.activeSubscriptions,
            totalOrders: statusAfterInit.totalOrders
        });
        // Test 3: Check monitoring stats
        logger.info('📋 Test 3: Checking monitoring statistics...');
        const stats = limitOrderMonitoringService.getStats();
        logger.info('Monitoring stats:', stats);
        // Test 4: Test manual order refresh
        logger.info('📋 Test 4: Testing manual order refresh...');
        await limitOrderMonitoringService.refreshOrders();
        // Final status check
        const finalStatus = limitOrderMonitoringService.getStatus();
        logger.info('Final status:', {
            connected: finalStatus.connected,
            connectionState: finalStatus.connectionState,
            activeSubscriptions: finalStatus.activeSubscriptions,
            totalOrders: finalStatus.totalOrders
        });
        logger.info('✅ Limit Order Monitoring Service test completed successfully');
    }
    catch (error) {
        logger.error('❌ Test failed:', error);
    }
}
// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testLimitOrderMonitoring()
        .then(() => {
        logger.info('🎯 Test execution completed');
        process.exit(0);
    })
        .catch((error) => {
        logger.error('💥 Test execution failed:', error);
        process.exit(1);
    });
}
export { testLimitOrderMonitoring };
//# sourceMappingURL=testLimitOrderMonitoring.js.map