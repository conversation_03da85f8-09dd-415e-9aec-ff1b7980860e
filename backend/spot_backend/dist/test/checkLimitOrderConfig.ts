import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Simple configuration check for Limit Order Monitoring
 */
function checkLimitOrderConfig() {
  console.log('🔍 Checking Limit Order Monitoring Configuration...\n');

  // Check API Key
  const apiKey = process.env.MOBULA_API_KEY;
  console.log('📋 API Key Configuration:');
  console.log(`  MOBULA_API_KEY: ${apiKey ? '✅ Set (' + apiKey.substring(0, 8) + '...)' : '❌ Not set'}`);

  // Check WebSocket URLs
  const primaryUrl = process.env.MOBULA_MARKET_WSS_PRIMARY || 'wss://api.mobula.io';
  const fallbackUrl = process.env.MOBULA_MARKET_WSS_FALLBACK || 'wss://api-prod.mobula.io';
  
  console.log('\n📋 WebSocket URL Configuration:');
  console.log(`  Primary URL: ${primaryUrl}`);
  console.log(`  Fallback URL: ${fallbackUrl}`);
  console.log(`  Primary URL set: ${process.env.MOBULA_MARKET_WSS_PRIMARY ? '✅ Yes' : '⚠️ Using default'}`);
  console.log(`  Fallback URL set: ${process.env.MOBULA_MARKET_WSS_FALLBACK ? '✅ Yes' : '⚠️ Using default'}`);

  // Check other relevant environment variables
  console.log('\n📋 Other Configuration:');
  console.log(`  SUPABASE_URL: ${process.env.SUPABASE_URL ? '✅ Set' : '❌ Not set'}`);
  console.log(`  SUPABASE_SERVICE_ROLE_KEY: ${process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ Set' : '❌ Not set'}`);

  // Test WebSocket URL format
  console.log('\n📋 URL Validation:');
  try {
    new URL(primaryUrl);
    console.log(`  Primary URL format: ✅ Valid`);
  } catch (error) {
    console.log(`  Primary URL format: ❌ Invalid - ${error.message}`);
  }

  try {
    new URL(fallbackUrl);
    console.log(`  Fallback URL format: ✅ Valid`);
  } catch (error) {
    console.log(`  Fallback URL format: ❌ Invalid - ${error.message}`);
  }

  // Summary
  console.log('\n📊 Configuration Summary:');
  const hasApiKey = !!apiKey;
  const hasSupabase = !!(process.env.SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY);
  const hasValidUrls = primaryUrl.startsWith('wss://') && fallbackUrl.startsWith('wss://');

  console.log(`  API Key: ${hasApiKey ? '✅' : '❌'}`);
  console.log(`  Supabase: ${hasSupabase ? '✅' : '❌'}`);
  console.log(`  WebSocket URLs: ${hasValidUrls ? '✅' : '❌'}`);

  const allGood = hasApiKey && hasSupabase && hasValidUrls;
  console.log(`\n🎯 Overall Status: ${allGood ? '✅ Ready for limit order monitoring' : '❌ Configuration issues detected'}`);

  if (!allGood) {
    console.log('\n🔧 Recommendations:');
    if (!hasApiKey) {
      console.log('  - Set MOBULA_API_KEY in your .env file');
    }
    if (!hasSupabase) {
      console.log('  - Set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env file');
    }
    if (!hasValidUrls) {
      console.log('  - Ensure WebSocket URLs start with wss://');
    }
  }

  return allGood;
}

// Run the check
const isConfigured = checkLimitOrderConfig();
process.exit(isConfigured ? 0 : 1);
