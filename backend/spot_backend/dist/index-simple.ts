import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createServer } from 'http';

// Load environment variables
dotenv.config();

// Import routes
import homeRoutes from './routes/homeRoutes.js';
import walletRoutes from './routes/walletRoutes.js';
import activityRoutes from './routes/activityRoutes.js';
import limitOrderRoutes from './routes/limitOrderRoutes.js';
import { errorHandler, notFound } from './middleware/errorHandler.js';
// import { initRedis } from './utils/redis.js';
import { logger } from './utils/logger.js';

// Initialize express app
const app = express();

// Middleware
app.use(cors({
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to the Spot Trading API' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    service: 'spot-backend',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Basic performance monitoring endpoint
app.get('/api/performance', (req, res) => {
  try {
    res.json({
      status: 'success',
      timestamp: Date.now(),
      data: {
        memory: {
          heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          external: Math.round(process.memoryUsage().external / 1024 / 1024),
          rss: Math.round(process.memoryUsage().rss / 1024 / 1024)
        },
        uptime: Math.round(process.uptime())
      }
    });
  } catch (error) {
    logger.error('Performance endpoint error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve performance metrics'
    });
  }
});

// WebSocket health check endpoint
app.get('/api/websocket/health', (req, res) => {
  res.status(503).json({
    ready: false,
    status: 'disabled',
    message: 'WebSocket service temporarily disabled',
    timestamp: Date.now()
  });
});

// Limit Order Monitoring health check endpoint
app.get('/api/limit-orders/monitoring/health', async (req, res) => {
  try {
    res.status(503).json({
      connected: false,
      activeSubscriptions: 0,
      totalOrders: 0,
      lastUpdate: 0,
      connectionState: 'disabled',
      config: { wsUrl: '', hasApiKey: false, maxReconnectAttempts: 0 },
      stats: { executedOrders: 0, failedExecutions: 0 },
      message: 'Monitoring service temporarily disabled',
      timestamp: Date.now()
    });
  } catch (error: any) {
    res.status(500).json({
      error: 'Failed to get Limit Order Monitoring status',
      message: error.message,
      timestamp: Date.now()
    });
  }
});

// Limit Order Monitoring subscriptions endpoint (for debugging)
app.get('/api/limit-orders/monitoring/subscriptions', async (req, res) => {
  try {
    res.json({
      success: false,
      data: [],
      count: 0,
      message: 'Monitoring service temporarily disabled',
      timestamp: Date.now()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: 'Failed to get monitoring subscriptions',
      message: error.message,
      timestamp: Date.now()
    });
  }
});

// Manual refresh endpoint (for testing/debugging)
app.post('/api/limit-orders/monitoring/refresh', async (req, res) => {
  try {
    res.json({
      success: false,
      message: 'Monitoring service temporarily disabled',
      timestamp: Date.now()
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: 'Failed to refresh orders',
      message: error.message,
      timestamp: Date.now()
    });
  }
});

// API Routes
app.use('/api/home', homeRoutes);
app.use('/api/wallet', walletRoutes);
app.use('/api/activity', activityRoutes);
app.use('/api/limit-orders', limitOrderRoutes);

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Create HTTP server
const httpServer = createServer(app);

// Start the server
const PORT = process.env.PORT || 5001;
const server = httpServer.listen(Number(PORT), '0.0.0.0', async () => {
  console.log(`🚀 Server running on port ${PORT}`);
  logger.info(`Server started on port ${PORT}`);

  try {
    // Skip Redis initialization for now
    console.log('🔧 Redis initialization skipped');
    console.log('✅ Server initialization completed');

  } catch (error) {
    logger.error('Error during server initialization:', error);
    console.error('❌ Server initialization failed:', error);
  }
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  console.log('🛑 SIGTERM received, shutting down gracefully');

  server.close(() => {
    logger.info('Process terminated');
    console.log('✅ Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  console.log('🛑 SIGINT received, shutting down gracefully');

  server.close(() => {
    logger.info('Process terminated');
    console.log('✅ Process terminated');
    process.exit(0);
  });
});

export default app;
