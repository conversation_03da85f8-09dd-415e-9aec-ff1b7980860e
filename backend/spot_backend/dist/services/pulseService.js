import axios from 'axios';
import { logger } from '../utils/logger.js';
import { mobulaWebSocketService } from './mobulaWebSocketService.js';
export async function fetchPulseData(apiKey, blockchain) {
    // 1. Try WebSocket cache first
    const cachedData = mobulaWebSocketService.getCachedData();
    if (cachedData && mobulaWebSocketService.hasFreshData()) {
        logger.info('Returning fresh pulse data from WebSocket cache');
        return cachedData;
    }
    logger.warn('No fresh WebSocket data available - falling back to REST API');
    // 2. Fallback to REST API if WebSocket data is stale or unavailable
    try {
        const apiData = await fetchPulseDataFromAPI(apiKey, blockchain);
        logger.info('Successfully fetched pulse data from REST API fallback');
        return apiData;
    }
    catch (fallbackError) {
        logger.error(`Fallback to REST API failed: ${fallbackError.message}`);
        return {
            new: [],
            bonding: [],
            bonded: []
        };
    }
}
/**
 * Fetch pulse data from REST API (fallback method)
 */
export async function fetchPulseDataFromAPI(apiKey, blockchain) {
    const url = 'https://api.mobula.io/api/1/pulse';
    const headers = {
        accept: 'application/json'
    };
    if (apiKey && apiKey.trim() !== '') {
        headers.Authorization = `Bearer ${apiKey}`;
    }
    else {
        logger.warn('No API key provided for Mobula Api Pulse data request');
    }
    logger.info(`Using Mobula Pulse Data REST API for ${blockchain || 'all blockchains'}`);
    const params = {
        blockchains: blockchain,
        factories: 'pumpfun',
    };
    if (blockchain && blockchain.trim() !== '') {
        params.blockchain = blockchain;
    }
    try {
        logger.info('Fetching pulse data from Mobula Pulse REST API for Solana PumpFun');
        const response = await axios.get(url, {
            params,
            headers,
        });
        logger.info(`Mobula Pulse REST API response status: ${response.status}`);
        const rawData = response.data?.data?.pumpfun?.['solana:solana'];
        if (!rawData) {
            logger.warn('No data returned from Mobula Pulse REST API for solana:pumpfun');
            return {
                new: [],
                bonding: [],
                bonded: []
            };
        }
        return processPulseData(rawData, 'solana');
    }
    catch (error) {
        logger.error(`Error fetching pulse data from REST API: ${error.message}`);
        // No fallback in WebSocket-only mode
        logger.error('REST API failed and no WebSocket fallback available');
        return {
            new: [],
            bonding: [],
            bonded: []
        };
    }
}
function mapPulseToken(item, network) {
    const baseTokenKey = item.pair.baseToken;
    const baseToken = item.pair[baseTokenKey];
    return {
        id: baseToken.address,
        name: baseToken.name,
        symbol: baseToken.symbol,
        image: baseToken.logo || '',
        holders_count: item.holders_count || '',
        baseimage: baseToken.logo || '',
        network,
        current_price: item.price || 0,
        price_change_percentage_24h: item.price_change_24h || 0,
        market_cap: baseToken.marketCap || 0,
        total_volume: item.pair.volume24h || 0,
        exchange_name: item.pair.exchange?.name || '',
        pool_address: item.pair.address || '',
        exchange_logo: item.pair.exchange?.logo || '',
        supply: baseToken.totalSupply,
        bonding: item.bondingPercentage || 0,
        volume_24h: item.volume_24h || 0,
        createdAt: item.created_at || null,
        trades_24h: item.trades_24h || 0,
        liquidity: item.pair.liquidity || 0
    };
}
export function processPulseData(rawData, network) {
    const categories = ['new', 'bonding', 'bonded'];
    const result = {
        new: [],
        bonding: [],
        bonded: []
    };
    for (const category of categories) {
        const tokens = rawData[category] || [];
        logger.info(`[${network}] Processing ${tokens.length} tokens in category: ${category}`);
        result[category] = tokens.map((item) => mapPulseToken(item, network));
    }
    logger.info(`[${network}] Final token counts — New: ${result.new.length}, Bonding: ${result.bonding.length}, Bonded: ${result.bonded.length}`);
    return result;
}
//# sourceMappingURL=pulseService.js.map