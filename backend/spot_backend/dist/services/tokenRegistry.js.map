{"version": 3, "file": "tokenRegistry.js", "sourceRoot": "", "sources": ["../../src/services/tokenRegistry.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAE5C;;;GAGG;AACH,MAAM,aAAa;IACT,MAAM,CAAC,QAAQ,CAAgB;IAC/B,UAAU,GAAqB,IAAI,GAAG,EAAE,CAAC;IACzC,WAAW,GAAW,CAAC,CAAC;IACxB,eAAe,GAAW,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,SAAS;IAE3D,gBAAuB,CAAC;IAEjB,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC5B,aAAa,CAAC,QAAQ,GAAG,IAAI,aAAa,EAAE,CAAC;QAC/C,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,gBAAgB,CAAC,OAAe,EAAE,YAAoB;QACjE,2CAA2C;QAC3C,IAAI,YAAY,KAAK,QAAQ,EAAE,CAAC;YAC9B,QAAQ,OAAO,EAAE,CAAC;gBAChB,KAAK,UAAU;oBACb,OAAO;wBACL,IAAI,EAAE,UAAU;wBAChB,MAAM,EAAE,KAAK;wBACb,KAAK,EAAE,kEAAkE;qBAC1E,CAAC;gBACJ,KAAK,KAAK;oBACR,OAAO;wBACL,IAAI,EAAE,cAAc;wBACpB,MAAM,EAAE,KAAK;wBACb,KAAK,EAAE,sEAAsE;qBAC9E,CAAC;gBACJ,KAAK,QAAQ;oBACX,OAAO;wBACL,IAAI,EAAE,QAAQ;wBACd,MAAM,EAAE,KAAK;wBACb,KAAK,EAAE,iEAAiE;qBACzE,CAAC;gBACJ;oBACE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,MAAM,QAAQ,GAAG,GAAG,OAAO,IAAI,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC;QAE5D,oBAAoB;QACpB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC;QAED,qCAAqC;QACrC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAE5E,mBAAmB;QACnB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAExC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CAAC,OAAe,EAAE,YAAoB;QAC1E,0BAA0B;QAC1B,IAAI,QAAQ,GAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;QACxD,MAAM,iBAAiB,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;QAErD,IAAI,CAAC;YACH,8BAA8B;YAC9B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;YAChF,IAAI,aAAa,EAAE,CAAC;gBAClB,QAAQ,GAAG,EAAE,GAAG,QAAQ,EAAE,GAAG,aAAa,EAAE,CAAC;gBAC7C,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,KAAK;oBAAE,OAAO,QAAQ,CAAC;YAC1E,CAAC;YAED,yCAAyC;YACzC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;YAC9E,IAAI,YAAY,EAAE,CAAC;gBACjB,QAAQ,GAAG;oBACT,GAAG,QAAQ;oBACX,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,YAAY,CAAC,IAAI;oBACxC,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM;oBAC9C,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK;iBAC5C,CAAC;gBACF,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,MAAM;oBAAE,OAAO,QAAQ,CAAC;YACxD,CAAC;YAED,iDAAiD;YACjD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;YAC/E,IAAI,WAAW,EAAE,CAAC;gBAChB,QAAQ,GAAG;oBACT,GAAG,QAAQ;oBACX,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI;oBACvC,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM;iBAC9C,CAAC;YACJ,CAAC;YAED,yBAAyB;YACzB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;YACxE,IAAI,OAAO,EAAE,CAAC;gBACZ,QAAQ,GAAG;oBACT,GAAG,QAAQ;oBACX,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI;oBACnC,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM;oBACzC,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK;iBACvC,CAAC;YACJ,CAAC;YAED,sDAAsD;YACtD,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACvC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAC9D,IAAI,QAAQ,EAAE,CAAC;oBACb,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACN,mDAAmD;oBACnD,QAAQ,CAAC,KAAK,GAAG,8EAA8E,CAAC;gBAClG,CAAC;YACH,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,qCAAqC,OAAO,IAAI,iBAAiB,KAAK,KAAK,EAAE,CAAC,CAAC;YAC5F,OAAO,QAAQ,CAAC;QAClB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,OAAe,EAAE,OAAe;QAC/D,IAAI,CAAC;YACH,IAAI,QAAQ,CAAC;YACb,QAAQ,OAAO,EAAE,CAAC;gBAChB,KAAK,UAAU;oBAAE,QAAQ,GAAG,UAAU,CAAC;oBAAC,MAAM;gBAC9C,KAAK,KAAK;oBAAE,QAAQ,GAAG,qBAAqB,CAAC;oBAAC,MAAM;gBACpD,KAAK,QAAQ;oBAAE,QAAQ,GAAG,QAAQ,CAAC;oBAAC,MAAM;gBAC1C,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC;YACvB,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAC9B,0CAA0C,QAAQ,aAAa,OAAO,EAAE,CACzE,CAAC;YAEF,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAClB,OAAO;oBACL,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;oBACxB,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE;oBAC3C,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK;iBAChE,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,yCAAyC;QAC3C,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,OAAe,EAAE,OAAe;QAC9D,IAAI,CAAC;YACH,IAAI,GAAG,GAAG,EAAE,CAAC;YACb,IAAI,MAAM,GAAG,EAAE,CAAC;YAEhB,QAAQ,OAAO,EAAE,CAAC;gBAChB,KAAK,UAAU;oBACb,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE,CAAC;oBAC7C,IAAI,CAAC,MAAM;wBAAE,OAAO,IAAI,CAAC;oBACzB,GAAG,GAAG,8EAA8E,OAAO,WAAW,MAAM,EAAE,CAAC;oBAC/G,MAAM;gBACR,KAAK,KAAK;oBACR,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,EAAE,CAAC;oBAC3C,IAAI,CAAC,MAAM;wBAAE,OAAO,IAAI,CAAC;oBACzB,GAAG,GAAG,6EAA6E,OAAO,WAAW,MAAM,EAAE,CAAC;oBAC9G,MAAM;gBACR;oBACE,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACtC,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC1E,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;oBAChD,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBACzB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBAEzB,OAAO;oBACL,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,KAAK,EAAE,EAAE,CAAE,2CAA2C;iBACvD,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,yCAAyC;QAC3C,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,OAAe,EAAE,OAAe;QAChE,IAAI,CAAC;YACH,IAAI,QAAQ,CAAC;YACb,QAAQ,OAAO,EAAE,CAAC;gBAChB,KAAK,UAAU;oBACb,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,uEAAuE,CAAC,CAAC;oBAC5J,MAAM;gBACR,KAAK,KAAK;oBACR,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,oCAAoC,CAAC,CAAC;oBACjH,MAAM;gBACR;oBACE,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,MAAM,QAAQ,GAAG;gBACf,uCAAuC;gBACvC,yCAAyC;aAC1C,CAAC;YAEF,MAAM,aAAa,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACvE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACvC,aAAa,CAAC,IAAI,EAAE;gBACpB,aAAa,CAAC,MAAM,EAAE;aACvB,CAAC,CAAC;YAEH,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,yCAAyC;QAC3C,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,OAAe,EAAE,OAAe;QAC7D,IAAI,CAAC;YACH,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;gBACtB,+BAA+B;gBAC/B,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;gBACjG,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;oBAC1C,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CACrC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,WAAW,EAAE,CAC9D,CAAC;oBAEF,IAAI,KAAK,EAAE,CAAC;wBACV,OAAO;4BACL,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,MAAM,EAAE,KAAK,CAAC,MAAM;4BACpB,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE;yBAC3B,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;iBAAM,IAAI,OAAO,KAAK,UAAU,EAAE,CAAC;gBAClC,2BAA2B;gBAC3B,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;gBAChE,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;oBAC1C,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CACrC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,WAAW,EAAE,CAC9D,CAAC;oBAEF,IAAI,KAAK,EAAE,CAAC;wBACV,OAAO;4BACL,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,MAAM,EAAE,KAAK,CAAC,MAAM;4BACpB,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE;yBAC3B,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,yCAAyC;QAC3C,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,MAAc;QAC3C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,iDAAiD,MAAM,EAAE,CAAC,CAAC;YAE5F,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3E,qCAAqC;gBACrC,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAC3C,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,WAAW,EAAE,CAC5D,CAAC;gBAEF,IAAI,YAAY,EAAE,CAAC;oBACjB,OAAO,YAAY,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK,IAAI,IAAI,CAAC;gBAC1D,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gBAAgB;QAClB,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED,eAAe,aAAa,CAAC,WAAW,EAAE,CAAC"}