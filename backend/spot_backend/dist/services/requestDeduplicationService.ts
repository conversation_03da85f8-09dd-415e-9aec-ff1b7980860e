import { logger } from '../utils/logger.js';
import crypto from 'crypto';

interface PendingRequest {
  promise: Promise<any>;
  timestamp: number;
  requestCount: number;
}

interface RequestStats {
  totalRequests: number;
  deduplicatedRequests: number;
  cacheHits: number;
  averageResponseTime: number;
}

class RequestDeduplicationService {
  private pendingRequests = new Map<string, PendingRequest>();
  private requestCache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private stats: RequestStats = {
    totalRequests: 0,
    deduplicatedRequests: 0,
    cacheHits: 0,
    averageResponseTime: 0
  };
  private responseTimes: number[] = [];
  private readonly maxCacheSize = 1000;
  private readonly defaultTtl = 30000; // 30 seconds
  private readonly cleanupInterval = 60000; // 1 minute

  constructor() {
    // Periodic cleanup of expired requests and cache
    setInterval(() => this.cleanup(), this.cleanupInterval);
  }

  /**
   * Generate a unique key for the request
   */
  private generateKey(endpoint: string, params: any): string {
    const paramString = JSON.stringify(params, Object.keys(params).sort());
    return crypto.createHash('md5').update(`${endpoint}:${paramString}`).digest('hex');
  }

  /**
   * Execute request with deduplication
   */
  async executeRequest<T>(
    endpoint: string,
    params: any,
    fetcher: () => Promise<T>,
    options: { ttl?: number; skipCache?: boolean } = {}
  ): Promise<T> {
    const startTime = Date.now();
    this.stats.totalRequests++;

    const key = this.generateKey(endpoint, params);
    const { ttl = this.defaultTtl, skipCache = false } = options;

    // Check cache first (if not skipping cache)
    if (!skipCache) {
      const cached = this.getCachedResult<T>(key);
      if (cached) {
        this.stats.cacheHits++;
        this.recordResponseTime(Date.now() - startTime);
        logger.debug(`Cache hit for request: ${endpoint}`);
        return cached;
      }
    }

    // Check if request is already pending
    const pending = this.pendingRequests.get(key);
    if (pending) {
      this.stats.deduplicatedRequests++;
      pending.requestCount++;
      logger.debug(`Deduplicating request: ${endpoint} (${pending.requestCount} total)`);
      
      try {
        const result = await pending.promise;
        this.recordResponseTime(Date.now() - startTime);
        return result;
      } catch (error) {
        // If the pending request failed, remove it so retry is possible
        this.pendingRequests.delete(key);
        throw error;
      }
    }

    // Execute new request
    const promise = this.executeWithTimeout(fetcher, endpoint);
    
    this.pendingRequests.set(key, {
      promise,
      timestamp: Date.now(),
      requestCount: 1
    });

    try {
      const result = await promise;
      
      // Cache successful result
      if (!skipCache) {
        this.setCachedResult(key, result, ttl);
      }
      
      this.recordResponseTime(Date.now() - startTime);
      logger.debug(`Request completed: ${endpoint} in ${Date.now() - startTime}ms`);
      
      return result;
    } catch (error) {
      logger.error(`Request failed: ${endpoint}`, error);
      throw error;
    } finally {
      // Always remove from pending requests
      this.pendingRequests.delete(key);
    }
  }

  /**
   * Execute request with timeout
   */
  private async executeWithTimeout<T>(
    fetcher: () => Promise<T>,
    endpoint: string,
    timeout: number = 30000
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Request timeout for ${endpoint} after ${timeout}ms`));
      }, timeout);

      fetcher()
        .then(result => {
          clearTimeout(timeoutId);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeoutId);
          reject(error);
        });
    });
  }

  /**
   * Get cached result if valid
   */
  private getCachedResult<T>(key: string): T | null {
    const cached = this.requestCache.get(key);
    if (!cached) return null;

    const now = Date.now();
    if (now - cached.timestamp > cached.ttl) {
      this.requestCache.delete(key);
      return null;
    }

    return cached.data;
  }

  /**
   * Set cached result
   */
  private setCachedResult(key: string, data: any, ttl: number): void {
    // Implement LRU eviction if cache is full
    if (this.requestCache.size >= this.maxCacheSize) {
      const oldestKey = this.requestCache.keys().next().value;
      if (oldestKey) {
        this.requestCache.delete(oldestKey);
      }
    }

    this.requestCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * Record response time for statistics
   */
  private recordResponseTime(time: number): void {
    this.responseTimes.push(time);
    
    // Keep only last 1000 response times
    if (this.responseTimes.length > 1000) {
      this.responseTimes = this.responseTimes.slice(-1000);
    }
    
    // Update average
    this.stats.averageResponseTime = 
      this.responseTimes.reduce((sum, t) => sum + t, 0) / this.responseTimes.length;
  }

  /**
   * Cleanup expired requests and cache entries
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredThreshold = 300000; // 5 minutes

    // Clean up pending requests that are too old
    for (const [key, request] of this.pendingRequests.entries()) {
      if (now - request.timestamp > expiredThreshold) {
        this.pendingRequests.delete(key);
        logger.warn(`Cleaned up expired pending request: ${key}`);
      }
    }

    // Clean up expired cache entries
    for (const [key, cached] of this.requestCache.entries()) {
      if (now - cached.timestamp > cached.ttl) {
        this.requestCache.delete(key);
      }
    }

    logger.debug(`Cleanup completed. Pending: ${this.pendingRequests.size}, Cached: ${this.requestCache.size}`);
  }

  /**
   * Get service statistics
   */
  getStats(): RequestStats & {
    pendingRequests: number;
    cachedEntries: number;
    deduplicationRate: number;
    cacheHitRate: number;
  } {
    return {
      ...this.stats,
      pendingRequests: this.pendingRequests.size,
      cachedEntries: this.requestCache.size,
      deduplicationRate: this.stats.deduplicatedRequests / this.stats.totalRequests || 0,
      cacheHitRate: this.stats.cacheHits / this.stats.totalRequests || 0
    };
  }

  /**
   * Clear all caches and pending requests
   */
  clear(): void {
    this.pendingRequests.clear();
    this.requestCache.clear();
    this.stats = {
      totalRequests: 0,
      deduplicatedRequests: 0,
      cacheHits: 0,
      averageResponseTime: 0
    };
    this.responseTimes = [];
    logger.info('Request deduplication service cleared');
  }

  /**
   * Invalidate cache entries by pattern
   */
  invalidateCache(pattern: string): number {
    let invalidated = 0;
    const regex = new RegExp(pattern);
    
    for (const [key] of this.requestCache.entries()) {
      if (regex.test(key)) {
        this.requestCache.delete(key);
        invalidated++;
      }
    }
    
    logger.info(`Invalidated ${invalidated} cache entries matching pattern: ${pattern}`);
    return invalidated;
  }
}

// Export singleton instance
export const requestDeduplicationService = new RequestDeduplicationService();
export default requestDeduplicationService;
