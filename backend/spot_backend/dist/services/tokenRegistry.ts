import axios from 'axios';
import { ethers } from 'ethers';
import { logger } from '../utils/logger.js';

/**
 * TokenRegistry - A service to retrieve token metadata from various sources
 * This eliminates the need for hardcoded token metadata in the codebase
 */
class TokenRegistry {
  private static instance: TokenRegistry;
  private tokenCache: Map<string, any> = new Map();
  private lastRefresh: number = 0;
  private refreshInterval: number = 1000 * 60 * 60; // 1 hour

  private constructor() {}

  public static getInstance(): TokenRegistry {
    if (!TokenRegistry.instance) {
      TokenRegistry.instance = new TokenRegistry();
    }
    return TokenRegistry.instance;
  }

  /**
   * Get token metadata from multiple sources
   * @param chainId - Chain identifier (ethereum, bsc, solana)
   * @param tokenAddress - Token contract address
   */
  public async getTokenMetadata(chainId: string, tokenAddress: string): Promise<any> {
    // For native tokens, return hardcoded data
    if (tokenAddress === 'native') {
      switch (chainId) {
        case 'ethereum':
          return {
            name: 'Ethereum',
            symbol: 'ETH',
            image: 'https://assets.coingecko.com/coins/images/279/small/ethereum.png'
          };
        case 'bsc':
          return {
            name: 'Binance Coin',
            symbol: 'BNB',
            image: 'https://assets.coingecko.com/coins/images/825/small/bnb-icon2_2x.png'
          };
        case 'solana':
          return {
            name: 'Solana',
            symbol: 'SOL',
            image: 'https://assets.coingecko.com/coins/images/4128/small/solana.png'
          };
        default:
          return { name: '', symbol: '', image: '' };
      }
    }

    // Create cache key
    const cacheKey = `${chainId}:${tokenAddress.toLowerCase()}`;

    // Check cache first
    if (this.tokenCache.has(cacheKey)) {
      return this.tokenCache.get(cacheKey);
    }

    // Try multiple data sources in order
    const metadata = await this.fetchFromMultipleSources(chainId, tokenAddress);
    
    // Cache the result
    this.tokenCache.set(cacheKey, metadata);
    
    return metadata;
  }

  /**
   * Try multiple sources to get token metadata
   */
  private async fetchFromMultipleSources(chainId: string, tokenAddress: string): Promise<any> {
    // Default metadata object
    let metadata: any = { name: '', symbol: '', image: '' };
    const normalizedAddress = tokenAddress.toLowerCase();

    try {
      // Source 1: Try CoinGecko API
      const coinGeckoData = await this.fetchFromCoinGecko(chainId, normalizedAddress);
      if (coinGeckoData) {
        metadata = { ...metadata, ...coinGeckoData };
        if (metadata.name && metadata.symbol && metadata.image) return metadata;
      }

      // Source 2: Try chain-specific explorers
      const explorerData = await this.fetchFromExplorer(chainId, normalizedAddress);
      if (explorerData) {
        metadata = { 
          ...metadata, 
          name: metadata.name || explorerData.name,
          symbol: metadata.symbol || explorerData.symbol,
          image: metadata.image || explorerData.image 
        };
        if (metadata.name && metadata.symbol) return metadata;
      }

      // Source 3: Try to read from blockchain directly
      const onChainData = await this.fetchFromBlockchain(chainId, normalizedAddress);
      if (onChainData) {
        metadata = { 
          ...metadata, 
          name: metadata.name || onChainData.name,
          symbol: metadata.symbol || onChainData.symbol 
        };
      }

      // Source 4: Try DEX APIs
      const dexData = await this.fetchFromDexApis(chainId, normalizedAddress);
      if (dexData) {
        metadata = {
          ...metadata,
          name: metadata.name || dexData.name,
          symbol: metadata.symbol || dexData.symbol,
          image: metadata.image || dexData.image
        };
      }

      // If we still don't have an image, try generic search
      if (!metadata.image && metadata.symbol) {
        const imageUrl = await this.searchTokenImage(metadata.symbol);
        if (imageUrl) {
          metadata.image = imageUrl;
        } else {
          // Use a generic image if nothing else is available
          metadata.image = 'https://assets.coingecko.com/coins/images/12632/small/generic-token-icon.png';
        }
      }

      return metadata;
    } catch (error) {
      logger.error(`Error fetching token metadata for ${chainId}:${normalizedAddress}: ${error}`);
      return metadata;
    }
  }

  /**
   * Get token data from CoinGecko
   */
  private async fetchFromCoinGecko(chainId: string, address: string): Promise<any | null> {
    try {
      let platform;
      switch (chainId) {
        case 'ethereum': platform = 'ethereum'; break;
        case 'bsc': platform = 'binance-smart-chain'; break;
        case 'solana': platform = 'solana'; break;
        default: return null;
      }

      const response = await axios.get(
        `https://api.coingecko.com/api/v3/coins/${platform}/contract/${address}`
      );

      if (response.data) {
        return {
          name: response.data.name,
          symbol: response.data.symbol?.toUpperCase(),
          image: response.data.image?.small || response.data.image?.thumb
        };
      }
    } catch (error) {
      // Silently fail, we'll try other sources
    }
    return null;
  }

  /**
   * Get token data from blockchain explorer APIs
   */
  private async fetchFromExplorer(chainId: string, address: string): Promise<any | null> {
    try {
      let url = '';
      let apiKey = '';

      switch (chainId) {
        case 'ethereum':
          apiKey = process.env.ETHERSCAN_API_KEY || '';
          if (!apiKey) return null;
          url = `https://api.etherscan.io/api?module=token&action=tokeninfo&contractaddress=${address}&apikey=${apiKey}`;
          break;
        case 'bsc':
          apiKey = process.env.BSCSCAN_API_KEY || '';
          if (!apiKey) return null;
          url = `https://api.bscscan.com/api?module=token&action=tokeninfo&contractaddress=${address}&apikey=${apiKey}`;
          break;
        default:
          return null;
      }

      const response = await axios.get(url);
      if (response.data && response.data.status === '1' && response.data.result) {
        const result = Array.isArray(response.data.result) 
          ? response.data.result[0] 
          : response.data.result;
          
        return {
          name: result.name,
          symbol: result.symbol,
          image: ''  // Explorers typically don't provide images
        };
      }
    } catch (error) {
      // Silently fail, we'll try other sources
    }
    return null;
  }

  /**
   * Get token data directly from blockchain
   */
  private async fetchFromBlockchain(chainId: string, address: string): Promise<any | null> {
    try {
      let provider;
      switch (chainId) {
        case 'ethereum':
          provider = new ethers.providers.JsonRpcProvider(process.env.ALCHEMY_API_KEY_ETH || 'https://eth-mainnet.g.alchemy.com/v2/sQLdRXYdXOU8D27i-pXh2SgQGBFJsNX6');
          break;
        case 'bsc':
          provider = new ethers.providers.JsonRpcProvider(process.env.BSC_RPC_URL || 'https://bsc-dataseed1.binance.org/');
          break;
        default:
          return null;
      }

      const tokenABI = [
        "function name() view returns (string)",
        "function symbol() view returns (string)"
      ];

      const tokenContract = new ethers.Contract(address, tokenABI, provider);
      const [name, symbol] = await Promise.all([
        tokenContract.name(),
        tokenContract.symbol()
      ]);

      return { name, symbol, image: '' };
    } catch (error) {
      // Silently fail, we'll try other sources
    }
    return null;
  }

  /**
   * Get token data from DEX APIs like PancakeSwap, Uniswap, etc.
   */
  private async fetchFromDexApis(chainId: string, address: string): Promise<any | null> {
    try {
      if (chainId === 'bsc') {
        // Try PancakeSwap's token list
        const response = await axios.get('https://tokens.pancakeswap.finance/pancakeswap-extended.json');
        if (response.data && response.data.tokens) {
          const token = response.data.tokens.find(
            (t: any) => t.address.toLowerCase() === address.toLowerCase()
          );
          
          if (token) {
            return {
              name: token.name,
              symbol: token.symbol,
              image: token.logoURI || ''
            };
          }
        }
      } else if (chainId === 'ethereum') {
        // Try Uniswap's token list
        const response = await axios.get('https://tokens.uniswap.org/');
        if (response.data && response.data.tokens) {
          const token = response.data.tokens.find(
            (t: any) => t.address.toLowerCase() === address.toLowerCase()
          );
          
          if (token) {
            return {
              name: token.name,
              symbol: token.symbol,
              image: token.logoURI || ''
            };
          }
        }
      }
    } catch (error) {
      // Silently fail, we'll try other sources
    }
    return null;
  }

  /**
   * Search for token image using the symbol
   */
  private async searchTokenImage(symbol: string): Promise<string | null> {
    try {
      const response = await axios.get(`https://api.coingecko.com/api/v3/search?query=${symbol}`);
      
      if (response.data && response.data.coins && response.data.coins.length > 0) {
        // Find the coin with matching symbol
        const matchingCoin = response.data.coins.find(
          (c: any) => c.symbol.toLowerCase() === symbol.toLowerCase()
        );
        
        if (matchingCoin) {
          return matchingCoin.large || matchingCoin.thumb || null;
        }
      }
    } catch (error) {
      // Silently fail
    }
    return null;
  }
}

export default TokenRegistry.getInstance(); 