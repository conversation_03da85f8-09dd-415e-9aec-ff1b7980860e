import axios from 'axios';
import { logger } from '../utils/logger.js';
/**
 * Fetches trading pair list from DexScreener API and processes it.
 * @param activeTokenAddress The address of the primary token.
 * @param selectedTokenAddress The address of the token to find a pair with.
 * @returns PairListServiceResponse object.
 */
export const fetchPairListFromDexScreener = async (activeTokenAddress, selectedTokenAddress) => {
    try {
        logger.info(`[DexService] Fetching pairs for token: ${activeTokenAddress}`);
        let pairs = [];
        let reversePairs = [];
        let availableTokens = [];
        let reverseAvailableTokens = [];
        let matchedPair = undefined;
        // Call DexScreener API to get pairs for activeToken
        try {
            const response = await axios.get(`https://api.dexscreener.com/latest/dex/tokens/${activeTokenAddress}`);
            if (response.data?.pairs && Array.isArray(response.data.pairs)) {
                pairs = response.data.pairs;
                logger.info(`[DexService] Found ${pairs.length} pairs for ${activeTokenAddress}`);
                // Extract available token symbols from the pairs
                availableTokens = Array.from(new Set(pairs.map(pair => {
                    const activeAddrLower = activeTokenAddress.toLowerCase();
                    if (pair.baseToken.address.toLowerCase() === activeAddrLower) {
                        return pair.quoteToken.symbol;
                    }
                    else if (pair.quoteToken.address.toLowerCase() === activeAddrLower) {
                        return pair.baseToken.symbol;
                    }
                    return null; // Should not happen if DexScreener returns correctly
                }).filter((symbol) => symbol !== null)));
                // Find a pair that matches the selected token
                const normalizedSelectedAddress = selectedTokenAddress.toLowerCase();
                matchedPair = pairs.find(pair => {
                    const baseTokenAddressLower = pair.baseToken.address.toLowerCase();
                    const quoteTokenAddressLower = pair.quoteToken.address.toLowerCase();
                    return (baseTokenAddressLower === normalizedSelectedAddress ||
                        quoteTokenAddressLower === normalizedSelectedAddress);
                });
            }
            else {
                logger.warn(`[DexService] No pairs found in DexScreener response for ${activeTokenAddress}`);
            }
        }
        catch (dexError) {
            logger.error(`[DexService] Error fetching pairs for ${activeTokenAddress} from DexScreener: ${dexError.message}`);
            // Don't throw here, let the reverse lookup proceed if needed
        }
        // If no direct match, try reverse lookup
        if (!matchedPair) {
            logger.info(`[DexService] No direct match found for ${activeTokenAddress} and ${selectedTokenAddress}. Trying reverse lookup...`);
            try {
                const reverseResponse = await axios.get(`https://api.dexscreener.com/latest/dex/tokens/${selectedTokenAddress}`);
                if (reverseResponse.data?.pairs && Array.isArray(reverseResponse.data.pairs)) {
                    reversePairs = reverseResponse.data.pairs;
                    logger.info(`[DexService] Found ${reversePairs.length} pairs for ${selectedTokenAddress} in reverse lookup`);
                    // Extract available token symbols from reverse pairs
                    reverseAvailableTokens = Array.from(new Set(reversePairs.map(pair => {
                        const selectedAddrLower = selectedTokenAddress.toLowerCase();
                        if (pair.baseToken.address.toLowerCase() === selectedAddrLower) {
                            return pair.quoteToken.symbol;
                        }
                        else if (pair.quoteToken.address.toLowerCase() === selectedAddrLower) {
                            return pair.baseToken.symbol;
                        }
                        return null;
                    }).filter((symbol) => symbol !== null)));
                    // Find a pair that matches the active token in the reverse list
                    const normalizedActiveAddress = activeTokenAddress.toLowerCase();
                    // Only assign to matchedPair if it wasn't found in the first lookup
                    if (!matchedPair) {
                        matchedPair = reversePairs.find(pair => {
                            const baseTokenAddressLower = pair.baseToken.address.toLowerCase();
                            const quoteTokenAddressLower = pair.quoteToken.address.toLowerCase();
                            return (baseTokenAddressLower === normalizedActiveAddress ||
                                quoteTokenAddressLower === normalizedActiveAddress);
                        });
                    }
                }
                else {
                    logger.warn(`[DexService] No pairs found in reverse DexScreener response for ${selectedTokenAddress}`);
                }
            }
            catch (dexError) {
                logger.error(`[DexService] Error fetching reverse pairs for ${selectedTokenAddress} from DexScreener: ${dexError.message}`);
                // Proceed without reverse pairs if fetch fails, don't throw
            }
        }
        const allFoundPairs = [...pairs, ...reversePairs];
        const combinedAvailableTokens = Array.from(new Set([...availableTokens, ...reverseAvailableTokens]));
        if (matchedPair) {
            logger.info(`[DexService] Found matching pair: ${matchedPair.baseToken.symbol}/${matchedPair.quoteToken.symbol} on ${matchedPair.dexId}`);
            return {
                success: true,
                pairs: allFoundPairs,
                matchedPair: matchedPair,
                availableTokens: combinedAvailableTokens
            };
        }
        else {
            logger.info(`[DexService] No matching pair found in either direction for ${activeTokenAddress} and ${selectedTokenAddress}`);
            return {
                success: false,
                message: `No trading pair available for the given tokens`,
                pairs: allFoundPairs,
                availableTokens: combinedAvailableTokens
            };
        }
    }
    catch (error) {
        logger.error("[DexService] Unexpected error in fetchPairListFromDexScreener:", error);
        // Throw an error to be caught by the controller for a 500 response
        throw new Error('Internal server error while fetching pair list from DexScreener');
    }
};
//# sourceMappingURL=dexService.js.map