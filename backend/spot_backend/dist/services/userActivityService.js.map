{"version": 3, "file": "userActivityService.js", "sourceRoot": "", "sources": ["../../src/services/userActivityService.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,sBAAsB,EAAE,MAAM,6BAA6B,CAAC;AASrE,MAAM,mBAAmB;IACf,QAAQ,GAAG,IAAI,GAAG,EAAuB,CAAC;IACjC,eAAe,GAAG,MAAM,CAAC,CAAC,YAAY;IAC/C,eAAe,CAAiB;IAExC;QACE,0CAA0C;QAC1C,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACtC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACjC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEV,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,MAAc,EAAE,SAAkB;QAC7D,MAAM,eAAe,GAAG,SAAS,IAAI,GAAG,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAC/D,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,oDAAoD;QACpD,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAC/D,IAAI,oBAAoB,IAAI,oBAAoB,KAAK,eAAe,EAAE,CAAC;YACrE,MAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,qCAAqC,oBAAoB,6BAA6B,CAAC,CAAC;YAClH,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAChE,IAAI,eAAe,EAAE,CAAC;gBACpB,eAAe,CAAC,YAAY,GAAG,GAAG,CAAC;gBACnC,OAAO,CAAC,iCAAiC;YAC3C,CAAC;QACH,CAAC;QAED,MAAM,OAAO,GAAgB;YAC3B,MAAM;YACN,SAAS,EAAE,eAAe;YAC1B,YAAY,EAAE,GAAG;YACjB,aAAa,EAAE,IAAI;SACpB,CAAC;QAEF,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC3D,MAAM,cAAc,GAAG,eAAe,EAAE,aAAa,IAAI,KAAK,CAAC;QAE/D,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;QAE5C,uEAAuE;QACvE,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,oCAAoC,eAAe,EAAE,CAAC,CAAC;YACjF,sBAAsB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC9C,CAAC;QAED,MAAM,CAAC,KAAK,CAAC,mCAAmC,MAAM,aAAa,eAAe,EAAE,CAAC,CAAC;IACxF,CAAC;IAED;;OAEG;IACI,uBAAuB,CAAC,MAAc,EAAE,SAAkB;QAC/D,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC7C,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBACrC,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;gBAC9B,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAClC,MAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,kBAAkB,CAAC,CAAC;gBAC9C,sBAAsB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,+EAA+E;YAC/E,IAAI,UAAU,GAAG,KAAK,CAAC;YACvB,KAAK,MAAM,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;gBAClD,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;oBACvD,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;oBAC9B,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAClC,UAAU,GAAG,IAAI,CAAC;gBACpB,CAAC;YACH,CAAC;YACD,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,iCAAiC,CAAC,CAAC;gBAC7D,sBAAsB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,MAAc,EAAE,SAAkB;QACtD,MAAM,eAAe,GAAG,SAAS,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAElE,IAAI,eAAe,EAAE,CAAC;YACpB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YACnD,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAClC,MAAM,CAAC,KAAK,CAAC,6BAA6B,MAAM,aAAa,eAAe,EAAE,CAAC,CAAC;YAClF,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C,IAAI,OAAO,CAAC,aAAa,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBACjF,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC1C,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;QAExC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBACxD,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAC,IAAI,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,gBAAgB;QAMrB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;QAExC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C,MAAM,QAAQ,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;YAErE,IAAI,QAAQ,EAAE,CAAC;gBACb,cAAc,EAAE,CAAC;gBACjB,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAElC,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;oBAC1B,cAAc,EAAE,CAAC;gBACnB,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YACjC,cAAc;YACd,cAAc;YACd,gBAAgB,EAAE,aAAa,CAAC,IAAI;SACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,MAAc;QACzC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3D,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM;gBACzB,OAAO,CAAC,aAAa;gBACrB,CAAC,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBACxD,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,MAAc;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3D,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrF,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,gBAAgB,GAAa,EAAE,CAAC;QAEtC,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3D,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBACxD,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAEjC,uDAAuD;gBACvD,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;oBAC1B,sBAAsB,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACxD,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,eAAe,gBAAgB,CAAC,MAAM,oBAAoB,CAAC,CAAC;YACxE,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACnC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,MAAM,QAAQ,GAAG,GAAG,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACnD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtC,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC/B,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAClC,CAAC;CACF;AAED,4BAA4B;AAC5B,MAAM,CAAC,MAAM,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC"}