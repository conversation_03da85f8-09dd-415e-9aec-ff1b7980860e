import { Worker } from 'worker_threads';
import { logger } from '../utils/logger.js';
import path from 'path';
import { fileURLToPath } from 'url';
import os from 'os';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
class WorkerThreadService {
    workers = new Map();
    taskQueue = [];
    activeWorkers = new Set();
    maxWorkers;
    workerIdCounter = 0;
    workerHandlers = new Map(); // Track event handlers for cleanup
    isProcessingQueue = false; // Prevent recursive queue processing
    constructor(maxWorkers = 4) {
        this.maxWorkers = Math.min(maxWorkers, os.cpus().length);
        logger.info(`🧵 WorkerThreadService initialized with ${this.maxWorkers} max workers`);
    }
    /**
     * Initialize worker pool
     */
    async initialize() {
        try {
            // Pre-create worker pool for better performance
            for (let i = 0; i < Math.min(2, this.maxWorkers); i++) {
                await this.createWorker();
            }
            logger.info(`✅ Worker pool initialized with ${this.workers.size} workers`);
        }
        catch (error) {
            logger.error('❌ Failed to initialize worker pool:', error);
            throw error;
        }
    }
    /**
     * Create a new worker thread
     */
    async createWorker() {
        return new Promise((resolve, reject) => {
            const workerId = `worker_${++this.workerIdCounter}`;
            // Use physical worker file instead of eval for security and stability
            const workerPath = path.join(__dirname, 'workers', 'taskWorker.js');
            try {
                const worker = new Worker(workerPath, {
                    workerData: { workerId }
                });
                // Initialize handler tracking for this worker
                this.workerHandlers.set(workerId, new Map());
                // Set up event handlers with proper tracking
                const messageHandler = (result) => {
                    this.handleWorkerResult(workerId, result);
                };
                const errorHandler = (error) => {
                    logger.error(`❌ Worker ${workerId} error:`, error);
                    this.removeWorker(workerId);
                    reject(error);
                };
                const exitHandler = (code) => {
                    if (code !== 0) {
                        logger.warn(`⚠️ Worker ${workerId} exited with code ${code}`);
                    }
                    this.removeWorker(workerId);
                };
                // Track handlers for proper cleanup
                const handlers = this.workerHandlers.get(workerId);
                handlers.set('message', messageHandler);
                handlers.set('error', errorHandler);
                handlers.set('exit', exitHandler);
                // Attach event listeners
                worker.on('message', messageHandler);
                worker.on('error', errorHandler);
                worker.on('exit', exitHandler);
                this.workers.set(workerId, worker);
                logger.debug(`🧵 Created worker: ${workerId}`);
                resolve(workerId);
            }
            catch (error) {
                logger.error(`❌ Failed to create worker ${workerId}:`, error);
                reject(error);
            }
        });
    }
    /**
     * Execute task in worker thread
     */
    async executeTask(task) {
        return new Promise(async (resolve, reject) => {
            let timeout = null;
            let resultHandler = null;
            let assignedWorkerId = null;
            const cleanup = () => {
                if (timeout) {
                    clearTimeout(timeout);
                    timeout = null;
                }
                if (resultHandler && assignedWorkerId) {
                    const worker = this.workers.get(assignedWorkerId);
                    if (worker) {
                        worker.off('message', resultHandler);
                    }
                }
                if (assignedWorkerId) {
                    this.activeWorkers.delete(assignedWorkerId);
                }
            };
            try {
                // Add task to queue if no workers available
                if (this.activeWorkers.size >= this.maxWorkers) {
                    // Limit queue size to prevent memory exhaustion
                    if (this.taskQueue.length >= 100) {
                        reject(new Error('Task queue is full'));
                        return;
                    }
                    this.taskQueue.push(task);
                    logger.debug(`📋 Task ${task.id} queued (${this.taskQueue.length} in queue)`);
                    // Schedule queue processing without recursion
                    setImmediate(() => this.processNextTask());
                    return;
                }
                // Get available worker or create new one
                assignedWorkerId = await this.getAvailableWorker();
                const worker = this.workers.get(assignedWorkerId);
                if (!worker) {
                    reject(new Error(`Worker ${assignedWorkerId} not found`));
                    return;
                }
                this.activeWorkers.add(assignedWorkerId);
                // Set up timeout for task with proper cleanup
                timeout = setTimeout(() => {
                    logger.warn(`⏰ Task ${task.id} timed out in worker ${assignedWorkerId}`);
                    cleanup();
                    reject(new Error(`Task ${task.id} timed out`));
                }, 30000); // 30 second timeout
                // Listen for result with proper cleanup
                resultHandler = (result) => {
                    if (result.id === task.id) {
                        cleanup();
                        // Schedule queue processing without recursion
                        setImmediate(() => this.processNextTask());
                        resolve(result);
                    }
                };
                worker.on('message', resultHandler);
                worker.postMessage(task);
                logger.debug(`🚀 Task ${task.id} sent to worker ${assignedWorkerId}`);
            }
            catch (error) {
                cleanup();
                logger.error(`❌ Failed to execute task ${task.id}:`, error);
                reject(error);
            }
        });
    }
    /**
     * Get available worker or create new one
     */
    async getAvailableWorker() {
        // Find idle worker
        for (const [workerId] of this.workers) {
            if (!this.activeWorkers.has(workerId)) {
                return workerId;
            }
        }
        // Create new worker if under limit
        if (this.workers.size < this.maxWorkers) {
            return await this.createWorker();
        }
        // Wait for worker to become available with timeout to prevent infinite loops
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                clearInterval(checkInterval);
                reject(new Error('Timeout waiting for available worker'));
            }, 10000); // 10 second timeout
            const checkInterval = setInterval(() => {
                for (const [workerId] of this.workers) {
                    if (!this.activeWorkers.has(workerId)) {
                        clearTimeout(timeout);
                        clearInterval(checkInterval);
                        resolve(workerId);
                        return;
                    }
                }
            }, 100);
        });
    }
    /**
     * Handle worker result
     */
    handleWorkerResult(workerId, result) {
        logger.debug(`📨 Received result from worker ${workerId} for task ${result.id}`);
        if (!result.success) {
            logger.error(`❌ Task ${result.id} failed in worker ${workerId}:`, result.error);
        }
    }
    /**
     * Process next task in queue (NON-RECURSIVE)
     */
    processNextTask() {
        // Prevent recursive processing and infinite loops
        if (this.isProcessingQueue || this.taskQueue.length === 0) {
            return;
        }
        // Check if we have available workers
        if (this.activeWorkers.size >= this.maxWorkers) {
            return; // No available workers
        }
        this.isProcessingQueue = true;
        try {
            // Sort queue by priority before processing
            this.taskQueue.sort((a, b) => {
                const priorityOrder = { HIGH: 3, MEDIUM: 2, LOW: 1 };
                return priorityOrder[b.priority] - priorityOrder[a.priority];
            });
            const nextTask = this.taskQueue.shift();
            if (nextTask) {
                // Process task without recursion
                this.executeTask(nextTask).catch(error => {
                    logger.error(`❌ Failed to process queued task ${nextTask.id}:`, error);
                }).finally(() => {
                    this.isProcessingQueue = false;
                    // Schedule next processing cycle if more tasks exist
                    if (this.taskQueue.length > 0 && this.activeWorkers.size < this.maxWorkers) {
                        setImmediate(() => this.processNextTask());
                    }
                });
            }
            else {
                this.isProcessingQueue = false;
            }
        }
        catch (error) {
            this.isProcessingQueue = false;
            logger.error('❌ Error in processNextTask:', error);
        }
    }
    /**
     * Remove worker from pool with proper cleanup
     */
    removeWorker(workerId) {
        // Clean up event handlers
        const handlers = this.workerHandlers.get(workerId);
        if (handlers) {
            const worker = this.workers.get(workerId);
            if (worker) {
                // Remove all tracked event listeners
                for (const [event, handler] of handlers) {
                    worker.off(event, handler);
                }
            }
            this.workerHandlers.delete(workerId);
        }
        // Remove worker from collections
        this.workers.delete(workerId);
        this.activeWorkers.delete(workerId);
        logger.debug(`🗑️ Removed worker with cleanup: ${workerId}`);
    }
    /**
     * Execute cache refresh in worker thread
     */
    async executeCacheRefresh(cacheType, params) {
        const task = {
            id: `cache_${cacheType}_${Date.now()}`,
            type: 'CACHE_REFRESH',
            data: { cacheType, params },
            priority: 'MEDIUM'
        };
        return this.executeTask(task);
    }
    /**
     * Execute API call in worker thread
     */
    async executeApiCall(apiConfig) {
        const task = {
            id: `api_${Date.now()}`,
            type: 'API_CALL',
            data: apiConfig,
            priority: 'HIGH'
        };
        return this.executeTask(task);
    }
    /**
     * Execute data processing in worker thread
     */
    async executeDataProcessing(processingConfig) {
        const task = {
            id: `processing_${Date.now()}`,
            type: 'DATA_PROCESSING',
            data: processingConfig,
            priority: 'LOW'
        };
        return this.executeTask(task);
    }
    /**
     * Get worker pool statistics
     */
    getStats() {
        return {
            totalWorkers: this.workers.size,
            activeWorkers: this.activeWorkers.size,
            queuedTasks: this.taskQueue.length,
            maxWorkers: this.maxWorkers
        };
    }
    /**
     * Shutdown all workers with comprehensive cleanup
     */
    async shutdown() {
        logger.info('🔄 Shutting down worker pool...');
        // Stop queue processing
        this.isProcessingQueue = false;
        // Clean up all workers with proper event handler cleanup
        const shutdownPromises = Array.from(this.workers.entries()).map(([workerId, worker]) => {
            return new Promise((resolve) => {
                // Clean up event handlers before terminating
                const handlers = this.workerHandlers.get(workerId);
                if (handlers) {
                    for (const [event, handler] of handlers) {
                        worker.off(event, handler);
                    }
                }
                worker.terminate().then(() => resolve()).catch(() => resolve());
            });
        });
        await Promise.all(shutdownPromises);
        // Clear all collections
        this.workers.clear();
        this.activeWorkers.clear();
        this.workerHandlers.clear();
        this.taskQueue.length = 0;
        logger.info('✅ Worker pool shutdown complete with full cleanup');
    }
}
// Export singleton instance
export const workerThreadService = new WorkerThreadService();
//# sourceMappingURL=workerThreadService.js.map