import WebSocket from 'ws';
import crypto from 'crypto';
import { logger } from '../utils/logger.js';
import { getApi<PERSON>ey } from './coinService.js';
import { processPulseData } from './pulseService.js';
import { websocketConfig, validateWebSocketConfig, getConfigInfo } from '../config/websocketConfig.js';
class MobulaWebSocketService {
    ws = null;
    reconnectAttempts = 0;
    isConnecting = false;
    activeUsers = new Set();
    disconnectTimer = null;
    heartbeatTimer = null;
    pulseDataCache = null;
    fallbackDataCache = null; // Long-term cache for first-time connections
    connectionState;
    isShuttingDown = false;
    constructor() {
        // Validate configuration on startup
        try {
            validateWebSocketConfig();
            logger.info('WebSocket configuration validated successfully');
        }
        catch (error) {
            logger.error('WebSocket configuration validation failed:', error.message);
            throw error;
        }
        this.connectionState = {
            isConnected: false,
            isConnecting: false,
            reconnectAttempts: 0,
            lastConnectTime: null,
            lastDisconnectTime: null,
            totalMessagesReceived: 0,
            lastMessageTime: null
        };
        this.setupGracefulShutdown();
        logger.info('Enhanced Mobula WebSocket Service initialized', getConfigInfo());
        // Removed auto-connection to prevent resource exhaustion
        logger.info('🔧 Mobula WebSocket Service ready (connection on-demand only)');
    }
    /**
     * Register a user as active on the pulse page
     */
    registerUser(userId) {
        logger.info(`Registering user ${userId} for pulse data`);
        this.activeUsers.add(userId);
        // Clear any pending disconnect timer
        if (this.disconnectTimer) {
            clearTimeout(this.disconnectTimer);
            this.disconnectTimer = null;
            logger.info(`Cleared disconnect timer due to new user registration: ${userId}`);
        }
        // Reset shutdown flag when new users register - this is critical for reconnection
        if (this.isShuttingDown) {
            logger.info(`🔄 Resetting shutdown state due to new user registration: ${userId}`);
            this.isShuttingDown = false;
        }
        // Always attempt connection when a user registers, even if we think we're connected
        // This ensures we have a fresh, working connection
        const currentState = this.ws?.readyState;
        logger.info(`Current WebSocket state for user ${userId}: ${currentState} (OPEN=${WebSocket.OPEN})`);
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            logger.info(`Initiating Mobula WebSocket connection for user ${userId}`);
            this.connect();
        }
        else {
            logger.info(`Mobula WebSocket already connected for user ${userId}, verifying connection health`);
            // Even if connected, verify the connection is healthy by sending a ping
            this.verifyConnectionHealth();
        }
    }
    /**
     * Unregister a user from the pulse page
     */
    unregisterUser(userId) {
        logger.info(`Unregistering user ${userId} from pulse data`);
        this.activeUsers.delete(userId);
        // If no active users, start disconnect timer
        if (this.activeUsers.size === 0) {
            this.scheduleDisconnect();
        }
    }
    /**
     * Get cached pulse data with fallback support
     */
    getCachedData() {
        const now = Date.now();
        // Try to get fresh data first
        if (this.pulseDataCache) {
            const isExpired = now - this.pulseDataCache.lastUpdated > websocketConfig.cacheExpiryMs;
            if (!isExpired) {
                return this.pulseDataCache.data;
            }
            else {
                logger.warn('Primary pulse data cache has expired');
                this.pulseDataCache.isStale = true;
            }
        }
        // If no fresh data, try fallback cache for first-time connections
        if (this.fallbackDataCache) {
            const fallbackAge = now - this.fallbackDataCache.lastUpdated;
            // Use fallback data if it's not too old (based on category-specific thresholds)
            const maxFallbackAge = Math.max(websocketConfig.categoryCache.new.fallbackExpiryMs, websocketConfig.categoryCache.bonding.fallbackExpiryMs, websocketConfig.categoryCache.bonded.fallbackExpiryMs);
            if (fallbackAge < maxFallbackAge) {
                logger.info(`📦 Using fallback cache data (age: ${Math.round(fallbackAge / 1000)}s)`);
                return this.fallbackDataCache.data;
            }
            else {
                logger.warn('Fallback cache data is too old, discarding');
                this.fallbackDataCache = null;
            }
        }
        return null;
    }
    /**
     * Check if cached data is available and fresh (category-aware)
     */
    hasFreshData(category) {
        if (!this.pulseDataCache) {
            return false;
        }
        const now = Date.now();
        if (category && this.pulseDataCache.categoryTimestamps) {
            // Check category-specific freshness
            const categoryTimestamp = this.pulseDataCache.categoryTimestamps[category];
            const categoryConfig = websocketConfig.categoryCache[category];
            if (categoryTimestamp) {
                return now - categoryTimestamp < categoryConfig.expiryMs;
            }
        }
        // Default: check overall freshness
        return now - this.pulseDataCache.lastUpdated < websocketConfig.cacheExpiryMs;
    }
    /**
     * Check if we have any usable data (fresh or fallback)
     */
    hasUsableData() {
        return this.getCachedData() !== null;
    }
    /**
     * Verify connection health by sending a heartbeat
     */
    verifyConnectionHealth() {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            logger.warn('Cannot verify connection health: WebSocket not connected');
            return;
        }
        try {
            // Send a simple ping to verify connection is alive
            this.ws.ping();
            logger.debug('Sent WebSocket ping to verify connection health');
        }
        catch (error) {
            logger.error('Failed to send WebSocket ping:', error.message);
            // If ping fails, force reconnection
            this.handleReconnect();
        }
    }
    /**
     * Get connection status
     */
    getStatus() {
        return {
            connected: this.ws?.readyState === WebSocket.OPEN,
            activeUsers: this.activeUsers.size,
            lastUpdate: this.pulseDataCache?.lastUpdated || null,
            connectionState: this.connectionState,
            config: getConfigInfo()
        };
    }
    /**
     * Reset service state for clean reconnection (useful for debugging)
     */
    resetServiceState() {
        logger.info('🔄 Manually resetting WebSocket service state');
        // Clear timers
        if (this.disconnectTimer) {
            clearTimeout(this.disconnectTimer);
            this.disconnectTimer = null;
        }
        this.stopHeartbeat();
        // Reset flags
        this.isShuttingDown = false;
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        // Reset connection state
        this.connectionState.isConnected = false;
        this.connectionState.isConnecting = false;
        this.connectionState.reconnectAttempts = 0;
        // Close existing connection if any
        if (this.ws) {
            this.ws.close(1000, 'Manual reset');
            this.ws = null;
        }
        logger.info('✅ WebSocket service state reset complete');
    }
    /**
     * Connect to Mobula WebSocket
     */
    async connect() {
        // Reset shutdown flag when attempting to connect - critical for reconnection
        if (this.isShuttingDown) {
            logger.info('🔄 Resetting shutdown state for new connection attempt');
            this.isShuttingDown = false;
        }
        if (this.isConnecting) {
            logger.info('WebSocket connection already in progress');
            return;
        }
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            logger.info('WebSocket already connected');
            return;
        }
        this.isConnecting = true;
        this.connectionState.isConnecting = true;
        try {
            const apiKey = await getApiKey();
            if (!apiKey) {
                throw new Error('Mobula API key not found');
            }
            logger.info(`Connecting to Mobula WebSocket: ${websocketConfig.websocketUrl}`);
            this.ws = new WebSocket(websocketConfig.websocketUrl);
            this.ws.on('open', () => {
                logger.info('✅ Connected to Mobula WebSocket successfully');
                console.log('✅ Connected to Mobula WebSocket successfully');
                this.isConnecting = false;
                this.reconnectAttempts = 0;
                this.connectionState.isConnected = true;
                this.connectionState.isConnecting = false;
                this.connectionState.reconnectAttempts = 0;
                this.connectionState.lastConnectTime = Date.now();
                // Send subscription message immediately after connection
                this.sendSubscriptionMessage(apiKey);
                this.startHeartbeat();
                // Log connection details for debugging
                logger.info('🔗 WebSocket connection established', {
                    url: websocketConfig.websocketUrl,
                    readyState: this.ws?.readyState,
                    activeUsers: this.activeUsers.size
                });
            });
            this.ws.on('message', (data) => {
                this.handleMessage(data);
            });
            this.ws.on('error', (error) => {
                logger.error('❌ Mobula WebSocket error:', {
                    message: error.message,
                    stack: error.stack,
                    activeUsers: this.activeUsers.size,
                    connectionState: this.connectionState.isConnected
                });
                console.error('❌ Mobula WebSocket error:', error);
                this.isConnecting = false;
                this.connectionState.isConnecting = false;
                this.handleReconnect();
            });
            this.ws.on('close', (code, reason) => {
                const reasonStr = reason.toString();
                logger.warn(`🔌 Mobula WebSocket closed:`, {
                    code,
                    reason: reasonStr,
                    activeUsers: this.activeUsers.size,
                    wasConnected: this.connectionState.isConnected,
                    messagesReceived: this.connectionState.totalMessagesReceived
                });
                console.warn(`🔌 Mobula WebSocket closed: ${code} - ${reasonStr}`);
                this.isConnecting = false;
                this.connectionState.isConnected = false;
                this.connectionState.isConnecting = false;
                this.connectionState.lastDisconnectTime = Date.now();
                this.stopHeartbeat();
                this.ws = null;
                // Only reconnect if we have active users and not shutting down
                if (this.activeUsers.size > 0 && !this.isShuttingDown) {
                    this.handleReconnect();
                }
            });
        }
        catch (error) {
            logger.error('Failed to connect to Mobula WebSocket:', error.message);
            this.isConnecting = false;
            this.connectionState.isConnecting = false;
            this.handleReconnect();
        }
    }
    /**
     * Send subscription message to Mobula WebSocket
     */
    sendSubscriptionMessage(apiKey) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            logger.error('Cannot send subscription message: WebSocket not connected');
            return;
        }
        const message = {
            type: websocketConfig.defaultSubscription.type,
            authorization: apiKey,
            payload: websocketConfig.defaultSubscription.payload
        };
        try {
            this.ws.send(JSON.stringify(message));
            logger.info('📤 Sent pulse subscription message to Mobula WebSocket', {
                factories: message.payload?.factories,
                blockchains: message.payload?.blockchains
            });
            console.log('📤 Sent pulse subscription message to Mobula WebSocket', message);
        }
        catch (error) {
            logger.error('Failed to send subscription message:', error.message);
            console.error('Failed to send subscription message:', error.message);
        }
    }
    /**
     * Handle incoming WebSocket messages
     */
    handleMessage(data) {
        try {
            const rawMessage = data.toString();
            logger.info('📥 Raw WebSocket message received:', {
                length: rawMessage.length,
                preview: rawMessage.substring(0, 200) + (rawMessage.length > 200 ? '...' : '')
            });
            const message = JSON.parse(rawMessage);
            // Update connection state
            this.connectionState.totalMessagesReceived++;
            this.connectionState.lastMessageTime = Date.now();
            logger.info('📥 Parsed WebSocket message:', {
                type: message.type,
                messageCount: this.connectionState.totalMessagesReceived,
                hasData: !!message.data,
                keys: Object.keys(message)
            });
            console.log('📥 Received WebSocket message:', message);
            // Handle different message types from Mobula WebSocket
            if (message.data) {
                // Check if this is pulse data with the expected structure
                const rawData = message.data?.pumpfun?.['solana:solana'] || message.data;
                if (rawData && (rawData.new || rawData.bonding || rawData.bonded)) {
                    // Process the pulse data using existing logic
                    const processedData = processPulseData(rawData, 'solana');
                    // Generate hash of the processed data to detect changes
                    const dataHash = crypto.createHash('md5').update(JSON.stringify(processedData)).digest('hex');
                    // Check if data has actually changed
                    const hasDataChanged = !this.pulseDataCache || this.pulseDataCache.dataHash !== dataHash;
                    if (hasDataChanged) {
                        const now = Date.now();
                        // Update primary cache with category timestamps
                        this.pulseDataCache = {
                            data: processedData,
                            lastUpdated: now,
                            isStale: false,
                            dataHash,
                            categoryTimestamps: {
                                new: now,
                                bonding: now,
                                bonded: now
                            }
                        };
                        // Update fallback cache for first-time connections
                        this.fallbackDataCache = {
                            data: processedData,
                            lastUpdated: now,
                            isStale: false,
                            dataHash,
                            categoryTimestamps: {
                                new: now,
                                bonding: now,
                                bonded: now
                            }
                        };
                        logger.info('🎯 Updated pulse data cache from WebSocket (data changed)', {
                            new: rawData.new?.length || 0,
                            bonding: rawData.bonding?.length || 0,
                            bonded: rawData.bonded?.length || 0,
                            hash: dataHash.substring(0, 8),
                            fallbackCacheUpdated: true
                        });
                        // Broadcast to frontend clients only when data has changed
                        this.broadcastToFrontendClients(processedData);
                    }
                    else {
                        logger.debug('📊 Received identical pulse data, skipping broadcast', {
                            hash: dataHash.substring(0, 8)
                        });
                    }
                }
                else {
                    logger.debug('Received WebSocket message without pulse data structure');
                }
            }
            else if (message.type === 'ping') {
                // Handle ping messages
                logger.debug('💓 Received ping from WebSocket');
            }
            else {
                logger.debug('Received WebSocket message without data field');
            }
        }
        catch (error) {
            logger.error('Failed to parse WebSocket message:', error.message);
        }
    }
    /**
     * Handle reconnection logic with circuit breaker
     */
    handleReconnect() {
        if (this.activeUsers.size === 0 || this.isShuttingDown) {
            logger.info('No active users or shutting down, skipping reconnection');
            return;
        }
        if (this.reconnectAttempts >= websocketConfig.maxReconnectAttempts) {
            logger.error(`❌ Max reconnection attempts (${websocketConfig.maxReconnectAttempts}) reached, entering circuit breaker mode`);
            this.connectionState.reconnectAttempts = this.reconnectAttempts;
            // Circuit breaker: wait 5 minutes before allowing reconnection attempts
            setTimeout(() => {
                logger.info('🔄 Circuit breaker reset, allowing reconnection attempts');
                this.reconnectAttempts = 0;
                this.connectionState.reconnectAttempts = 0;
            }, 5 * 60 * 1000); // 5 minutes
            return;
        }
        this.reconnectAttempts++;
        this.connectionState.reconnectAttempts = this.reconnectAttempts;
        const delay = Math.min(websocketConfig.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1), websocketConfig.reconnectInterval * 6 // Max 6x the base interval
        );
        logger.info(`🔄 Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${websocketConfig.maxReconnectAttempts})`);
        setTimeout(() => {
            if (!this.isShuttingDown && this.activeUsers.size > 0) {
                this.connect();
            }
        }, delay);
    }
    /**
     * Schedule disconnect after delay
     */
    scheduleDisconnect() {
        if (this.disconnectTimer) {
            clearTimeout(this.disconnectTimer);
        }
        logger.info(`⏰ Scheduling disconnect in ${websocketConfig.disconnectDelay / 1000}s if no users become active`);
        this.disconnectTimer = setTimeout(() => {
            if (this.activeUsers.size === 0) {
                logger.info(`🔌 No active users for ${websocketConfig.disconnectDelay / 1000}s, disconnecting from Mobula WebSocket`);
                this.disconnect();
            }
        }, websocketConfig.disconnectDelay);
    }
    /**
     * Start heartbeat to keep connection alive
     */
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatTimer = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                try {
                    this.ws.ping();
                    logger.debug('💓 Sent heartbeat ping');
                }
                catch (error) {
                    logger.error('❌ Failed to send heartbeat:', error.message);
                }
            }
        }, websocketConfig.heartbeatInterval);
        logger.debug(`💓 Started heartbeat with ${websocketConfig.heartbeatInterval / 1000}s interval`);
    }
    /**
     * Stop heartbeat
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
            logger.debug('💓 Stopped heartbeat');
        }
    }
    /**
     * Disconnect from WebSocket
     */
    disconnect() {
        logger.info('🔌 Disconnecting from Mobula WebSocket', {
            activeUsers: this.activeUsers.size,
            wasConnected: this.connectionState.isConnected,
            reason: 'scheduled_disconnect_due_to_no_active_users'
        });
        this.isShuttingDown = true;
        if (this.disconnectTimer) {
            clearTimeout(this.disconnectTimer);
            this.disconnectTimer = null;
        }
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close(1000, 'Normal disconnect');
            this.ws = null;
        }
        this.connectionState.isConnected = false;
        this.connectionState.isConnecting = false;
        this.connectionState.lastDisconnectTime = Date.now();
    }
    /**
     * Setup graceful shutdown
     */
    setupGracefulShutdown() {
        const shutdown = (signal) => {
            logger.info(`🛑 Received ${signal}. Shutting down Enhanced Mobula WebSocket service gracefully...`);
            this.isShuttingDown = true;
            this.disconnect();
            // Give some time for cleanup
            setTimeout(() => {
                logger.info('✅ Enhanced Mobula WebSocket service shutdown complete');
                process.exit(0);
            }, 1000);
        };
        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('SIGQUIT', () => shutdown('SIGQUIT'));
        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            logger.error('❌ Uncaught exception in WebSocket service:', error.message);
            this.disconnect();
            process.exit(1);
        });
        process.on('unhandledRejection', (reason, promise) => {
            logger.error('❌ Unhandled rejection in WebSocket service:', { reason, promise });
            this.disconnect();
            process.exit(1);
        });
    }
    /**
     * Update subscription configuration at runtime
     */
    updateSubscription(factories, blockchains) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            logger.warn('⚠️  Cannot update subscription: WebSocket not connected');
            return false;
        }
        const customPayload = {};
        if (factories) {
            customPayload.factories = factories;
        }
        if (blockchains) {
            customPayload.blockchains = blockchains;
        }
        logger.info('🔄 Updating subscription with new parameters', customPayload);
        try {
            const apiKey = process.env.MOBULA_API_KEY || websocketConfig.apiKey;
            const message = {
                type: 'pulse',
                authorization: apiKey,
                payload: { ...websocketConfig.defaultSubscription.payload, ...customPayload }
            };
            this.ws.send(JSON.stringify(message));
            logger.info('✅ Subscription updated successfully');
            return true;
        }
        catch (error) {
            logger.error('❌ Failed to update subscription:', error.message);
            return false;
        }
    }
    /**
     * Broadcast new pulse data to frontend clients
     */
    async broadcastToFrontendClients(data) {
        try {
            // Lazy import to avoid circular dependency
            const { frontendWebSocketService } = await import('./frontendWebSocketService.js');
            // Check if there are any clients to broadcast to
            const stats = frontendWebSocketService.getStats();
            if (stats.pulseRoomClients > 0) {
                frontendWebSocketService.broadcastPulseData(data, 'websocket');
                logger.info(`📡 Broadcasted real-time pulse data to ${stats.pulseRoomClients} frontend clients`);
            }
            else {
                logger.debug('📡 No frontend clients in pulse room - skipping broadcast');
            }
        }
        catch (error) {
            logger.error('❌ Failed to broadcast to frontend clients:', error.message);
        }
    }
}
// Export singleton instance
export const mobulaWebSocketService = new MobulaWebSocketService();
//# sourceMappingURL=mobulaWebSocketService.js.map