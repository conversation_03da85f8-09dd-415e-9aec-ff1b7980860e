import axios from 'axios';
import dotenv from 'dotenv';
import { getApi<PERSON>ey } from './coinService.js';
// Load environment variables
dotenv.config();
// Helper function to fetch tokens from Mobula API
async function fetchTokensFromMobula(apiKey, blockchain, fields) {
    try {
        // Set request options based on network
        const params = {};
        // Use the market/query/token endpoint which allows direct blockchain filtering
        const url = 'https://api.mobula.io/api/1/market/query/token';
        // Add blockchain filter if provided
        if (blockchain) {
            params.blockchain = blockchain;
        }
        // Add sorting and limit
        params.sortBy = 'market_cap';
        params.sortOrder = 'desc';
        params.limit = 100; // We'll fetch more and filter/sort client-side
        const headers = {
            accept: 'application/json'
        };
        // Only add Authorization header if apiKey is provided
        if (apiKey && apiKey.trim() !== '') {
            headers.Authorization = `Bearer ${apiKey}`;
        }
        const response = await axios.get(url, {
            params,
            headers,
            timeout: 30000 // Increased timeout for potentially slow API
        });
        if (response.data && Array.isArray(response.data.data)) {
            return response.data.data;
        }
        else if (response.data) {
            // Try to handle different response formats
            if (Array.isArray(response.data)) {
                return response.data;
            }
        }
        return [];
    }
    catch (error) {
        return [];
    }
}
// Helper function to filter tokens by network
function filterTokensByNetwork(tokens, network) {
    // If already filtered by API request, just return
    if (network === 'binance-smart-chain' || network === 'solana') {
        return tokens;
    }
    // Otherwise perform client-side filtering
    return tokens.filter((token) => {
        return token.blockchains &&
            Array.isArray(token.blockchains) &&
            token.blockchains.some((chain) => network === 'binance-smart-chain'
                ? (chain.toLowerCase().includes('bnb') || chain.toLowerCase().includes('bsc') || chain.toLowerCase().includes('binance'))
                : chain.toLowerCase().includes(network));
    });
}
// Helper function to fetch tokens from Mobula Blockchain Pairs API
async function fetchTokensFromMobulaPairs(apiKey, blockchain) {
    try {
        const url = 'https://api.mobula.io/api/1/market/blockchain/pairs';
        const headers = {
            accept: 'application/json'
        };
        if (apiKey && apiKey.trim() !== '') {
            headers.Authorization = `Bearer ${apiKey}`;
        }
        const params = {
            sortBy: 'latest_trade_date',
            sortOrder: 'desc',
            limit: 100
        };
        if (blockchain && blockchain.trim() !== '') {
            params.blockchain = blockchain;
        }
        const response = await axios.get(url, {
            params,
            headers,
            timeout: 30000
        });
        if (response.data && Array.isArray(response.data.data)) {
            const pairs = response.data.data;
            const rawTokens = pairs.map((item) => {
                const baseTokenKey = item.pair?.baseToken;
                if (!baseTokenKey || !item.pair || !item.pair[baseTokenKey])
                    return null;
                const token = item.pair[baseTokenKey];
                const baseToken = baseTokenKey === 'token0' ? item.pair["token1"] : item.pair["token0"];
                const contractAddress = token.address || '';
                return {
                    id: token.address || token.id || '',
                    name: token.name || '',
                    baseimage: baseToken.logo || '',
                    symbol: token.symbol || '',
                    contractAddress,
                    image: token.logo || '',
                    holders_count: item.holders_count || 0,
                    blockchain: blockchain || 'ethereum',
                    price: token.price || 0,
                    price_change_24h: item.price_change_24h || 0,
                    price_change_1h: item.price_change_1h || 0,
                    price_change_7d: item.price_change_7d || 0,
                    price_change_30d: item.price_change_30d || 0,
                    price_change_1y: item.price_change_1y || 0,
                    market_cap: token.marketCap,
                    market_cap_diluted: token.marketCapDiluted,
                    volume_24h: item.volume_24h || 0,
                    trades_24h: item.trades_24h || 0,
                    liquidity: item.liquidity || null,
                    total_supply: token.totalSupply || null,
                    circulating_supply: token.circulatingSupply || null,
                    created_at: item.created_at || null,
                    listed_at: item.created_at || null,
                    rank: null
                };
            }).filter(Boolean);
            // Enrich tokens with logo, twitter, and holders
            const tokensWithMetadata = await Promise.all(rawTokens.map(async (token) => {
                try {
                    const [{ logo, twitter, website, telegram }] = await Promise.all([
                        getMetaData(token.symbol, apiKey)
                    ]);
                    return {
                        ...token,
                        twitter,
                        website,
                        telegram,
                    };
                }
                catch (e) {
                    const errorMessage = e instanceof Error ? e.message : String(e);
                    return {
                        ...token,
                        twitter: null,
                    };
                }
            }));
            return tokensWithMetadata;
        }
        else {
            return [];
        }
    }
    catch (error) {
        return [];
    }
}
/**
 * Helper function to get token image URL with multiple fallbacks
 * If no direct image is provided, it tries to generate one from various token image services
 */
async function getTotalHolders(contractAddress, network, apiKey) {
    const url = 'https://api.mobula.io/api/1/market/token/holders';
    const headers = {
        accept: 'application/json',
    };
    if (apiKey?.trim()) {
        headers.Authorization = `Bearer ${apiKey}`;
    }
    const params = {
        asset: contractAddress,
        blockchain: network,
        limit: 10
    };
    try {
        const response = await axios.get(url, {
            params,
            headers,
            timeout: 30000,
        });
        return response.data?.total_count ?? 0;
    }
    catch (error) {
        return 0;
    }
}
async function getMetaData(symbol, apiKey) {
    const url = 'https://api.mobula.io/api/1/metadata';
    const headers = {
        accept: 'application/json',
    };
    if (apiKey?.trim()) {
        headers.Authorization = `Bearer ${apiKey}`;
    }
    const params = { symbol: symbol };
    try {
        const response = await axios.get(url, {
            params,
            headers,
            timeout: 30000,
        });
        const { logo, twitter, website, chat } = response.data.data || {};
        return {
            logo: logo || '',
            twitter: twitter || null,
            website: website || null,
            telegram: chat || null
        };
    }
    catch (error) {
        const message = error instanceof Error ? error.message : String(error);
        return { logo: '', twitter: null, website: '', telegram: '' };
    }
}
function getTokenImageUrl(contractAddress, symbol, blockchain, ...providedUrls) {
    // First try any provided URLs that are not empty
    for (const url of providedUrls) {
        if (url && url.trim() !== '') {
            return url;
        }
    }
    // If no URL provided and we have a contract address, try common token image APIs
    if (contractAddress && contractAddress.trim() !== '') {
        // Clean the contract address
        const cleanAddress = contractAddress.trim().toLowerCase();
        // For BSC tokens
        if (blockchain.toLowerCase().includes('binance') ||
            blockchain.toLowerCase().includes('bsc') ||
            blockchain.toLowerCase().includes('bnb')) {
            return `https://assets-cdn.trustwallet.com/blockchains/smartchain/assets/${cleanAddress}/logo.png`;
        }
        // For Solana tokens
        if (blockchain.toLowerCase().includes('solana')) {
            // Try the Solana token list
            return `https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/${cleanAddress}/logo.png`;
        }
        // For Ethereum tokens (or as a fallback for other chains)
        return `https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/${cleanAddress}/logo.png`;
    }
    // If all else fails, try to use a generic icon based on symbol
    if (symbol && symbol.trim() !== '') {
        // Return a generated placeholder with the token symbol
        return `https://ui-avatars.com/api/?name=${encodeURIComponent(symbol)}&background=random&size=128`;
    }
    // Absolute fallback - generic token icon
    return 'https://cryptologos.cc/logos/ethereum-eth-logo.png';
}
// Helper function to get token age
async function fetchTokenCreationDate(apiKey, token) {
    if (!token.id)
        return null;
    try {
        const url = 'https://api.mobula.io/api/1/metadata/creation-date';
        const headers = {
            accept: 'application/json'
        };
        // Only add Authorization header if apiKey is provided
        if (apiKey && apiKey.trim() !== '') {
            headers.Authorization = `Bearer ${apiKey}`;
        }
        const response = await axios.get(url, {
            params: { asset: token.id },
            headers,
            timeout: 5000
        });
        if (response.data && response.data.data) {
            const creationDate = new Date(response.data.data);
            const now = new Date();
            const ageInDays = Math.floor((now.getTime() - creationDate.getTime()) / (1000 * 60 * 60 * 24));
            return ageInDays > 0 ? ageInDays : null;
        }
        return null;
    }
    catch (error) {
        return null;
    }
}
// Helper function to sort tokens based on category
function sortTokens(tokens, category) {
    if (category === 'gainers') {
        return tokens.sort((a, b) => (b.price_change_percentage || 0) - (a.price_change_percentage || 0));
    }
    else if (category === 'losers') {
        return tokens.sort((a, b) => (a.price_change_percentage || 0) - (b.price_change_percentage || 0));
    }
    else if (category === 'volume') {
        return tokens.sort((a, b) => (b.total_volume || 0) - (a.total_volume || 0));
    }
    else {
        // Default sort by market cap
        return tokens.sort((a, b) => (b.market_cap || 0) - (a.market_cap || 0));
    }
}
// Helper function to process token data with timeframe selection
async function processTokenData(apiKey, tokens, network, timeframe, limit) {
    try {
        const validTimeframes = ['1h', '24h', '7d', '1m', '1y'];
        const effectiveTimeframe = validTimeframes.includes(timeframe) ? timeframe : '24h';
        // Process tokens in parallel batches
        const formattingPromises = tokens.map(async (token) => {
            // Get contract address from the token data
            const contract = token.address || '';
            // Map price change based on the effective timeframe
            let priceChange = 0;
            switch (effectiveTimeframe) {
                case '1h':
                    priceChange = token.price_change_1h || 0;
                    break;
                case '7d':
                    priceChange = token.price_change_7d || 0;
                    break;
                case '1m':
                    // Mobula API might use price_change_30d for 1 month
                    priceChange = token.price_change_30d || token.price_change_1m || 0;
                    break;
                case '1y':
                    priceChange = token.price_change_1y || 0;
                    break;
                case '24h':
                default:
                    priceChange = token.price_change_24h || 0;
                    break;
            }
            // Format network name according to UI requirements
            let formattedNetwork = '';
            if (network === 'binance-smart-chain') {
                formattedNetwork = 'BNB Smart Chain (BEP20)';
            }
            else if (network === 'solana') {
                formattedNetwork = 'Solana';
            }
            else {
                formattedNetwork = token.blockchain || network;
            }
            // Try to get token age
            let age = null;
            if (token.listed_at) {
                const creationDate = new Date(token.listed_at);
                const now = new Date();
                age = Math.floor((now.getTime() - creationDate.getTime()) / (1000 * 60 * 60 * 24));
            }
            else if (token.created_at) {
                const creationDate = new Date(token.created_at);
                const now = new Date();
                age = Math.floor((now.getTime() - creationDate.getTime()) / (1000 * 60 * 60 * 24));
            }
            else {
                age = await fetchTokenCreationDate(apiKey, token);
            }
            // Get token blockchain for image generation fallbacks
            const tokenBlockchain = formattedNetwork.toLowerCase().includes('bnb') ? 'binance-smart-chain' :
                formattedNetwork.toLowerCase().includes('solana') ? 'solana' : 'ethereum';
            // Get image url with multiple fallbacks
            const imageUrl = getTokenImageUrl(contract, token.symbol || '', tokenBlockchain, token.image, token.logo);
            // Map the token data to our RadarToken interface
            return {
                id: token.id?.toString() || '',
                image: token.image || '',
                name: token.name || '',
                baseimage: token.baseimage || '',
                symbol: token.symbol || '',
                contract,
                network: formattedNetwork || '',
                current_price: token.price || 0,
                price: token.price || 0,
                price_change_percentage_24h: token.price_change_24h || 0,
                price_change_percentage: priceChange,
                market_cap: token.market_cap || null,
                fdv: token.market_cap_diluted || null,
                fully_diluted_valuation: token.market_cap_diluted || null,
                total_volume: token.volume_24h || 0,
                volume_24h: token.volume_24h || 0,
                price_change_1h: token.price_change_1h || null,
                price_change_7d: token.price_change_7d || null,
                price_change_1m: token.price_change_30d || null,
                price_change_1y: token.price_change_1y || null,
                trades_24h: token.trades_24h,
                holders_count: token.holders_count,
                volume_7d: token.volume_7d || null,
                ath: token.ath || null,
                atl: token.atl || null,
                liquidity: token.liquidity || null,
                age_days: age,
                social_links: {
                    website: token.website || null,
                    twitter: token.twitter || null,
                    telegram: token.telegram || null
                },
                website: token.website || null,
                twitter: token.twitter || null,
                telegram: token.telegram || null,
                circulating_supply: token.circulating_supply ? parseFloat(token.circulating_supply) : null,
                total_supply: token.total_supply ? parseFloat(token.total_supply) : null,
                max_supply: null,
                rank: token.rank || null
            };
        });
        // Process all tokens in parallel
        const formattedTokens = await Promise.all(formattingPromises);
        // Return limited set of tokens (will be sorted later by caller)
        return formattedTokens.slice(0, limit);
    }
    catch (error) {
        return [];
    }
}
/**
 * Fetch combined token radar data from BSC and Solana for universal view
 * Uses the fetchTokenRadar function internally for each network
 * @param limit Number of tokens to return
 * @param timeframe Timeframe for price change
 * @param category Token category
 * @returns Combined array of token data
 */
export async function fetchUniversalTokenRadarMobula(limit = 30, timeframe, category) {
    try {
        // Get API key using the imported utility
        const apiKey = await getApiKey();
        if (!apiKey) {
            return [];
        }
        const effectiveTimeframe = timeframe || '24h';
        // Try to fetch directly from the blockchain/pairs API without network filter first
        let universalTokens = await fetchTokensFromMobulaPairs(apiKey, '');
        if (universalTokens && universalTokens.length > 0) {
            // Process, sort, and limit these tokens
            const processed = await processTokenData(apiKey, universalTokens, 'universal', effectiveTimeframe, limit * 2);
            const sortedTokens = sortTokens(processed, category);
            const limitedTokens = limit < sortedTokens.length ? sortedTokens.slice(0, limit) : sortedTokens;
            return limitedTokens;
        }
        // Fetch tokens for both networks in parallel
        const bscPromise = fetchTokenRadar(limit * 2, 'binance-smart-chain', effectiveTimeframe, category)
            .catch(error => {
            return [];
        });
        const solanaPromise = fetchTokenRadar(limit * 2, 'solana', effectiveTimeframe, category)
            .catch(error => {
            return [];
        });
        // Try fetching from both networks
        const [bscTokens, solanaTokens] = await Promise.all([bscPromise, solanaPromise]);
        if (!bscTokens.length && !solanaTokens.length) {
            return [];
        }
        // Combine results
        let allTokens = [...(bscTokens || []), ...(solanaTokens || [])];
        // Sort the combined list
        allTokens = sortTokens(allTokens, category);
        // Limit to requested number
        const limitedTokens = limit < allTokens.length ? allTokens.slice(0, limit) : allTokens;
        return limitedTokens;
    }
    catch (error) {
        return [];
    }
}
/**
 * Fetch token radar data for a specific network
 * @param limit Number of tokens to return (default 30)
 * @param network Network to filter by (default 'binance-smart-chain')
 * @param timeframe Timeframe for price change (default '24h')
 * @param category Category for sorting (gainers, losers, volume)
 * @returns Array of token data
 */
export async function fetchTokenRadar(limit = 30, network, timeframe, category) {
    try {
        // Get API key using the imported utility
        const apiKey = await getApiKey();
        if (!apiKey) {
            return [];
        }
        const effectiveNetwork = network || 'binance-smart-chain';
        const effectiveTimeframe = timeframe || '24h';
        // Handle universal network by delegating to the universal handler
        if (effectiveNetwork === 'universal') {
            return await fetchUniversalTokenRadarMobula(limit, effectiveTimeframe, category);
        }
        // Format blockchain parameter for the API
        let formattedBlockchain = '';
        if (effectiveNetwork === 'binance-smart-chain') {
            formattedBlockchain = 'BNB Smart Chain (BEP20)';
        }
        else if (effectiveNetwork === 'solana') {
            formattedBlockchain = 'Solana';
        }
        else {
            formattedBlockchain = effectiveNetwork; // In case of other blockchain inputs
        }
        // Try the blockchain/pairs API
        const allTokens = await fetchTokensFromMobulaPairs(apiKey, formattedBlockchain);
        // If no tokens found, return empty array
        if (!allTokens || allTokens.length === 0) {
            return [];
        }
        // Process token data with our helper function
        const formattedTokens = await processTokenData(apiKey, allTokens, effectiveNetwork, effectiveTimeframe, limit * 2);
        // Sort and limit the results
        const sortedTokens = sortTokens(formattedTokens, category);
        const limitedTokens = limit < sortedTokens.length ? sortedTokens.slice(0, limit) : sortedTokens;
        return limitedTokens;
    }
    catch (error) {
        return [];
    }
}
//# sourceMappingURL=tokenRadarService.js.map