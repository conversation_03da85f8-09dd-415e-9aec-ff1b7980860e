import { logger } from '../utils/logger.js';
import { EventEmitter } from 'events';
import { cpus } from 'os';
class PerformanceMonitorService extends EventEmitter {
    metrics = [];
    apiMetrics = [];
    systemMetrics = [];
    monitoringInterval = null;
    timers = new Set(); // Track all timers for cleanup
    maxMetricsHistory = 100; // Reduced from 1000 to prevent memory issues
    alertThresholds = {
        responseTime: 5000, // 5 seconds
        memoryUsage: 512 * 1024 * 1024, // 512MB
        cpuUsage: 80, // 80%
        eventLoopDelay: 100 // 100ms
    };
    prevCpuUsage = null;
    prevCpuTime = Date.now();
    numCpus = cpus().length;
    constructor() {
        super();
        this.startSystemMonitoring();
    }
    /**
     * Start system performance monitoring
     */
    startSystemMonitoring() {
        this.monitoringInterval = setInterval(() => {
            this.collectSystemMetrics();
        }, 30000); // Collect every 30 seconds (reduced from 5 seconds)
        // Track timer for cleanup
        this.timers.add(this.monitoringInterval);
        logger.info('📊 Performance monitoring started (30s interval)');
    }
    /**
     * Collect system metrics
     */
    collectSystemMetrics() {
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        // --- Improved CPU usage calculation ---
        const now = Date.now();
        let cpuPercent = 0;
        if (this.prevCpuUsage) {
            const elapsedUser = cpuUsage.user - this.prevCpuUsage.user; // μs since last sample
            const elapsedSys = cpuUsage.system - this.prevCpuUsage.system; // μs since last sample
            const elapsedCpu = elapsedUser + elapsedSys; // total μs CPU time
            const elapsedTime = (now - this.prevCpuTime) * 1000; // wall-clock μs
            if (elapsedTime > 0) {
                cpuPercent = (elapsedCpu / elapsedTime) / this.numCpus * 100;
            }
        }
        // Update previous sample trackers
        this.prevCpuUsage = cpuUsage;
        this.prevCpuTime = now;
        const systemMetric = {
            cpuUsage: cpuPercent,
            memoryUsage: memUsage,
            eventLoopDelay: this.measureEventLoopDelay(),
            activeHandles: process._getActiveHandles().length,
            timestamp: Date.now()
        };
        this.systemMetrics.push(systemMetric);
        // Keep only recent metrics
        if (this.systemMetrics.length > this.maxMetricsHistory) {
            this.systemMetrics = this.systemMetrics.slice(-this.maxMetricsHistory);
        }
        // Check for alerts
        this.checkSystemAlerts(systemMetric);
        // Emit metric for real-time monitoring
        this.emit('systemMetric', systemMetric);
    }
    /**
     * Measure event loop delay
     */
    measureEventLoopDelay() {
        const start = process.hrtime.bigint();
        setImmediate(() => {
            const delay = Number(process.hrtime.bigint() - start) / 1000000; // Convert to ms
            return delay;
        });
        return 0; // Simplified for now
    }
    /**
     * Record API performance metric
     */
    recordApiMetric(metric) {
        this.apiMetrics.push(metric);
        // Keep only recent metrics
        if (this.apiMetrics.length > this.maxMetricsHistory) {
            this.apiMetrics = this.apiMetrics.slice(-this.maxMetricsHistory);
        }
        // Check for performance alerts
        if (metric.responseTime > this.alertThresholds.responseTime) {
            logger.warn(`⚠️ Slow API response: ${metric.endpoint} took ${metric.responseTime}ms`);
            this.emit('slowResponse', metric);
        }
        // Emit metric for real-time monitoring
        this.emit('apiMetric', metric);
    }
    /**
     * Record custom performance metric
     */
    recordMetric(name, value, tags) {
        const metric = {
            name,
            value,
            timestamp: Date.now(),
            tags
        };
        this.metrics.push(metric);
        // Keep only recent metrics
        if (this.metrics.length > this.maxMetricsHistory) {
            this.metrics = this.metrics.slice(-this.maxMetricsHistory);
        }
        this.emit('customMetric', metric);
    }
    /**
     * Check for system alerts with enhanced monitoring
     */
    checkSystemAlerts(metric) {
        const memoryUsageMB = Math.round(metric.memoryUsage.heapUsed / 1024 / 1024);
        // Memory usage alert with critical threshold
        if (metric.memoryUsage.heapUsed > this.alertThresholds.memoryUsage) {
            logger.warn(`⚠️ High memory usage: ${memoryUsageMB}MB`);
            this.emit('highMemoryUsage', metric);
            // Critical memory threshold - force garbage collection and warn
            if (metric.memoryUsage.heapUsed > this.alertThresholds.memoryUsage * 1.5) {
                logger.error(`🚨 Critical memory usage: ${memoryUsageMB}MB - forcing garbage collection`);
                if (global.gc) {
                    global.gc();
                }
                this.emit('criticalMemoryUsage', metric);
            }
        }
        // CPU usage alert
        if (metric.cpuUsage > this.alertThresholds.cpuUsage) {
            logger.warn(`⚠️ High CPU usage: ${metric.cpuUsage.toFixed(2)}%`);
            this.emit('highCpuUsage', metric);
        }
        // Event loop delay alert
        if (metric.eventLoopDelay > this.alertThresholds.eventLoopDelay) {
            logger.warn(`⚠️ High event loop delay: ${metric.eventLoopDelay}ms`);
            this.emit('highEventLoopDelay', metric);
        }
        // Monitor active handles for resource leaks
        if (metric.activeHandles > 1000) {
            logger.warn(`⚠️ High number of active handles: ${metric.activeHandles}`);
            this.emit('highActiveHandles', metric);
        }
    }
    /**
     * Get enhanced performance statistics with detailed breakdowns
     */
    getStats() {
        const now = Date.now();
        const last5Minutes = now - 5 * 60 * 1000;
        const last1Hour = now - 60 * 60 * 1000;
        // API metrics for last 5 minutes
        const recentApiMetrics = this.apiMetrics.filter(m => m.timestamp > last5Minutes);
        const avgResponseTime = recentApiMetrics.length > 0
            ? recentApiMetrics.reduce((sum, m) => sum + m.responseTime, 0) / recentApiMetrics.length
            : 0;
        // Calculate percentiles for response times
        const responseTimes = recentApiMetrics.map(m => m.responseTime).sort((a, b) => a - b);
        const p50 = this.calculatePercentile(responseTimes, 50);
        const p95 = this.calculatePercentile(responseTimes, 95);
        const p99 = this.calculatePercentile(responseTimes, 99);
        // Group by endpoint for detailed analysis
        const endpointStats = this.groupMetricsByEndpoint(recentApiMetrics);
        // System metrics for last 5 minutes
        const recentSystemMetrics = this.systemMetrics.filter(m => m.timestamp > last5Minutes);
        const avgMemoryUsage = recentSystemMetrics.length > 0
            ? recentSystemMetrics.reduce((sum, m) => sum + m.memoryUsage.heapUsed, 0) / recentSystemMetrics.length
            : 0;
        // Calculate memory growth rate
        const memoryGrowthRate = this.calculateMemoryGrowthRate(recentSystemMetrics);
        // Error rate calculation
        const errorMetrics = recentApiMetrics.filter(m => m.statusCode >= 400);
        const errorRate = recentApiMetrics.length > 0
            ? (errorMetrics.length / recentApiMetrics.length) * 100
            : 0;
        // Request rate (requests per minute)
        const requestsLastHour = this.apiMetrics.filter(m => m.timestamp > last1Hour);
        const requestRate = requestsLastHour.length / 60; // per minute
        return {
            api: {
                averageResponseTime: Math.round(avgResponseTime),
                requestRate: Math.round(requestRate * 100) / 100,
                errorRate: Math.round(errorRate * 100) / 100,
                totalRequests: recentApiMetrics.length
            },
            system: {
                averageMemoryUsage: Math.round(avgMemoryUsage / 1024 / 1024), // MB
                currentMemoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024), // MB
                uptime: Math.round(process.uptime()),
                nodeVersion: process.version
            },
            thresholds: this.alertThresholds,
            timestamp: now
        };
    }
    /**
     * Get endpoint-specific statistics
     */
    getEndpointStats(endpoint) {
        const endpointMetrics = this.apiMetrics.filter(m => m.endpoint === endpoint);
        if (endpointMetrics.length === 0) {
            return null;
        }
        const responseTimes = endpointMetrics.map(m => m.responseTime);
        const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
        const minResponseTime = Math.min(...responseTimes);
        const maxResponseTime = Math.max(...responseTimes);
        // Calculate percentiles
        const sortedTimes = responseTimes.sort((a, b) => a - b);
        const p50 = sortedTimes[Math.floor(sortedTimes.length * 0.5)];
        const p95 = sortedTimes[Math.floor(sortedTimes.length * 0.95)];
        const p99 = sortedTimes[Math.floor(sortedTimes.length * 0.99)];
        return {
            endpoint,
            totalRequests: endpointMetrics.length,
            averageResponseTime: Math.round(avgResponseTime),
            minResponseTime,
            maxResponseTime,
            percentiles: {
                p50: Math.round(p50),
                p95: Math.round(p95),
                p99: Math.round(p99)
            }
        };
    }
    /**
     * Create performance middleware for Express
     */
    createMiddleware() {
        return (req, res, next) => {
            const startTime = Date.now();
            // Override res.end to capture response time
            const originalEnd = res.end;
            res.end = (...args) => {
                const responseTime = Date.now() - startTime;
                this.recordApiMetric({
                    endpoint: req.route?.path || req.path,
                    method: req.method,
                    responseTime,
                    statusCode: res.statusCode,
                    timestamp: Date.now(),
                    userAgent: req.get('User-Agent')
                });
                originalEnd.apply(res, args);
            };
            next();
        };
    }
    /**
     * Get real-time metrics for dashboard
     */
    getRealTimeMetrics() {
        const latest = this.systemMetrics[this.systemMetrics.length - 1];
        const recentApi = this.apiMetrics.slice(-10);
        return {
            system: latest || null,
            recentApiCalls: recentApi,
            stats: this.getStats()
        };
    }
    /**
     * Update alert thresholds
     */
    updateThresholds(thresholds) {
        this.alertThresholds = { ...this.alertThresholds, ...thresholds };
        logger.info('📊 Performance alert thresholds updated', this.alertThresholds);
    }
    /**
     * Calculate percentile for response times
     */
    calculatePercentile(sortedArray, percentile) {
        if (sortedArray.length === 0)
            return 0;
        const index = Math.floor((percentile / 100) * sortedArray.length);
        return sortedArray[Math.min(index, sortedArray.length - 1)] || 0;
    }
    /**
     * Group metrics by endpoint for detailed analysis
     */
    groupMetricsByEndpoint(metrics) {
        const grouped = metrics.reduce((acc, metric) => {
            const key = `${metric.method} ${metric.endpoint}`;
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(metric);
            return acc;
        }, {});
        const result = {};
        for (const [endpoint, endpointMetrics] of Object.entries(grouped)) {
            const responseTimes = endpointMetrics.map(m => m.responseTime);
            const avgResponseTime = responseTimes.reduce((sum, t) => sum + t, 0) / responseTimes.length;
            const errorCount = endpointMetrics.filter(m => m.statusCode >= 400).length;
            result[endpoint] = {
                requestCount: endpointMetrics.length,
                avgResponseTime: Math.round(avgResponseTime),
                errorRate: Math.round((errorCount / endpointMetrics.length) * 100) / 100,
                p95ResponseTime: Math.round(this.calculatePercentile(responseTimes.sort((a, b) => a - b), 95))
            };
        }
        return result;
    }
    /**
     * Calculate memory growth rate
     */
    calculateMemoryGrowthRate(metrics) {
        if (metrics.length < 2)
            return 0;
        const first = metrics[0];
        const last = metrics[metrics.length - 1];
        const timeDiff = (last.timestamp - first.timestamp) / 1000 / 60; // minutes
        const memoryDiff = (last.memoryUsage.heapUsed - first.memoryUsage.heapUsed) / 1024 / 1024; // MB
        return timeDiff > 0 ? memoryDiff / timeDiff : 0;
    }
    /**
     * Calculate performance trends
     */
    calculateTrends(since) {
        const hourlyMetrics = this.apiMetrics.filter(m => m.timestamp > since);
        const hourlySystemMetrics = this.systemMetrics.filter(m => m.timestamp > since);
        if (hourlyMetrics.length === 0)
            return null;
        const avgResponseTime = hourlyMetrics.reduce((sum, m) => sum + m.responseTime, 0) / hourlyMetrics.length;
        const avgMemory = hourlySystemMetrics.length > 0
            ? hourlySystemMetrics.reduce((sum, m) => sum + m.memoryUsage.heapUsed, 0) / hourlySystemMetrics.length / 1024 / 1024
            : 0;
        return {
            responseTimeTrend: Math.round(avgResponseTime),
            memoryTrend: Math.round(avgMemory),
            requestVolumeTrend: hourlyMetrics.length,
            errorTrend: hourlyMetrics.filter(m => m.statusCode >= 400).length
        };
    }
    /**
     * Get active alerts based on current metrics
     */
    getActiveAlerts() {
        const alerts = [];
        const latest = this.systemMetrics[this.systemMetrics.length - 1];
        if (latest) {
            if (latest.memoryUsage.heapUsed > this.alertThresholds.memoryUsage) {
                alerts.push(`High memory usage: ${Math.round(latest.memoryUsage.heapUsed / 1024 / 1024)}MB`);
            }
            if (latest.eventLoopDelay > this.alertThresholds.eventLoopDelay) {
                alerts.push(`High event loop delay: ${latest.eventLoopDelay}ms`);
            }
        }
        return alerts;
    }
    /**
     * Stop monitoring with comprehensive timer cleanup
     */
    stop() {
        // Clear all tracked timers
        for (const timer of this.timers) {
            clearInterval(timer);
        }
        this.timers.clear();
        // Clear specific timer
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        logger.info('📊 Performance monitoring stopped with timer cleanup');
    }
}
// Export singleton instance
export const performanceMonitorService = new PerformanceMonitorService();
//# sourceMappingURL=performanceMonitorService.js.map