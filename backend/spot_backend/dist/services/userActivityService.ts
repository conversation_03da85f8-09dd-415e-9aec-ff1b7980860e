import { logger } from '../utils/logger.js';
import { mobulaWebSocketService } from './mobulaWebSocketService.js';

interface UserSession {
  userId: string;
  sessionId: string;
  lastActivity: number;
  isOnPulsePage: boolean;
}

class UserActivityService {
  private sessions = new Map<string, UserSession>();
  private readonly SESSION_TIMEOUT = 300000; // 5 minutes
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up inactive sessions every minute
    this.cleanupInterval = setInterval(() => {
      this.cleanupInactiveSessions();
    }, 60000);

    this.setupGracefulShutdown();
  }

  /**
   * Register user activity on pulse page
   */
  public registerPulseActivity(userId: string, sessionId?: string): void {
    const actualSessionId = sessionId || `${userId}-${Date.now()}`;
    const now = Date.now();

    // Check if user already has an active pulse session
    const existingPulseSession = this.findUserPulseSession(userId);
    if (existingPulseSession && existingPulseSession !== actualSessionId) {
      logger.info(`User ${userId} already has active pulse session ${existingPulseSession}, updating existing session`);
      const existingSession = this.sessions.get(existingPulseSession);
      if (existingSession) {
        existingSession.lastActivity = now;
        return; // Don't create duplicate session
      }
    }

    const session: UserSession = {
      userId,
      sessionId: actualSessionId,
      lastActivity: now,
      isOnPulsePage: true
    };

    const existingSession = this.sessions.get(actualSessionId);
    const wasOnPulsePage = existingSession?.isOnPulsePage || false;

    this.sessions.set(actualSessionId, session);

    // If user wasn't on pulse page before, register with WebSocket service
    if (!wasOnPulsePage) {
      logger.info(`User ${userId} entered pulse page with session ${actualSessionId}`);
      mobulaWebSocketService.registerUser(userId);
    }

    logger.debug(`Updated pulse activity for user ${userId}, session ${actualSessionId}`);
  }

  /**
   * Register user leaving pulse page
   */
  public unregisterPulseActivity(userId: string, sessionId?: string): void {
    if (sessionId) {
      const session = this.sessions.get(sessionId);
      if (session && session.isOnPulsePage) {
        session.isOnPulsePage = false;
        session.lastActivity = Date.now();
        logger.info(`User ${userId} left pulse page`);
        mobulaWebSocketService.unregisterUser(userId);
      }
    } else {
      // If no session ID provided, remove all sessions for this user from pulse page
      let removedAny = false;
      for (const [, session] of this.sessions.entries()) {
        if (session.userId === userId && session.isOnPulsePage) {
          session.isOnPulsePage = false;
          session.lastActivity = Date.now();
          removedAny = true;
        }
      }
      if (removedAny) {
        logger.info(`User ${userId} left pulse page (all sessions)`);
        mobulaWebSocketService.unregisterUser(userId);
      }
    }
  }

  /**
   * Update general user activity (heartbeat)
   */
  public updateActivity(userId: string, sessionId?: string): void {
    const actualSessionId = sessionId || this.findUserSession(userId);
    
    if (actualSessionId) {
      const session = this.sessions.get(actualSessionId);
      if (session) {
        session.lastActivity = Date.now();
        logger.debug(`Updated activity for user ${userId}, session ${actualSessionId}`);
      }
    }
  }

  /**
   * Get active users on pulse page
   */
  public getActivePulseUsers(): string[] {
    const activeUsers: string[] = [];
    const now = Date.now();

    for (const session of this.sessions.values()) {
      if (session.isOnPulsePage && (now - session.lastActivity) < this.SESSION_TIMEOUT) {
        if (!activeUsers.includes(session.userId)) {
          activeUsers.push(session.userId);
        }
      }
    }

    return activeUsers;
  }

  /**
   * Get total active users
   */
  public getTotalActiveUsers(): number {
    const now = Date.now();
    const activeUserIds = new Set<string>();

    for (const session of this.sessions.values()) {
      if ((now - session.lastActivity) < this.SESSION_TIMEOUT) {
        activeUserIds.add(session.userId);
      }
    }

    return activeUserIds.size;
  }

  /**
   * Get activity statistics
   */
  public getActivityStats(): {
    totalSessions: number;
    activeSessions: number;
    pulsePageUsers: number;
    totalActiveUsers: number;
  } {
    const now = Date.now();
    let activeSessions = 0;
    let pulsePageUsers = 0;
    const activeUserIds = new Set<string>();

    for (const session of this.sessions.values()) {
      const isActive = (now - session.lastActivity) < this.SESSION_TIMEOUT;
      
      if (isActive) {
        activeSessions++;
        activeUserIds.add(session.userId);
        
        if (session.isOnPulsePage) {
          pulsePageUsers++;
        }
      }
    }

    return {
      totalSessions: this.sessions.size,
      activeSessions,
      pulsePageUsers,
      totalActiveUsers: activeUserIds.size
    };
  }

  /**
   * Find a user's active pulse session
   */
  private findUserPulseSession(userId: string): string | null {
    const now = Date.now();

    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.userId === userId &&
          session.isOnPulsePage &&
          (now - session.lastActivity) < this.SESSION_TIMEOUT) {
        return sessionId;
      }
    }

    return null;
  }

  /**
   * Find a user's active session
   */
  private findUserSession(userId: string): string | null {
    const now = Date.now();

    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.userId === userId && (now - session.lastActivity) < this.SESSION_TIMEOUT) {
        return sessionId;
      }
    }

    return null;
  }

  /**
   * Clean up inactive sessions
   */
  private cleanupInactiveSessions(): void {
    const now = Date.now();
    const sessionsToRemove: string[] = [];

    for (const [sessionId, session] of this.sessions.entries()) {
      if ((now - session.lastActivity) > this.SESSION_TIMEOUT) {
        sessionsToRemove.push(sessionId);
        
        // If user was on pulse page, unregister from WebSocket
        if (session.isOnPulsePage) {
          mobulaWebSocketService.unregisterUser(session.userId);
        }
      }
    }

    if (sessionsToRemove.length > 0) {
      logger.info(`Cleaning up ${sessionsToRemove.length} inactive sessions`);
      sessionsToRemove.forEach(sessionId => {
        this.sessions.delete(sessionId);
      });
    }
  }

  /**
   * Setup graceful shutdown
   */
  private setupGracefulShutdown(): void {
    const shutdown = () => {
      logger.info('Shutting down user activity service');
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
      }
    };

    process.on('SIGINT', shutdown);
    process.on('SIGTERM', shutdown);
  }
}

// Export singleton instance
export const userActivityService = new UserActivityService();
