{"version": 3, "file": "taskWorker.js", "sourceRoot": "", "sources": ["../../../src/services/workers/taskWorker.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AACxD,OAAO,KAAK,MAAM,OAAO,CAAC;AAiB1B,MAAM,UAAU;IACN,QAAQ,CAAS;IAEzB,YAAY,QAAgB;QAC1B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,UAAU,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,IAAgB,EAAE,EAAE;YAClD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,MAAoB,CAAC;YAEzB,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAC1C,MAAM,GAAG;oBACP,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,OAAO,EAAE,IAAI;oBACb,IAAI;oBACJ,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACtC,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,MAAM,GAAG;oBACP,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACtC,CAAC;YACJ,CAAC;YAED,UAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,IAAgB;QACxC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,eAAe;gBAClB,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,KAAK,UAAU;gBACb,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,KAAK,iBAAiB;gBACpB,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,KAAK,sBAAsB;gBACzB,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD;gBACE,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,IAAS;QACzC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QAEnC,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,eAAe;gBAClB,OAAO,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;YAC/C,KAAK,oBAAoB;gBACvB,OAAO,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,CAAC;YACpD,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;YAC7C;gBACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,SAAS,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,MAAW;QACtC,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,KAAK,EAAE,OAAO,GAAG,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,MAAM,CAAC;QAEpF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC;YAC3B,GAAG;YACH,MAAM;YACN,OAAO;YACP,MAAM;YACN,IAAI;YACJ,OAAO;SACR,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,MAAW;QAC7C,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;QAE9B,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACnD,KAAK,eAAe;gBAClB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACtD,KAAK,gBAAgB;gBACnB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAC5D,KAAK,gBAAgB;gBACnB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YACjE;gBACE,MAAM,IAAI,KAAK,CAAC,4BAA4B,IAAI,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,MAAW;QAClD,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;QAE9B,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,oBAAoB;gBACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACrC,KAAK,4BAA4B;gBAC/B,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;YAC7C;gBACE,MAAM,IAAI,KAAK,CAAC,sCAAsC,IAAI,EAAE,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED,gCAAgC;IACxB,KAAK,CAAC,wBAAwB,CAAC,MAAW;QAChD,uCAAuC;QACvC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,+CAA+C,EAAE;YAChF,MAAM,EAAE;gBACN,GAAG,EAAE,yBAAyB;gBAC9B,aAAa,EAAE,KAAK;gBACpB,kBAAkB,EAAE,IAAI;gBACxB,gBAAgB,EAAE,IAAI;gBACtB,mBAAmB,EAAE,IAAI;aAC1B;YACD,OAAO,EAAE,KAAK;SACf,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAEO,KAAK,CAAC,6BAA6B,CAAC,MAAW;QACrD,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;QAE3B,uCAAuC;QACvC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,0CAA0C,EAAE;YAC3E,MAAM,EAAE;gBACN,UAAU,EAAE,OAAO;gBACnB,KAAK,EAAE,EAAE;aACV;YACD,OAAO,EAAE;gBACP,aAAa,EAAE,UAAU,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE;aACtD;YACD,OAAO,EAAE,KAAK;SACf,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,8BAA8B,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACrE,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,MAAW;QAC9C,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;QAE7C,qCAAqC;QACrC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,0CAA0C,EAAE;YAC3E,MAAM,EAAE;gBACN,UAAU,EAAE,OAAO;gBACnB,KAAK;gBACL,SAAS;aACV;YACD,OAAO,EAAE;gBACP,aAAa,EAAE,UAAU,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE;aACtD;YACD,OAAO,EAAE,KAAK;SACf,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IACzE,CAAC;IAED,kCAAkC;IAC1B,UAAU,CAAC,MAAa,EAAE,MAAc;QAC9C,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,kBAAkB;oBACrB,OAAO,CAAC,CAAC,CAAC,2BAA2B,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,2BAA2B,IAAI,CAAC,CAAC,CAAC;gBACrF,KAAK,YAAY;oBACf,OAAO,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;gBACnD,KAAK,QAAQ;oBACX,OAAO,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC;gBACvD,KAAK,YAAY;oBACf,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;gBACrF;oBACE,OAAO,CAAC,CAAC;YACb,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,MAAa,EAAE,OAAY;QAC9C,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAC3B,IAAI,OAAO,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;gBAC3E,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,OAAO,CAAC,YAAY,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;gBAC3E,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,EAAE,CAAC;gBACzD,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,aAAa,CAAC,KAAU,EAAE,aAAqB;QACrD,QAAQ,aAAa,EAAE,CAAC;YACtB,KAAK,kBAAkB;gBACrB,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACrC,KAAK,uBAAuB;gBAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAC1C,KAAK,gBAAgB;gBACnB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACnC;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,QAAe,EAAE,eAAuB;QAC5D,QAAQ,eAAe,EAAE,CAAC;YACxB,KAAK,gBAAgB;gBACnB,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACzC,KAAK,oBAAoB;gBACvB,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC7C;gBACE,OAAO,QAAQ,CAAC;QACpB,CAAC;IACH,CAAC;IAED,uCAAuC;IAC/B,gBAAgB,CAAC,IAAS;QAChC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAErC,gCAAgC;QAChC,MAAM,SAAS,GAAG;YAChB,GAAG,EAAE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,EAAE,KAAK,CAAC;YACxD,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,EAAE,SAAS,CAAC;YACpE,MAAM,EAAE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,EAAE,QAAQ,CAAC;SAClE,CAAC;QAEF,OAAO;YACL,GAAG,SAAS;YACZ,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;YACvB,UAAU;SACX,CAAC;IACJ,CAAC;IAEO,wBAAwB,CAAC,IAAS;QACxC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED,iBAAiB;IACT,yBAAyB,CAAC,IAAS;QACzC,MAAM,KAAK,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAChD,OAAO,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC1B,EAAE,EAAE,MAAM;YACV,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACtD,MAAM,EAAE,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK;YAC5E,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC;YACrC,2BAA2B,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,cAAc,IAAI,CAAC;YAC9D,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,cAAc,IAAI,CAAC;YAC7C,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,WAAW,IAAI,CAAC;SAC7C,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,8BAA8B,CAAC,IAAS,EAAE,OAAe;QAC/D,oCAAoC;QACpC,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE;YACxC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE;YACvC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE;YACtC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE;SACpC,CAAC;IACJ,CAAC;IAEO,uBAAuB,CAAC,IAAS,EAAE,OAAe,EAAE,SAAiB;QAC3E,OAAO,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;IACzC,CAAC;IAEO,oBAAoB,CAAC,MAAa,EAAE,QAAgB;QAC1D,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;YAC9C,GAAG,KAAK;YACR,QAAQ;YACR,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,eAAe,CAAC,IAAS;QAC/B,uBAAuB;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,oBAAoB,CAAC,IAAS;QACpC,+BAA+B;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,aAAa,CAAC,IAAS;QAC7B,6BAA6B;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,gBAAgB,CAAC,QAAe;QACtC,oCAAoC;QACpC,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IACzE,CAAC;IAEO,oBAAoB,CAAC,QAAe;QAC1C,wCAAwC;QACxC,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAED,oBAAoB;AACpB,IAAI,UAAU,EAAE,QAAQ,EAAE,CAAC;IACzB,IAAI,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;AACtC,CAAC"}