import axios from 'axios';
import dotenv from 'dotenv';
import { deduplicateBySymbol } from '../utils/formatter.js'; // Adjusted import path

// Load environment variables
dotenv.config();

// Helper function to process Mobula API pairs into network highlights
function processNetworkPairs(pairs: any[], network: string): any {

  // Transform pairs into base tokens, filtering out ETH on BSC
  let baseTokens = pairs.map((item: any) => {
    const baseTokenKey = item.pair.baseToken;
    const token = item.pair[baseTokenKey];
    const baseToken = baseTokenKey === 'token0' ? item.pair["token1"] : item.pair["token0"];
  
    // Explicitly filter out tokens with symbol 'ETH' when network is BSC
    if (network === 'binance-smart-chain' && token.symbol?.toUpperCase() === 'ETH') {
        return null; // Mark for removal
    }

    return {
      id: token.address,
      name: token.name,
      symbol: token.symbol,
      image: token.logo || '',
      baseimage:baseToken.logo ||'',
      network: network,
      current_price: item.price || 0,
      price_change_percentage_24h: item.price_change_24h || 0,
      market_cap: token.circulatingSupply ? token.price * token.circulatingSupply : null,
      total_volume: item.volume_24h || 0,
      createdAt: item.created_at || null
    };
  }).filter(token => token !== null); // Remove null entries

  // Sort into trending, gainers, losers, new categories
  const trending = deduplicateBySymbol(
    [...baseTokens].sort((a, b) => b.total_volume - a.total_volume),
    20
  );

  const gainers = deduplicateBySymbol(
    [...baseTokens]
      .filter(token => token.price_change_percentage_24h > 0)
      .sort((a, b) => b.price_change_percentage_24h - a.price_change_percentage_24h),
    20
  );

  const losers = deduplicateBySymbol(
    [...baseTokens]
      .filter(token => token.price_change_percentage_24h < 0)
      .sort((a, b) => a.price_change_percentage_24h - b.price_change_percentage_24h),
    20
  );

  const recentlyAdded = deduplicateBySymbol(
    [...baseTokens]
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()),
    20
  );

  return {
    trending,
    gainers,
    losers,
    new: recentlyAdded
  };
}

/**
 * Fetch network highlights from Mobula API (Standard Endpoint)
 * @param network Network to filter by (binance-smart-chain or solana)
 * @param apiKey Mobula API key
 * @returns Object with trending, gainers, losers, and new tokens, or empty object on failure.
 */
export async function fetchNetworkHighlights(network: string, apiKey: string): Promise<any> {
  // We only support BSC and Solana networks directly for highlights
  if (network !== 'binance-smart-chain' && network !== 'solana') {
    return {
      trending: [],
      gainers: [],
      losers: [],
      new: []
    };
  }

  // Adjust network names to match Mobula API expected format
  let formattedNetwork = network;
  if (network === 'binance-smart-chain') {
    formattedNetwork = 'BNB Smart Chain (BEP20)';
  } else if (network === 'solana') {
    formattedNetwork = 'Solana';
  }

  try {

    // Use Mobula's Standard API endpoint
    const response = await axios.get('https://api.mobula.io/api/1/market/blockchain/pairs', {
      params: {
        sortBy: 'latest_trade_date',
        sortOrder: 'desc',
        limit: 50, // Standard limit for this endpoint might be lower
        blockchain: formattedNetwork
      },
      headers: {
        Authorization: `Bearer ${apiKey}`,
        accept: 'application/json'
      },
      timeout: 10000 // Standard timeout
    });

    const pairs = Array.isArray(response?.data?.data) ? response.data.data : [];

    if (pairs.length === 0) {
      // Return empty object if no pairs found
      return {
        trending: [],
        gainers: [],
        losers: [],
        new: []
      };
    } else {
      // Process data from Standard API response
      return processNetworkPairs(pairs, network);
    }

  } catch (error: any) {
    // Return empty object on error
    return {
      trending: [],
      gainers: [],
      losers: [],
      new: []
    };
  }
}