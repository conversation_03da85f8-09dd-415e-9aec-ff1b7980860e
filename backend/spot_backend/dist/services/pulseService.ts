import axios from 'axios';
import { logger } from '../utils/logger.js';
import { getApiKey } from './coinService.js';
import { mobulaWebSocketService } from './mobulaWebSocketService.js';
interface TokenData {
    id: string;
    name: string;
    baseimage:string;
    holders_count:string;
    symbol: string;
    image: string;
    network: string;
    current_price: number;
    pool_address:string;
    price_change_percentage_24h: number;
    market_cap: number ;
    total_volume: number;
    volume_24h: number;
    exchange_name:string;
    exchange_logo:string;
    bonding:number;
    trades_24h?: number; // Optional, may not be present in all data
    bonding_percentage?: number; // Optional, may not be present in all data
    supply:string;
    createdAt: string | null;
    liquidity: number;
  }
  

  export async function fetchPulseData(apiKey: string, blockchain: string): Promise<any> {
    // 1. Try WebSocket cache first
    const cachedData = mobulaWebSocketService.getCachedData();
  
    if (cachedData && mobulaWebSocketService.hasFreshData()) {
      logger.info('Returning fresh pulse data from WebSocket cache');
      return cachedData;
    }
  
    logger.warn('No fresh WebSocket data available - falling back to REST API');
  
    // 2. Fallback to REST API if WebSocket data is stale or unavailable
    try {
      const apiData = await fetchPulseDataFromAPI(apiKey, blockchain);
      logger.info('Successfully fetched pulse data from REST API fallback');
      return apiData;
    } catch (fallbackError: any) {
      logger.error(`Fallback to REST API failed: ${fallbackError.message}`);
      return {
        new: [],
        bonding: [],
        bonded: []
      };
    }
  }
  
  /**
   * Fetch pulse data from REST API (fallback method)
   */
  export async function fetchPulseDataFromAPI(apiKey: string, blockchain: string): Promise<any> {
    const url = 'https://api.mobula.io/api/1/pulse';
    const headers: Record<string, string> = {
      accept: 'application/json'
    };

    if (apiKey && apiKey.trim() !== '') {
      headers.Authorization = `Bearer ${apiKey}`;
    } else {
      logger.warn('No API key provided for Mobula Api Pulse data request');
    }

    logger.info(`Using Mobula Pulse Data REST API for ${blockchain || 'all blockchains'}`);
    const params: Record<string, any> = {
      blockchains: blockchain,
      factories: 'pumpfun',
    };

    if (blockchain && blockchain.trim() !== '') {
      params.blockchain = blockchain;
    }

    try {
      logger.info('Fetching pulse data from Mobula Pulse REST API for Solana PumpFun');

      const response = await axios.get(url, {
        params,
        headers,
      });

      logger.info(`Mobula Pulse REST API response status: ${response.status}`);

      const rawData = response.data?.data?.pumpfun?.['solana:solana'];
      if (!rawData) {
        logger.warn('No data returned from Mobula Pulse REST API for solana:pumpfun');
        return {
          new: [],
          bonding: [],
          bonded: []
        };
      }

      return processPulseData(rawData, 'solana');
    } catch (error: any) {
      logger.error(`Error fetching pulse data from REST API: ${error.message}`);

      // No fallback in WebSocket-only mode
      logger.error('REST API failed and no WebSocket fallback available');
      return {
        new: [],
        bonding: [],
        bonded: []
      };
    }
  }
  
  function mapPulseToken(item: any, network: string): TokenData {

    const baseTokenKey = item.pair.baseToken as 'token0' | 'token1';
    const baseToken = item.pair[baseTokenKey];

  
    return {
      id: baseToken.address,
      name: baseToken.name,
      symbol: baseToken.symbol,
      image: baseToken.logo || '',
      holders_count:item.holders_count || '',
      baseimage: baseToken.logo || '',
      network,
      current_price: item.price || 0,
      price_change_percentage_24h: item.price_change_24h || 0,
      market_cap: baseToken.marketCap ||0,
      total_volume: item.pair.volume24h || 0,
      exchange_name:item.pair.exchange?.name||'',
      pool_address:item.pair.address||'',
      exchange_logo:item.pair.exchange?.logo||'',
      supply:baseToken.totalSupply,
      bonding:item.bondingPercentage||0,
      volume_24h: item.volume_24h || 0,
      createdAt: item.created_at || null,
      trades_24h: item.trades_24h || 0,
      liquidity: item.pair.liquidity || 0
    };
  }
  
  
  export function processPulseData(rawData: any, network: string): any {
    const categories = ['new', 'bonding', 'bonded'];
  
    const result: Record<string, TokenData[]> = {
      new: [],
      bonding: [],
      bonded: []
    };
  
    for (const category of categories) {
      const tokens = rawData[category] || [];
      logger.info(`[${network}] Processing ${tokens.length} tokens in category: ${category}`);
  
      result[category] = tokens.map((item: any) => mapPulseToken(item, network));
    }
  
    logger.info(`[${network}] Final token counts — New: ${result.new.length}, Bonding: ${result.bonding.length}, Bonded: ${result.bonded.length}`);
  
    return result;
  }