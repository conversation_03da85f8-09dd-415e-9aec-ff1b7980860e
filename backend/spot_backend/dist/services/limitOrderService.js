import { supabase, setUserContext } from '../config/supabase.js';
import { logger } from '../utils/logger.js';
export class LimitOrderService {
    /**
     * Create a new limit order
     */
    async createLimitOrder(orderData) {
        try {
            // Set user context for RLS
            await setUserContext(orderData.user_id);
            // Validate order data
            const validationError = this.validateOrderData(orderData);
            if (validationError) {
                return { success: false, error: validationError };
            }
            // Insert the order
            const { data, error } = await supabase
                .from('limit_orders')
                .insert([orderData])
                .select()
                .single();
            if (error) {
                logger.error('Error creating limit order:', error);
                return { success: false, error: error.message };
            }
            logger.info(`Limit order created successfully: ${data.id}`);
            return { success: true, data };
        }
        catch (error) {
            logger.error('Unexpected error creating limit order:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Get limit orders for a user with filtering and pagination
     */
    async getLimitOrders(userId, filters = {}) {
        try {
            // Set user context for RLS
            await setUserContext(userId);
            let query = supabase
                .from('limit_orders')
                .select('*', { count: 'exact' });
            // Apply filters
            if (filters.status) {
                query = query.eq('status', filters.status);
            }
            if (filters.token_address) {
                query = query.eq('token_address', filters.token_address);
            }
            if (filters.direction) {
                query = query.eq('direction', filters.direction);
            }
            if (filters.dex_type) {
                query = query.eq('dex_type', filters.dex_type);
            }
            // Apply ordering
            const orderBy = filters.order_by || 'created_at';
            const orderDirection = filters.order_direction || 'desc';
            query = query.order(orderBy, { ascending: orderDirection === 'asc' });
            // Apply pagination
            if (filters.limit) {
                query = query.limit(filters.limit);
            }
            if (filters.offset) {
                query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
            }
            const { data, error, count } = await query;
            if (error) {
                logger.error('Error fetching limit orders:', error);
                return { success: false, error: error.message };
            }
            return { success: true, data: data || [], count: count || 0 };
        }
        catch (error) {
            logger.error('Unexpected error fetching limit orders:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Get a specific limit order by ID
     */
    async getLimitOrderById(userId, orderId) {
        try {
            // Set user context for RLS
            await setUserContext(userId);
            const { data, error } = await supabase
                .from('limit_orders')
                .select('*')
                .eq('id', orderId)
                .single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return { success: false, error: 'Order not found' };
                }
                logger.error('Error fetching limit order:', error);
                return { success: false, error: error.message };
            }
            return { success: true, data };
        }
        catch (error) {
            logger.error('Unexpected error fetching limit order:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Update a limit order
     */
    async updateLimitOrder(userId, orderId, updateData) {
        try {
            // Set user context for RLS
            await setUserContext(userId);
            const { data, error } = await supabase
                .from('limit_orders')
                .update(updateData)
                .eq('id', orderId)
                .select()
                .single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return { success: false, error: 'Order not found' };
                }
                logger.error('Error updating limit order:', error);
                return { success: false, error: error.message };
            }
            logger.info(`Limit order updated successfully: ${orderId}`);
            return { success: true, data };
        }
        catch (error) {
            logger.error('Unexpected error updating limit order:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Cancel a limit order
     */
    async cancelLimitOrder(userId, orderId) {
        return this.updateLimitOrder(userId, orderId, { status: 'cancelled' });
    }
    /**
     * Delete a limit order
     */
    async deleteLimitOrder(userId, orderId) {
        try {
            // Set user context for RLS
            await setUserContext(userId);
            const { error } = await supabase
                .from('limit_orders')
                .delete()
                .eq('id', orderId);
            if (error) {
                logger.error('Error deleting limit order:', error);
                return { success: false, error: error.message };
            }
            logger.info(`Limit order deleted successfully: ${orderId}`);
            return { success: true };
        }
        catch (error) {
            logger.error('Unexpected error deleting limit order:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Expire old orders
     */
    async expireOldOrders() {
        try {
            const { error } = await supabase.rpc('expire_old_orders');
            if (error) {
                logger.error('Error expiring old orders:', error);
                return { success: false, error: error.message };
            }
            // Get count of expired orders
            const { count } = await supabase
                .from('limit_orders')
                .select('*', { count: 'exact', head: true })
                .eq('status', 'expired')
                .gte('updated_at', new Date(Date.now() - 60000).toISOString()); // Last minute
            logger.info(`Expired ${count || 0} old orders`);
            return { success: true, expiredCount: count || 0 };
        }
        catch (error) {
            logger.error('Unexpected error expiring old orders:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Mark order as executed (used by monitoring service)
     */
    async markOrderExecuted(orderId, txHash, executionData) {
        try {
            const updateData = {
                status: 'executed',
                executed_at: new Date().toISOString(),
                execution_tx_hash: txHash
            };
            // Add execution data to error_message field as JSON (temporary storage)
            if (executionData) {
                updateData.error_message = JSON.stringify(executionData);
            }
            const { data, error } = await supabase
                .from('limit_orders')
                .update(updateData)
                .eq('id', orderId)
                .eq('status', 'pending') // Only update if still pending
                .select()
                .single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return { success: false, error: 'Order not found or already executed' };
                }
                logger.error('Error marking order as executed:', error);
                return { success: false, error: error.message };
            }
            logger.info(`Order ${orderId} marked as executed with tx: ${txHash}`);
            return { success: true, data };
        }
        catch (error) {
            logger.error('Unexpected error marking order as executed:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Mark order as failed (used by monitoring service)
     */
    async markOrderFailed(orderId, errorMessage) {
        try {
            const updateData = {
                status: 'cancelled', // Use cancelled status for failed executions
                error_message: `Execution failed: ${errorMessage}`
            };
            const { data, error } = await supabase
                .from('limit_orders')
                .update(updateData)
                .eq('id', orderId)
                .eq('status', 'pending') // Only update if still pending
                .select()
                .single();
            if (error) {
                if (error.code === 'PGRST116') {
                    return { success: false, error: 'Order not found or already processed' };
                }
                logger.error('Error marking order as failed:', error);
                return { success: false, error: error.message };
            }
            logger.info(`Order ${orderId} marked as failed: ${errorMessage}`);
            return { success: true, data };
        }
        catch (error) {
            logger.error('Unexpected error marking order as failed:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Get all pending orders (used by monitoring service)
     */
    async getPendingOrders() {
        try {
            const { data, error } = await supabase
                .from('limit_orders')
                .select('*')
                .eq('status', 'pending')
                .or('expires_at.is.null,expires_at.gt.now()') // Not expired
                .order('created_at', { ascending: true });
            if (error) {
                logger.error('Error fetching pending orders:', error);
                return { success: false, error: error.message };
            }
            logger.debug(`Fetched ${data?.length || 0} pending orders for monitoring`);
            return { success: true, data: data || [] };
        }
        catch (error) {
            logger.error('Unexpected error fetching pending orders:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Validate order data before creation
     */
    validateOrderData(orderData) {
        // Required fields validation
        const requiredFields = [
            'user_id', 'token_address', 'token_name', 'token_symbol',
            'pool_address', 'dex_type', 'direction', 'amount',
            'target_price', 'current_price', 'wallet_address', 'wallet_id'
        ];
        for (const field of requiredFields) {
            if (!orderData[field]) {
                return `Missing required field: ${field}`;
            }
        }
        // Numeric validations
        if (orderData.amount <= 0) {
            return 'Amount must be greater than 0';
        }
        if (orderData.target_price <= 0) {
            return 'Target price must be greater than 0';
        }
        if (orderData.current_price <= 0) {
            return 'Current price must be greater than 0';
        }
        // Market cap validations (optional fields)
        if (orderData.current_market_cap !== undefined && orderData.current_market_cap < 0) {
            return 'Current market cap must be non-negative';
        }
        if (orderData.target_market_cap !== undefined && orderData.target_market_cap < 0) {
            return 'Target market cap must be non-negative';
        }
        if (orderData.slippage < 0 || orderData.slippage > 1) {
            return 'Slippage must be between 0 and 1';
        }
        // Enum validations
        if (!['buy', 'sell'].includes(orderData.direction)) {
            return 'Direction must be either "buy" or "sell"';
        }
        if (!['pumpfun', 'pumpswap', 'launchlab'].includes(orderData.dex_type)) {
            return 'Invalid dex_type. Must be one of: pumpfun, pumpswap, launchlab';
        }
        // Address format validation (basic check)
        if (orderData.token_address.length < 32 || orderData.token_address.length > 44) {
            return 'Invalid token address format';
        }
        if (orderData.wallet_address.length < 32 || orderData.wallet_address.length > 44) {
            return 'Invalid wallet address format';
        }
        return null;
    }
}
// Export singleton instance
export const limitOrderService = new LimitOrderService();
//# sourceMappingURL=limitOrderService.js.map