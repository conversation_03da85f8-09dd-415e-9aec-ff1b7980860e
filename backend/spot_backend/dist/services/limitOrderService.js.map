{"version": 3, "file": "limitOrderService.js", "sourceRoot": "", "sources": ["../../src/services/limitOrderService.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,QAAQ,EAKR,cAAc,EACf,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAE5C,MAAM,OAAO,iBAAiB;IAE5B;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,SAA+B;QACpD,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAExC,sBAAsB;YACtB,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAC1D,IAAI,eAAe,EAAE,CAAC;gBACpB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC;YACpD,CAAC;YAED,mBAAmB;YACnB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;iBACnC,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,CAAC,CAAC,SAAS,CAAC,CAAC;iBACnB,MAAM,EAAE;iBACR,MAAM,EAAE,CAAC;YAEZ,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBACnD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;YAClD,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,qCAAqC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YAC5D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAEjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,UAA6B,EAAE;QAClE,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,cAAc,CAAC,MAAM,CAAC,CAAC;YAE7B,IAAI,KAAK,GAAG,QAAQ;iBACjB,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;YAEnC,gBAAgB;YAChB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAC7C,CAAC;YAED,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,eAAe,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACtB,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;YACnD,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;YACjD,CAAC;YAED,iBAAiB;YACjB,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,IAAI,YAAY,CAAC;YACjD,MAAM,cAAc,GAAG,OAAO,CAAC,eAAe,IAAI,MAAM,CAAC;YACzD,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,cAAc,KAAK,KAAK,EAAE,CAAC,CAAC;YAEtE,mBAAmB;YACnB,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBAClB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACrC,CAAC;YAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YAClF,CAAC;YAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,KAAK,CAAC;YAE3C,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;gBACpD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;YAClD,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC;QAEhE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,OAAe;QACrD,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,cAAc,CAAC,MAAM,CAAC,CAAC;YAE7B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;iBACnC,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,CAAC,GAAG,CAAC;iBACX,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC;iBACjB,MAAM,EAAE,CAAC;YAEZ,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAC9B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC;gBACtD,CAAC;gBACD,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBACnD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;YAClD,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAEjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,OAAe,EAAE,UAAgC;QACtF,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,cAAc,CAAC,MAAM,CAAC,CAAC;YAE7B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;iBACnC,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,CAAC,UAAU,CAAC;iBAClB,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC;iBACjB,MAAM,EAAE;iBACR,MAAM,EAAE,CAAC;YAEZ,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAC9B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC;gBACtD,CAAC;gBACD,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBACnD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;YAClD,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,qCAAqC,OAAO,EAAE,CAAC,CAAC;YAC5D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAEjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,OAAe;QACpD,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,OAAe;QACpD,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,cAAc,CAAC,MAAM,CAAC,CAAC;YAE7B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;iBAC7B,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,EAAE;iBACR,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAErB,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBACnD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;YAClD,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,qCAAqC,OAAO,EAAE,CAAC,CAAC;YAC5D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAE3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAMD;;OAEG;IACH,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC;YACH,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YAE1D,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;gBAClD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;YAClD,CAAC;YAED,8BAA8B;YAC9B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;iBAC7B,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;iBAC3C,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;iBACvB,GAAG,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,cAAc;YAEhF,MAAM,CAAC,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC;QAErD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC7D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACrB,OAAe,EACf,MAAc,EACd,aAAmB;QAEnB,IAAI,CAAC;YACH,MAAM,UAAU,GAAyB;gBACvC,MAAM,EAAE,UAAU;gBAClB,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACrC,iBAAiB,EAAE,MAAM;aAC1B,CAAC;YAEF,wEAAwE;YACxE,IAAI,aAAa,EAAE,CAAC;gBAClB,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC3D,CAAC;YAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;iBACnC,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,CAAC,UAAU,CAAC;iBAClB,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC;iBACjB,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,+BAA+B;iBACvD,MAAM,EAAE;iBACR,MAAM,EAAE,CAAC;YAEZ,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAC9B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,qCAAqC,EAAE,CAAC;gBAC1E,CAAC;gBACD,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;gBACxD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;YAClD,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,SAAS,OAAO,gCAAgC,MAAM,EAAE,CAAC,CAAC;YACtE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAEjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACnB,OAAe,EACf,YAAoB;QAEpB,IAAI,CAAC;YACH,MAAM,UAAU,GAAyB;gBACvC,MAAM,EAAE,WAAW,EAAE,6CAA6C;gBAClE,aAAa,EAAE,qBAAqB,YAAY,EAAE;aACnD,CAAC;YAEF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;iBACnC,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,CAAC,UAAU,CAAC;iBAClB,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC;iBACjB,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,+BAA+B;iBACvD,MAAM,EAAE;iBACR,MAAM,EAAE,CAAC;YAEZ,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAC9B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,sCAAsC,EAAE,CAAC;gBAC3E,CAAC;gBACD,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;gBACtD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;YAClD,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,SAAS,OAAO,sBAAsB,YAAY,EAAE,CAAC,CAAC;YAClE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QAEjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;iBACnC,IAAI,CAAC,cAAc,CAAC;iBACpB,MAAM,CAAC,GAAG,CAAC;iBACX,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;iBACvB,EAAE,CAAC,wCAAwC,CAAC,CAAC,cAAc;iBAC3D,KAAK,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAE5C,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;gBACtD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;YAClD,CAAC;YAED,MAAM,CAAC,KAAK,CAAC,WAAW,IAAI,EAAE,MAAM,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC3E,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC;QAE7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,SAA+B;QACvD,6BAA6B;QAC7B,MAAM,cAAc,GAAG;YACrB,SAAS,EAAE,eAAe,EAAE,YAAY,EAAE,cAAc;YACxD,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ;YACjD,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,WAAW;SAC/D,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,SAAS,CAAC,KAAmC,CAAC,EAAE,CAAC;gBACpD,OAAO,2BAA2B,KAAK,EAAE,CAAC;YAC5C,CAAC;QACH,CAAC;QAED,sBAAsB;QACtB,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC1B,OAAO,+BAA+B,CAAC;QACzC,CAAC;QAED,IAAI,SAAS,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC;YAChC,OAAO,qCAAqC,CAAC;QAC/C,CAAC;QAED,IAAI,SAAS,CAAC,aAAa,IAAI,CAAC,EAAE,CAAC;YACjC,OAAO,sCAAsC,CAAC;QAChD,CAAC;QAED,2CAA2C;QAC3C,IAAI,SAAS,CAAC,kBAAkB,KAAK,SAAS,IAAI,SAAS,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;YACnF,OAAO,yCAAyC,CAAC;QACnD,CAAC;QAED,IAAI,SAAS,CAAC,iBAAiB,KAAK,SAAS,IAAI,SAAS,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;YACjF,OAAO,wCAAwC,CAAC;QAClD,CAAC;QAED,IAAI,SAAS,CAAC,QAAQ,GAAG,CAAC,IAAI,SAAS,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YACrD,OAAO,kCAAkC,CAAC;QAC5C,CAAC;QAED,mBAAmB;QACnB,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC;YACnD,OAAO,0CAA0C,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvE,OAAO,gEAAgE,CAAC;QAC1E,CAAC;QAED,0CAA0C;QAC1C,IAAI,SAAS,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,IAAI,SAAS,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAC/E,OAAO,8BAA8B,CAAC;QACxC,CAAC;QAED,IAAI,SAAS,CAAC,cAAc,CAAC,MAAM,GAAG,EAAE,IAAI,SAAS,CAAC,cAAc,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACjF,OAAO,+BAA+B,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED,4BAA4B;AAC5B,MAAM,CAAC,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC"}