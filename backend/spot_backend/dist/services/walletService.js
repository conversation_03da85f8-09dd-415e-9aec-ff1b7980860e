import axios from 'axios';
import { ethers } from 'ethers';
import { Connection, PublicKey, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { logger } from '../utils/logger.js';
import tokenRegistry from './tokenRegistry.js';
// Standard ERC20 ABI (only for balanceOf and decimals functions)
const ERC20_ABI = [
    {
        "constant": true,
        "inputs": [{ "name": "owner", "type": "address" }],
        "name": "balanceOf",
        "outputs": [{ "name": "balance", "type": "uint256" }],
        "type": "function"
    },
    {
        "constant": true,
        "inputs": [],
        "name": "decimals",
        "outputs": [{ "name": "", "type": "uint8" }],
        "type": "function"
    }
];
class WalletService {
    alchemyEthUrl;
    alchemyPolyUrl;
    solanaRpcUrl;
    bscRpcUrl;
    bscRpcUrls;
    constructor() {
        // Load environment variables
        this.alchemyEthUrl = process.env.ALCHEMY_API_KEY_ETH || 'https://eth-mainnet.g.alchemy.com/v2/sQLdRXYdXOU8D27i-pXh2SgQGBFJsNX6';
        this.alchemyPolyUrl = process.env.ALCHEMY_API_KEY_POLY || 'https://polygon-mainnet.g.alchemy.com/v2/sQLdRXYdXOU8D27i-pXh2SgQGBFJsNX6';
        this.solanaRpcUrl = process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com';
        this.bscRpcUrl = process.env.BSC_RPC_URL || 'https://bsc-dataseed1.binance.org/';
        // Add fallback options for BSC RPC URLs if the primary one fails
        this.bscRpcUrls = [
            'https://bsc-dataseed1.binance.org/',
            'https://bsc-dataseed2.binance.org/',
            'https://bsc-dataseed3.binance.org/',
            'https://bsc-dataseed4.binance.org/',
            'https://bsc-dataseed.binance.org/',
        ];
        if (!this.alchemyEthUrl || !this.alchemyPolyUrl) {
            logger.warn('Alchemy API URLs not configured for all networks. Some functionality may be limited.');
        }
    }
    /**
     * Identify wallet type (Ethereum, Binance Smart Chain, or Solana) based on address format
     */
    identifyWalletType(walletAddress) {
        try {
            // Ethereum and BSC addresses are 42 characters long (including '0x')
            if (walletAddress.startsWith('0x') && walletAddress.length === 42) {
                // We can't differentiate between ETH and BSC addresses by format alone
                // They use the same format, so we'll return a generic 'ethereum' type
                // which can be used for both ETH and BSC
                return 'ethereum';
            }
            // Try to create a Solana PublicKey (will throw if invalid)
            new PublicKey(walletAddress);
            return 'solana';
        }
        catch (error) {
            logger.error(`Error identifying wallet type: ${error}`);
            return 'unknown';
        }
    }
    /**
     * Get token pairs from DexScreener
     */
    async getTokenPairsFromDexScreener(tokenAddress) {
        try {
            const response = await axios.get(`https://api.dexscreener.com/latest/dex/tokens/${tokenAddress}`);
            if (response.data && response.data.pairs) {
                logger.info(`Found ${response.data.pairs.length} pairs for token ${tokenAddress}`);
                return response.data.pairs;
            }
            return [];
        }
        catch (error) {
            logger.error(`Error fetching token pairs from DexScreener: ${error.message}`);
            throw new Error(`Failed to fetch token pairs: ${error.message}`);
        }
    }
    /**
     * Get wallet token balances for Ethereum/EVM chains using direct API calls
     */
    async getEthereumWalletBalance(walletAddress, tokenAddresses) {
        try {
            // Extract and validate the Alchemy URL
            let alchemyUrl = this.alchemyEthUrl;
            // If it's just the API key, construct the full URL
            if (!alchemyUrl.startsWith('http')) {
                alchemyUrl = `https://eth-mainnet.g.alchemy.com/v2/${alchemyUrl}`;
            }
            // Add logging to see what URL we're using (without showing the full API key)
            const maskedUrl = alchemyUrl.replace(/\/v2\/([^\/]+)/, '/v2/****');
            logger.info(`Using Alchemy URL: ${maskedUrl}`);
            logger.info(`Using wallet address: ${walletAddress}`);
            const balances = [];
            // Test with a simpler JSONRPC call first
            try {
                const testResponse = await axios({
                    method: 'post',
                    url: alchemyUrl,
                    data: {
                        jsonrpc: "2.0",
                        id: 0,
                        method: "eth_blockNumber",
                        params: []
                    },
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                logger.info(`Test call successful: ${JSON.stringify(testResponse.data)}`);
            }
            catch (testError) {
                logger.error(`Test call failed: ${testError.message}`);
                if (testError.response) {
                    logger.error(`Response status: ${testError.response.status}`);
                    logger.error(`Response data: ${JSON.stringify(testError.response.data)}`);
                }
                // Continue anyway to try the main call
            }
            // Get ETH balance using direct axios call
            const balanceRequest = {
                jsonrpc: "2.0",
                id: 1,
                method: "eth_getBalance",
                params: [walletAddress, "latest"]
            };
            logger.info(`Sending balance request: ${JSON.stringify(balanceRequest)}`);
            const ethBalanceResponse = await axios({
                method: 'post',
                url: alchemyUrl,
                data: balanceRequest,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });
            logger.info(`ETH balance response: ${JSON.stringify(ethBalanceResponse.data)}`);
            if (ethBalanceResponse.data && ethBalanceResponse.data.result) {
                const ethBalanceHex = ethBalanceResponse.data.result;
                const ethBalance = parseInt(ethBalanceHex, 16) / 1e18; // Convert from wei to ETH
                logger.info(`ETH balance: ${ethBalance}`);
                balances.push({
                    token: 'ETH',
                    tokenAddress: 'native',
                    balance: ethBalance.toString(),
                    decimals: 18
                });
            }
            // For ERC20 tokens, we need to make contract calls
            for (const tokenAddress of tokenAddresses) {
                if (tokenAddress === 'native')
                    continue; // Skip native token as we already have it
                try {
                    // Call the ERC20 balanceOf method using JSON-RPC
                    const data = ethers.utils.defaultAbiCoder.encode(['address'], [walletAddress]).slice(2);
                    const balanceOfCallData = {
                        jsonrpc: '2.0',
                        id: 1,
                        method: 'eth_call',
                        params: [
                            {
                                to: tokenAddress,
                                data: '0x70a08231' + data // balanceOf function signature
                            },
                            'latest'
                        ]
                    };
                    logger.info(`Sending token balance request for ${tokenAddress}`);
                    const balanceResponse = await axios({
                        method: 'post',
                        url: alchemyUrl,
                        data: balanceOfCallData,
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });
                    if (balanceResponse.data && balanceResponse.data.result) {
                        const balance = ethers.BigNumber.from(balanceResponse.data.result);
                        // Now get the decimals
                        const decimalsCallData = {
                            jsonrpc: '2.0',
                            id: 1,
                            method: 'eth_call',
                            params: [
                                {
                                    to: tokenAddress,
                                    data: '0x313ce567' // decimals function signature
                                },
                                'latest'
                            ]
                        };
                        const decimalsResponse = await axios({
                            method: 'post',
                            url: alchemyUrl,
                            data: decimalsCallData,
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json'
                            }
                        });
                        let decimals = 18; // Default to 18 decimals
                        if (decimalsResponse.data && decimalsResponse.data.result) {
                            decimals = parseInt(decimalsResponse.data.result, 16);
                        }
                        // Format the balance with proper decimals
                        const formattedBalance = ethers.utils.formatUnits(balance, decimals);
                        logger.info(`Token ${tokenAddress} balance: ${formattedBalance}`);
                        balances.push({
                            token: tokenAddress, // We would typically get the symbol, but that requires additional API calls
                            tokenAddress: tokenAddress,
                            balance: formattedBalance,
                            decimals: decimals
                        });
                    }
                }
                catch (error) {
                    logger.warn(`Error fetching balance for token ${tokenAddress}: ${error.message}`);
                    if (error.response) {
                        logger.warn(`Response status: ${error.response.status}`);
                        logger.warn(`Response data: ${JSON.stringify(error.response.data)}`);
                    }
                    // Continue with other tokens
                }
            }
            return balances;
        }
        catch (error) {
            logger.error(`Error fetching Ethereum wallet balances: ${error.message}`);
            if (error.response) {
                logger.error(`Response status: ${error.response.status}`);
                logger.error(`Response data: ${JSON.stringify(error.response.data)}`);
            }
            throw new Error(`Failed to fetch Ethereum wallet balances: ${error.message}`);
        }
    }
    /**
     * Get wallet token balances for Solana using Solana RPC
     */
    async getSolanaWalletBalance(walletAddress, tokenAddresses) {
        try {
            const connection = new Connection(this.solanaRpcUrl, 'confirmed');
            const publicKey = new PublicKey(walletAddress);
            const balances = [];
            // Get SOL balance
            const solBalance = await connection.getBalance(publicKey);
            balances.push({
                token: 'SOL',
                tokenAddress: 'So11111111111111111111111111111111111111112',
                balance: (solBalance / 1e9).toString(),
                decimals: 9
            });
            // Get SPL token balances
            // This gets all token accounts owned by the wallet
            const tokenAccounts = await connection.getParsedTokenAccountsByOwner(publicKey, { programId: TOKEN_PROGRAM_ID });
            for (const item of tokenAccounts.value) {
                const accountInfo = item.account.data.parsed.info;
                const tokenAddress = accountInfo.mint;
                // If we have specific token addresses to check, filter by those
                if (tokenAddresses.length > 0 && !tokenAddresses.includes(tokenAddress) && !tokenAddresses.includes('all')) {
                    continue;
                }
                const tokenBalance = accountInfo.tokenAmount;
                balances.push({
                    token: tokenAddress, // We would get symbols in a production app
                    tokenAddress: tokenAddress,
                    balance: tokenBalance.uiAmountString || tokenBalance.uiAmount || '0',
                    decimals: tokenBalance.decimals
                });
            }
            return balances;
        }
        catch (error) {
            logger.error(`Error fetching Solana wallet balances: ${error.message}`);
            throw new Error(`Failed to fetch Solana wallet balances: ${error.message}`);
        }
    }
    /**
     * Get wallet token balances for Binance Smart Chain using BSC RPC
     */
    async getBscWalletBalance(walletAddress, tokenAddresses) {
        try {
            // Use BSC RPC URL with specific network options to disable ENS
            const provider = new ethers.providers.JsonRpcProvider(this.bscRpcUrl, {
                name: 'binance',
                chainId: 56,
                ensAddress: undefined
            });
            const balances = [];
            // Get BNB balance
            const bnbBalance = await provider.getBalance(walletAddress);
            balances.push({
                token: 'BNB',
                tokenAddress: 'native',
                balance: ethers.utils.formatEther(bnbBalance),
                decimals: 18,
                symbol: 'BNB',
                name: 'Binance Coin'
            });
            // Get BEP-20 token balances
            for (const tokenAddress of tokenAddresses) {
                if (tokenAddress === 'native')
                    continue; // Skip native token as we already have it
                try {
                    const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, provider);
                    const balance = await tokenContract.balanceOf(walletAddress);
                    const decimals = await tokenContract.decimals();
                    const formattedBalance = ethers.utils.formatUnits(balance, decimals);
                    logger.info(`Token ${tokenAddress} balance: ${formattedBalance}`);
                    balances.push({
                        token: tokenAddress,
                        tokenAddress: tokenAddress,
                        balance: formattedBalance,
                        decimals: decimals
                    });
                }
                catch (error) {
                    logger.warn(`Error fetching balance for BSC token ${tokenAddress}: ${error.message}`);
                    // Continue with other tokens
                }
            }
            return balances;
        }
        catch (error) {
            logger.error(`Error fetching BSC wallet balances: ${error.message}`);
            throw new Error(`Failed to fetch BSC wallet balances: ${error.message}`);
        }
    }
    /**
     * Get wallet balances based on DexScreener pairs
     */
    async getWalletBalancesByPairs(walletAddress, tokenAddress, network) {
        try {
            // Identify wallet type
            let walletType = this.identifyWalletType(walletAddress);
            // If network is explicitly specified, override the detected type
            if (network) {
                if (['ethereum', 'eth'].includes(network.toLowerCase())) {
                    walletType = 'ethereum';
                }
                else if (['binance', 'bsc', 'bnb'].includes(network.toLowerCase())) {
                    walletType = 'bsc';
                }
                else if (['solana', 'sol'].includes(network.toLowerCase())) {
                    walletType = 'solana';
                }
            }
            if (walletType === 'unknown') {
                throw new Error('Invalid wallet address format');
            }
            // Get token pairs from DEX Screener
            const pairs = await this.getTokenPairsFromDexScreener(tokenAddress);
            if (!pairs || pairs.length === 0) {
                return {
                    walletType,
                    walletAddress,
                    tokenAddress,
                    balances: [],
                    pairs: []
                };
            }
            // Extract quote tokens from pairs
            const quoteTokens = pairs.map(pair => {
                return {
                    address: pair.quoteToken?.address || '',
                    symbol: pair.quoteToken?.symbol || '',
                    name: pair.quoteToken?.name || ''
                };
            }).filter(token => token.address && token.address !== 'native'); // Filter out empty addresses and native tokens
            // Remove duplicates based on address
            const uniqueQuoteTokens = quoteTokens.reduce((acc, token) => {
                if (!acc.find(t => t.address === token.address)) {
                    acc.push(token);
                }
                return acc;
            }, []);
            logger.info(`Found ${uniqueQuoteTokens.length} unique quote tokens for ${walletAddress}`);
            // Get wallet balances based on wallet type
            let balances = [];
            const tokenAddresses = uniqueQuoteTokens.map(token => token.address);
            tokenAddresses.push(tokenAddress); // Also check the original token
            if (walletType === 'ethereum') {
                balances = await this.getEthereumWalletBalance(walletAddress, tokenAddresses);
            }
            else if (walletType === 'bsc') {
                balances = await this.getBscWalletBalance(walletAddress, tokenAddresses);
            }
            else if (walletType === 'solana') {
                balances = await this.getSolanaWalletBalance(walletAddress, tokenAddresses);
            }
            // Enrich balances with token information from pairs
            const enrichedBalances = balances.map(balance => {
                const matchingToken = uniqueQuoteTokens.find(t => t.address.toLowerCase() === balance.tokenAddress.toLowerCase());
                if (matchingToken) {
                    return {
                        ...balance,
                        symbol: matchingToken.symbol,
                        name: matchingToken.name
                    };
                }
                return balance;
            });
            return {
                walletType,
                walletAddress,
                tokenAddress,
                network: network || walletType,
                balances: enrichedBalances,
                pairs: pairs.map(p => ({
                    pairAddress: p.pairAddress,
                    dexId: p.dexId,
                    baseToken: p.baseToken,
                    quoteToken: p.quoteToken,
                    priceUsd: p.priceUsd,
                    volume24h: p.volume24h
                }))
            };
        }
        catch (error) {
            logger.error(`Error in getWalletBalancesByPairs: ${error.message}`);
            throw new Error(`Failed to get wallet balances: ${error.message}`);
        }
    }
    /**
     * Get token metadata from multiple sources using TokenRegistry
     */
    async getTokenMetadata(chainId, tokenAddress) {
        return await tokenRegistry.getTokenMetadata(chainId, tokenAddress);
    }
    /**
     * Get all wallet balances across chains using our dynamic token discovery
     */
    async getAllWalletBalances(walletAddress) {
        logger.info(`Checking balances across all chains for wallet: ${walletAddress}`);
        try {
            // Use the comprehensive token discovery method with production settings
            // Changed to include both dust balances and NFTs to ensure we find everything
            const discoveryResults = await this.discoverAllTokens(walletAddress, {
                includeDust: false, // Include all tokens, even with small balances
                includeNFTs: false, // Include NFTs in case they're important
                includeHistory: false // Include transaction history if API keys available
            });
            // Return the results in the expected format for backwards compatibility
            return {
                walletAddress,
                walletType: discoveryResults.walletType,
                balances: discoveryResults.balances
            };
        }
        catch (error) {
            logger.error(`Error in getAllWalletBalances: ${error.message}`);
            // If comprehensive check fails, fall back to basic checks
            const walletType = this.identifyWalletType(walletAddress);
            const results = {
                ethereum: [],
                bsc: [],
                solana: []
            };
            try {
                // Basic check for native tokens and common tokens
                await this.checkDirectBalances(walletAddress, results);
                // Always try additional checks regardless of initial results
                if (walletType === 'ethereum') {
                    try {
                        await this.checkEthereumTokens(walletAddress, results);
                    }
                    catch (error) {
                        logger.error(`Basic balance checks failed: ${error.message}`);
                    }
                }
                else if (walletType === 'solana') {
                    try {
                        await this.checkSolanaBalances(walletAddress, results);
                    }
                    catch (error) {
                        logger.error(`Solana balance checks failed: ${error.message}`);
                    }
                }
                // Return consolidated results
                return {
                    walletAddress,
                    walletType,
                    balances: {
                        ethereum: results.ethereum.length > 0 ? results.ethereum : null,
                        bsc: results.bsc.length > 0 ? results.bsc : null,
                        solana: results.solana.length > 0 ? results.solana : null
                    }
                };
            }
            catch (fallbackError) {
                logger.error(`All balance checks failed: ${fallbackError.message}`);
                throw new Error(`Failed to get wallet balances: ${error.message}`);
            }
        }
    }
    /**
     * Check direct balances of native tokens and common tokens
     */
    async checkDirectBalances(walletAddress, results) {
        try {
            const walletType = this.identifyWalletType(walletAddress);
            // Check for ETH/BNB
            if (walletType === 'ethereum') {
                try {
                    // Try BSC first (since the wallet format is the same)
                    const bscProvider = new ethers.providers.JsonRpcProvider(this.bscRpcUrl);
                    const bnbBalance = await bscProvider.getBalance(walletAddress);
                    const formattedBnbBalance = ethers.utils.formatEther(bnbBalance);
                    if (parseFloat(formattedBnbBalance) > 0) {
                        const metadata = await this.getTokenMetadata('bsc', 'native');
                        results.bsc.push({
                            token: 'BNB',
                            tokenAddress: 'native',
                            balance: formattedBnbBalance,
                            decimals: 18,
                            name: metadata.name || 'Binance Coin',
                            symbol: metadata.symbol || 'BNB',
                            image: metadata.image || 'https://assets.coingecko.com/coins/images/825/small/bnb-icon2_2x.png'
                        });
                    }
                    // Then check ETH
                    const ethProvider = new ethers.providers.JsonRpcProvider(this.alchemyEthUrl);
                    const ethBalance = await ethProvider.getBalance(walletAddress);
                    const formattedEthBalance = ethers.utils.formatEther(ethBalance);
                    if (parseFloat(formattedEthBalance) > 0) {
                        const metadata = await this.getTokenMetadata('ethereum', 'native');
                        results.ethereum.push({
                            token: 'ETH',
                            tokenAddress: 'native',
                            balance: formattedEthBalance,
                            decimals: 18,
                            name: metadata.name || 'Ethereum',
                            symbol: metadata.symbol || 'ETH',
                            image: metadata.image || 'https://assets.coingecko.com/coins/images/279/small/ethereum.png'
                        });
                    }
                }
                catch (error) {
                    logger.error(`Error checking ETH/BNB balances: ${error.message}`);
                }
            }
            else if (walletType === 'solana') {
                try {
                    const connection = new Connection(this.solanaRpcUrl);
                    const publicKey = new PublicKey(walletAddress);
                    const solBalance = await connection.getBalance(publicKey);
                    const formattedSolBalance = (solBalance / LAMPORTS_PER_SOL).toString();
                    if (parseFloat(formattedSolBalance) > 0) {
                        const metadata = await this.getTokenMetadata('solana', 'native');
                        results.solana.push({
                            token: 'SOL',
                            tokenAddress: 'So11111111111111111111111111111111111111112',
                            balance: formattedSolBalance,
                            decimals: 9,
                            name: metadata.name || 'Solana',
                            symbol: metadata.symbol || 'SOL',
                            image: metadata.image || 'https://assets.coingecko.com/coins/images/4128/small/solana.png'
                        });
                    }
                }
                catch (error) {
                    logger.error(`Error checking SOL balance: ${error.message}`);
                }
            }
        }
        catch (error) {
            logger.error(`Error in checkDirectBalances: ${error.message}`);
        }
    }
    /**
     * Check Ethereum token balances
     */
    async checkEthereumTokens(walletAddress, results) {
        const ethTimeout = 15000; // 15 seconds timeout for Alchemy call
        const alchemyUrl = this.alchemyEthUrl.includes('alchemyapi.io') || this.alchemyEthUrl.includes('g.alchemy.com')
            ? this.alchemyEthUrl
            : `https://eth-mainnet.g.alchemy.com/v2/${this.alchemyEthUrl}`; // Construct URL if only key is provided
        // --- Helper functions defined outside the Promise executor --- 
        // They still close over walletAddress, results, alchemyUrl, this.getTokenMetadata
        const checkNativeBalance = async (opts) => {
            try {
                const provider = new ethers.providers.JsonRpcProvider(alchemyUrl);
                const ethBalance = await provider.getBalance(walletAddress);
                const formattedEthBalance = ethers.utils.formatEther(ethBalance);
                if (parseFloat(formattedEthBalance) > 0 || opts.includeDust) {
                    results.ethereum.push({
                        token: 'ETH', tokenAddress: 'native', balance: formattedEthBalance, decimals: 18,
                        name: 'Ethereum', symbol: 'ETH',
                        image: 'https://assets.coingecko.com/coins/images/279/small/ethereum.png'
                    });
                }
            }
            catch (nativeErr) {
                logger.error(`Failed to fetch native ETH balance: ${nativeErr instanceof Error ? nativeErr.message : String(nativeErr)}`);
            }
        };
        const checkTokenBalances = async (opts) => {
            try {
                const response = await axios.post(alchemyUrl, {
                    jsonrpc: '2.0',
                    id: 1,
                    method: 'alchemy_getTokenBalances',
                    params: [walletAddress, 'erc20']
                }, {
                    headers: { 'Content-Type': 'application/json' },
                    timeout: ethTimeout - 1000
                });
                if (response.data?.error) {
                    throw new Error(`Alchemy API Error: ${response.data.error.message}`);
                }
                if (response.data?.result?.tokenBalances) {
                    const tokenBalances = response.data.result.tokenBalances;
                    const metadataPromises = [];
                    for (const token of tokenBalances) {
                        const balanceHex = token.tokenBalance;
                        if (!balanceHex || balanceHex === '0x00')
                            continue;
                        const tokenAddress = token.contractAddress;
                        metadataPromises.push((async () => {
                            try {
                                const balance = ethers.BigNumber.from(balanceHex);
                                const metadata = await this.getTokenMetadata('ethereum', tokenAddress);
                                const decimals = metadata?.decimals ?? 18;
                                const formattedBalance = ethers.utils.formatUnits(balance, decimals);
                                if (parseFloat(formattedBalance) > 0 || opts.includeDust) {
                                    if (!results.ethereum.some((t) => t.tokenAddress.toLowerCase() === tokenAddress.toLowerCase())) {
                                        results.ethereum.push({
                                            token: metadata?.symbol || tokenAddress.substring(0, 6),
                                            tokenAddress: tokenAddress,
                                            balance: formattedBalance,
                                            decimals: decimals,
                                            name: metadata?.name || 'Unknown Token',
                                            symbol: metadata?.symbol || '???',
                                            image: metadata?.image || 'https://assets.coingecko.com/coins/images/12632/small/generic-token-icon.png'
                                        });
                                    }
                                }
                            }
                            catch (metaErr) {
                                logger.warn(`Failed to process metadata/balance for ETH token ${tokenAddress}: ${metaErr instanceof Error ? metaErr.message : String(metaErr)}`);
                            }
                        })());
                    }
                    await Promise.allSettled(metadataPromises);
                }
            }
            catch (alchemyError) {
                logger.error(`Alchemy getTokenBalances failed for ETH: ${alchemyError instanceof Error ? alchemyError.message : String(alchemyError)}`);
                throw alchemyError;
            }
        };
        // --- End Helper functions ---
        // Now, use the helpers within the Promise executor for timeout handling
        await new Promise(async (resolve, reject) => {
            const timeoutHandle = setTimeout(() => reject(new Error('ETH discovery timeout via Alchemy')), ethTimeout);
            try {
                // Execute helpers in parallel, passing options explicitly
                const checks = [
                    checkNativeBalance({ includeDust: false }),
                    checkTokenBalances({ includeDust: false })
                ];
                await Promise.allSettled(checks);
                resolve(undefined);
            }
            catch (error) {
                reject(error);
            }
            finally {
                clearTimeout(timeoutHandle);
            }
        });
    }
    /**
     * Check Solana token balances
     */
    async checkSolanaBalances(walletAddress, results) {
        try {
            const connection = new Connection(this.solanaRpcUrl);
            const publicKey = new PublicKey(walletAddress);
            // Get all token accounts owned by this wallet
            const tokenAccounts = await connection.getParsedTokenAccountsByOwner(publicKey, { programId: TOKEN_PROGRAM_ID });
            for (const account of tokenAccounts.value) {
                const parsedInfo = account.account.data.parsed.info;
                const mintAddress = parsedInfo.mint;
                const amount = parsedInfo.tokenAmount.uiAmountString || '0';
                if (parseFloat(amount) > 0) {
                    const metadata = await this.getTokenMetadata('solana', mintAddress);
                    results.solana.push({
                        token: metadata.symbol || mintAddress,
                        tokenAddress: mintAddress,
                        balance: amount,
                        decimals: parsedInfo.tokenAmount.decimals,
                        name: metadata.name || '',
                        symbol: metadata.symbol || '',
                        image: metadata.image || ''
                    });
                }
            }
        }
        catch (error) {
            logger.error(`Error in checkSolanaBalances: ${error.message}`);
        }
    }
    /**
     * Get comprehensive list of all tokens in a wallet
     */
    async discoverAllTokens(walletAddress, options) {
        logger.info(`Discovering all tokens for wallet: ${walletAddress} with options: ${JSON.stringify(options)}`);
        const walletType = this.identifyWalletType(walletAddress);
        if (walletType === 'unknown') {
            throw new Error('Invalid wallet address');
        }
        const results = {
            ethereum: [],
            bsc: [],
            solana: []
        };
        try {
            // Discover tokens from all supported chains
            const scanPromises = [];
            if (walletType === 'ethereum') {
                scanPromises.push(this.discoverBscTokens(walletAddress, options, results));
                scanPromises.push(this.discoverEthTokens(walletAddress, options, results));
            }
            else if (walletType === 'solana') {
                scanPromises.push(this.discoverSolanaTokens(walletAddress, options, results));
            }
            await Promise.allSettled(scanPromises);
            // Perform final deduplication of tokens
            // This is needed because parallel methods might add the same token
            this.deduplicateTokens(results);
            // Remove any empty chains
            const filteredResults = {};
            for (const chain of Object.keys(results)) {
                if (results[chain].length > 0) {
                    filteredResults[chain] = results[chain];
                }
                else {
                    filteredResults[chain] = null;
                }
            }
            return {
                walletAddress,
                walletType,
                balances: filteredResults
            };
        }
        catch (error) {
            logger.error(`Error in discoverAllTokens: ${error.message}`);
            throw new Error(`Failed to discover all tokens: ${error.message}`);
        }
    }
    /**
     * Deduplicate tokens in the result set
     */
    deduplicateTokens(results) {
        // Deduplicate tokens for each chain
        for (const chain of Object.keys(results)) {
            if (!results[chain] || !Array.isArray(results[chain]))
                continue;
            const seen = new Map();
            // First pass - keep the token with the most complete information
            for (const token of results[chain]) {
                if (!token.tokenAddress)
                    continue;
                const normalizedAddress = token.tokenAddress.toLowerCase();
                const existing = seen.get(normalizedAddress);
                if (!existing) {
                    // First time seeing this token
                    seen.set(normalizedAddress, token);
                }
                else {
                    // We've seen this token before - keep the one with more complete info
                    const completeToken = this.getMostCompleteToken(existing, token);
                    seen.set(normalizedAddress, completeToken);
                }
            }
            // Replace the original array with deduplicated tokens
            results[chain] = Array.from(seen.values());
            logger.info(`Deduplicated ${chain} tokens: found ${results[chain].length} unique tokens`);
        }
    }
    /**
     * Compare two token objects and return the one with more complete information
     */
    getMostCompleteToken(token1, token2) {
        // Create a scoring system to determine which token has more complete info
        const score1 = this.getTokenCompletenessScore(token1);
        const score2 = this.getTokenCompletenessScore(token2);
        // Return the token with the higher score
        return score1 >= score2 ? token1 : token2;
    }
    /**
     * Calculate a score for how complete a token's information is
     */
    getTokenCompletenessScore(token) {
        let score = 0;
        // Basic required fields
        if (token.tokenAddress)
            score += 1;
        if (token.balance && parseFloat(token.balance) > 0)
            score += 2;
        if (token.decimals)
            score += 1;
        // Metadata fields
        if (token.name && token.name.length > 0)
            score += 1;
        if (token.symbol && token.symbol.length > 0)
            score += 1;
        if (token.image && token.image.length > 0 && !token.image.includes('generic-token-icon'))
            score += 2;
        return score;
    }
    /**
     * Discover Ethereum tokens
     */
    async discoverEthTokens(walletAddress, options, results) {
        const ethTimeout = 15000; // 15 seconds timeout for Alchemy call
        const alchemyUrl = this.alchemyEthUrl.includes('alchemyapi.io') || this.alchemyEthUrl.includes('g.alchemy.com')
            ? this.alchemyEthUrl
            : `https://eth-mainnet.g.alchemy.com/v2/${this.alchemyEthUrl}`; // Construct URL if only key is provided
        // --- Helper functions defined outside the Promise executor --- 
        // They still close over walletAddress, results, alchemyUrl, this.getTokenMetadata
        const checkNativeBalance = async (opts) => {
            try {
                const provider = new ethers.providers.JsonRpcProvider(alchemyUrl);
                const ethBalance = await provider.getBalance(walletAddress);
                const formattedEthBalance = ethers.utils.formatEther(ethBalance);
                if (parseFloat(formattedEthBalance) > 0 || opts.includeDust) {
                    results.ethereum.push({
                        token: 'ETH', tokenAddress: 'native', balance: formattedEthBalance, decimals: 18,
                        name: 'Ethereum', symbol: 'ETH',
                        image: 'https://assets.coingecko.com/coins/images/279/small/ethereum.png'
                    });
                }
            }
            catch (nativeErr) {
                logger.error(`Failed to fetch native ETH balance: ${nativeErr instanceof Error ? nativeErr.message : String(nativeErr)}`);
            }
        };
        const checkTokenBalances = async (opts) => {
            try {
                const response = await axios.post(alchemyUrl, {
                    jsonrpc: '2.0',
                    id: 1,
                    method: 'alchemy_getTokenBalances',
                    params: [walletAddress, 'erc20']
                }, {
                    headers: { 'Content-Type': 'application/json' },
                    timeout: ethTimeout - 1000
                });
                if (response.data?.error) {
                    throw new Error(`Alchemy API Error: ${response.data.error.message}`);
                }
                if (response.data?.result?.tokenBalances) {
                    const tokenBalances = response.data.result.tokenBalances;
                    const metadataPromises = [];
                    for (const token of tokenBalances) {
                        const balanceHex = token.tokenBalance;
                        if (!balanceHex || balanceHex === '0x00')
                            continue;
                        const tokenAddress = token.contractAddress;
                        metadataPromises.push((async () => {
                            try {
                                const balance = ethers.BigNumber.from(balanceHex);
                                const metadata = await this.getTokenMetadata('ethereum', tokenAddress);
                                const decimals = metadata?.decimals ?? 18;
                                const formattedBalance = ethers.utils.formatUnits(balance, decimals);
                                if (parseFloat(formattedBalance) > 0 || opts.includeDust) {
                                    if (!results.ethereum.some((t) => t.tokenAddress.toLowerCase() === tokenAddress.toLowerCase())) {
                                        results.ethereum.push({
                                            token: metadata?.symbol || tokenAddress.substring(0, 6),
                                            tokenAddress: tokenAddress,
                                            balance: formattedBalance,
                                            decimals: decimals,
                                            name: metadata?.name || 'Unknown Token',
                                            symbol: metadata?.symbol || '???',
                                            image: metadata?.image || 'https://assets.coingecko.com/coins/images/12632/small/generic-token-icon.png'
                                        });
                                    }
                                }
                            }
                            catch (metaErr) {
                                logger.warn(`Failed to process metadata/balance for ETH token ${tokenAddress}: ${metaErr instanceof Error ? metaErr.message : String(metaErr)}`);
                            }
                        })());
                    }
                    await Promise.allSettled(metadataPromises);
                }
            }
            catch (alchemyError) {
                logger.error(`Alchemy getTokenBalances failed for ETH: ${alchemyError instanceof Error ? alchemyError.message : String(alchemyError)}`);
                throw alchemyError;
            }
        };
        // --- End Helper functions ---
        // Now, use the helpers within the Promise executor for timeout handling
        await new Promise(async (resolve, reject) => {
            const timeoutHandle = setTimeout(() => reject(new Error('ETH discovery timeout via Alchemy')), ethTimeout);
            try {
                // Execute helpers in parallel, passing options explicitly
                const checks = [
                    checkNativeBalance(options),
                    checkTokenBalances(options)
                ];
                await Promise.allSettled(checks);
                resolve(undefined);
            }
            catch (error) {
                reject(error);
            }
            finally {
                clearTimeout(timeoutHandle);
            }
        });
    }
    /**
     * Discover Solana tokens
     */
    async discoverSolanaTokens(walletAddress, options, results) {
        const solTimeout = 12000; // 12 seconds for Solana
        await new Promise(async (resolve, reject) => {
            const timeoutHandle = setTimeout(() => reject(new Error('Solana discovery timeout')), solTimeout);
            try {
                const connection = new Connection(this.solanaRpcUrl, 'confirmed');
                const publicKey = new PublicKey(walletAddress);
                const checks = [];
                // 1. Get Native SOL Balance
                checks.push((async (opts) => {
                    try {
                        const solBalance = await connection.getBalance(publicKey);
                        const formattedSolBalance = (solBalance / LAMPORTS_PER_SOL).toString();
                        if (parseFloat(formattedSolBalance) > 0 || opts.includeDust) { // Use passed options
                            const metadata = await this.getTokenMetadata('solana', 'native');
                            results.solana.push({
                                token: 'SOL', tokenAddress: 'So11111111111111111111111111111111111111112',
                                balance: formattedSolBalance, decimals: 9, name: metadata?.name || 'Solana',
                                symbol: metadata?.symbol || 'SOL',
                                image: metadata?.image || 'https://assets.coingecko.com/coins/images/4128/small/solana.png'
                            });
                        }
                    }
                    catch (nativeErr) {
                        logger.error(`Failed to fetch native SOL balance: ${nativeErr instanceof Error ? nativeErr.message : String(nativeErr)}`);
                    }
                })(options)); // Immediately invoke
                // 2. Get SPL Token Accounts (Process Top N - e.g., 50)
                checks.push((async (opts) => {
                    try {
                        const tokenAccounts = await connection.getParsedTokenAccountsByOwner(publicKey, { programId: TOKEN_PROGRAM_ID });
                        // Limit processing to avoid potential slowdowns with wallets holding thousands of NFTs/tokens
                        const accountsToProcess = tokenAccounts.value.slice(0, 50);
                        const metadataPromises = [];
                        for (const item of accountsToProcess) {
                            const accountInfo = item.account.data.parsed.info;
                            const mintAddress = accountInfo.mint;
                            const tokenAmount = accountInfo.tokenAmount;
                            const balance = tokenAmount.uiAmountString || tokenAmount.uiAmount || '0';
                            const decimals = tokenAmount.decimals;
                            if (parseFloat(balance) > 0 || opts.includeDust) { // Use passed options
                                metadataPromises.push((async () => {
                                    try {
                                        const metadata = await this.getTokenMetadata('solana', mintAddress).catch(() => null);
                                        if (!results.solana.some((t) => t.tokenAddress === mintAddress)) {
                                            results.solana.push({
                                                token: metadata?.symbol || mintAddress.substring(0, 6),
                                                tokenAddress: mintAddress,
                                                balance: balance,
                                                decimals: decimals,
                                                name: metadata?.name || 'Unknown SPL Token',
                                                symbol: metadata?.symbol || '???',
                                                image: metadata?.image || 'https://assets.coingecko.com/coins/images/12632/small/generic-token-icon.png'
                                            });
                                        }
                                    }
                                    catch (tokenError) {
                                        logger.warn(`Error processing SPL token ${mintAddress}: ${tokenError instanceof Error ? tokenError.message : String(tokenError)}`);
                                    }
                                })());
                            }
                        }
                        await Promise.allSettled(metadataPromises);
                    }
                    catch (splError) {
                        logger.error(`Failed to fetch SPL token accounts: ${splError instanceof Error ? splError.message : String(splError)}`);
                    }
                })(options)); // Immediately invoke
                await Promise.allSettled(checks);
                resolve(undefined);
            }
            catch (error) {
                reject(error);
            }
            finally {
                clearTimeout(timeoutHandle);
            }
        });
    }
    /**
     * Check BSC balances using dynamic token discovery
     * without relying on hardcoded token lists
     */
    async checkBscTokens(walletAddress, provider, results) {
        try {
            // Get list of popular tokens from PancakeSwap API instead of hardcoding
            const response = await axios.get('https://tokens.pancakeswap.finance/pancakeswap-extended.json');
            if (!response.data || !response.data.tokens) {
                throw new Error('Failed to fetch token list from PancakeSwap');
            }
            // Take the top 30 tokens by market cap (usually the most popular)
            const popularTokens = response.data.tokens.slice(0, 30);
            // Check each token balance
            for (const token of popularTokens) {
                try {
                    const tokenContract = new ethers.Contract(token.address, ERC20_ABI, provider);
                    const balance = await tokenContract.balanceOf(walletAddress);
                    const decimals = token.decimals || await tokenContract.decimals();
                    const formattedBalance = ethers.utils.formatUnits(balance, decimals);
                    if (parseFloat(formattedBalance) > 0.00001) {
                        // Get metadata for this token from our dynamic registry
                        const metadata = await this.getTokenMetadata('bsc', token.address);
                        results.bsc.push({
                            token: metadata.symbol || token.symbol,
                            tokenAddress: token.address,
                            balance: formattedBalance,
                            decimals: decimals,
                            name: metadata.name || token.name,
                            symbol: metadata.symbol || token.symbol,
                            image: metadata.image || token.logoURI || 'https://assets.coingecko.com/coins/images/12632/small/generic-token-icon.png'
                        });
                        logger.info(`Found BSC token: ${token.symbol} with balance: ${formattedBalance}`);
                    }
                }
                catch (error) {
                    logger.warn(`Error checking balance for token ${token.symbol}: ${error.message}`);
                }
            }
        }
        catch (error) {
            logger.error(`Error checking popular BSC tokens: ${error.message}`);
        }
    }
    /**
     * Discover BSC tokens using dynamic token discovery without hardcoded lists
     */
    async discoverBscTokens(walletAddress, options, results) {
        const bscTimeout = 18000; // 18 seconds for BSC checks
        await new Promise(async (resolve, reject) => {
            const timeoutHandle = setTimeout(() => reject(new Error('BSC discovery timeout')), bscTimeout);
            try {
                let connectedRpcUrl = null;
                let provider = null;
                for (const rpcUrl of this.bscRpcUrls) {
                    try {
                        // Use a shorter connection timeout for testing each RPC
                        const testProvider = new ethers.providers.JsonRpcProvider({ url: rpcUrl, timeout: 3000 });
                        await testProvider.getNetwork();
                        provider = new ethers.providers.JsonRpcProvider(rpcUrl); // Re-create without short timeout
                        connectedRpcUrl = rpcUrl;
                        logger.debug(`Connected to BSC RPC: ${connectedRpcUrl}`);
                        break;
                    }
                    catch (err) {
                        logger.warn(`Failed connection to BSC RPC: ${rpcUrl}`);
                        continue;
                    }
                }
                if (!provider || !connectedRpcUrl) {
                    throw new Error('Failed to connect to any BSC RPC endpoint');
                }
                const checks = [];
                // 1. Get Native BNB Balance
                checks.push((async (opts) => {
                    try {
                        const bnbBalance = await provider.getBalance(walletAddress);
                        const formattedBnbBalance = ethers.utils.formatEther(bnbBalance);
                        if (parseFloat(formattedBnbBalance) > 0 || opts.includeDust) { // Use passed options
                            const metadata = await this.getTokenMetadata('bsc', 'native');
                            results.bsc.push({
                                token: 'BNB', tokenAddress: 'native', balance: formattedBnbBalance, decimals: 18,
                                name: metadata?.name || 'Binance Coin', symbol: metadata?.symbol || 'BNB',
                                image: metadata?.image || 'https://assets.coingecko.com/coins/images/825/small/bnb-icon2_2x.png'
                            });
                        }
                    }
                    catch (nativeErr) {
                        logger.error(`Failed to fetch native BNB balance: ${nativeErr instanceof Error ? nativeErr.message : String(nativeErr)}`);
                    }
                })(options)); // Immediately invoke
                // 2. Try alchemy_getTokenBalances for BSC (May require specific Alchemy BSC endpoint)
                // Note: Standard BSC RPC might not support this method. This is speculative.
                checks.push((async (opts) => {
                    try {
                        const response = await axios.post(connectedRpcUrl, {
                            jsonrpc: '2.0',
                            id: 2, // Different ID from ETH call
                            method: 'alchemy_getTokenBalances',
                            params: [walletAddress, 'erc20']
                        }, {
                            headers: { 'Content-Type': 'application/json' },
                            timeout: bscTimeout - 2000
                        });
                        if (response.data?.error) {
                            // Log error but don't fail entirely, maybe fallback below
                            logger.warn(`alchemy_getTokenBalances failed on BSC RPC ${connectedRpcUrl}: ${response.data.error.message}. Falling back if possible.`);
                            // Potentially trigger BscScan API check here if needed
                            if (process.env.BSCSCAN_API_KEY) {
                                await this.checkBscScanApiTokens(walletAddress, results.bsc, opts.includeDust);
                            }
                            return;
                        }
                        if (response.data?.result?.tokenBalances) {
                            const tokenBalances = response.data.result.tokenBalances;
                            const metadataPromises = [];
                            for (const token of tokenBalances) {
                                const balanceHex = token.tokenBalance;
                                if (!balanceHex || balanceHex === '0x00')
                                    continue;
                                const tokenAddress = token.contractAddress;
                                metadataPromises.push((async () => {
                                    try {
                                        const balance = ethers.BigNumber.from(balanceHex);
                                        const metadata = await this.getTokenMetadata('bsc', tokenAddress);
                                        const decimals = metadata?.decimals ?? 18;
                                        const formattedBalance = ethers.utils.formatUnits(balance, decimals);
                                        if (parseFloat(formattedBalance) > 0 || opts.includeDust) { // Use passed options
                                            if (!results.bsc.some((t) => t.tokenAddress.toLowerCase() === tokenAddress.toLowerCase())) {
                                                results.bsc.push({
                                                    token: metadata?.symbol || tokenAddress.substring(0, 6),
                                                    tokenAddress: tokenAddress,
                                                    balance: formattedBalance,
                                                    decimals: decimals,
                                                    name: metadata?.name || 'Unknown BEP20',
                                                    symbol: metadata?.symbol || '???',
                                                    image: metadata?.image || 'https://assets.coingecko.com/coins/images/12632/small/generic-token-icon.png'
                                                });
                                            }
                                        }
                                    }
                                    catch (metaErr) {
                                        logger.warn(`Failed to process metadata/balance for BSC token ${tokenAddress}: ${metaErr instanceof Error ? metaErr.message : String(metaErr)}`);
                                    }
                                })());
                            }
                            await Promise.allSettled(metadataPromises);
                        }
                        else {
                            // If method exists but returns no balances, potentially fallback
                            logger.warn(`alchemy_getTokenBalances on BSC RPC ${connectedRpcUrl} returned no balances. Falling back if possible.`);
                            if (process.env.BSCSCAN_API_KEY) {
                                await this.checkBscScanApiTokens(walletAddress, results.bsc, opts.includeDust);
                            }
                        }
                    }
                    catch (alchemyError) {
                        logger.warn(`Error calling alchemy_getTokenBalances on BSC RPC ${connectedRpcUrl}: ${alchemyError instanceof Error ? alchemyError.message : String(alchemyError)}. Falling back if possible.`);
                        // Fallback to BscScan API if Alchemy method fails
                        if (process.env.BSCSCAN_API_KEY) {
                            await this.checkBscScanApiTokens(walletAddress, results.bsc, opts.includeDust);
                        }
                    }
                })(options)); // Immediately invoke
                // NOTE: Removed the explicit common token checks and PancakeSwap checks
                // Relying on alchemy_getTokenBalances or the BscScan API fallback for comprehensive list
                await Promise.allSettled(checks);
                resolve(undefined);
            }
            catch (error) {
                // Catch errors like failing to connect to *any* RPC
                reject(error);
            }
            finally {
                clearTimeout(timeoutHandle);
            }
        });
    }
    // New Helper Function to use BscScan API as fallback
    async checkBscScanApiTokens(walletAddress, resultsArray, includeDust) {
        logger.info(`Using BscScan API as fallback to find token balances for ${walletAddress}`);
        try {
            const apiKey = process.env.BSCSCAN_API_KEY;
            // Use the account balance multi endpoint if available, otherwise fallback to individual checks
            // For simplicity here, we'll use the transaction list to find potential tokens, 
            // similar to previous history checks but as a primary discovery method ONLY if Alchemy fails.
            const historyLimit = 50; // Check tokens from last 50 transfers
            const url = `https://api.bscscan.com/api?module=account&action=tokentx&address=${walletAddress}&startblock=0&endblock=********&sort=desc&page=1&offset=${historyLimit}&apikey=${apiKey}`;
            const response = await axios.get(url, { timeout: 10000 }); // 10s timeout
            if (response.data?.status === '1' && response.data.result) {
                const tokensToCheck = new Set();
                response.data.result.forEach((tx) => tokensToCheck.add(tx.contractAddress.toLowerCase()));
                logger.debug(`Found ${tokensToCheck.size} potential tokens from BscScan history for ${walletAddress}`);
                let provider = null;
                for (const rpcUrl of this.bscRpcUrls) { // Find a working provider again for balance checks
                    try {
                        provider = new ethers.providers.JsonRpcProvider({ url: rpcUrl, timeout: 3000 });
                        await provider.getNetwork();
                        break;
                    }
                    catch {
                        provider = null;
                    }
                }
                if (!provider) {
                    logger.error('BscScan fallback: Could not connect to any BSC RPC for balance checks.');
                    return;
                }
                const balanceChecks = Array.from(tokensToCheck).map(addr => this.checkEvmTokenBalance('bsc', provider, walletAddress, addr, resultsArray, includeDust));
                await Promise.allSettled(balanceChecks);
            }
            else {
                logger.warn(`BscScan API token history call failed or returned no results for ${walletAddress}: ${response.data?.message}`);
            }
        }
        catch (error) {
            logger.error(`BscScan API fallback check failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    // Removed discoverSolanaTokens, checkEvmTokenBalance, checkEtherscanHistory, checkBscScanHistory, checkPancakeSwapTopTokens
    // They need to be re-added or adjusted based on the new structure
    // Let's re-add checkEvmTokenBalance as it's used by the BscScan fallback
    async checkEvmTokenBalance(chain, provider, walletAddress, tokenAddress, resultsArray, includeDust) {
        try {
            const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, provider);
            const [balance, decimals] = await Promise.all([
                tokenContract.balanceOf(walletAddress),
                tokenContract.decimals().catch(() => 18)
            ]);
            const formattedBalance = ethers.utils.formatUnits(balance, decimals);
            if (parseFloat(formattedBalance) > 0 || includeDust) {
                const metadata = await this.getTokenMetadata(chain, tokenAddress);
                if (!resultsArray.some(t => t.tokenAddress.toLowerCase() === tokenAddress.toLowerCase())) {
                    resultsArray.push({
                        token: metadata?.symbol || tokenAddress.substring(0, 6),
                        tokenAddress: tokenAddress,
                        balance: formattedBalance,
                        decimals: decimals,
                        name: metadata?.name || (chain === 'bsc' ? 'Unknown BEP20' : 'Unknown Token'),
                        symbol: metadata?.symbol || '???',
                        image: metadata?.image || 'https://assets.coingecko.com/coins/images/12632/small/generic-token-icon.png'
                    });
                }
            }
        }
        catch (error) {
            // Reduce log noise for individual token check failures during fallback
            // logger.warn(`Error checking ${chain} token ${tokenAddress}: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}
export default new WalletService();
//# sourceMappingURL=walletService.js.map