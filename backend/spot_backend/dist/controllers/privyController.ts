import { Request, Response } from 'express';
import axios from 'axios';
import { logger } from '../utils/logger.js';

interface PrivyUserResponse {
  id: string;
  linked_accounts: Array<{
    id: string;
    type: string;
    chain_type?: string;
    address?: string;
    verified_at: string;
  }>;
}

interface SolanaWalletInfo {
  id: string;
  address: string;
  verified_at: string;
}

/**
 * Get Solana wallet information for a user from Privy API
 * @route POST /api/wallet/privy-solana-info
 * @desc Retrieves Solana wallet ID and address from Privy's REST API
 * @access Public
 */
export const getPrivySolanaWalletInfo = async (req: Request, res: Response) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    // Clean user ID - remove 'did:privy:' prefix if present
    let cleanUserId = userId;
    if (userId.startsWith('did:privy:')) {
      cleanUserId = userId.replace('did:privy:', '');
      logger.info(`Cleaned user ID from ${userId} to ${cleanUserId}`);
    }

    // Validate cleaned userId format (should be alphanumeric)
    if (!/^[a-zA-Z0-9]+$/.test(cleanUserId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid user ID format. Expected alphanumeric string.'
      });
    }

    // Get Privy credentials from environment
    const privyAppId = process.env.PRIVY_APP_ID;
    const privyAppSecret = process.env.PRIVY_APP_SECRET;

    if (!privyAppId || !privyAppSecret) {
      logger.error('Privy credentials not configured');
      return res.status(500).json({
        success: false,
        error: 'Privy credentials not configured'
      });
    }

    // Create Basic Auth header
    const credentials = Buffer.from(`${privyAppId}:${privyAppSecret}`).toString('base64');

    logger.info(`Fetching Privy user data for: ${cleanUserId}`);

    // Call Privy API to get user data using cleaned user ID
    const response = await axios.get<PrivyUserResponse>(
      `https://auth.privy.io/api/v1/users/${cleanUserId}`,
      {
        headers: {
          'Authorization': `Basic ${credentials}`,
          'Content-Type': 'application/json',
          'privy-app-id': privyAppId
        },
        timeout: 10000
      }
    );

    const userData = response.data;

    // Find Solana wallets in linked accounts
    const solanaWallets: SolanaWalletInfo[] = userData.linked_accounts
      .filter(account => 
        account.type === 'wallet' && 
        account.chain_type === 'solana' && 
        account.address && 
        account.id
      )
      .map(account => ({
        id: account.id,
        address: account.address!,
        verified_at: account.verified_at
      }));

    if (solanaWallets.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No Solana wallets found for this user',
        data: {
          userId: cleanUserId,
          originalUserId: userId,
          totalLinkedAccounts: userData.linked_accounts.length,
          linkedAccountTypes: userData.linked_accounts.map(acc => acc.type)
        }
      });
    }

    // Return the first (primary) Solana wallet
    const primaryWallet = solanaWallets[0];

    logger.info(`Retrieved Solana wallet info for user ${cleanUserId}: ${primaryWallet.address.slice(0, 8)}...${primaryWallet.address.slice(-8)}`);

    res.json({
      success: true,
      data: {
        walletId: primaryWallet.id,
        address: primaryWallet.address,
        verifiedAt: primaryWallet.verified_at,
        totalSolanaWallets: solanaWallets.length,
        allSolanaWallets: solanaWallets.map(wallet => ({
          id: wallet.id,
          address: wallet.address,
          verifiedAt: wallet.verified_at
        }))
      }
    });

  } catch (error) {
    logger.error('Error fetching Privy user data:', error);

    if (axios.isAxiosError(error)) {
      const status = error.response?.status || 500;
      const message = error.response?.data?.message || error.message;

      // Handle specific Privy API errors
      if (status === 401) {
        return res.status(401).json({
          success: false,
          error: 'Invalid Privy credentials'
        });
      }

      if (status === 404) {
        return res.status(404).json({
          success: false,
          error: 'User not found in Privy'
        });
      }

      return res.status(status).json({
        success: false,
        error: `Privy API error: ${message}`
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error while fetching wallet information'
    });
  }
};

/**
 * Unlink a specific wallet from a Privy user
 */
export const unlinkWallet = async (req: Request, res: Response) => {
  const { userId, walletAddress, walletType } = req.body;

  // Validate required fields
  if (!userId || !walletAddress) {
    return res.status(400).json({
      success: false,
      error: 'userId and walletAddress are required'
    });
  }

  // Clean the user ID (remove 'did:privy:' prefix if present)
  const cleanUserId = userId.startsWith('did:privy:') ? userId.replace('did:privy:', '') : userId;

  // Validate environment variables
  const appId = process.env.PRIVY_APP_ID;
  const appSecret = process.env.PRIVY_APP_SECRET;

  if (!appId || !appSecret) {
    logger.error('Missing Privy credentials in environment variables');
    return res.status(500).json({
      success: false,
      error: 'Server configuration error'
    });
  }

  logger.info(`Attempting to unlink wallet ${walletAddress} from user ${cleanUserId}`);

  // Prepare the request to Privy's REST API (declare variables early for error logging)
  const privyApiUrl = `https://auth.privy.io/api/v1/apps/${appId}/users/unlink`;
  const requestData = {
    user_id: `did:privy:${cleanUserId}`, // Privy expects the full DID format
    type: walletType === 'smart_wallet' ? 'smart_wallet' : 'wallet',
    handle: walletAddress
  };

  try {

    // First, verify the user and wallet exist by fetching user data
    try {
      const credentials = Buffer.from(`${appId}:${appSecret}`).toString('base64');
      const userResponse = await axios.get(
        `https://auth.privy.io/api/v1/users/${cleanUserId}`,
        {
          headers: {
            'Authorization': `Basic ${credentials}`,
            'Content-Type': 'application/json',
            'privy-app-id': appId
          },
          timeout: 10000
        }
      );

      const userData = userResponse.data;
      logger.info(`User found with ${userData.linked_accounts?.length || 0} linked accounts`);

      // Log detailed account information for debugging
      logger.info('User linked accounts details:', {
        accounts: userData.linked_accounts?.map((acc: any) => ({
          type: acc.type,
          address: acc.address,
          chain_type: acc.chain_type,
          connector_type: acc.connector_type,
          wallet_client_type: acc.wallet_client_type,
          imported_at: acc.imported_at,
          verified_at: acc.verified_at
        }))
      });

      // Check if the wallet exists in the user's linked accounts
      const targetAccount = userData.linked_accounts?.find((account: any) =>
        account.address === walletAddress &&
        (account.type === 'wallet' || account.type === 'smart_wallet')
      );

      if (!targetAccount) {
        logger.warn(`Wallet ${walletAddress} not found in user ${cleanUserId}'s linked accounts`);
        return res.status(400).json({
          success: false,
          error: 'Wallet not found in user\'s linked accounts',
          details: {
            walletAddress,
            userId: cleanUserId,
            linkedAccounts: userData.linked_accounts?.map((acc: any) => ({
              type: acc.type,
              address: acc.address,
              chain_type: acc.chain_type,
              connector_type: acc.connector_type
            }))
          }
        });
      }

      // Log the specific account we're trying to unlink
      logger.info('Target account to unlink:', {
        type: targetAccount.type,
        address: targetAccount.address,
        chain_type: targetAccount.chain_type,
        connector_type: targetAccount.connector_type,
        wallet_client_type: targetAccount.wallet_client_type
      });

      logger.info(`Wallet ${walletAddress} found in user's linked accounts, proceeding with unlink`);
    } catch (verifyError) {
      logger.error('Error verifying user/wallet:', verifyError);
      if (axios.isAxiosError(verifyError) && verifyError.response?.status === 404) {
        return res.status(404).json({
          success: false,
          error: 'User not found in Privy'
        });
      }
      // Continue with unlink attempt even if verification fails
    }

    // Variables already declared above for error logging

    logger.info('Sending unlink request to Privy:', {
      url: privyApiUrl,
      data: requestData
    });

    // Make the API call to Privy
    const response = await axios.post(privyApiUrl, requestData, {
      auth: {
        username: appId,
        password: appSecret
      },
      headers: {
        'Content-Type': 'application/json',
        'privy-app-id': appId
      },
      timeout: 10000
    });

    logger.info('Wallet unlinked successfully:', {
      userId: cleanUserId,
      walletAddress,
      status: response.status
    });

    res.json({
      success: true,
      message: 'Wallet unlinked successfully'
    });

  } catch (error) {
    logger.error('Error unlinking wallet:', error);

    if (axios.isAxiosError(error)) {
      const status = error.response?.status || 500;
      const message = error.response?.data?.message || error.message;

      logger.error('Privy API error details:', {
        status,
        message,
        data: error.response?.data,
        requestData,
        url: privyApiUrl
      });

      // Handle specific Privy API errors
      if (status === 400) {
        return res.status(400).json({
          success: false,
          error: `Privy API error: ${message}`,
          details: error.response?.data
        });
      }

      if (status === 401) {
        return res.status(401).json({
          success: false,
          error: 'Invalid Privy credentials'
        });
      }

      if (status === 404) {
        return res.status(404).json({
          success: false,
          error: 'User not found in Privy'
        });
      }

      return res.status(status).json({
        success: false,
        error: `Privy API error: ${message}`
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error while unlinking wallet'
    });
  }
};

/**
 * Get detailed user account information for debugging
 */
export const getUserAccountDetails = async (req: Request, res: Response) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    // Clean user ID
    const cleanUserId = userId.startsWith('did:privy:') ? userId.replace('did:privy:', '') : userId;

    // Get Privy credentials
    const appId = process.env.PRIVY_APP_ID;
    const appSecret = process.env.PRIVY_APP_SECRET;

    if (!appId || !appSecret) {
      return res.status(500).json({
        success: false,
        error: 'Privy credentials not configured'
      });
    }

    const credentials = Buffer.from(`${appId}:${appSecret}`).toString('base64');

    // Fetch user data from Privy
    const response = await axios.get(
      `https://auth.privy.io/api/v1/users/${cleanUserId}`,
      {
        headers: {
          'Authorization': `Basic ${credentials}`,
          'Content-Type': 'application/json',
          'privy-app-id': appId
        },
        timeout: 10000
      }
    );

    const userData = response.data;

    // Categorize accounts by type and authentication capability
    const accountsByType = {
      authentication_accounts: [] as any[],
      wallet_accounts: [] as any[],
      smart_wallet_accounts: [] as any[],
      other_accounts: [] as any[]
    };

    userData.linked_accounts?.forEach((account: any) => {
      const accountInfo = {
        type: account.type,
        address: account.address,
        chain_type: account.chain_type,
        connector_type: account.connector_type,
        wallet_client_type: account.wallet_client_type,
        imported_at: account.imported_at,
        verified_at: account.verified_at,
        can_authenticate: ['email', 'phone', 'google_oauth', 'apple_oauth', 'twitter_oauth', 'discord_oauth', 'github_oauth', 'linkedin_oauth', 'spotify_oauth', 'instagram_oauth', 'tiktok_oauth', 'telegram', 'farcaster', 'passkey'].includes(account.type)
      };

      if (accountInfo.can_authenticate) {
        accountsByType.authentication_accounts.push(accountInfo);
      } else if (account.type === 'smart_wallet') {
        accountsByType.smart_wallet_accounts.push(accountInfo);
      } else if (account.type === 'wallet') {
        accountsByType.wallet_accounts.push(accountInfo);
      } else {
        accountsByType.other_accounts.push(accountInfo);
      }
    });

    res.json({
      success: true,
      data: {
        userId: cleanUserId,
        totalAccounts: userData.linked_accounts?.length || 0,
        accountsByType,
        summary: {
          authentication_accounts: accountsByType.authentication_accounts.length,
          wallet_accounts: accountsByType.wallet_accounts.length,
          smart_wallet_accounts: accountsByType.smart_wallet_accounts.length,
          other_accounts: accountsByType.other_accounts.length
        }
      }
    });

  } catch (error) {
    logger.error('Error fetching user account details:', error);

    if (axios.isAxiosError(error)) {
      const status = error.response?.status || 500;
      const message = error.response?.data?.message || error.message;

      return res.status(status).json({
        success: false,
        error: `Privy API error: ${message}`
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error while fetching user account details'
    });
  }
};

/**
 * Cache management for wallet IDs
 * This could be extended to use Redis or another caching solution
 */
export const cacheWalletInfo = (userId: string, walletInfo: any) => {
  // For now, this is a placeholder for future caching implementation
  // In production, you might want to use Redis or another caching solution
  logger.info(`Caching wallet info for user: ${userId}`);
};

export const getCachedWalletInfo = (userId: string) => {
  // Placeholder for cache retrieval
  // In production, implement actual cache lookup
  return null;
};
