import walletService from '../services/walletService.js';
import { logger } from '../utils/logger.js';
import { getSolanaTokenBalances } from '../services/solanaTokenBalanceService.js';
/**
 * Get wallet balances by token pairs
 *
 * This endpoint takes a wallet address and token address, fetches all trading pairs for the token
 * from DexScreener, and then checks the wallet's balance for all quote tokens in those pairs.
 */
export const getWalletBalancesByPairs = async (req, res) => {
    try {
        const { walletAddress, tokenAddress, network } = req.body;
        // Validate inputs
        if (!walletAddress || !tokenAddress) {
            return res.status(400).json({
                success: false,
                message: 'Wallet address and token address are required',
                data: null,
            });
        }
        logger.info(`Getting wallet balances for ${walletAddress} with token ${tokenAddress}${network ? ` on ${network}` : ''}`);
        // Call service to get balances
        const result = await walletService.getWalletBalancesByPairs(walletAddress, tokenAddress, network);
        return res.json({
            success: true,
            message: 'Wallet balances retrieved successfully',
            data: result,
        });
    }
    catch (error) {
        logger.error(`Error in getWalletBalancesByPairs: ${error.message}`);
        return res.status(500).json({
            success: false,
            message: error.message || 'Failed to get wallet balances',
            data: null,
        });
    }
};
/**
 * Identify wallet type
 *
 * Determines if a wallet address is Ethereum or Solana based on its format
 */
export const identifyWalletType = async (req, res) => {
    try {
        const { walletAddress } = req.body;
        if (!walletAddress) {
            return res.status(400).json({
                success: false,
                message: 'Wallet address is required',
                data: null,
            });
        }
        const walletType = walletService.identifyWalletType(walletAddress);
        return res.json({
            success: true,
            message: 'Wallet type identified',
            data: {
                walletAddress,
                walletType,
            },
        });
    }
    catch (error) {
        logger.error(`Error identifying wallet type: ${error.message}`);
        return res.status(500).json({
            success: false,
            message: error.message || 'Failed to identify wallet type',
            data: null,
        });
    }
};
/**
 * Get token pairs from DexScreener
 *
 * Fetches all trading pairs for a token from DexScreener
 */
export const getTokenPairs = async (req, res) => {
    try {
        const { tokenAddress } = req.body;
        if (!tokenAddress) {
            return res.status(400).json({
                success: false,
                message: 'Token address is required',
                data: null,
            });
        }
        const pairs = await walletService.getTokenPairsFromDexScreener(tokenAddress);
        return res.json({
            success: true,
            message: 'Token pairs retrieved successfully',
            data: {
                tokenAddress,
                pairsCount: pairs.length,
                pairs,
            },
        });
    }
    catch (error) {
        logger.error(`Error getting token pairs: ${error.message}`);
        return res.status(500).json({
            success: false,
            message: error.message || 'Failed to get token pairs',
            data: null,
        });
    }
};
/**
 * Get balances across all supported networks
 *
 * This endpoint checks a wallet address across ETH, BSC, and Solana networks
 * and returns only the networks and tokens that have a balance
 */
export const getAllWalletBalances = async (req, res) => {
    try {
        const { walletAddress } = req.body;
        // Validate input
        if (!walletAddress) {
            return res.status(400).json({
                success: false,
                message: 'Wallet address is required',
                data: null,
            });
        }
        logger.info(`Getting all balances for wallet ${walletAddress} across all networks`);
        // Call service to get all balances
        const result = await walletService.getAllWalletBalances(walletAddress);
        return res.json({
            success: true,
            message: 'Wallet balances retrieved successfully',
            data: result,
        });
    }
    catch (error) {
        logger.error(`Error in getAllWalletBalances: ${error.message}`);
        return res.status(500).json({
            success: false,
            message: error.message || 'Failed to get wallet balances',
            data: null,
        });
    }
};
/**
 * Scan for all tokens in a wallet address
 *
 * This endpoint uses BSCscan, Etherscan and other APIs to get a complete list of all tokens
 * held by the wallet, not just common tokens
 */
export const scanAllWalletTokens = async (req, res) => {
    try {
        const { walletAddress } = req.body;
        // Validate input
        if (!walletAddress) {
            return res.status(400).json({
                success: false,
                message: 'Wallet address is required',
                data: null,
            });
        }
        logger.info(`Scanning all tokens for wallet ${walletAddress}`);
        // Call service to get all tokens
        //const result = await walletService.scanAllWalletTokens(walletAddress);
        // return res.json({
        //   success: true,
        //   message: 'Wallet tokens scanned successfully',
        //   data: result,
        // });
    }
    catch (error) {
        logger.error(`Error in scanAllWalletTokens: ${error.message}`);
        return res.status(500).json({
            success: false,
            message: error.message || 'Failed to scan wallet tokens',
            data: null,
        });
    }
};
/**
 * Comprehensive token discovery across all chains
 *
 * This endpoint provides a more thorough scan of all tokens using various methods:
 * - Deep scanning using multiple RPC endpoints
 * - Checking transaction history (if API keys available)
 * - Using additional token lists
 * - Options for including dust balances, NFTs, etc.
 */
export const discoverAllTokens = async (req, res) => {
    try {
        const { walletAddress, includeDust, includeNFTs, includeHistory } = req.body;
        // Validate input
        if (!walletAddress) {
            return res.status(400).json({
                success: false,
                message: 'Wallet address is required',
                data: null,
            });
        }
        logger.info(`Discovering all tokens for wallet ${walletAddress} with options: includeDust=${includeDust}, includeNFTs=${includeNFTs}, includeHistory=${includeHistory}`);
        // Call service to discover all tokens
        const result = await walletService.discoverAllTokens(walletAddress, {
            includeDust: includeDust === true,
            includeNFTs: includeNFTs === true,
            includeHistory: includeHistory === true
        });
        return res.json({
            success: true,
            message: 'Token discovery completed successfully',
            data: result,
        });
    }
    catch (error) {
        logger.error(`Error in discoverAllTokens: ${error.message}`);
        return res.status(500).json({
            success: false,
            message: error.message || 'Failed to discover tokens',
            data: null,
        });
    }
};
/**
 * Get Solana token balances
 *
 * This endpoint provides detailed token balance information for a Solana wallet, including:
 * - Native SOL balance
 * - SPL token balances with metadata
 * - Token images, symbols, and other metadata from on-chain and off-chain sources
 */
export const getSolanaTokenBalance = async (req, res) => {
    try {
        const { walletAddress } = req.body;
        // Validate input
        if (!walletAddress) {
            return res.status(400).json({
                success: false,
                message: 'Solana wallet address is required',
                data: null,
            });
        }
        logger.info(`Getting Solana token balances for wallet ${walletAddress}`);
        // Call service to get Solana token balances
        const result = await getSolanaTokenBalances(walletAddress);
        if (!result.success) {
            return res.status(500).json({
                success: false,
                message: result.message || 'Failed to get Solana token balances',
                data: null,
            });
        }
        return res.json({
            success: true,
            message: 'Solana token balances retrieved successfully',
            data: result,
        });
    }
    catch (error) {
        logger.error(`Error in getSolanaTokenBalance: ${error.message}`);
        return res.status(500).json({
            success: false,
            message: error.message || 'Failed to get Solana token balances',
            data: null,
        });
    }
};
//# sourceMappingURL=walletController.js.map