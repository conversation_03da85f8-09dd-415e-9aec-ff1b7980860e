import { logger } from '../utils/logger.js';
import { userActivityService } from '../services/userActivityService.js';
import { mobulaWebSocketService } from '../services/mobulaWebSocketService.js';
import { frontendWebSocketService } from '../services/frontendWebSocketService.js';
/**
 * Register user activity on pulse page
 */
export const registerPulseActivity = async (req, res) => {
    try {
        const { userId, sessionId } = req.body;
        if (!userId) {
            return res.status(400).json({
                error: 'User ID is required',
                status: 'error'
            });
        }
        userActivityService.registerPulseActivity(userId, sessionId);
        logger.info(`Registered pulse activity for user ${userId}`);
        return res.json({
            message: 'Pulse activity registered successfully',
            status: 'success'
        });
    }
    catch (error) {
        logger.error(`Error registering pulse activity: ${error.message}`);
        return res.status(500).json({
            error: 'Server error registering pulse activity',
            message: error.message,
            status: 'error'
        });
    }
};
/**
 * Unregister user activity from pulse page
 */
export const unregisterPulseActivity = async (req, res) => {
    try {
        const { userId, sessionId } = req.body;
        if (!userId) {
            return res.status(400).json({
                error: 'User ID is required',
                status: 'error'
            });
        }
        userActivityService.unregisterPulseActivity(userId, sessionId);
        logger.info(`Unregistered pulse activity for user ${userId}`);
        return res.json({
            message: 'Pulse activity unregistered successfully',
            status: 'success'
        });
    }
    catch (error) {
        logger.error(`Error unregistering pulse activity: ${error.message}`);
        return res.status(500).json({
            error: 'Server error unregistering pulse activity',
            message: error.message,
            status: 'error'
        });
    }
};
/**
 * Update user activity (heartbeat)
 */
export const updateActivity = async (req, res) => {
    try {
        const { userId, sessionId } = req.body;
        if (!userId) {
            return res.status(400).json({
                error: 'User ID is required',
                status: 'error'
            });
        }
        userActivityService.updateActivity(userId, sessionId);
        return res.json({
            message: 'Activity updated successfully',
            status: 'success'
        });
    }
    catch (error) {
        logger.error(`Error updating activity: ${error.message}`);
        return res.status(500).json({
            error: 'Server error updating activity',
            message: error.message,
            status: 'error'
        });
    }
};
/**
 * Get activity statistics
 */
export const getActivityStats = async (_req, res) => {
    try {
        const activityStats = userActivityService.getActivityStats();
        const webSocketStatus = mobulaWebSocketService.getStatus();
        const frontendWebSocketStats = frontendWebSocketService.getStats();
        return res.json({
            data: {
                activity: activityStats,
                webSocket: webSocketStatus,
                frontendWebSocket: frontendWebSocketStats
            },
            status: 'success'
        });
    }
    catch (error) {
        logger.error(`Error getting activity stats: ${error.message}`);
        return res.status(500).json({
            error: 'Server error getting activity stats',
            message: error.message,
            status: 'error'
        });
    }
};
/**
 * Get WebSocket connection status
 */
export const getWebSocketStatus = async (_req, res) => {
    try {
        const status = mobulaWebSocketService.getStatus();
        const activePulseUsers = userActivityService.getActivePulseUsers();
        const frontendWebSocketStats = frontendWebSocketService.getStats();
        return res.json({
            data: {
                ...status,
                activePulseUsers: activePulseUsers.length,
                userList: activePulseUsers,
                frontendWebSocket: frontendWebSocketStats
            },
            status: 'success'
        });
    }
    catch (error) {
        logger.error(`Error getting WebSocket status: ${error.message}`);
        return res.status(500).json({
            error: 'Server error getting WebSocket status',
            message: error.message,
            status: 'error'
        });
    }
};
//# sourceMappingURL=activityController.js.map