import express from 'express';
import { registerPulseActivity, unregisterPulseActivity, updateActivity, getActivityStats, getWebSocketStatus } from '../controllers/activityController.js';
const router = express.Router();
// Register user activity on pulse page
router.post('/pulse/register', registerPulseActivity);
// Unregister user activity from pulse page
router.post('/pulse/unregister', unregisterPulseActivity);
// Update user activity (heartbeat)
router.post('/heartbeat', updateActivity);
// Get activity statistics
router.get('/stats', getActivityStats);
// Get WebSocket connection status
router.get('/websocket-status', getWebSocketStatus);
export default router;
//# sourceMappingURL=activityRoutes.js.map