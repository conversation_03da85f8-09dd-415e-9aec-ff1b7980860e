import express from 'express';
import { createLimitOrder, getLimitOrders, getLimitOrderById, updateLimitOrder, cancelLimitOrder, deleteLimitOrder, getLimitOrderStats } from '../controllers/limitOrderController.js';
const router = express.Router();
// Apply rate limiting to all limit order routes
// const limitOrderRateLimit = createApiRateLimiter(50, 60000); // 50 requests per minute
// router.use(limitOrderRateLimit);
/**
 * @route   POST /api/limit-orders
 * @desc    Create a new limit order
 * @access  Private (requires user_id)
 * @body    {
 *   user_id: string,
 *   token_address: string,
 *   token_name: string,
 *   token_symbol: string,
 *   pool_address: string,
 *   dex_type: string,
 *   direction: 'buy' | 'sell',
 *   amount: number,
 *   target_price: number,
 *   current_price: number,
 *   slippage?: number,
 *   wallet_address: string,
 *   wallet_id: string,
 *   expires_at?: string
 * }
 */
router.post('/', createLimitOrder);
/**
 * @route   GET /api/limit-orders
 * @desc    Get limit orders for a user with filtering and pagination
 * @access  Private (requires user_id)
 * @query   {
 *   user_id: string (required),
 *   status?: 'pending' | 'executed' | 'cancelled' | 'expired',
 *   token_address?: string,
 *   direction?: 'buy' | 'sell',
 *   dex_type?: string,
 *   limit?: number (1-100),
 *   offset?: number,
 *   order_by?: 'created_at' | 'updated_at' | 'target_price',
 *   order_direction?: 'asc' | 'desc'
 * }
 */
router.get('/', getLimitOrders);
/**
 * @route   GET /api/limit-orders/stats
 * @desc    Get order statistics for a user
 * @access  Private (requires user_id)
 * @query   {
 *   user_id: string (required)
 * }
 */
router.get('/stats', getLimitOrderStats);
/**
 * @route   GET /api/limit-orders/:id
 * @desc    Get a specific limit order by ID
 * @access  Private (requires user_id)
 * @params  {
 *   id: string (order UUID)
 * }
 * @query   {
 *   user_id: string (required)
 * }
 */
router.get('/:id', getLimitOrderById);
/**
 * @route   PUT /api/limit-orders/:id
 * @desc    Update a limit order (mainly for cancellation)
 * @access  Private (requires user_id)
 * @params  {
 *   id: string (order UUID)
 * }
 * @body    {
 *   user_id: string,
 *   status?: 'pending' | 'cancelled',
 *   error_message?: string
 * }
 */
router.put('/:id', updateLimitOrder);
/**
 * @route   DELETE /api/limit-orders/:id/cancel
 * @desc    Cancel a limit order
 * @access  Private (requires user_id)
 * @params  {
 *   id: string (order UUID)
 * }
 * @query   {
 *   user_id: string (required)
 * }
 */
router.delete('/:id/cancel', cancelLimitOrder);
/**
 * @route   DELETE /api/limit-orders/:id
 * @desc    Permanently delete a limit order (only cancelled/expired orders)
 * @access  Private (requires user_id)
 * @params  {
 *   id: string (order UUID)
 * }
 * @query   {
 *   user_id: string (required)
 * }
 */
router.delete('/:id', deleteLimitOrder);
export default router;
//# sourceMappingURL=limitOrderRoutes.js.map