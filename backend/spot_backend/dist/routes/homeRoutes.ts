import express from 'express';
import {
  getNetworkHighlights,
  getConnectCoins,
  getTokenRadar,
  getSearchResults,
  get<PERSON><PERSON><PERSON>ist,
  handleMarketDataRequests,
  refreshConnectCoinsCache,
  refreshTokenRadarCache,
  refreshNetworkHighlightsCache,
  getNetworkIcons,
  getPulseData,
  getMarketData
} from '../controllers/homeController.js';
import { fetchPulseData } from '../services/pulseService.js';
import { checkCoinGeckoApiHealth, getApiKey } from '../services/coinService.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// Get connect coins (BTC, ETH, SOL) for the connect section
router.get('/connect-coins', getConnectCoins);
router.get('/network-icons',getNetworkIcons);
// router.get('/market-pair', getMarketPair);
// router.get('/market-pairs', getMarketPairs);
// router.get('/holders', getHolders);
// router.get('/chart-data', getChart);
router.get('/search-results', getSearchResults);

router.get('/pulse-data',getPulseData)

// Direct market data endpoint for pulse tokens
router.get('/market-data', getMarketData);

// Unified market data handler endpoint
router.get('/market-data-handler', handleMarketDataRequests);

// Get network-specific highlights (trending, gainers, losers, new)
router.get('/network-highlights', getNetworkHighlights);

// Get token radar data with comprehensive info for home page
router.get('/token-radar', getTokenRadar);

// Direct access to highlights API (same as universal network highlights)
router.get('/highlights', (req, res) => {
  // Set network to universal and forward to getNetworkHighlights
  req.query.network = 'universal';
  return getNetworkHighlights(req, res);
});

// Health check endpoint for CoinGecko Pro API
router.get('/coingecko-health', async (req, res) => {
  try {
    const apiKey = await getApiKey();
    
    if (!apiKey) {
      return res.status(500).json({
        status: 'error',
        isHealthy: false,
        message: 'CoinGecko API key is not configured'
      });
    }
    
    const healthResult = await checkCoinGeckoApiHealth(apiKey);
    
    if (healthResult.isHealthy) {
      return res.json({
        status: 'success',
        ...healthResult
      });
    } else {
      return res.status(503).json({
        status: 'error',
        ...healthResult
      });
    }
  } catch (error: any) {
    logger.error(`Error checking CoinGecko API health: ${error.message}`);
    return res.status(500).json({
      status: 'error',
      isHealthy: false,
      message: `Error checking CoinGecko API health: ${error.message}`
    });
  }
});

// Manually refresh connect coins cache
router.post('/refresh-connect-coins', async (req, res) => {
  try {
    const result = await refreshConnectCoinsCache();
    if (result) {
      return res.json({ 
        message: 'Connect coins cache refreshed successfully',
        status: 'success'
      });
    } else {
      return res.status(500).json({ 
        message: 'Failed to refresh connect coins cache',
        status: 'error'
      });
    }
  } catch (error: any) {
    return res.status(500).json({
      message: `Error refreshing connect coins cache: ${error.message}`,
      status: 'error'
    });
  }
});

// Manually refresh token radar cache
router.post('/refresh-token-radar', async (req, res) => {
  try {
    // Get optional limit parameter, default to 100
    const limit = req.query.limit ? parseInt(String(req.query.limit), 10) : 100;
    
    // Get optional filter parameters
    const network = req.query.network ? String(req.query.network) : undefined;
    const timeframe = req.query.timeframe ? String(req.query.timeframe) : undefined;
    
    // Validate limit parameter
    if (isNaN(limit) || limit < 1 || limit > 250) {
      return res.status(400).json({
        message: 'Invalid limit parameter. Must be a number between 1 and 250.',
        status: 'error'
      });
    }
    
    // Validate timeframe parameter if provided
    if (timeframe) {
      const validTimeframes = ['1m', '5m', '1h', '4h', '24h'];
      if (!validTimeframes.includes(timeframe.toLowerCase())) {
        return res.status(400).json({
          message: `Invalid timeframe parameter. Must be one of: ${validTimeframes.join(', ')}`,
          status: 'error'
        });
      }
    }
    
    // Validate network parameter if provided
    if (network) {
      const validNetworks = ['universal', 'ethereum', 'solana', 'binance-smart-chain', 'base'];
      if (!validNetworks.includes(network.toLowerCase())) {
        return res.status(400).json({
          message: `Invalid network parameter. Must be one of: ${validNetworks.join(', ')}`,
          status: 'error'
        });
      }
    }

    // Log all combinations being applied
    const filterCombination = [
      limit ? `limit=${limit}` : '',
      network ? `network=${network}` : 'network=universal',
      timeframe ? `timeframe=${timeframe}` : 'timeframe=24h'
    ].filter(Boolean).join(', ');

    logger.info(`Manual refresh requested for token radar with ${filterCombination}`);

    // Call refresh with all parameters
    const result = await refreshTokenRadarCache(limit, network, timeframe);
    if (result) {
      return res.json({ 
        message: `Token radar cache refreshed successfully with filters: ${filterCombination}`,
        status: 'success'
      });
    } else {
      return res.status(500).json({ 
        message: 'Failed to refresh token radar cache',
        status: 'error'
      });
    }
  } catch (error: any) {
    return res.status(500).json({
      message: `Error refreshing token radar cache: ${error.message}`,
      status: 'error'
    });
  }
});

// Manually refresh network highlights cache
router.post('/refresh-network-highlights', async (req, res) => {
  try {
    // Get optional network parameter
    const { network } = req.query;
    
    if (network) {
      // Refresh specific network
      const networkStr = String(network).toLowerCase();
      
      // Check if network is supported
      const isValid = ['universal', 'solana', 'binance-smart-chain'].includes(networkStr);
      if (!isValid) {
        return res.status(400).json({
          message: `Invalid network parameter. Supported networks are: universal, solana, binance-smart-chain`,
          status: 'error'
        });
      }
      
      logger.info(`Manual refresh requested for network: ${networkStr}`);
      
      // Call the refresh function for specific network
      const results = await refreshNetworkHighlightsCache();
      
      if (results[networkStr]) {
        return res.json({
          message: `Network highlights cache for ${networkStr} refreshed successfully`,
          status: 'success'
        });
      } else {
        return res.status(500).json({
          message: `Failed to refresh network highlights cache for ${networkStr}`,
          status: 'error'
        });
      }
    } else {
      // Refresh all networks
      logger.info('Manual refresh requested for all networks');
      
      const results = await refreshNetworkHighlightsCache();
      const successCount = Object.values(results).filter(result => result).length;
      
      if (successCount > 0) {
        return res.json({
          message: `Network highlights cache refreshed for ${successCount} networks`,
          networks: results,
          status: 'success'
        });
      } else {
        return res.status(500).json({
          message: 'Failed to refresh network highlights cache for any network',
          networks: results,
          status: 'error'
        });
      }
    }
  } catch (error: any) {
    return res.status(500).json({
      message: `Error refreshing network highlights cache: ${error.message}`,
      status: 'error'
    });
  }
});

// Manually refresh home page data
// router.post('/refresh', refreshHomePageData);

router.get('/get-pair-list', getPairList);

export default router; 