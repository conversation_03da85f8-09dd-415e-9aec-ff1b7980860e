// Test script to verify Supabase connection
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('Testing Supabase connection...');
console.log('URL:', supabaseUrl);
console.log('Service Key:', supabaseServiceKey ? 'Set' : 'Not set');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  console.log('Please set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env file');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testConnection() {
  try {
    // Test basic connection
    console.log('\n🔍 Testing database connection...');
    const { data, error, count } = await supabase
      .from('limit_orders')
      .select('*', { count: 'exact', head: true });

    if (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }

    console.log('✅ Database connection successful');
    console.log(`📊 Found ${count || 0} limit orders in the database`);

    // Test table structure
    console.log('\n🔍 Testing table structure...');
    const { data: tableData, error: tableError } = await supabase
      .from('limit_orders')
      .select('*')
      .limit(1);

    if (tableError) {
      console.error('❌ Table query failed:', tableError.message);
      return false;
    }

    console.log('✅ Table structure is correct');

    // Test enum types
    console.log('\n🔍 Testing enum types...');
    const { data: enumData, error: enumError } = await supabase
      .rpc('expire_old_orders');

    if (enumError) {
      console.error('❌ Function test failed:', enumError.message);
      return false;
    }

    console.log('✅ Functions are working correctly');

    console.log('\n🎉 All tests passed! Supabase is ready for limit orders.');
    return true;

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
    return false;
  }
}

// Run the test
testConnection().then(success => {
  process.exit(success ? 0 : 1);
});
