// Simple configuration verification
console.log('🔍 Verifying Price Feed Configuration');
console.log('====================================');

// Check environment variables
require('dotenv').config();

console.log('📋 Environment Variables:');
console.log(`   MOBULA_API_KEY: ${process.env.MOBULA_API_KEY ? '✅ Set' : '❌ Missing'}`);
console.log(`   MOBULA_PRICE_WSS_PRIMARY: ${process.env.MOBULA_PRICE_WSS_PRIMARY || '❌ Not set'}`);
console.log(`   MOBULA_PRICE_WSS_FALLBACK: ${process.env.MOBULA_PRICE_WSS_FALLBACK || '❌ Not set'}`);

console.log('\n🎯 Expected Configuration:');
console.log('   Primary URL: wss://production-feed.mobula.io');
console.log('   Fallback URL: wss://production-feed.mobula.io');
console.log('   API Key: Enterprise plan required');

console.log('\n📊 Price Feed vs Market Feed:');
console.log('   ✅ Price Feed: Unlimited assets, real-time updates');
console.log('   ❌ Market Feed: 100 assets max, 15s intervals');

console.log('\n🚀 Next Steps:');
console.log('   1. Start your service: npm run dev');
console.log('   2. Look for: "💎 Using Price Feed (Enterprise)"');
console.log('   3. Monitor logs for price updates');
console.log('   4. Check limit order execution performance');

console.log('\n✅ Configuration verification complete!');
