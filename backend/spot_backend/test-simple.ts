console.log('🔧 Test script starting...');

import express from 'express';
console.log('✅ Express imported successfully');

const app = express();
console.log('✅ Express app created');

app.get('/', (req, res) => {
  res.json({ message: 'Hello World' });
});

const PORT = 5001;
console.log(`🔧 Starting server on port ${PORT}...`);

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Test server running on port ${PORT}`);
});

server.on('error', (error: any) => {
  console.error('❌ Server error:', error);
});

server.on('listening', () => {
  console.log(`✅ Server is listening on port ${PORT}`);
});

console.log('🔧 Script execution completed');
