#!/usr/bin/env node

/**
 * Comprehensive WebSocket Data Flow Diagnostic Tool
 */

import { mobulaWebSocketService } from './dist/services/mobulaWebSocketService.js';
import { frontendWebSocketService } from './dist/services/frontendWebSocketService.js';
import { userActivityService } from './dist/services/userActivityService.js';

console.log('🔍 WebSocket Data Flow Diagnostic Tool\n');

async function diagnoseMobulaWebSocket() {
  console.log('1️⃣ Diagnosing Mobula WebSocket Service...');
  
  const status = mobulaWebSocketService.getStatus();
  console.log('   📊 Mobula WebSocket Status:', {
    connected: status.connected,
    activeUsers: status.activeUsers,
    lastUpdate: status.lastUpdate ? new Date(status.lastUpdate).toISOString() : 'Never',
    connectionState: status.connectionState
  });
  
  const cachedData = mobulaWebSocketService.getCachedData();
  const hasFreshData = mobulaWebSocketService.hasFreshData();
  
  console.log('   💾 Cache Status:', {
    hasData: !!cachedData,
    isFresh: hasFreshData,
    dataKeys: cachedData ? Object.keys(cachedData) : [],
    dataSize: cachedData ? JSON.stringify(cachedData).length : 0
  });
  
  if (cachedData) {
    console.log('   📈 Data Structure:', {
      new: cachedData.new?.length || 0,
      bonding: cachedData.bonding?.length || 0,
      bonded: cachedData.bonded?.length || 0
    });
  }
  
  return {
    connected: status.connected,
    hasData: !!cachedData,
    hasFreshData,
    activeUsers: status.activeUsers
  };
}

async function diagnoseFrontendWebSocket() {
  console.log('\n2️⃣ Diagnosing Frontend WebSocket Service...');
  
  const stats = frontendWebSocketService.getStats();
  console.log('   📊 Frontend WebSocket Stats:', stats);
  
  return stats;
}

async function diagnoseUserActivity() {
  console.log('\n3️⃣ Diagnosing User Activity Service...');
  
  const activityStats = userActivityService.getActivityStats();
  console.log('   📊 User Activity Stats:', activityStats);
  
  return activityStats;
}

async function testDataFlow() {
  console.log('\n4️⃣ Testing Data Flow...');
  
  // Test user registration
  console.log('   🧪 Testing user registration...');
  const testUserId = 'test-user-diagnostic';
  
  try {
    // Register test user
    userActivityService.registerPulseActivity(testUserId, 'test-session-diagnostic');
    console.log('   ✅ User registered successfully');
    
    // Wait a moment for connection
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if Mobula WebSocket connected
    const mobulaStatus = mobulaWebSocketService.getStatus();
    console.log('   📡 Mobula connection after registration:', {
      connected: mobulaStatus.connected,
      activeUsers: mobulaStatus.activeUsers
    });
    
    // Check for data
    const cachedData = mobulaWebSocketService.getCachedData();
    const hasFreshData = mobulaWebSocketService.hasFreshData();
    
    console.log('   💾 Data availability after registration:', {
      hasData: !!cachedData,
      isFresh: hasFreshData
    });
    
    // Unregister test user
    userActivityService.unregisterPulseActivity(testUserId, 'test-session-diagnostic');
    console.log('   🧹 Test user unregistered');
    
    return {
      registrationWorked: true,
      connectionEstablished: mobulaStatus.connected,
      dataAvailable: !!cachedData && hasFreshData
    };
    
  } catch (error) {
    console.error('   ❌ Error during data flow test:', error.message);
    return {
      registrationWorked: false,
      connectionEstablished: false,
      dataAvailable: false,
      error: error.message
    };
  }
}

async function generateRecommendations(diagnostics) {
  console.log('\n🔧 RECOMMENDATIONS:\n');
  
  const { mobula, frontend, userActivity, dataFlow } = diagnostics;
  
  if (!mobula.connected) {
    console.log('❌ CRITICAL: Mobula WebSocket not connected');
    console.log('   → Check API key configuration');
    console.log('   → Verify network connectivity to Mobula API');
    console.log('   → Check firewall settings');
  }
  
  if (!mobula.hasData) {
    console.log('❌ CRITICAL: No cached data available');
    console.log('   → Mobula WebSocket may not be receiving data');
    console.log('   → Check subscription configuration');
    console.log('   → Verify data processing pipeline');
  }
  
  if (!mobula.hasFreshData && mobula.hasData) {
    console.log('⚠️  WARNING: Data is stale');
    console.log('   → Check cache expiry settings');
    console.log('   → Verify real-time data updates');
  }
  
  if (frontend.totalClients === 0) {
    console.log('ℹ️  INFO: No frontend clients connected');
    console.log('   → This is normal if no users are on pulse page');
  }
  
  if (userActivity.pulsePageUsers === 0) {
    console.log('ℹ️  INFO: No users on pulse page');
    console.log('   → Data flow will only activate when users join');
  }
  
  if (!dataFlow.connectionEstablished && dataFlow.registrationWorked) {
    console.log('❌ CRITICAL: User registration doesn\'t trigger connection');
    console.log('   → Check MobulaWebSocketService.registerUser() method');
    console.log('   → Verify connection logic');
  }
  
  if (!dataFlow.dataAvailable && dataFlow.connectionEstablished) {
    console.log('❌ CRITICAL: Connection established but no data flowing');
    console.log('   → Check message handling in MobulaWebSocketService');
    console.log('   → Verify data processing and caching');
  }
  
  // Provide specific fixes
  console.log('\n🛠️  SPECIFIC FIXES NEEDED:\n');
  
  if (!mobula.connected || !mobula.hasData) {
    console.log('1. Implement fallback data mechanism');
    console.log('2. Add initial data seeding on service startup');
    console.log('3. Improve error handling and retry logic');
  }
  
  if (frontend.pulseRoomClients > 0 && !mobula.hasFreshData) {
    console.log('4. Ensure clients receive initial data even if stale');
    console.log('5. Implement better data freshness indicators');
  }
  
  console.log('6. Add real-time broadcasting instead of polling');
  console.log('7. Implement health checks and monitoring');
  console.log('8. Add data validation and error recovery');
}

async function runDiagnostics() {
  try {
    const mobula = await diagnoseMobulaWebSocket();
    const frontend = await diagnoseFrontendWebSocket();
    const userActivity = await diagnoseUserActivity();
    const dataFlow = await testDataFlow();
    
    const diagnostics = { mobula, frontend, userActivity, dataFlow };
    
    console.log('\n📋 DIAGNOSTIC SUMMARY:');
    console.log('======================');
    console.log(`Mobula Connected: ${mobula.connected ? '✅' : '❌'}`);
    console.log(`Data Available: ${mobula.hasData ? '✅' : '❌'}`);
    console.log(`Data Fresh: ${mobula.hasFreshData ? '✅' : '❌'}`);
    console.log(`Frontend Clients: ${frontend.totalClients}`);
    console.log(`Pulse Room Clients: ${frontend.pulseRoomClients}`);
    console.log(`Active Users: ${userActivity.totalActiveUsers}`);
    console.log(`Pulse Page Users: ${userActivity.pulsePageUsers}`);
    console.log(`Data Flow Test: ${dataFlow.dataAvailable ? '✅' : '❌'}`);
    
    await generateRecommendations(diagnostics);
    
    const overallHealth = mobula.connected && mobula.hasFreshData && dataFlow.dataAvailable;
    console.log(`\n🎯 Overall System Health: ${overallHealth ? '✅ HEALTHY' : '❌ NEEDS ATTENTION'}`);
    
    process.exit(overallHealth ? 0 : 1);
    
  } catch (error) {
    console.error('💥 Diagnostic failed:', error);
    process.exit(1);
  }
}

runDiagnostics();
