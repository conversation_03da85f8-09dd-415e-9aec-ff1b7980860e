const WebSocket = require('ws');

console.log('🧪 Simple Price Feed Test');
console.log('=========================');

const API_KEY = process.env.MOBULA_API_KEY || 'fffa68cd-6bde-4ac5-909d-eb627d8baca0';

// Test Price Feed (Enterprise)
const testPriceFeed = () => {
  console.log('💎 Testing Price Feed (Enterprise)');
  console.log('URL: wss://production-feed.mobula.io');
  
  const ws = new WebSocket('wss://production-feed.mobula.io');
  let messageCount = 0;
  
  // Auto-close after 20 seconds
  const timeout = setTimeout(() => {
    console.log('⏰ Test timeout - closing connection');
    ws.close();
  }, 20000);
  
  ws.on('open', () => {
    console.log('✅ Price Feed connected successfully');
    
    // Subscribe to SOL token
    const subscription = {
      type: "feed",
      authorization: API_KEY,
      kind: "tokens",
      tokens: [
        {
          blockchain: "solana",
          address: "So11111111111111111111111111111111111111112"
        }
      ]
    };
    
    console.log('📤 Sending subscription:', JSON.stringify(subscription, null, 2));
    ws.send(JSON.stringify(subscription));
  });
  
  ws.on('message', (data) => {
    messageCount++;
    
    try {
      const message = JSON.parse(data.toString());
      
      console.log(`📥 Message ${messageCount}:`, {
        type: typeof message,
        hasPrice: message.price !== undefined,
        hasSymbol: message.baseSymbol !== undefined,
        hasTimestamp: message.timestamp !== undefined,
        time: new Date().toISOString()
      });
      
      // If it's price data, show the price
      if (message.price && message.baseSymbol) {
        console.log(`💰 ${message.baseSymbol} Price: $${message.price}`);
        console.log(`📊 Market Depth: Up $${message.marketDepthUSDUp}, Down $${message.marketDepthUSDDown}`);
        console.log(`📈 Volume 24h: $${message.volume24h}`);
      } else {
        console.log('ℹ️ Non-price message:', message);
      }
      
      // Show first message in full
      if (messageCount === 1) {
        console.log('📋 Full first message:', JSON.stringify(message, null, 2));
      }
      
    } catch (error) {
      console.error('❌ Error parsing message:', error.message);
      console.log('📄 Raw message:', data.toString());
    }
  });
  
  ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error.message);
  });
  
  ws.on('close', (code, reason) => {
    clearTimeout(timeout);
    console.log(`🔌 Connection closed: ${code} - ${reason.toString()}`);
    console.log(`📊 Total messages received: ${messageCount}`);
    
    if (messageCount > 0) {
      console.log('✅ Price Feed test successful!');
      console.log('💡 Price Feed is working and sending data');
    } else {
      console.log('❌ No messages received');
      console.log('💡 Possible issues:');
      console.log('   - API key might not have Enterprise access');
      console.log('   - Network connectivity issues');
      console.log('   - Price Feed endpoint might be different');
    }
  });
};

// Run the test
console.log(`🔑 Using API Key: ${API_KEY.substring(0, 8)}...`);
console.log('⏱️ Test will run for 20 seconds');
console.log('');

testPriceFeed();
