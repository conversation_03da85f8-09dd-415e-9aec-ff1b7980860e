# Limit Order Monitoring Service

## Overview

The Limit Order Monitoring Service is a background service that monitors pending limit orders and automatically executes them when target prices are reached. It uses Mobula's WebSocket API for real-time price data and implements intelligent grouping to minimize API calls.

## Architecture

### Core Components

1. **LimitOrderMonitoringService**: Main service class that manages WebSocket connections and order monitoring
2. **TokenSubscription**: Groups orders by token/pool combination for efficient monitoring
3. **MonitoringStats**: Tracks service performance and execution metrics
4. **WebSocket Integration**: Real-time price updates from Mobula API

### Key Features

- **Efficient Grouping**: Orders are grouped by `token_address` and `pool_address` to minimize API calls
- **Real-time Monitoring**: WebSocket connection for live price updates
- **Automatic Reconnection**: Robust connection management with exponential backoff
- **Performance Tracking**: Comprehensive metrics and logging
- **Graceful Shutdown**: Clean service termination with proper resource cleanup

## Configuration

### Environment Variables

```bash
# Required: Mobula API key for WebSocket access
MOBULA_API_KEY=your_mobula_api_key_here

# Optional: Service configuration
LIMIT_ORDER_MONITORING_INTERVAL=30000  # 30 seconds (default)
LIMIT_ORDER_MAX_RECONNECT_ATTEMPTS=10  # Default: 10
LIMIT_ORDER_RECONNECT_DELAY=5000       # 5 seconds (default)
```

### Mobula WebSocket Configuration

- **Endpoint**: `wss://api.mobula.io`
- **Protocol**: Market data stream
- **Update Interval**: 15 seconds
- **Authentication**: API key required

## Service Lifecycle

### Initialization

1. Load pending orders from database
2. Group orders by token/pool combinations
3. Establish WebSocket connection to Mobula
4. Subscribe to price feeds for active tokens
5. Start monitoring loop (30-second intervals)

### Monitoring Loop

```typescript
// Every 30 seconds:
1. Refresh order subscriptions from database
2. Check for new/cancelled orders
3. Update WebSocket subscriptions if needed
4. Validate connection health
```

### Order Execution

```typescript
// When target price is reached:
1. Validate execution conditions
2. Mark order as executed in database
3. Log execution details
4. Remove from active monitoring
5. Emit execution event
```

## API Endpoints

### Health Check
```http
GET /api/limit-orders/monitoring/health
```

**Response:**
```json
{
  "connected": true,
  "activeSubscriptions": 5,
  "totalOrders": 12,
  "lastUpdate": 1703123456789,
  "connectionState": "connected",
  "config": {
    "wsUrl": "wss://api.mobula.io",
    "hasApiKey": true,
    "maxReconnectAttempts": 10
  },
  "stats": {
    "executedOrders": 3,
    "failedExecutions": 0,
    "reconnectAttempts": 0
  }
}
```

### Subscriptions Debug
```http
GET /api/limit-orders/monitoring/subscriptions
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "key": "token_address_pool_address",
      "asset": {
        "address": "0x...",
        "blockchain": "1"
      },
      "orderCount": 3,
      "lastPrice": 0.00001234,
      "lastUpdate": 1703123456789,
      "orders": [
        {
          "id": "uuid",
          "symbol": "TOKEN",
          "direction": "buy",
          "amount": 100,
          "targetPrice": 0.00001200
        }
      ]
    }
  ],
  "count": 1
}
```

### Manual Refresh
```http
POST /api/limit-orders/monitoring/refresh
```

## Database Integration

### Order Status Updates

The service updates the `limit_orders` table when orders are executed:

```sql
UPDATE limit_orders 
SET 
  status = 'executed',
  executed_at = NOW(),
  execution_tx_hash = 'executed_at_price_X.XXXXXX',
  error_message = '{"execution_price": X.XXXXXX, "market_data": {...}}'
WHERE id = 'order_id' AND status = 'pending';
```

### Pending Orders Query

```sql
SELECT * FROM limit_orders 
WHERE status = 'pending' 
  AND (expires_at IS NULL OR expires_at > NOW())
ORDER BY created_at ASC;
```

## Performance Optimization

### Efficient Grouping Strategy

Orders are grouped by `token_address + pool_address` combination:

```typescript
// Single subscription for multiple orders on same token/pool
const subscriptionKey = `${tokenAddress}_${poolAddress}`;

// Example: 5 orders on PEPE/WETH pool = 1 WebSocket subscription
// Instead of 5 separate subscriptions
```

### Connection Management

- **Heartbeat**: 30-second ping to keep connection alive
- **Reconnection**: Exponential backoff (5s → 10s → 20s → ... → 300s max)
- **Rate Limiting**: Respects Mobula API limits
- **Error Handling**: Graceful degradation on connection issues

### Memory Efficiency

- **Order Cleanup**: Executed orders removed from memory immediately
- **Subscription Cleanup**: Empty subscriptions deleted automatically
- **Event Cleanup**: Proper event listener management

## Monitoring & Debugging

### Logging Levels

- **INFO**: Service lifecycle, order executions, connection status
- **WARN**: Connection issues, missing API keys, stale data
- **ERROR**: Execution failures, database errors, critical issues
- **DEBUG**: Detailed order processing, WebSocket messages

### Key Metrics

```typescript
interface MonitoringStats {
  activeSubscriptions: number;    // Current WebSocket subscriptions
  totalOrders: number;           // Total orders being monitored
  executedOrders: number;        // Successfully executed orders
  failedExecutions: number;      // Failed execution attempts
  lastUpdate: number;            // Last price update timestamp
  connectionState: string;       // WebSocket connection status
  reconnectAttempts: number;     // Current reconnection attempts
}
```

### Performance Monitoring Integration

The service integrates with the existing `performanceMonitorService`:

```typescript
// Records WebSocket performance metrics
performanceMonitorService.recordApiMetric({
  endpoint: 'mobula-websocket',
  method: 'WS',
  responseTime: 0,
  statusCode: 200,
  timestamp: Date.now(),
  success: true
});
```

## Error Handling

### Connection Errors

- **Network Issues**: Automatic reconnection with exponential backoff
- **Authentication**: Clear error messages for invalid API keys
- **Rate Limiting**: Graceful handling of API limits

### Execution Errors

- **Database Errors**: Orders marked as failed with error details
- **Price Validation**: Ensures logical execution conditions
- **Concurrency**: Handles multiple orders executing simultaneously

### Recovery Strategies

1. **Connection Recovery**: Automatic reconnection with state preservation
2. **Data Recovery**: Re-fetch orders on service restart
3. **Partial Failure**: Continue monitoring other orders if some fail

## Testing

### Manual Testing Endpoints

```bash
# Check service health
curl http://localhost:5001/api/limit-orders/monitoring/health

# View active subscriptions
curl http://localhost:5001/api/limit-orders/monitoring/subscriptions

# Force refresh orders
curl -X POST http://localhost:5001/api/limit-orders/monitoring/refresh
```

### Integration Testing

The service can be tested by:

1. Creating test limit orders in the database
2. Monitoring the health endpoint for subscription updates
3. Checking logs for price updates and execution attempts
4. Verifying order status changes in the database

## Future Enhancements

### Planned Features

1. **Real-time Notifications**: Push notifications for order executions
2. **Advanced Order Types**: Stop-loss, take-profit, trailing stops
3. **Performance Analytics**: Detailed execution performance tracking
4. **Multi-Exchange Support**: Support for additional price sources
5. **Smart Execution**: MEV protection and optimal execution timing

### Scalability Improvements

1. **Horizontal Scaling**: Support for multiple monitoring instances
2. **Database Sharding**: Efficient handling of large order volumes
3. **Caching Layer**: Redis integration for frequently accessed data
4. **Load Balancing**: Distribute monitoring load across instances
