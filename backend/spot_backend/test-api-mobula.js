const WebSocket = require('ws');

console.log('🧪 Testing wss://api.mobula.io (Standard Endpoint)');
console.log('==================================================');

// Use your API key
const API_KEY = 'fffa68cd-6bde-4ac5-909d-eb627d8baca0';

console.log(`🔑 API Key: ${API_KEY.substring(0, 8)}...${API_KEY.substring(API_KEY.length - 4)}`);
console.log('🎯 Testing endpoint: wss://api.mobula.io');
console.log('📊 Expected format: Market Feed (arrays)');
console.log('⏱️ Test duration: 30 seconds');
console.log('');

const testApiMobula = () => {
  return new Promise((resolve) => {
    const ws = new WebSocket('wss://api.mobula.io');
    let messageCount = 0;
    let priceUpdates = 0;
    let connectionTime = null;
    
    // Test timeout
    const timeout = setTimeout(() => {
      console.log('⏰ Test timeout reached');
      ws.close();
    }, 30000);
    
    ws.on('open', () => {
      connectionTime = Date.now();
      console.log('✅ Connected to wss://api.mobula.io successfully!');
      
      // Try Market Feed subscription format
      const subscription = {
        type: 'market',
        authorization: API_KEY,
        payload: {
          assets: [
            {
              address: "So11111111111111111111111111111111111111112",
              blockchain: "1399811149" // Solana blockchain ID
            },
            {
              address: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", 
              blockchain: "1399811149" // USDC on Solana
            }
          ],
          interval: 5
        }
      };
      
      console.log('📤 Sending Market Feed subscription...');
      console.log('   Format: Market Feed (type: "market")');
      console.log('   Tokens: SOL, USDC');
      console.log('   Interval: 5 seconds');
      console.log('   Subscription:', JSON.stringify(subscription, null, 2));
      
      ws.send(JSON.stringify(subscription));
    });
    
    ws.on('message', (data) => {
      messageCount++;
      
      try {
        const message = JSON.parse(data.toString());
        
        console.log(`📥 Message ${messageCount}:`, {
          type: typeof message,
          isArray: Array.isArray(message),
          length: Array.isArray(message) ? message.length : 'N/A',
          hasData: message.length > 0 || message.price !== undefined,
          time: new Date().toISOString()
        });
        
        // Handle array format (Market Feed)
        if (Array.isArray(message)) {
          console.log(`📊 Market Feed array with ${message.length} items`);
          
          message.forEach((item, index) => {
            if (item.price && item.baseSymbol && item.timestamp) {
              priceUpdates++;
              console.log(`💰 [${index}] ${item.baseSymbol} Price: $${item.price}`);
              console.log(`   📊 Market Depth: Up $${item.marketDepthUSDUp?.toLocaleString()}, Down $${item.marketDepthUSDDown?.toLocaleString()}`);
              console.log(`   📈 Volume 24h: $${item.volume24h?.toLocaleString()}`);
              console.log(`   ⏰ Timestamp: ${new Date(item.timestamp).toISOString()}`);
            } else {
              console.log(`   ⚠️ [${index}] Invalid item:`, Object.keys(item));
            }
          });
          console.log('');
        }
        // Handle single object format
        else if (message.price && message.baseSymbol && message.timestamp) {
          priceUpdates++;
          console.log(`💰 ${message.baseSymbol} Price: $${message.price}`);
          console.log(`📊 Market Depth: Up $${message.marketDepthUSDUp?.toLocaleString()}, Down $${message.marketDepthUSDDown?.toLocaleString()}`);
          console.log(`📈 Volume 24h: $${message.volume24h?.toLocaleString()}`);
          console.log(`⏰ Timestamp: ${new Date(message.timestamp).toISOString()}`);
          console.log('');
        }
        // Handle control messages
        else if (message.type || message.status || message.error) {
          console.log('ℹ️ Control message:', {
            type: message.type,
            status: message.status,
            error: message.error,
            message: message.message || 'N/A'
          });
          console.log('');
        }
        // Handle unknown format
        else {
          console.log('❓ Unknown message format:');
          console.log('   Keys:', Object.keys(message));
          console.log('   Sample:', JSON.stringify(message).substring(0, 200) + '...');
          console.log('');
        }
        
        // Show first message in full for debugging
        if (messageCount === 1) {
          console.log('📋 Full first message:');
          console.log(JSON.stringify(message, null, 2));
          console.log('');
        }
        
      } catch (error) {
        console.error('❌ Error parsing message:', error.message);
        console.log('📄 Raw message:', data.toString().substring(0, 300) + '...');
        console.log('');
      }
    });
    
    ws.on('error', (error) => {
      console.error('❌ WebSocket error:', error.message);
      
      if (error.message.includes('401') || error.message.includes('403')) {
        console.log('🔐 Authentication issue - API key might be invalid');
      } else if (error.message.includes('404')) {
        console.log('🔍 Endpoint not found');
      } else if (error.message.includes('429')) {
        console.log('⏱️ Rate limited - too many requests');
      } else {
        console.log('🌐 Network or connection issue');
      }
      console.log('');
    });
    
    ws.on('close', (code, reason) => {
      clearTimeout(timeout);
      const duration = connectionTime ? (Date.now() - connectionTime) / 1000 : 0;
      
      console.log('🔌 Connection closed');
      console.log(`   Code: ${code}`);
      console.log(`   Reason: ${reason.toString() || 'Normal closure'}`);
      console.log(`   Duration: ${duration.toFixed(1)}s`);
      console.log('');
      
      console.log('📊 TEST RESULTS:');
      console.log('================');
      console.log(`✉️ Total messages: ${messageCount}`);
      console.log(`💰 Price updates: ${priceUpdates}`);
      console.log(`⏱️ Connection time: ${duration.toFixed(1)}s`);
      console.log(`🎯 Endpoint: wss://api.mobula.io`);
      
      if (priceUpdates > 0) {
        console.log('');
        console.log('🎉 SUCCESS! wss://api.mobula.io is working!');
        console.log('✅ Your limit order service can use this endpoint for:');
        console.log('   • Reliable market data');
        console.log('   • Real-time price updates');
        console.log('   • Stable WebSocket connection');
        console.log('   • Market Feed format (arrays)');
      } else if (messageCount > 0) {
        console.log('');
        console.log('⚠️ PARTIAL SUCCESS: Connected but no price data');
        console.log('💡 Possible reasons:');
        console.log('   • Subscription format might need adjustment');
        console.log('   • Assets might not be available');
        console.log('   • Different message format than expected');
      } else {
        console.log('');
        console.log('❌ FAILED: No data received');
        console.log('💡 Possible issues:');
        console.log('   • API key authentication failed');
        console.log('   • Network connectivity issues');
        console.log('   • Endpoint might be down');
      }
      
      resolve({
        success: priceUpdates > 0,
        messages: messageCount,
        priceUpdates: priceUpdates,
        duration: duration,
        endpoint: 'wss://api.mobula.io'
      });
    });
  });
};

// Run the test
console.log('🚀 Starting test...');
testApiMobula().then((result) => {
  console.log('');
  console.log('🏁 Test completed');
  
  if (result.success) {
    console.log('✅ wss://api.mobula.io is ready for production use!');
    console.log('💡 Update your service to use this endpoint');
  } else {
    console.log('❌ wss://api.mobula.io needs investigation');
    console.log('💡 Try different subscription format or check API key');
  }
}).catch((error) => {
  console.error('💥 Test failed with error:', error);
});
