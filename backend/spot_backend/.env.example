# Spot Backend Environment Variables

# Server Configuration
PORT=3001
NODE_ENV=development

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# API Keys
MOBULA_API_KEY=your_mobula_api_key_here
COINGECKO_API_KEY=your_coingecko_api_key_here

# WebSocket Configuration
WEBSOCKET_PORT=3002

# Supabase Configuration (for Limit Orders)
SUPABASE_URL=your_supabase_project_url_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Rate Limiting
API_RATE_LIMIT_REQUESTS=100
API_RATE_LIMIT_WINDOW_MS=60000

# Logging
LOG_LEVEL=info

# Cache Configuration
CACHE_TTL_SECONDS=300
CACHE_MAX_SIZE=1000

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_SAMPLE_RATE=0.1
