# Server configuration
PORT=5000

# Redis connection (default: localhost:6379)
REDIS_URL=redis://localhost:6379

# CoinGecko API Key (Pro version)
COINGECKO_API_KEY=your_coingecko_api_key

# Mobula API Configuration
MOBULA_API_KEY=your_mobula_api_key
MOBULA_WSS_URL=wss://api-prod.mobula.io

# Privy authentication
PRIVY_APP_ID=your_privy_app_id

# Environment
NODE_ENV=development

JWT_SECRET=your_secure_jwt_secret