/**
 * Test script for transaction confirmation logic
 * This script tests the priority-level-specific timeout behavior
 */

const { Connection } = require('@solana/web3.js');

// Mock the confirmation function for testing
async function mockWaitForTransactionConfirmation(
  connection,
  signature,
  priorityLevel
) {
  const timeoutSettings = {
    low: 90000,      // 90 seconds for low priority
    medium: 45000,   // 45 seconds for medium priority  
    high: 30000,     // 30 seconds for high priority
    veryHigh: 15000  // 15 seconds for very high priority
  };

  const timeout = timeoutSettings[priorityLevel];
  const startTime = Date.now();
  
  console.log(`Testing confirmation for ${priorityLevel} priority with ${timeout}ms timeout`);

  return new Promise((resolve, reject) => {
    const timeoutId = setTimeout(() => {
      const elapsed = Date.now() - startTime;
      console.log(`❌ Timeout after ${elapsed}ms for ${priorityLevel} priority`);
      reject(new Error(
        `Transaction confirmation timeout after ${timeout}ms for ${priorityLevel} priority transaction. ` +
        `Signature: ${signature}. The transaction may still be processing on the blockchain.`
      ));
    }, timeout);

    // Simulate random confirmation time
    const randomConfirmTime = Math.random() * timeout * 1.5; // Sometimes exceeds timeout
    
    setTimeout(() => {
      clearTimeout(timeoutId);
      const elapsed = Date.now() - startTime;
      
      if (elapsed < timeout) {
        console.log(`✅ Confirmed in ${elapsed}ms for ${priorityLevel} priority`);
        resolve(signature);
      }
    }, randomConfirmTime);
  });
}

// Test function
async function testPriorityLevels() {
  const testSignature = 'test_signature_12345';
  const priorities = ['low', 'medium', 'high', 'veryHigh'];
  
  console.log('🧪 Testing Priority Level Confirmation Logic\n');
  
  for (const priority of priorities) {
    try {
      console.log(`Testing ${priority} priority...`);
      await mockWaitForTransactionConfirmation(null, testSignature, priority);
    } catch (error) {
      console.log(`Error for ${priority}:`, error.message.substring(0, 100) + '...');
    }
    console.log('---');
  }
  
  console.log('✅ Test completed');
}

// Run tests if this file is executed directly
if (require.main === module) {
  testPriorityLevels().catch(console.error);
}

module.exports = { mockWaitForTransactionConfirmation };
