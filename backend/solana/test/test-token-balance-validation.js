const axios = require('axios');

async function testTokenBalanceValidation() {
  console.log('🔍 Testing Token Balance Validation for PumpFun Sell...');
  
  // Test case 1: Try to sell more tokens than the user has
  const sellRequestExcessive = {
    tokenAddress: "GxfZBiRyNbtPdPvxHyjnPKRJyR5mGJiCGY4wzKwUpump",
    poolAddress: "81oRNu8jP5oeVNQL8injYe9DSfK5CnJUruBq3qGZsCj8",
    dexType: "pumpfun",
    amount: 999999999, // Excessive amount that user definitely doesn't have
    direction: "sell",
    slippage: 0.1, // 10% slippage
    walletAddress: "********************************************",
    walletId: "cbq2lb54zo7rtzv14i5sp75j"
  };

  console.log('\n=== Test Case 1: Excessive Sell Amount ===');
  console.log('Request:', JSON.stringify(sellRequestExcessive, null, 2));

  try {
    const response = await axios.post('http://localhost:6001/api/pump/swap', sellRequestExcessive, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30 second timeout
    });
    
    console.log('❌ UNEXPECTED: Request should have failed but succeeded');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    if (error.response) {
      console.log('✅ EXPECTED: Request failed with validation error');
      console.log('Status:', error.response.status);
      console.log('Error:', JSON.stringify(error.response.data, null, 2));
      
      // Check if the error message contains token balance information
      const errorMessage = error.response.data.error || '';
      if (errorMessage.includes('Insufficient token balance') || 
          errorMessage.includes('tokens') || 
          errorMessage.includes('Shortfall')) {
        console.log('✅ Error message contains proper token balance information');
      } else {
        console.log('❌ Error message does not contain expected token balance information');
      }
    } else {
      console.error('❌ Network or other error:', error.message);
    }
  }

  // Test case 2: Try to sell a reasonable amount (should work if user has tokens)
  const sellRequestReasonable = {
    tokenAddress: "GxfZBiRyNbtPdPvxHyjnPKRJyR5mGJiCGY4wzKwUpump",
    poolAddress: "81oRNu8jP5oeVNQL8injYe9DSfK5CnJUruBq3qGZsCj8",
    dexType: "pumpfun",
    amount: 0.1, // Small reasonable amount
    direction: "sell",
    slippage: 0.1, // 10% slippage
    walletAddress: "********************************************",
    walletId: "cbq2lb54zo7rtzv14i5sp75j"
  };

  console.log('\n=== Test Case 2: Reasonable Sell Amount ===');
  console.log('Request:', JSON.stringify(sellRequestReasonable, null, 2));

  try {
    const response = await axios.post('http://localhost:6001/api/pump/swap', sellRequestReasonable, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60 second timeout for actual transaction
    });
    
    console.log('✅ SUCCESS: Reasonable sell request succeeded');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    if (error.response) {
      console.log('ℹ️  Request failed (may be due to insufficient balance or other issues)');
      console.log('Status:', error.response.status);
      console.log('Error:', JSON.stringify(error.response.data, null, 2));
      
      // Check if it's a token balance error or something else
      const errorMessage = error.response.data.error || '';
      if (errorMessage.includes('Insufficient token balance')) {
        console.log('ℹ️  This is a token balance validation error (expected if user has no tokens)');
      } else {
        console.log('ℹ️  This is a different type of error');
      }
    } else {
      console.error('❌ Network or other error:', error.message);
    }
  }

  console.log('\n🎯 Token Balance Validation Test Complete!');
}

// Run the test
testTokenBalanceValidation().catch(console.error);
