/**
 * Comprehensive Jito Execution Diagnostics
 *
 * This script investigates why Jito execution is failing and provides
 * detailed diagnostics for troubleshooting.
 */

const axios = require('axios');
const { Connection, PublicKey, Transaction, SystemProgram, Keypair } = require('@solana/web3.js');

// Test configuration
const API_BASE_URL = 'http://localhost:6001';
const SOLANA_RPC_URL = 'https://mainnet.helius-rpc.com/?api-key=52496df8-0e8c-472c-b15c-defef68bed70';

// Jito endpoints to test
const JITO_ENDPOINTS = [
    'https://mainnet.block-engine.jito.wtf/api/v1/bundles',
    'https://amsterdam.mainnet.block-engine.jito.wtf/api/v1/bundles',
    'https://frankfurt.mainnet.block-engine.jito.wtf/api/v1/bundles',
    'https://ny.mainnet.block-engine.jito.wtf/api/v1/bundles',
    'https://tokyo.mainnet.block-engine.jito.wtf/api/v1/bundles'
];

// Jito tip accounts
const JITO_TIP_ACCOUNTS = [
    '96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5',
    'HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe',
    'Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY',
    'ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49',
    'DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh',
    'ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt',
    'DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL',
    '3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT'
];

/**
 * Test direct Jito endpoint connectivity
 */
async function testJitoEndpointConnectivity() {
    console.log('\n=== Testing Jito Endpoint Connectivity ===');
    
    const results = [];
    
    for (const endpoint of JITO_ENDPOINTS) {
        try {
            console.log(`\n🔍 Testing endpoint: ${endpoint}`);
            
            const startTime = Date.now();
            const response = await axios.post(endpoint, {
                jsonrpc: '2.0',
                id: 1,
                method: 'sendBundle',
                params: [[]] // Empty bundle to test connectivity
            }, {
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const responseTime = Date.now() - startTime;
            
            console.log(`   ✅ Connected (${responseTime}ms)`);
            console.log(`   📊 Status: ${response.status}`);
            console.log(`   📋 Response: ${JSON.stringify(response.data)}`);
            
            results.push({
                endpoint,
                success: true,
                responseTime,
                status: response.status,
                data: response.data
            });
            
        } catch (error) {
            console.log(`   ❌ Failed: ${error.message}`);
            if (error.response) {
                console.log(`   📊 Status: ${error.response.status}`);
                console.log(`   📋 Response: ${JSON.stringify(error.response.data)}`);
            }
            
            results.push({
                endpoint,
                success: false,
                error: error.message,
                status: error.response?.status,
                data: error.response?.data
            });
        }
    }
    
    return results;
}

/**
 * Test transaction serialization
 */
async function testTransactionSerialization() {
    console.log('\n=== Testing Transaction Serialization ===');
    
    try {
        // Create a test connection
        const connection = new Connection(SOLANA_RPC_URL, 'confirmed');
        
        // Create test keypairs
        const fromKeypair = Keypair.generate();
        const toKeypair = Keypair.generate();
        
        console.log(`📝 Test from address: ${fromKeypair.publicKey.toString()}`);
        console.log(`📝 Test to address: ${toKeypair.publicKey.toString()}`);
        
        // Create a simple transfer transaction
        const transaction = new Transaction();
        
        // Add a transfer instruction
        const transferInstruction = SystemProgram.transfer({
            fromPubkey: fromKeypair.publicKey,
            toPubkey: toKeypair.publicKey,
            lamports: 1000000 // 0.001 SOL
        });
        transaction.add(transferInstruction);
        
        // Add Jito tip instruction
        const randomTipAccount = JITO_TIP_ACCOUNTS[Math.floor(Math.random() * JITO_TIP_ACCOUNTS.length)];
        const tipAccount = new PublicKey(randomTipAccount);
        
        const tipInstruction = SystemProgram.transfer({
            fromPubkey: fromKeypair.publicKey,
            toPubkey: tipAccount,
            lamports: 1000000 // 0.001 SOL tip
        });
        transaction.add(tipInstruction);
        
        console.log(`💰 Tip account: ${tipAccount.toString()}`);
        console.log(`🔧 Instructions: ${transaction.instructions.length}`);
        
        // Get recent blockhash
        const { blockhash } = await connection.getLatestBlockhash('finalized');
        transaction.recentBlockhash = blockhash;
        transaction.feePayer = fromKeypair.publicKey;
        
        console.log(`🔗 Blockhash: ${blockhash}`);
        console.log(`💳 Fee payer: ${fromKeypair.publicKey.toString()}`);
        
        // Sign the transaction
        transaction.sign(fromKeypair);
        
        // Serialize the transaction
        const serialized = transaction.serialize({ requireAllSignatures: false });
        const base64Tx = serialized.toString('base64');
        
        console.log(`✅ Transaction serialized successfully`);
        console.log(`📏 Serialized length: ${base64Tx.length} characters`);
        console.log(`🔍 First 100 chars: ${base64Tx.substring(0, 100)}...`);
        
        return {
            success: true,
            transaction: base64Tx,
            instructions: transaction.instructions.length,
            size: base64Tx.length
        };
        
    } catch (error) {
        console.error(`❌ Transaction serialization failed: ${error.message}`);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Test bundle submission with real transaction
 */
async function testBundleSubmission() {
    console.log('\n=== Testing Bundle Submission ===');
    
    // First create a test transaction
    const txResult = await testTransactionSerialization();
    if (!txResult.success) {
        console.log('❌ Cannot test bundle submission - transaction creation failed');
        return { success: false, error: 'Transaction creation failed' };
    }
    
    const results = [];
    
    for (const endpoint of JITO_ENDPOINTS.slice(0, 3)) { // Test first 3 endpoints
        try {
            console.log(`\n🚀 Testing bundle submission to: ${endpoint}`);
            
            const bundlePayload = {
                jsonrpc: '2.0',
                id: 1,
                method: 'sendBundle',
                params: [[txResult.transaction]]
            };
            
            console.log(`📦 Bundle payload: ${JSON.stringify(bundlePayload, null, 2)}`);
            
            const response = await axios.post(endpoint, bundlePayload, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            console.log(`✅ Bundle submitted successfully`);
            console.log(`📊 Status: ${response.status}`);
            console.log(`📋 Response: ${JSON.stringify(response.data, null, 2)}`);
            
            results.push({
                endpoint,
                success: true,
                status: response.status,
                data: response.data
            });
            
        } catch (error) {
            console.log(`❌ Bundle submission failed: ${error.message}`);
            if (error.response) {
                console.log(`📊 Status: ${error.response.status}`);
                console.log(`📋 Response: ${JSON.stringify(error.response.data, null, 2)}`);
            }
            
            results.push({
                endpoint,
                success: false,
                error: error.message,
                status: error.response?.status,
                data: error.response?.data
            });
        }
    }
    
    return results;
}

/**
 * Test our API's MEV swap with detailed logging
 */
async function testApiMevSwap() {
    console.log('\n=== Testing API MEV Swap with Priority Level ===');
    
    const testData = {
        "tokenAddress": "4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump",
        "poolAddress": "Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b",
        "dexType": "pumpfun",
        "amount": 0.01,
        "direction": "buy",
        "slippage": 0.10,
        "walletAddress": "********************************************",
        "walletId": "cbq2lb54zo7rtzv14i5sp75j",
        "priorityLevel": "high" // This should trigger MEV protection
    };
    
    try {
        console.log('📤 Sending swap request with priorityLevel=high...');
        console.log(`📋 Request: ${JSON.stringify(testData, null, 2)}`);
        
        const response = await axios.post(`${API_BASE_URL}/api/pump/swap`, testData, {
            timeout: 60000
        });
        
        console.log('✅ API swap completed');
        console.log(`📊 Status: ${response.status}`);
        console.log(`📋 Response: ${JSON.stringify(response.data, null, 2)}`);
        
        return {
            success: true,
            data: response.data
        };
        
    } catch (error) {
        console.error('❌ API swap failed:', error.response?.data || error.message);
        return {
            success: false,
            error: error.response?.data || error.message
        };
    }
}

/**
 * Main diagnostic function
 */
async function runJitoDiagnostics() {
    console.log('🔍 Comprehensive Jito Execution Diagnostics');
    console.log('=============================================');
    
    // Test 1: Jito endpoint connectivity
    const connectivityResults = await testJitoEndpointConnectivity();
    
    // Test 2: Transaction serialization
    const serializationResult = await testTransactionSerialization();
    
    // Test 3: Bundle submission
    const bundleResults = await testBundleSubmission();
    
    // Test 4: API MEV swap
    const apiResult = await testApiMevSwap();
    
    // Generate comprehensive report
    console.log('\n=============================================');
    console.log('🏁 JITO DIAGNOSTICS REPORT');
    console.log('=============================================');
    
    console.log('\n📊 ENDPOINT CONNECTIVITY:');
    connectivityResults.forEach(result => {
        const status = result.success ? '✅' : '❌';
        console.log(`  ${status} ${result.endpoint}: ${result.success ? `${result.responseTime}ms` : result.error}`);
        if (result.data?.error) {
            console.log(`      Error: ${result.data.error.message || result.data.error}`);
        }
    });
    
    console.log('\n📊 TRANSACTION SERIALIZATION:');
    if (serializationResult.success) {
        console.log(`  ✅ Success: ${serializationResult.instructions} instructions, ${serializationResult.size} bytes`);
    } else {
        console.log(`  ❌ Failed: ${serializationResult.error}`);
    }
    
    console.log('\n📊 BUNDLE SUBMISSION:');
    bundleResults.forEach(result => {
        const status = result.success ? '✅' : '❌';
        console.log(`  ${status} ${result.endpoint}: ${result.success ? 'Success' : result.error}`);
        if (result.data?.error) {
            console.log(`      Error: ${result.data.error.message || result.data.error}`);
        }
    });
    
    console.log('\n📊 API MEV SWAP:');
    if (apiResult.success) {
        const data = apiResult.data.data;
        console.log(`  ✅ Success: ${data.executionMethod} execution`);
        console.log(`      MEV Protected: ${data.mevProtected}`);
        console.log(`      Tip Amount: ${data.tipAmount} lamports`);
        if (data.bundleId) {
            console.log(`      Bundle ID: ${data.bundleId}`);
        }
        if (data.signature) {
            console.log(`      Signature: ${data.signature}`);
        }
    } else {
        console.log(`  ❌ Failed: ${apiResult.error}`);
    }
    
    // Analysis and recommendations
    console.log('\n📊 ANALYSIS:');
    const workingEndpoints = connectivityResults.filter(r => r.success).length;
    const totalEndpoints = connectivityResults.length;
    
    if (workingEndpoints === 0) {
        console.log('  🚨 CRITICAL: No Jito endpoints are accessible');
        console.log('      - Check network connectivity');
        console.log('      - Verify Jito service status');
        console.log('      - Check firewall/proxy settings');
    } else if (workingEndpoints < totalEndpoints) {
        console.log(`  ⚠️ PARTIAL: ${workingEndpoints}/${totalEndpoints} endpoints working`);
        console.log('      - Some endpoints may be rate-limited');
        console.log('      - Endpoint rotation should handle this');
    } else {
        console.log(`  ✅ GOOD: All ${workingEndpoints} endpoints accessible`);
    }
    
    if (!serializationResult.success) {
        console.log('  🚨 CRITICAL: Transaction serialization failing');
        console.log('      - Check Solana web3.js version');
        console.log('      - Verify transaction construction');
    }
    
    const bundleSuccesses = bundleResults.filter(r => r.success).length;
    if (bundleSuccesses === 0) {
        console.log('  🚨 CRITICAL: Bundle submission failing on all endpoints');
        console.log('      - Check transaction format');
        console.log('      - Verify bundle structure');
        console.log('      - Check for rate limiting');
    }
    
    console.log('\n🎯 NEXT STEPS:');
    if (workingEndpoints > 0 && serializationResult.success && bundleSuccesses > 0) {
        console.log('  ✅ Jito infrastructure appears to be working');
        console.log('  🔍 Issue may be in application-level logic');
        console.log('  📋 Check enhanced-swap.service.ts for bugs');
    } else {
        console.log('  🔧 Fix infrastructure issues first:');
        if (workingEndpoints === 0) console.log('     - Resolve network connectivity');
        if (!serializationResult.success) console.log('     - Fix transaction serialization');
        if (bundleSuccesses === 0) console.log('     - Debug bundle submission format');
    }
}

// Run the diagnostics
runJitoDiagnostics().catch(error => {
    console.error('Error running Jito diagnostics:', error);
    process.exit(1);
});
