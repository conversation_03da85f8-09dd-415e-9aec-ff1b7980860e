/**
 * Investigate the specific failing sell request
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:6001';

// The exact failing request payload from the user
const FAILING_SELL_REQUEST = {
  "tokenAddress": "a65PGnePgew3XKFM7R3Mu3DxnFykzTLtSdxRrzPpump",
  "poolAddress": "61LpLiUSyp9ACL6cDJ5fREKrk2628toxjTFXwuMM1vNR",
  "dexType": "pumpfun",
  "amount": 354,
  "direction": "sell",
  "slippage": 0.1,
  "walletAddress": "********************************************",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "mevProtection": true,
  "bribeAmount": 0.00005,
  "priorityLevel": "high",
  "priorityFee": 0.0002
};

async function investigateSpecificSellFailure() {
    console.log('🔍 INVESTIGATING SPECIFIC SELL FAILURE');
    console.log('='.repeat(60));
    console.log('Request Details:');
    console.log(JSON.stringify(FAILING_SELL_REQUEST, null, 2));
    console.log('='.repeat(60));
    
    try {
        // Step 1: Check token balance first
        console.log('\n📊 STEP 1: Checking Token Balance');
        console.log('-'.repeat(40));
        
        const balanceRequest = {
            tokenAddress: FAILING_SELL_REQUEST.tokenAddress,
            walletAddress: FAILING_SELL_REQUEST.walletAddress
        };
        
        console.log('Balance request:', JSON.stringify(balanceRequest, null, 2));
        
        const balanceResponse = await axios.post(`${API_BASE_URL}/api/token/balance`, balanceRequest, {
            timeout: 15000
        });
        
        console.log('Balance response:', JSON.stringify(balanceResponse.data, null, 2));
        
        if (balanceResponse.data.success) {
            const tokenBalance = parseFloat(balanceResponse.data.data.tokenBalance);
            const requestedAmount = FAILING_SELL_REQUEST.amount;
            const solBalance = parseFloat(balanceResponse.data.data.solBalance);
            
            console.log(`\n💰 Balance Analysis:`);
            console.log(`  - Token Balance: ${tokenBalance} tokens`);
            console.log(`  - SOL Balance: ${solBalance} SOL`);
            console.log(`  - Requested Sell Amount: ${requestedAmount} tokens`);
            console.log(`  - Has Sufficient Tokens: ${tokenBalance >= requestedAmount ? '✅ YES' : '❌ NO'}`);
            
            if (tokenBalance < requestedAmount) {
                const shortfall = requestedAmount - tokenBalance;
                console.log(`  - Shortfall: ${shortfall.toFixed(6)} tokens`);
                console.log(`  - Maximum Sellable: ${(tokenBalance * 0.99).toFixed(6)} tokens (99% of balance)`);
            }
            
            // Step 2: Test quote endpoint
            console.log('\n📈 STEP 2: Testing Quote Endpoint');
            console.log('-'.repeat(40));
            
            const quoteRequest = {
                tokenAddress: FAILING_SELL_REQUEST.tokenAddress,
                poolAddress: FAILING_SELL_REQUEST.poolAddress,
                dexType: FAILING_SELL_REQUEST.dexType,
                amount: FAILING_SELL_REQUEST.amount,
                direction: FAILING_SELL_REQUEST.direction
            };
            
            console.log('Quote request:', JSON.stringify(quoteRequest, null, 2));
            
            const quoteResponse = await axios.post(`${API_BASE_URL}/api/pump/quote`, quoteRequest, {
                timeout: 15000
            });
            
            console.log('Quote response:', JSON.stringify(quoteResponse.data, null, 2));
            
            if (quoteResponse.data.success) {
                console.log(`\n📊 Quote Analysis:`);
                console.log(`  - Expected SOL Output: ${quoteResponse.data.data.outAmount} SOL`);
                console.log(`  - Price per Token: ${quoteResponse.data.data.price} SOL`);
                console.log(`  - Min Output (with slippage): ${quoteResponse.data.data.minOut} SOL`);
                console.log(`  - Platform Fee: ${quoteResponse.data.data.fee * 100}%`);
                
                if (quoteResponse.data.data.mevProtection) {
                    console.log(`  - MEV Protection Recommended: ✅ YES`);
                    console.log(`  - Recommended Tip: ${quoteResponse.data.data.mevProtection.tipAmountSol} SOL`);
                }
                
                // Step 3: Test the actual swap endpoint
                console.log('\n🔄 STEP 3: Testing Swap Endpoint');
                console.log('-'.repeat(40));
                
                console.log('Swap request:', JSON.stringify(FAILING_SELL_REQUEST, null, 2));
                
                const swapResponse = await axios.post(`${API_BASE_URL}/api/pump/swap`, FAILING_SELL_REQUEST, {
                    timeout: 30000
                });
                
                console.log('Swap response:', JSON.stringify(swapResponse.data, null, 2));
                
                if (swapResponse.data.success) {
                    console.log('\n🎉 SWAP SUCCESSFUL!');
                    console.log(`  - Transaction Signature: ${swapResponse.data.data.signature}`);
                    console.log(`  - SOL Received: ${swapResponse.data.data.outAmount} SOL`);
                    console.log(`  - Execution Method: ${swapResponse.data.data.executionMethod}`);
                    console.log(`  - MEV Protected: ${swapResponse.data.data.mevProtected}`);
                    console.log(`  - Solscan URL: ${swapResponse.data.data.solscanUrl}`);
                } else {
                    console.log('\n❌ SWAP FAILED!');
                    console.log(`  - Error: ${swapResponse.data.error}`);
                    
                    // Analyze the error
                    analyzeSwapError(swapResponse.data.error);
                }
                
            } else {
                console.log('\n❌ QUOTE FAILED!');
                console.log(`  - Error: ${quoteResponse.data.error}`);
                
                // Analyze quote error
                analyzeQuoteError(quoteResponse.data.error);
            }
            
        } else {
            console.log('\n❌ BALANCE CHECK FAILED!');
            console.log(`  - Error: ${balanceResponse.data.error}`);
        }
        
    } catch (error) {
        console.error('\n💥 REQUEST FAILED WITH EXCEPTION:');
        console.error(`  - Error Type: ${error.name}`);
        console.error(`  - Error Message: ${error.message}`);
        
        if (error.response) {
            console.error(`  - HTTP Status: ${error.response.status}`);
            console.error(`  - Response Data:`, JSON.stringify(error.response.data, null, 2));
        }
        
        if (error.code) {
            console.error(`  - Error Code: ${error.code}`);
        }
        
        // Analyze network/connection errors
        if (error.code === 'ECONNREFUSED') {
            console.error('\n🔧 DIAGNOSIS: Connection refused - Solana service may not be running');
        } else if (error.code === 'ETIMEDOUT') {
            console.error('\n🔧 DIAGNOSIS: Request timeout - Service may be overloaded or RPC issues');
        }
    }
}

function analyzeSwapError(errorMessage) {
    console.log('\n🔍 SWAP ERROR ANALYSIS:');
    
    if (errorMessage.includes('Insufficient token balance')) {
        console.log('  - Issue: Token balance validation failed');
        console.log('  - Cause: User does not have enough tokens to sell');
        console.log('  - Solution: Reduce sell amount or check actual token balance');
    } else if (errorMessage.includes('Insufficient SOL balance')) {
        console.log('  - Issue: SOL balance validation failed');
        console.log('  - Cause: User does not have enough SOL for transaction fees');
        console.log('  - Solution: Add more SOL to wallet for fees');
    } else if (errorMessage.includes('slippage')) {
        console.log('  - Issue: Slippage protection triggered');
        console.log('  - Cause: Price moved beyond acceptable slippage tolerance');
        console.log('  - Solution: Increase slippage tolerance or retry');
    } else if (errorMessage.includes('pool')) {
        console.log('  - Issue: Pool validation failed');
        console.log('  - Cause: Pool may not exist or be invalid');
        console.log('  - Solution: Verify pool address and token address');
    } else if (errorMessage.includes('Jito') || errorMessage.includes('bundle')) {
        console.log('  - Issue: Jito/MEV protection failed');
        console.log('  - Cause: Jito bundle submission or MEV protection issue');
        console.log('  - Solution: Try without MEV protection or adjust bribe amount');
    } else if (errorMessage.includes('transaction')) {
        console.log('  - Issue: Transaction creation/execution failed');
        console.log('  - Cause: Blockchain transaction issue');
        console.log('  - Solution: Check network status and retry');
    } else {
        console.log('  - Issue: Unknown error');
        console.log('  - Cause: Unrecognized error pattern');
        console.log('  - Solution: Check logs for more details');
    }
}

function analyzeQuoteError(errorMessage) {
    console.log('\n🔍 QUOTE ERROR ANALYSIS:');
    
    if (errorMessage.includes('pool')) {
        console.log('  - Issue: Pool not found or invalid');
        console.log('  - Cause: Pool address may be incorrect');
        console.log('  - Solution: Verify pool address');
    } else if (errorMessage.includes('token')) {
        console.log('  - Issue: Token validation failed');
        console.log('  - Cause: Token address may be incorrect');
        console.log('  - Solution: Verify token address');
    } else {
        console.log('  - Issue: Unknown quote error');
        console.log('  - Solution: Check network and try again');
    }
}

// Run the investigation
investigateSpecificSellFailure().catch(console.error);
