/**
 * Simple Sell Transaction
 * Based on the previous buy transaction, you should have around 35,384 tokens
 */

const axios = require('axios');

// Configuration
const API_BASE_URL = 'http://localhost:6001';

// Your test data for selling
const SELL_DATA = {
    tokenAddress: "4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump",
    poolAddress: "Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b",
    dexType: "pumpfun",
    amount: 35000, // Sell most of your tokens (you had ~35,384 from the buy)
    direction: "sell",
    slippage: 0.10, // 10% slippage
    walletAddress: "********************************************",
    walletId: "cbq2lb54zo7rtzv14i5sp75j",
    
    // MEV Protection
    mevProtection: true,
    bribeAmount: 1000000, // 0.001 SOL bribe
    priorityLevel: "high",
    priorityFee: 100000
};

async function executeSell() {
    console.log('🚀 Executing Sell Transaction');
    console.log('='.repeat(40));
    console.log(`Selling ${SELL_DATA.amount} tokens`);
    console.log(`Wallet: ${SELL_DATA.walletAddress}`);
    console.log(`Token: ${SELL_DATA.tokenAddress}`);
    
    try {
        // First, get a quote
        console.log('\n📊 Getting sell quote...');
        const quoteResponse = await axios.post(`${API_BASE_URL}/api/pump/quote`, {
            tokenAddress: SELL_DATA.tokenAddress,
            poolAddress: SELL_DATA.poolAddress,
            dexType: SELL_DATA.dexType,
            amount: SELL_DATA.amount,
            direction: SELL_DATA.direction
        });

        if (quoteResponse.data.success) {
            const quote = quoteResponse.data.data;
            console.log('✅ Quote successful:');
            console.log(`- You will receive: ${quote.outAmount} SOL`);
            console.log(`- Price per token: ${quote.price} SOL`);
            console.log(`- Minimum output: ${quote.minOut} SOL`);
            console.log(`- Platform fee: ${quote.fee * 100}%`);
            
            // Now execute the sell
            console.log('\n💸 Executing sell transaction...');
            const sellResponse = await axios.post(`${API_BASE_URL}/api/pump/swap`, SELL_DATA);
            
            if (sellResponse.data.success) {
                const result = sellResponse.data.data;
                console.log('\n🎉 Sell transaction successful!');
                console.log(`- SOL received: ${result.outAmount} SOL`);
                console.log(`- Transaction signature: ${result.signature}`);
                console.log(`- Solscan URL: ${result.solscanUrl}`);
                
                if (result.bundleId) {
                    console.log(`- Jito Bundle ID: ${result.bundleId}`);
                    console.log(`- Jito URL: ${result.jitoUrl}`);
                    console.log('✅ MEV Protection: ACTIVE (Jito)');
                } else {
                    console.log(`- Execution method: ${result.executionMethod}`);
                    console.log(`- MEV Protected: ${result.mevProtected}`);
                }
                
                console.log('\n💰 Transaction Summary:');
                console.log(`- Sold: ${SELL_DATA.amount} tokens`);
                console.log(`- Received: ${result.outAmount} SOL`);
                console.log(`- Bribe paid: ${result.tipAmountSol || 0} SOL`);
                
            } else {
                console.log('❌ Sell transaction failed:', sellResponse.data.error);
            }
            
        } else {
            console.log('❌ Quote failed:', quoteResponse.data.error);
        }
        
    } catch (error) {
        if (error.response?.data?.error) {
            console.error('❌ Error:', error.response.data.error);
            
            // Check if it's a balance error
            if (error.response.data.error.includes('Insufficient token balance')) {
                console.log('\n💡 It looks like you might not have enough tokens or the amount is incorrect.');
                console.log('Let me try with a smaller amount...');
                
                // Try with smaller amounts
                const smallerAmounts = [10000, 5000, 1000, 500, 100];
                
                for (const amount of smallerAmounts) {
                    console.log(`\nTrying to sell ${amount} tokens...`);
                    
                    try {
                        const smallSellData = { ...SELL_DATA, amount };
                        const response = await axios.post(`${API_BASE_URL}/api/pump/swap`, smallSellData);
                        
                        if (response.data.success) {
                            console.log(`✅ Successfully sold ${amount} tokens!`);
                            console.log(`- Received: ${response.data.data.outAmount} SOL`);
                            console.log(`- Transaction: ${response.data.data.signature}`);
                            break;
                        }
                    } catch (smallError) {
                        console.log(`❌ ${amount} tokens failed:`, smallError.response?.data?.error || smallError.message);
                    }
                }
            }
        } else {
            console.error('❌ Network error:', error.message);
        }
    }
}

// Run the sell
executeSell().catch(console.error);
