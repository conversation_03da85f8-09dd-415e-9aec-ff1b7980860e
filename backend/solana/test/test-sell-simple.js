/**
 * Simple test for sell functionality
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:6001';

async function testSell() {
    console.log('Testing sell functionality...');
    
    // Test 1: Quote
    try {
        console.log('\n1. Testing quote...');
        const quoteResponse = await axios.post(`${API_BASE_URL}/api/pump/quote`, {
            tokenAddress: "********************************************",
            poolAddress: "DrpCbEhfx2LyToPMNRLhDGhiWsovm2Zg46R3H7Egy6zM",
            dexType: "pumpfun",
            amount: 246,
            direction: "sell"
        });
        
        console.log('Quote response:', JSON.stringify(quoteResponse.data, null, 2));
    } catch (error) {
        console.error('Quote error:', error.response?.data || error.message);
    }
    
    // Test 2: Balance
    try {
        console.log('\n2. Testing balance...');
        const balanceResponse = await axios.post(`${API_BASE_URL}/api/token/balance`, {
            tokenAddress: "********************************************",
            walletAddress: "********************************************"
        });
        
        console.log('Balance response:', JSON.stringify(balanceResponse.data, null, 2));
    } catch (error) {
        console.error('Balance error:', error.response?.data || error.message);
    }
    
    // Test 3: Swap
    try {
        console.log('\n3. Testing swap...');
        const swapResponse = await axios.post(`${API_BASE_URL}/api/pump/swap`, {
            tokenAddress: "********************************************",
            poolAddress: "DrpCbEhfx2LyToPMNRLhDGhiWsovm2Zg46R3H7Egy6zM",
            dexType: "pumpfun",
            amount: 1, // Small amount for testing
            direction: "sell",
            slippage: 0.1,
            walletAddress: "********************************************",
            walletId: "cbq2lb54zo7rtzv14i5sp75j"
        });
        
        console.log('Swap response:', JSON.stringify(swapResponse.data, null, 2));
    } catch (error) {
        console.error('Swap error:', error.response?.data || error.message);
    }
}

testSell().catch(console.error);
