/**
 * Test Dynamic Parameter Calculation for PumpFun Trading
 * Tests the new intelligent parameter recommendations in quote responses
 */

const axios = require('axios');

// Test configuration
const API_BASE_URL = 'http://localhost:6001';

// Test cases for different token scenarios
const TEST_CASES = [
  {
    name: 'Small Trade - New Token',
    description: 'Test 0.001 SOL trade to see recommendations for small amounts',
    data: {
      tokenAddress: '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump',
      poolAddress: 'Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b',
      dexType: 'pumpfun',
      amount: 0.001,
      direction: 'buy'
    },
    expectedRecommendations: {
      slippage: 'Should be higher for small liquidity',
      bribeAmount: 'Should be lower percentage for small trades',
      priorityFee: 'Should be minimal for small trades'
    }
  },
  {
    name: 'Medium Trade - Established Token',
    description: 'Test 0.01 SOL trade for balanced recommendations',
    data: {
      tokenAddress: '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump',
      poolAddress: 'Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b',
      dexType: 'pumpfun',
      amount: 0.01,
      direction: 'buy'
    },
    expectedRecommendations: {
      slippage: 'Should be moderate for medium trades',
      bribeAmount: 'Should be reasonable percentage',
      priorityFee: 'Should be standard for medium trades'
    }
  },
  {
    name: 'Large Trade - High Impact',
    description: 'Test 0.1 SOL trade for high-impact recommendations',
    data: {
      tokenAddress: '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump',
      poolAddress: 'Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b',
      dexType: 'pumpfun',
      amount: 0.1,
      direction: 'buy'
    },
    expectedRecommendations: {
      slippage: 'Should be higher for large impact',
      bribeAmount: 'Should be higher for MEV protection',
      priorityFee: 'Should be higher for priority'
    }
  },
  {
    name: 'Sell Trade - Different Dynamics',
    description: 'Test sell trade to see different recommendations',
    data: {
      tokenAddress: '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump',
      poolAddress: 'Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b',
      dexType: 'pumpfun',
      amount: 1000, // 1000 tokens
      direction: 'sell'
    },
    expectedRecommendations: {
      slippage: 'Should account for sell pressure',
      bribeAmount: 'Should protect against sandwich attacks',
      priorityFee: 'Should ensure quick execution'
    }
  }
];

/**
 * Test a single quote request and analyze parameter recommendations
 */
async function testDynamicParameters(testCase) {
  console.log(`\n🧪 ${testCase.name}`);
  console.log(`📝 ${testCase.description}`);
  console.log('-'.repeat(60));
  
  try {
    console.log('📤 Request Data:');
    console.log(JSON.stringify(testCase.data, null, 2));
    
    const startTime = Date.now();
    const response = await axios.post(`${API_BASE_URL}/api/pump/quote`, testCase.data);
    const responseTime = Date.now() - startTime;
    
    if (response.data.success) {
      console.log(`\n✅ Quote successful (${responseTime}ms)`);
      const data = response.data.data;
      
      console.log('\n📊 Basic Quote Information:');
      console.log(`- Output Amount: ${data.outAmount}`);
      console.log(`- Price: ${data.price}`);
      console.log(`- Default Slippage: ${(data.slippage * 100).toFixed(2)}%`);
      console.log(`- Platform Fee: ${(data.fee * 100).toFixed(2)}%`);
      console.log(`- Min Output: ${data.minOut}`);
      
      // Analyze MEV protection recommendations
      if (data.mevProtection) {
        console.log('\n🛡️ MEV Protection Analysis:');
        console.log(`- Recommended: ${data.mevProtection.recommended}`);
        console.log(`- Tip Amount: ${data.mevProtection.tipAmount} lamports (${data.mevProtection.tipAmountSol} SOL)`);
        console.log(`- Cost Percentage: ${data.mevProtection.costPercentage}%`);
        console.log(`- Reason: ${data.mevProtection.reason}`);
      }
      
      // Analyze intelligent parameter recommendations
      if (data.recommendations) {
        console.log('\n🎯 Intelligent Parameter Recommendations:');
        console.log(`- Recommended Slippage: ${data.recommendations.slippage}%`);
        console.log(`- Recommended Bribe Amount: ${data.recommendations.bribeAmount}% of trade`);
        console.log(`- Recommended Priority Fee: ${data.recommendations.priorityFee}% of trade`);
        
        console.log('\n💡 Reasoning:');
        console.log(`- Slippage: ${data.recommendations.reasoning.slippage}`);
        console.log(`- Bribe Amount: ${data.recommendations.reasoning.bribeAmount}`);
        console.log(`- Priority Fee: ${data.recommendations.reasoning.priorityFee}`);
        
        // Calculate actual costs
        const tradeAmountSOL = testCase.data.direction === 'buy' ? testCase.data.amount : 
                              parseFloat(data.outAmount); // For sells, use SOL output
        
        const bribeCostSOL = (data.recommendations.bribeAmount / 100) * tradeAmountSOL;
        const priorityFeeCostSOL = (data.recommendations.priorityFee / 100) * tradeAmountSOL;
        const totalMEVCostSOL = bribeCostSOL + priorityFeeCostSOL;
        
        console.log('\n💰 Cost Analysis:');
        console.log(`- Trade Amount: ${tradeAmountSOL.toFixed(6)} SOL`);
        console.log(`- Bribe Cost: ${bribeCostSOL.toFixed(6)} SOL (${data.recommendations.bribeAmount}%)`);
        console.log(`- Priority Fee Cost: ${priorityFeeCostSOL.toFixed(6)} SOL (${data.recommendations.priorityFee}%)`);
        console.log(`- Total MEV Cost: ${totalMEVCostSOL.toFixed(6)} SOL (${((totalMEVCostSOL / tradeAmountSOL) * 100).toFixed(2)}%)`);
        
        // Validate recommendations are reasonable
        console.log('\n✅ Recommendation Validation:');
        if (data.recommendations.slippage >= 2 && data.recommendations.slippage <= 20) {
          console.log(`✅ Slippage (${data.recommendations.slippage}%) is within reasonable range (2-20%)`);
        } else {
          console.log(`⚠️ Slippage (${data.recommendations.slippage}%) may be outside optimal range`);
        }
        
        if (data.recommendations.bribeAmount >= 0.2 && data.recommendations.bribeAmount <= 5) {
          console.log(`✅ Bribe amount (${data.recommendations.bribeAmount}%) is within reasonable range (0.2-5%)`);
        } else {
          console.log(`⚠️ Bribe amount (${data.recommendations.bribeAmount}%) may be outside optimal range`);
        }
        
        if (data.recommendations.priorityFee >= 0.001 && data.recommendations.priorityFee <= 0.5) {
          console.log(`✅ Priority fee (${data.recommendations.priorityFee}%) is within reasonable range (0.001-0.5%)`);
        } else {
          console.log(`⚠️ Priority fee (${data.recommendations.priorityFee}%) may be outside optimal range`);
        }
        
        return { success: true, hasRecommendations: true, data };
      } else {
        console.log('\n⚠️ No intelligent parameter recommendations found');
        console.log('This may be expected for non-PumpFun tokens or if analysis failed');
        return { success: true, hasRecommendations: false, data };
      }
      
    } else {
      console.log('❌ Quote Request Failed');
      console.log('Error:', response.data.error);
      return { success: false, error: response.data.error };
    }
    
  } catch (error) {
    console.log('❌ Request Error');
    if (error.response?.data) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data.error || error.response.data);
    } else {
      console.log('Network Error:', error.message);
    }
    return { success: false, error: error.message };
  }
}

/**
 * Compare recommendations across different trade sizes
 */
async function compareTradeSize() {
  console.log('\n📈 Trade Size Comparison Analysis');
  console.log('='.repeat(60));
  
  const tradeSizes = [0.001, 0.01, 0.1];
  const results = [];
  
  for (const amount of tradeSizes) {
    console.log(`\n💰 Testing ${amount} SOL trade...`);
    
    const testData = {
      tokenAddress: '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump',
      poolAddress: 'Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b',
      dexType: 'pumpfun',
      amount: amount,
      direction: 'buy'
    };
    
    try {
      const response = await axios.post(`${API_BASE_URL}/api/pump/quote`, testData);
      
      if (response.data.success && response.data.data.recommendations) {
        const rec = response.data.data.recommendations;
        results.push({
          amount,
          slippage: rec.slippage,
          bribeAmount: rec.bribeAmount,
          priorityFee: rec.priorityFee
        });
        
        console.log(`✅ ${amount} SOL: Slippage ${rec.slippage}%, Bribe ${rec.bribeAmount}%, Priority ${rec.priorityFee}%`);
      } else {
        console.log(`❌ ${amount} SOL: Failed to get recommendations`);
      }
    } catch (error) {
      console.log(`❌ ${amount} SOL: Error - ${error.message}`);
    }
  }
  
  // Analyze trends
  if (results.length >= 2) {
    console.log('\n📊 Trend Analysis:');
    console.log('Trade Size | Slippage | Bribe Amount | Priority Fee');
    console.log('-'.repeat(50));
    
    results.forEach(result => {
      console.log(`${result.amount.toString().padEnd(9)} | ${result.slippage.toString().padEnd(8)} | ${result.bribeAmount.toString().padEnd(12)} | ${result.priorityFee}`);
    });
    
    console.log('\n💡 Expected Trends:');
    console.log('- Larger trades should generally have higher slippage tolerance');
    console.log('- Bribe amounts should scale with trade value for MEV protection');
    console.log('- Priority fees should increase for larger, more time-sensitive trades');
  }
}

/**
 * Main test function
 */
async function runDynamicParameterTests() {
  console.log('🚀 Dynamic Parameter Calculation Testing');
  console.log('='.repeat(80));
  
  let totalTests = 0;
  let passedTests = 0;
  let testsWithRecommendations = 0;
  
  // Test individual cases
  for (const testCase of TEST_CASES) {
    totalTests++;
    const result = await testDynamicParameters(testCase);
    
    if (result.success) {
      passedTests++;
      if (result.hasRecommendations) {
        testsWithRecommendations++;
      }
    }
  }
  
  // Compare trade sizes
  await compareTradeSize();
  
  // Summary
  console.log('\n' + '='.repeat(80));
  console.log('🏁 Test Summary');
  console.log('='.repeat(80));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Successful Quotes: ${passedTests}`);
  console.log(`Tests with Recommendations: ${testsWithRecommendations}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  console.log(`Recommendation Rate: ${((testsWithRecommendations / passedTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests && testsWithRecommendations > 0) {
    console.log('\n🎉 All tests passed! Dynamic parameter calculation is working correctly.');
    console.log('✅ Intelligent recommendations are being generated');
    console.log('✅ Parameters scale appropriately with trade size');
    console.log('✅ Reasoning is provided for each recommendation');
  } else if (passedTests === totalTests) {
    console.log('\n⚠️ All quotes succeeded but some lack intelligent recommendations.');
    console.log('This may be expected for non-PumpFun tokens or during analysis failures.');
  } else {
    console.log('\n😞 Some tests failed. Please check the implementation.');
  }
  
  console.log('\n💡 Next Steps:');
  console.log('- Use the recommended parameters in your swap requests');
  console.log('- Test with different tokens to see varying recommendations');
  console.log('- Monitor how recommendations change with network conditions');
}

// Run tests
if (require.main === module) {
  runDynamicParameterTests().catch(console.error);
}

module.exports = { runDynamicParameterTests, testDynamicParameters, compareTradeSize };
