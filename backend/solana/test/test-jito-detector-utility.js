/**
 * Test Jito Detector Utility
 * 
 * Demonstrates how to use the Jito detection utilities
 */

const axios = require('axios');

// Import the utility functions (in a real app, you'd import from the compiled JS)
// For this test, we'll redefine the key functions

function detectJitoFromResponse(response) {
  if (!response.success || !response.data) {
    return {
      type: 'failed',
      jitoEnabled: false,
      mevAttempted: false,
      reason: response.error || 'Transaction failed'
    };
  }

  const data = response.data;

  // ✅ CONFIRMED JITO EXECUTION
  if (data.bundleId) {
    return {
      type: 'jito',
      jitoEnabled: true,
      mevAttempted: true,
      bundleId: data.bundleId,
      tipAmount: data.tipAmount,
      tipAmountSol: data.tipAmountSol,
      executionMethod: data.executionMethod,
      jitoUrl: data.jitoUrl,
      reason: 'Transaction executed via Jito bundle'
    };
  }

  if (data.executionMethod === 'jito') {
    return {
      type: 'jito',
      jitoEnabled: true,
      mevAttempted: true,
      signature: data.signature,
      tipAmount: data.tipAmount,
      tipAmountSol: data.tipAmountSol,
      executionMethod: data.executionMethod,
      reason: 'Transaction executed via Jito (no bundle ID returned)'
    };
  }

  // ⚠️ MEV ATTEMPTED (fell back to regular execution)
  if (data.mevProtected && data.tipAmount && data.tipAmount > 0) {
    return {
      type: 'mev_attempted',
      jitoEnabled: false,
      mevAttempted: true,
      signature: data.signature,
      tipAmount: data.tipAmount,
      tipAmountSol: data.tipAmountSol,
      executionMethod: data.executionMethod,
      reason: 'MEV protection attempted but fell back to regular execution'
    };
  }

  // ❌ REGULAR EXECUTION
  return {
    type: 'regular',
    jitoEnabled: false,
    mevAttempted: false,
    signature: data.signature,
    executionMethod: data.executionMethod,
    reason: 'Regular execution without MEV protection'
  };
}

function getTransactionStatus(response) {
  const detection = detectJitoFromResponse(response);
  
  switch (detection.type) {
    case 'jito':
      return '🎉 Jito MEV Protection Active';
    case 'mev_attempted':
      return '⚠️ MEV Protection Applied (Fallback)';
    case 'regular':
      return 'ℹ️ Regular Execution';
    case 'failed':
      return '❌ Transaction Failed';
    default:
      return '❓ Unknown Status';
  }
}

function formatTipAmount(lamports) {
  const sol = lamports / 1_000_000_000;
  return `${lamports.toLocaleString()} lamports (${sol.toFixed(6)} SOL)`;
}

function wouldTriggerMev(params) {
  const { priorityLevel, mevProtection, amount, slippage } = params;

  if (mevProtection === true) return true;
  if (priorityLevel === 'high' || priorityLevel === 'veryHigh') return true;
  if (amount && amount > 1.0) return true;
  if (slippage && slippage > 0.05) return true;

  return false;
}

/**
 * Test different scenarios
 */
async function testJitoDetection() {
  console.log('🧪 Testing Jito Detection Utility');
  console.log('=================================');

  const API_BASE_URL = 'http://localhost:6001';
  
  const testCases = [
    {
      name: 'Regular Transaction (no MEV)',
      params: {
        "tokenAddress": "4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump",
        "poolAddress": "Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b",
        "dexType": "pumpfun",
        "amount": 0.01,
        "direction": "buy",
        "slippage": 0.10,
        "walletAddress": "********************************************",
        "walletId": "cbq2lb54zo7rtzv14i5sp75j",
        "priorityLevel": "low"
      }
    },
    {
      name: 'High Priority (should trigger MEV)',
      params: {
        "tokenAddress": "4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump",
        "poolAddress": "Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b",
        "dexType": "pumpfun",
        "amount": 0.01,
        "direction": "buy",
        "slippage": 0.10,
        "walletAddress": "********************************************",
        "walletId": "cbq2lb54zo7rtzv14i5sp75j",
        "priorityLevel": "high"
      }
    },
    {
      name: 'Explicit MEV Protection',
      params: {
        "tokenAddress": "4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump",
        "poolAddress": "Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b",
        "dexType": "pumpfun",
        "amount": 0.01,
        "direction": "buy",
        "slippage": 0.10,
        "walletAddress": "********************************************",
        "walletId": "cbq2lb54zo7rtzv14i5sp75j",
        "mevProtection": true,
        "priorityLevel": "medium"
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n🧪 Testing: ${testCase.name}`);
    
    // Check if parameters would trigger MEV
    const wouldTrigger = wouldTriggerMev(testCase.params);
    console.log(`   📋 Would trigger MEV: ${wouldTrigger ? '✅ Yes' : '❌ No'}`);
    
    try {
      const response = await axios.post(`${API_BASE_URL}/api/pump/swap`, testCase.params, {
        timeout: 60000
      });
      
      // Use detection utility
      const detection = detectJitoFromResponse(response.data);
      const status = getTransactionStatus(response.data);
      
      console.log(`   📊 Detection Result:`);
      console.log(`      Status: ${status}`);
      console.log(`      Type: ${detection.type}`);
      console.log(`      Jito Enabled: ${detection.jitoEnabled ? '✅' : '❌'}`);
      console.log(`      MEV Attempted: ${detection.mevAttempted ? '✅' : '❌'}`);
      
      if (detection.bundleId) {
        console.log(`      Bundle ID: ${detection.bundleId}`);
      }
      
      if (detection.signature) {
        console.log(`      Signature: ${detection.signature}`);
      }
      
      if (detection.tipAmount) {
        console.log(`      Tip: ${formatTipAmount(detection.tipAmount)}`);
      }
      
      console.log(`      Reason: ${detection.reason}`);
      
    } catch (error) {
      console.log(`   ❌ Request failed: ${error.response?.data?.error || error.message}`);
    }
  }
}

/**
 * Demonstrate frontend usage patterns
 */
function demonstrateFrontendUsage() {
  console.log('\n=================================');
  console.log('🖥️ Frontend Usage Examples');
  console.log('=================================');

  // Example API responses
  const examples = [
    {
      name: 'Successful Jito Execution',
      response: {
        success: true,
        data: {
          bundleId: 'abc123...',
          jitoUrl: 'https://explorer.jito.wtf/bundle/abc123...',
          executionMethod: 'jito',
          mevProtected: true,
          tipAmount: 750000,
          tipAmountSol: 0.00075
        }
      }
    },
    {
      name: 'MEV Attempted (Fallback)',
      response: {
        success: true,
        data: {
          signature: 'xyz789...',
          executionMethod: 'privy',
          mevProtected: true,
          tipAmount: 750000,
          tipAmountSol: 0.00075
        }
      }
    },
    {
      name: 'Regular Transaction',
      response: {
        success: true,
        data: {
          signature: 'def456...',
          executionMethod: 'privy',
          mevProtected: false,
          tipAmount: 0,
          tipAmountSol: 0
        }
      }
    }
  ];

  examples.forEach(example => {
    console.log(`\n📱 ${example.name}:`);
    
    const detection = detectJitoFromResponse(example.response);
    const status = getTransactionStatus(example.response);
    
    console.log(`   User Message: "${status}"`);
    console.log(`   Detection: ${JSON.stringify(detection, null, 2)}`);
    
    // Example frontend logic
    console.log(`   Frontend Action:`);
    switch (detection.type) {
      case 'jito':
        console.log(`     - Show success with Jito badge`);
        console.log(`     - Link to Jito explorer: ${detection.jitoUrl}`);
        break;
      case 'mev_attempted':
        console.log(`     - Show MEV protection badge`);
        console.log(`     - Explain fallback execution`);
        break;
      case 'regular':
        console.log(`     - Show regular transaction status`);
        break;
    }
  });
}

/**
 * Main test function
 */
async function runTests() {
  await testJitoDetection();
  demonstrateFrontendUsage();
  
  console.log('\n=================================');
  console.log('🎯 Quick Reference');
  console.log('=================================');
  console.log('');
  console.log('✅ Confirmed Jito Execution:');
  console.log('   - response.data.bundleId exists');
  console.log('   - response.data.executionMethod === "jito"');
  console.log('');
  console.log('⚠️ MEV Attempted (Fallback):');
  console.log('   - response.data.mevProtected === true');
  console.log('   - response.data.tipAmount > 0');
  console.log('   - response.data.executionMethod === "privy"');
  console.log('');
  console.log('❌ Regular Execution:');
  console.log('   - response.data.mevProtected === false');
  console.log('   - response.data.tipAmount === 0');
  console.log('');
  console.log('🎯 Auto-MEV Triggers:');
  console.log('   - priorityLevel: "high" or "veryHigh"');
  console.log('   - mevProtection: true');
  console.log('   - Large trade amounts (> 1 SOL)');
  console.log('   - High slippage (> 5%)');
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
  process.exit(1);
});
