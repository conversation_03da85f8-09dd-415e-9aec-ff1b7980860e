/**
 * Simple verification script
 */

// Import required modules
require('ts-node/register');

// Import our implementation
const pumpFunUtils = require('../src/services/pumpFun/pumpFunUtils');
const pdaUtils = require('../src/services/pumpFun/pda.utils');
const { PublicKey } = require('@solana/web3.js');

// Define the expected addresses
const EXPECTED = {
  FEE_ACCOUNT: 'CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM',
  LIQUIDITY_MIGRATOR: '39azUYFWPz3VHgKCf3VChUwbpURdCHRxjWVowf5jUJjg'
};

// Check fee recipient
const feeRecipient = pumpFunUtils.PUMP_FEE_RECIPIENT.toString();
console.log(`Fee Recipient: ${feeRecipient}`);
console.log(`Expected: ${EXPECTED.FEE_ACCOUNT}`);
console.log(`Match: ${feeRecipient === EXPECTED.FEE_ACCOUNT ? 'YES ✅' : 'NO ❌'}`);

// Check liquidity migrator
const liquidityMigrator = pumpFunUtils.LIQUIDITY_MIGRATOR.toString();
console.log(`\nLiquidity Migrator: ${liquidityMigrator}`);
console.log(`Expected: ${EXPECTED.LIQUIDITY_MIGRATOR}`);
console.log(`Match: ${liquidityMigrator === EXPECTED.LIQUIDITY_MIGRATOR ? 'YES ✅' : 'NO ❌'}`);

// Check creator vault seed
console.log('\n--- Verifying Creator Vault Seed ---');
const feeRecipientPubkey = new PublicKey(EXPECTED.FEE_ACCOUNT);
const creatorVault = pdaUtils.derivePumpFunCreatorVaultPDA(feeRecipientPubkey);
console.log(`Derived Creator Vault PDA: ${creatorVault.toString()}`);
