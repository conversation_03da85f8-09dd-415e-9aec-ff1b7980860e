/**
 * Test Jito Transaction Serialization
 *
 * This script tests different serialization methods to find the correct
 * format for Jito bundle submission.
 */

const { Connection, PublicKey, Transaction, SystemProgram, Keypair } = require('@solana/web3.js');
const axios = require('axios');

const SOLANA_RPC_URL = 'https://mainnet.helius-rpc.com/?api-key=52496df8-0e8c-472c-b15c-defef68bed70';
const JITO_ENDPOINT = 'https://amsterdam.mainnet.block-engine.jito.wtf/api/v1/bundles';

// Jito tip accounts
const JITO_TIP_ACCOUNTS = [
    '96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5',
    'HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe',
    'Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY'
];

/**
 * Test different serialization methods
 */
async function testSerializationMethods() {
    console.log('\n=== Testing Different Serialization Methods ===');
    
    try {
        const connection = new Connection(SOLANA_RPC_URL, 'confirmed');
        
        // Create test keypair (this simulates a user's wallet)
        const userKeypair = Keypair.generate();
        console.log(`👤 User address: ${userKeypair.publicKey.toString()}`);
        
        // Get recent blockhash
        const { blockhash } = await connection.getLatestBlockhash('finalized');
        console.log(`🔗 Blockhash: ${blockhash}`);
        
        // Create a transaction similar to what our app creates
        const transaction = new Transaction();
        
        // Add Jito tip instruction (user pays tip)
        const randomTipAccount = JITO_TIP_ACCOUNTS[Math.floor(Math.random() * JITO_TIP_ACCOUNTS.length)];
        const tipAccount = new PublicKey(randomTipAccount);
        
        const tipInstruction = SystemProgram.transfer({
            fromPubkey: userKeypair.publicKey,
            toPubkey: tipAccount,
            lamports: 1000000 // 0.001 SOL tip
        });
        transaction.add(tipInstruction);
        
        // Add a dummy swap instruction (simulate PumpFun swap)
        const dummyRecipient = Keypair.generate().publicKey;
        const swapInstruction = SystemProgram.transfer({
            fromPubkey: userKeypair.publicKey,
            toPubkey: dummyRecipient,
            lamports: ******** // 0.01 SOL
        });
        transaction.add(swapInstruction);
        
        // Set transaction properties
        transaction.recentBlockhash = blockhash;
        transaction.feePayer = userKeypair.publicKey;
        
        console.log(`💰 Tip account: ${tipAccount.toString()}`);
        console.log(`🔧 Instructions: ${transaction.instructions.length}`);
        console.log(`💳 Fee payer: ${userKeypair.publicKey.toString()}`);
        
        // Test different serialization methods
        const methods = [
            {
                name: 'Unsigned (requireAllSignatures: false)',
                serialize: () => transaction.serialize({ requireAllSignatures: false }).toString('base64')
            },
            {
                name: 'Signed (requireAllSignatures: true)',
                serialize: () => {
                    const signedTx = transaction.clone();
                    signedTx.sign(userKeypair);
                    return signedTx.serialize({ requireAllSignatures: true }).toString('base64');
                }
            },
            {
                name: 'Signed (requireAllSignatures: false)',
                serialize: () => {
                    const signedTx = transaction.clone();
                    signedTx.sign(userKeypair);
                    return signedTx.serialize({ requireAllSignatures: false }).toString('base64');
                }
            },
            {
                name: 'Signed with verifySignatures: false',
                serialize: () => {
                    const signedTx = transaction.clone();
                    signedTx.sign(userKeypair);
                    return signedTx.serialize({ 
                        requireAllSignatures: true,
                        verifySignatures: false 
                    }).toString('base64');
                }
            }
        ];
        
        const results = [];
        
        for (const method of methods) {
            try {
                console.log(`\n🧪 Testing: ${method.name}`);
                
                const serialized = method.serialize();
                console.log(`   ✅ Serialization successful`);
                console.log(`   📏 Length: ${serialized.length} characters`);
                console.log(`   🔍 First 50 chars: ${serialized.substring(0, 50)}...`);
                
                // Test with Jito
                const bundlePayload = {
                    jsonrpc: '2.0',
                    id: 1,
                    method: 'sendBundle',
                    params: [[serialized]]
                };
                
                try {
                    const response = await axios.post(JITO_ENDPOINT, bundlePayload, {
                        timeout: 10000,
                        headers: { 'Content-Type': 'application/json' }
                    });
                    
                    console.log(`   ✅ Jito accepted the transaction!`);
                    console.log(`   📋 Response: ${JSON.stringify(response.data)}`);
                    
                    results.push({
                        method: method.name,
                        serialization: 'success',
                        jito: 'success',
                        data: response.data
                    });
                    
                } catch (jitoError) {
                    console.log(`   ❌ Jito rejected: ${jitoError.response?.data?.error?.message || jitoError.message}`);
                    
                    results.push({
                        method: method.name,
                        serialization: 'success',
                        jito: 'failed',
                        error: jitoError.response?.data?.error?.message || jitoError.message
                    });
                }
                
            } catch (serializationError) {
                console.log(`   ❌ Serialization failed: ${serializationError.message}`);
                
                results.push({
                    method: method.name,
                    serialization: 'failed',
                    jito: 'not_tested',
                    error: serializationError.message
                });
            }
        }
        
        return results;
        
    } catch (error) {
        console.error(`❌ Test setup failed: ${error.message}`);
        return [];
    }
}

/**
 * Test with a real PumpFun-like transaction structure
 */
async function testPumpFunLikeTransaction() {
    console.log('\n=== Testing PumpFun-like Transaction Structure ===');
    
    try {
        // This simulates what our enhanced-swap.service.ts does
        console.log('🔄 Simulating enhanced-swap.service.ts flow...');
        
        // 1. Create base transaction (this would come from PumpFun service)
        const connection = new Connection(SOLANA_RPC_URL, 'confirmed');
        const userKeypair = Keypair.generate();
        const { blockhash } = await connection.getLatestBlockhash('finalized');
        
        const baseTransaction = new Transaction();
        
        // Simulate a PumpFun swap instruction
        const swapInstruction = SystemProgram.transfer({
            fromPubkey: userKeypair.publicKey,
            toPubkey: Keypair.generate().publicKey,
            lamports: ******** // 0.01 SOL
        });
        baseTransaction.add(swapInstruction);
        baseTransaction.recentBlockhash = blockhash;
        baseTransaction.feePayer = userKeypair.publicKey;
        
        console.log('✅ Base transaction created (simulates PumpFun output)');
        
        // 2. Add Jito tip instruction (this is what addJitoTipToTransaction does)
        const tipAccount = new PublicKey(JITO_TIP_ACCOUNTS[0]);
        const tipInstruction = SystemProgram.transfer({
            fromPubkey: userKeypair.publicKey,
            toPubkey: tipAccount,
            lamports: 1500000 // 0.0015 SOL tip (high priority)
        });
        
        // Add tip instruction to the beginning
        baseTransaction.instructions.unshift(tipInstruction);
        
        console.log('✅ Jito tip instruction added');
        console.log(`   💰 Tip: 1500000 lamports to ${tipAccount.toString()}`);
        console.log(`   🔧 Total instructions: ${baseTransaction.instructions.length}`);
        
        // 3. Sign the transaction (this would be done by Privy)
        baseTransaction.sign(userKeypair);
        console.log('✅ Transaction signed');
        
        // 4. Serialize for Jito (this is what submitBundle does)
        const serialized = baseTransaction.serialize({
            requireAllSignatures: true,
            verifySignatures: false
        }).toString('base64');
        
        console.log('✅ Transaction serialized for Jito');
        console.log(`   📏 Length: ${serialized.length} characters`);
        
        // 5. Submit to Jito
        const bundlePayload = {
            jsonrpc: '2.0',
            id: 1,
            method: 'sendBundle',
            params: [[serialized]]
        };
        
        try {
            const response = await axios.post(JITO_ENDPOINT, bundlePayload, {
                timeout: 15000,
                headers: { 'Content-Type': 'application/json' }
            });
            
            console.log('🎉 SUCCESS: Jito accepted the PumpFun-like transaction!');
            console.log(`📋 Response: ${JSON.stringify(response.data, null, 2)}`);
            
            return { success: true, data: response.data };
            
        } catch (jitoError) {
            console.log(`❌ Jito rejected PumpFun-like transaction: ${jitoError.response?.data?.error?.message || jitoError.message}`);
            console.log(`📋 Full error: ${JSON.stringify(jitoError.response?.data, null, 2)}`);
            
            return { 
                success: false, 
                error: jitoError.response?.data?.error?.message || jitoError.message,
                fullError: jitoError.response?.data
            };
        }
        
    } catch (error) {
        console.error(`❌ PumpFun-like test failed: ${error.message}`);
        return { success: false, error: error.message };
    }
}

/**
 * Main test function
 */
async function runSerializationTests() {
    console.log('🧪 Jito Transaction Serialization Tests');
    console.log('=======================================');
    
    // Test 1: Different serialization methods
    const methodResults = await testSerializationMethods();
    
    // Test 2: PumpFun-like transaction
    const pumpFunResult = await testPumpFunLikeTransaction();
    
    // Generate report
    console.log('\n=======================================');
    console.log('🏁 SERIALIZATION TEST REPORT');
    console.log('=======================================');
    
    console.log('\n📊 SERIALIZATION METHODS:');
    methodResults.forEach(result => {
        const serializationStatus = result.serialization === 'success' ? '✅' : '❌';
        const jitoStatus = result.jito === 'success' ? '✅' : result.jito === 'failed' ? '❌' : '⚠️';
        console.log(`  ${serializationStatus}${jitoStatus} ${result.method}`);
        if (result.error) {
            console.log(`      Error: ${result.error}`);
        }
    });
    
    console.log('\n📊 PUMPFUN-LIKE TRANSACTION:');
    if (pumpFunResult.success) {
        console.log('  ✅ SUCCESS: Transaction format is correct!');
        console.log('      The issue is not with serialization');
    } else {
        console.log(`  ❌ FAILED: ${pumpFunResult.error}`);
        console.log('      Need to fix transaction format');
    }
    
    console.log('\n🎯 CONCLUSIONS:');
    const successfulMethods = methodResults.filter(r => r.jito === 'success');
    if (successfulMethods.length > 0) {
        console.log('  ✅ Found working serialization method(s):');
        successfulMethods.forEach(method => {
            console.log(`     - ${method.method}`);
        });
    } else {
        console.log('  ❌ No serialization methods worked');
        console.log('     - Check Jito endpoint status');
        console.log('     - Verify transaction structure');
        console.log('     - Check for rate limiting');
    }
}

// Run the tests
runSerializationTests().catch(error => {
    console.error('Error running serialization tests:', error);
    process.exit(1);
});
