/**
 * Test sell functionality with the correct available balance
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:6001';

async function testWithCorrectBalance() {
    console.log('🔧 TESTING SELL WITH CORRECT AVAILABLE BALANCE');
    console.log('='.repeat(60));
    
    try {
        // First get the actual balance
        const balanceRequest = {
            tokenAddress: "a65PGnePgew3XKFM7R3Mu3DxnFykzTLtSdxRrzPpump",
            walletAddress: "********************************************"
        };
        
        const balanceResponse = await axios.post(`${API_BASE_URL}/api/token/balance`, balanceRequest);
        
        if (!balanceResponse.data.success) {
            console.error('Failed to get balance:', balanceResponse.data.error);
            return;
        }
        
        const tokenBalance = parseFloat(balanceResponse.data.data.tokenBalance);
        console.log(`Available token balance: ${tokenBalance} tokens`);
        
        if (tokenBalance <= 0) {
            console.log('❌ No tokens available for testing');
            return;
        }
        
        // Test with 90% of available balance to avoid precision issues
        const sellAmount = Math.floor(tokenBalance * 0.9 * 1000000) / 1000000; // Round to 6 decimals
        console.log(`Testing sell with ${sellAmount} tokens (90% of balance)`);
        
        // Test 1: Quote with correct amount
        console.log('\n📈 STEP 1: Testing Quote with Correct Amount');
        console.log('-'.repeat(40));
        
        const quoteRequest = {
            tokenAddress: "a65PGnePgew3XKFM7R3Mu3DxnFykzTLtSdxRrzPpump",
            poolAddress: "61LpLiUSyp9ACL6cDJ5fREKrk2628toxjTFXwuMM1vNR",
            dexType: "pumpfun",
            amount: sellAmount,
            direction: "sell"
        };
        
        const quoteResponse = await axios.post(`${API_BASE_URL}/api/pump/quote`, quoteRequest);
        console.log('Quote response:', JSON.stringify(quoteResponse.data, null, 2));
        
        if (!quoteResponse.data.success) {
            console.error('Quote failed:', quoteResponse.data.error);
            return;
        }
        
        // Test 2: Swap without MEV protection first
        console.log('\n🔄 STEP 2: Testing Swap WITHOUT MEV Protection');
        console.log('-'.repeat(40));
        
        const swapRequestNoMEV = {
            tokenAddress: "a65PGnePgew3XKFM7R3Mu3DxnFykzTLtSdxRrzPpump",
            poolAddress: "61LpLiUSyp9ACL6cDJ5fREKrk2628toxjTFXwuMM1vNR",
            dexType: "pumpfun",
            amount: sellAmount,
            direction: "sell",
            slippage: 0.1,
            walletAddress: "********************************************",
            walletId: "cbq2lb54zo7rtzv14i5sp75j",
            mevProtection: false, // Disable MEV protection first
            bribeAmount: 0,
            priorityLevel: "medium",
            priorityFee: 0.0001
        };
        
        console.log('Swap request (no MEV):', JSON.stringify(swapRequestNoMEV, null, 2));
        
        try {
            const swapResponseNoMEV = await axios.post(`${API_BASE_URL}/api/pump/swap`, swapRequestNoMEV, {
                timeout: 30000
            });
            
            console.log('Swap response (no MEV):', JSON.stringify(swapResponseNoMEV.data, null, 2));
            
            if (swapResponseNoMEV.data.success) {
                console.log('\n✅ SELL WITHOUT MEV PROTECTION SUCCESSFUL!');
                console.log(`  - Transaction: ${swapResponseNoMEV.data.data.signature}`);
                console.log(`  - SOL Received: ${swapResponseNoMEV.data.data.outAmount}`);
                console.log(`  - Execution Method: ${swapResponseNoMEV.data.data.executionMethod}`);
                return; // Don't test with MEV if this works
            } else {
                console.log('\n❌ SELL WITHOUT MEV PROTECTION FAILED');
                console.log(`  - Error: ${swapResponseNoMEV.data.error}`);
            }
        } catch (error) {
            console.log('\n❌ SELL WITHOUT MEV PROTECTION FAILED WITH EXCEPTION');
            console.log(`  - Error: ${error.message}`);
            if (error.response?.data) {
                console.log(`  - Response: ${JSON.stringify(error.response.data, null, 2)}`);
            }
        }
        
        // Test 3: Swap with MEV protection (original failing request style)
        console.log('\n🔄 STEP 3: Testing Swap WITH MEV Protection');
        console.log('-'.repeat(40));
        
        const swapRequestWithMEV = {
            tokenAddress: "a65PGnePgew3XKFM7R3Mu3DxnFykzTLtSdxRrzPpump",
            poolAddress: "61LpLiUSyp9ACL6cDJ5fREKrk2628toxjTFXwuMM1vNR",
            dexType: "pumpfun",
            amount: sellAmount,
            direction: "sell",
            slippage: 0.1,
            walletAddress: "********************************************",
            walletId: "cbq2lb54zo7rtzv14i5sp75j",
            mevProtection: true,
            bribeAmount: 0.00005, // Very small bribe amount from original request
            priorityLevel: "high",
            priorityFee: 0.0002
        };
        
        console.log('Swap request (with MEV):', JSON.stringify(swapRequestWithMEV, null, 2));
        
        try {
            const swapResponseWithMEV = await axios.post(`${API_BASE_URL}/api/pump/swap`, swapRequestWithMEV, {
                timeout: 30000
            });
            
            console.log('Swap response (with MEV):', JSON.stringify(swapResponseWithMEV.data, null, 2));
            
            if (swapResponseWithMEV.data.success) {
                console.log('\n✅ SELL WITH MEV PROTECTION SUCCESSFUL!');
                console.log(`  - Transaction: ${swapResponseWithMEV.data.data.signature}`);
                console.log(`  - SOL Received: ${swapResponseWithMEV.data.data.outAmount}`);
                console.log(`  - Execution Method: ${swapResponseWithMEV.data.data.executionMethod}`);
                console.log(`  - MEV Protected: ${swapResponseWithMEV.data.data.mevProtected}`);
                if (swapResponseWithMEV.data.data.bundleId) {
                    console.log(`  - Jito Bundle ID: ${swapResponseWithMEV.data.data.bundleId}`);
                }
            } else {
                console.log('\n❌ SELL WITH MEV PROTECTION FAILED');
                console.log(`  - Error: ${swapResponseWithMEV.data.error}`);
                
                // Analyze MEV-specific errors
                if (swapResponseWithMEV.data.error.includes('bribe') || swapResponseWithMEV.data.error.includes('tip')) {
                    console.log('\n🔍 MEV ERROR ANALYSIS:');
                    console.log('  - Issue: Bribe/tip amount may be too low');
                    console.log('  - Recommended tip from quote:', quoteResponse.data.data.mevProtection?.tipAmountSol, 'SOL');
                    console.log('  - Current bribe amount:', swapRequestWithMEV.bribeAmount, 'SOL');
                    console.log('  - Solution: Increase bribe amount to recommended level');
                }
            }
        } catch (error) {
            console.log('\n❌ SELL WITH MEV PROTECTION FAILED WITH EXCEPTION');
            console.log(`  - Error: ${error.message}`);
            if (error.response?.data) {
                console.log(`  - Response: ${JSON.stringify(error.response.data, null, 2)}`);
            }
            
            // Check for Jito-specific errors
            if (error.message.includes('timeout') || error.message.includes('ETIMEDOUT')) {
                console.log('\n🔍 JITO TIMEOUT ANALYSIS:');
                console.log('  - Issue: Request timeout during Jito bundle submission');
                console.log('  - Cause: Jito network congestion or bundle rejection');
                console.log('  - Solution: Retry without MEV protection or increase timeout');
            }
        }
        
        // Test 4: Test with recommended MEV parameters
        console.log('\n🔄 STEP 4: Testing Swap WITH RECOMMENDED MEV Parameters');
        console.log('-'.repeat(40));
        
        const recommendedBribe = quoteResponse.data.data.mevProtection?.tipAmountSol || 0.005;
        
        const swapRequestRecommended = {
            tokenAddress: "a65PGnePgew3XKFM7R3Mu3DxnFykzTLtSdxRrzPpump",
            poolAddress: "61LpLiUSyp9ACL6cDJ5fREKrk2628toxjTFXwuMM1vNR",
            dexType: "pumpfun",
            amount: sellAmount,
            direction: "sell",
            slippage: 0.1,
            walletAddress: "********************************************",
            walletId: "cbq2lb54zo7rtzv14i5sp75j",
            mevProtection: true,
            bribeAmount: recommendedBribe, // Use recommended bribe amount
            priorityLevel: "high",
            priorityFee: 0.0008 // Increase priority fee
        };
        
        console.log('Swap request (recommended MEV):', JSON.stringify(swapRequestRecommended, null, 2));
        
        try {
            const swapResponseRecommended = await axios.post(`${API_BASE_URL}/api/pump/swap`, swapRequestRecommended, {
                timeout: 45000 // Longer timeout for Jito
            });
            
            console.log('Swap response (recommended MEV):', JSON.stringify(swapResponseRecommended.data, null, 2));
            
            if (swapResponseRecommended.data.success) {
                console.log('\n🎉 SELL WITH RECOMMENDED MEV PARAMETERS SUCCESSFUL!');
                console.log(`  - Transaction: ${swapResponseRecommended.data.data.signature}`);
                console.log(`  - SOL Received: ${swapResponseRecommended.data.data.outAmount}`);
                console.log(`  - Execution Method: ${swapResponseRecommended.data.data.executionMethod}`);
                console.log(`  - MEV Protected: ${swapResponseRecommended.data.data.mevProtected}`);
                if (swapResponseRecommended.data.data.bundleId) {
                    console.log(`  - Jito Bundle ID: ${swapResponseRecommended.data.data.bundleId}`);
                }
            } else {
                console.log('\n❌ SELL WITH RECOMMENDED MEV PARAMETERS FAILED');
                console.log(`  - Error: ${swapResponseRecommended.data.error}`);
            }
        } catch (error) {
            console.log('\n❌ SELL WITH RECOMMENDED MEV PARAMETERS FAILED WITH EXCEPTION');
            console.log(`  - Error: ${error.message}`);
            if (error.response?.data) {
                console.log(`  - Response: ${JSON.stringify(error.response.data, null, 2)}`);
            }
        }
        
    } catch (error) {
        console.error('Test failed:', error.message);
        if (error.response?.data) {
            console.error('Response data:', JSON.stringify(error.response.data, null, 2));
        }
    }
}

testWithCorrectBalance().catch(console.error);
