/**
 * Test the new /api/token/balance endpoint
 */

const axios = require('axios');

// Test configuration
const API_BASE_URL = 'http://localhost:6001';

// Test cases
const TEST_CASES = [
  {
    name: 'Valid token with balance',
    data: {
      tokenAddress: '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump',
      walletAddress: '********************************************'
    },
    expectedSuccess: true
  },
  {
    name: 'Valid token with no balance (different wallet)',
    data: {
      tokenAddress: '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump',
      walletAddress: '********************************' // System program address (should have no tokens)
    },
    expectedSuccess: true
  },
  {
    name: 'Invalid token address',
    data: {
      tokenAddress: 'invalid-token-address',
      walletAddress: '********************************************'
    },
    expectedSuccess: false
  },
  {
    name: 'Invalid wallet address',
    data: {
      tokenAddress: '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump',
      walletAddress: 'invalid-wallet-address'
    },
    expectedSuccess: false
  },
  {
    name: 'Missing token address',
    data: {
      walletAddress: '********************************************'
    },
    expectedSuccess: false
  },
  {
    name: 'Missing wallet address',
    data: {
      tokenAddress: '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump'
    },
    expectedSuccess: false
  },
  {
    name: 'Empty request body',
    data: {},
    expectedSuccess: false
  }
];

/**
 * Test the health endpoint
 */
async function testHealthEndpoint() {
  console.log('\n🏥 Testing Token Balance Health Endpoint');
  console.log('='.repeat(50));
  
  try {
    const response = await axios.get(`${API_BASE_URL}/api/token/health`);
    
    console.log('✅ Health check successful');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
    return true;
  } catch (error) {
    console.log('❌ Health check failed');
    console.log('Error:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Test a single balance request
 */
async function testBalanceRequest(testCase) {
  console.log(`\n🧪 Testing: ${testCase.name}`);
  console.log('-'.repeat(30));
  console.log('Request data:', JSON.stringify(testCase.data, null, 2));
  
  try {
    const response = await axios.post(`${API_BASE_URL}/api/token/balance`, testCase.data);
    
    if (testCase.expectedSuccess) {
      console.log('✅ Request successful (as expected)');
      console.log('Status:', response.status);
      console.log('Response:', JSON.stringify(response.data, null, 2));
      
      // Validate response structure
      if (response.data.success && response.data.data) {
        const data = response.data.data;
        console.log('\n📊 Balance Summary:');
        console.log(`- Token Balance: ${data.tokenBalance}`);
        console.log(`- Raw Balance: ${data.rawBalance}`);
        console.log(`- Decimals: ${data.decimals}`);
        console.log(`- SOL Balance: ${data.solBalance} SOL`);
        console.log(`- Token Account: ${data.tokenAccountAddress}`);
        console.log(`- Has Token Account: ${data.hasTokenAccount}`);
        
        return { success: true, data };
      } else {
        console.log('⚠️ Unexpected response structure');
        return { success: false };
      }
    } else {
      console.log('❌ Request succeeded but was expected to fail');
      console.log('Response:', JSON.stringify(response.data, null, 2));
      return { success: false };
    }
    
  } catch (error) {
    if (!testCase.expectedSuccess) {
      console.log('✅ Request failed (as expected)');
      console.log('Status:', error.response?.status);
      console.log('Error:', error.response?.data?.error || error.message);
      return { success: true };
    } else {
      console.log('❌ Request failed unexpectedly');
      console.log('Status:', error.response?.status);
      console.log('Error:', error.response?.data || error.message);
      return { success: false };
    }
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Token Balance API Tests');
  console.log('='.repeat(60));
  
  // Test health endpoint first
  const healthOk = await testHealthEndpoint();
  
  if (!healthOk) {
    console.log('\n❌ Health check failed - skipping balance tests');
    return;
  }
  
  // Run all test cases
  let passedTests = 0;
  let totalTests = TEST_CASES.length;
  
  for (const testCase of TEST_CASES) {
    const result = await testBalanceRequest(testCase);
    if (result.success) {
      passedTests++;
    }
  }
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('🏁 Test Summary');
  console.log('='.repeat(60));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${totalTests - passedTests}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! The Token Balance API is working correctly.');
  } else {
    console.log('\n😞 Some tests failed. Please check the implementation.');
  }
}

/**
 * Quick balance check for your wallet
 */
async function quickBalanceCheck() {
  console.log('\n💰 Quick Balance Check for Your Wallet');
  console.log('='.repeat(50));
  
  const testCase = TEST_CASES[0]; // Use the first test case (your wallet)
  const result = await testBalanceRequest(testCase);
  
  if (result.success && result.data) {
    const balance = parseFloat(result.data.tokenBalance);
    console.log('\n🎯 Quick Summary:');
    if (balance > 0) {
      console.log(`✅ You have ${result.data.tokenBalance} tokens`);
      console.log(`💰 SOL Balance: ${result.data.solBalance} SOL`);
      console.log('💡 You can use this data to create sell transactions');
    } else {
      console.log('❌ You have no tokens of this type');
    }
  }
}

// Run tests
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--quick')) {
    quickBalanceCheck().catch(console.error);
  } else {
    runAllTests().catch(console.error);
  }
}

module.exports = { runAllTests, quickBalanceCheck, testBalanceRequest };
