const axios = require('axios');

async function testSellSlippage() {
  console.log('Testing PumpFun sell with slippage fix...');
  
  const sellRequest = {
    tokenAddress: "GxfZBiRyNbtPdPvxHyjnPKRJyR5mGJiCGY4wzKwUpump",
    poolAddress: "81oRNu8jP5oeVNQL8injYe9DSfK5CnJUruBq3qGZsCj8",
    dexType: "pumpfun",
    amount: 0.01,
    direction: "sell",
    slippage: 0.5, // 50% slippage for testing
    walletAddress: "********************************************",
    walletId: "cbq2lb54zo7rtzv14i5sp75j"
  };

  try {
    console.log('Making request to:', 'http://localhost:6001/api/pump/swap');
    console.log('Request body:', JSON.stringify(sellRequest, null, 2));
    
    const response = await axios.post('http://localhost:6001/api/pump/swap', sellRequest, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60 second timeout
    });
    
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('Error occurred:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.error('No response received:', error.message);
    } else {
      console.error('Request setup error:', error.message);
    }
  }
}

// Run the test
testSellSlippage().catch(console.error);
