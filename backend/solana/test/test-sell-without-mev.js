/**
 * Test sell functionality without MEV protection to isolate <PERSON><PERSON> issues
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:6001';

async function testSellWithoutMEV() {
    console.log('🔧 TESTING SELL WITHOUT MEV PROTECTION');
    console.log('='.repeat(50));
    
    try {
        // Get balance first
        const balanceResponse = await axios.post(`${API_BASE_URL}/api/token/balance`, {
            tokenAddress: "a65PGnePgew3XKFM7R3Mu3DxnFykzTLtSdxRrzPpump",
            walletAddress: "********************************************"
        });
        
        if (!balanceResponse.data.success) {
            console.error('Balance check failed:', balanceResponse.data.error);
            return;
        }
        
        const tokenBalance = parseFloat(balanceResponse.data.data.tokenBalance);
        console.log(`Available balance: ${tokenBalance} tokens`);
        
        if (tokenBalance <= 0) {
            console.log('❌ No tokens available for testing');
            return;
        }
        
        // Use 80% of available balance
        const sellAmount = Math.floor(tokenBalance * 0.8 * 1000000) / 1000000;
        console.log(`Testing sell with ${sellAmount} tokens`);
        
        // Test 1: Simple sell without MEV protection
        console.log('\n🔄 STEP 1: Testing Simple Sell (No MEV)');
        console.log('-'.repeat(40));
        
        const simpleSellRequest = {
            tokenAddress: "a65PGnePgew3XKFM7R3Mu3DxnFykzTLtSdxRrzPpump",
            poolAddress: "61LpLiUSyp9ACL6cDJ5fREKrk2628toxjTFXwuMM1vNR",
            dexType: "pumpfun",
            amount: sellAmount,
            direction: "sell",
            slippage: 0.1,
            walletAddress: "********************************************",
            walletId: "cbq2lb54zo7rtzv14i5sp75j",
            mevProtection: false,
            bribeAmount: 0,
            priorityLevel: "low",
            priorityFee: 0.00001
        };
        
        console.log('Request:', JSON.stringify(simpleSellRequest, null, 2));
        
        const sellResponse = await axios.post(`${API_BASE_URL}/api/pump/swap`, simpleSellRequest, {
            timeout: 30000
        });
        
        console.log('Response:', JSON.stringify(sellResponse.data, null, 2));
        
        if (sellResponse.data.success) {
            console.log('\n✅ SIMPLE SELL SUCCESSFUL!');
            console.log(`  - Transaction: ${sellResponse.data.data.signature}`);
            console.log(`  - SOL Received: ${sellResponse.data.data.outAmount}`);
            console.log(`  - Execution Method: ${sellResponse.data.data.executionMethod}`);
            console.log(`  - MEV Protected: ${sellResponse.data.data.mevProtected}`);
            
            // Test 2: Now try with minimal MEV protection
            console.log('\n🔄 STEP 2: Testing with Minimal MEV Protection');
            console.log('-'.repeat(40));
            
            // Wait a bit before next test
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Get updated balance
            const balanceResponse2 = await axios.post(`${API_BASE_URL}/api/token/balance`, {
                tokenAddress: "a65PGnePgew3XKFM7R3Mu3DxnFykzTLtSdxRrzPpump",
                walletAddress: "********************************************"
            });
            
            if (balanceResponse2.data.success) {
                const newBalance = parseFloat(balanceResponse2.data.data.tokenBalance);
                console.log(`Updated balance: ${newBalance} tokens`);
                
                if (newBalance > 0) {
                    const sellAmount2 = Math.floor(newBalance * 0.5 * 1000000) / 1000000;
                    console.log(`Testing MEV sell with ${sellAmount2} tokens`);
                    
                    const mevSellRequest = {
                        tokenAddress: "a65PGnePgew3XKFM7R3Mu3DxnFykzTLtSdxRrzPpump",
                        poolAddress: "61LpLiUSyp9ACL6cDJ5fREKrk2628toxjTFXwuMM1vNR",
                        dexType: "pumpfun",
                        amount: sellAmount2,
                        direction: "sell",
                        slippage: 0.1,
                        walletAddress: "********************************************",
                        walletId: "cbq2lb54zo7rtzv14i5sp75j",
                        mevProtection: true,
                        bribeAmount: 0.001, // Increase bribe amount
                        priorityLevel: "medium", // Lower priority level
                        priorityFee: 0.0005
                    };
                    
                    console.log('MEV Request:', JSON.stringify(mevSellRequest, null, 2));
                    
                    try {
                        const mevSellResponse = await axios.post(`${API_BASE_URL}/api/pump/swap`, mevSellRequest, {
                            timeout: 45000 // Longer timeout for MEV
                        });
                        
                        console.log('MEV Response:', JSON.stringify(mevSellResponse.data, null, 2));
                        
                        if (mevSellResponse.data.success) {
                            console.log('\n✅ MEV SELL SUCCESSFUL!');
                            console.log(`  - Transaction: ${mevSellResponse.data.data.signature}`);
                            console.log(`  - SOL Received: ${mevSellResponse.data.data.outAmount}`);
                            console.log(`  - Execution Method: ${mevSellResponse.data.data.executionMethod}`);
                            console.log(`  - MEV Protected: ${mevSellResponse.data.data.mevProtected}`);
                            if (mevSellResponse.data.data.bundleId) {
                                console.log(`  - Bundle ID: ${mevSellResponse.data.data.bundleId}`);
                            }
                        } else {
                            console.log('\n❌ MEV SELL FAILED');
                            console.log(`  - Error: ${mevSellResponse.data.error}`);
                            
                            // Analyze MEV-specific errors
                            analyzeMEVError(mevSellResponse.data.error);
                        }
                    } catch (mevError) {
                        console.log('\n❌ MEV SELL FAILED WITH EXCEPTION');
                        console.log(`  - Error: ${mevError.message}`);
                        if (mevError.response?.data) {
                            console.log(`  - Response: ${JSON.stringify(mevError.response.data, null, 2)}`);
                        }
                        
                        // Analyze timeout and Jito errors
                        if (mevError.message.includes('timeout') || mevError.code === 'ETIMEDOUT') {
                            console.log('\n🔍 TIMEOUT ANALYSIS:');
                            console.log('  - Issue: Request timeout during MEV/Jito processing');
                            console.log('  - Likely Cause: Jito bundle submission taking too long');
                            console.log('  - Solution: Disable MEV protection or increase timeout');
                        }
                    }
                } else {
                    console.log('No tokens left for MEV test');
                }
            }
            
        } else {
            console.log('\n❌ SIMPLE SELL FAILED');
            console.log(`  - Error: ${sellResponse.data.error}`);
            
            // Analyze basic sell errors
            if (sellResponse.data.error.includes('Insufficient token balance')) {
                console.log('\n🔍 BALANCE ERROR ANALYSIS:');
                console.log('  - Issue: Token balance validation failed');
                console.log('  - Available:', tokenBalance, 'tokens');
                console.log('  - Requested:', sellAmount, 'tokens');
                console.log('  - Solution: The balance validation fix should handle this');
            }
        }
        
    } catch (error) {
        console.error('Test failed:', error.message);
        if (error.response?.data) {
            console.error('Response:', JSON.stringify(error.response.data, null, 2));
        }
        
        if (error.code === 'ECONNREFUSED') {
            console.error('\n🔧 DIAGNOSIS: Solana service not running');
        } else if (error.code === 'ETIMEDOUT') {
            console.error('\n🔧 DIAGNOSIS: Service timeout - possible RPC or Jito issues');
        }
    }
}

function analyzeMEVError(errorMessage) {
    console.log('\n🔍 MEV ERROR ANALYSIS:');
    
    if (errorMessage.includes('bribe') || errorMessage.includes('tip')) {
        console.log('  - Issue: Bribe/tip amount insufficient');
        console.log('  - Solution: Increase bribe amount to at least 0.001 SOL');
    } else if (errorMessage.includes('Jito') || errorMessage.includes('bundle')) {
        console.log('  - Issue: Jito bundle submission failed');
        console.log('  - Solution: Try without MEV protection or check Jito network status');
    } else if (errorMessage.includes('timeout')) {
        console.log('  - Issue: MEV processing timeout');
        console.log('  - Solution: Increase timeout or disable MEV protection');
    } else if (errorMessage.includes('rate limit')) {
        console.log('  - Issue: Jito rate limiting');
        console.log('  - Solution: Wait and retry, or use different endpoint');
    } else {
        console.log('  - Issue: Unknown MEV error');
        console.log('  - Error:', errorMessage);
    }
}

testSellWithoutMEV().catch(console.error);
