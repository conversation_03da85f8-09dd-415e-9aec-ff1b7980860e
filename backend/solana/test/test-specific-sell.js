/**
 * Test the specific failing sell request provided by the user
 */

const axios = require('axios');

// Configuration
const API_BASE_URL = 'http://localhost:6001';

// The exact failing request payload from the user
const FAILING_SELL_REQUEST = {
  "tokenAddress": "********************************************",
  "poolAddress": "DrpCbEhfx2LyToPMNRLhDGhiWsovm2Zg46R3H7Egy6zM",
  "dexType": "pumpfun",
  "amount": 246,
  "direction": "sell",
  "slippage": 0.1,
  "walletAddress": "********************************************",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "mevProtection": true,
  "bribeAmount": 0.00005,
  "priorityLevel": "high",
  "priorityFee": 0.0002
};

async function testSpecificSellRequest() {
    console.log('🚀 Testing Specific Failing Sell Request');
    console.log('='.repeat(50));
    console.log('Request Details:');
    console.log(JSON.stringify(FAILING_SELL_REQUEST, null, 2));
    
    try {
        // Step 1: Test the quote endpoint first
        console.log('\n📊 Step 1: Testing quote endpoint...');
        const quoteRequest = {
            tokenAddress: FAILING_SELL_REQUEST.tokenAddress,
            poolAddress: FAILING_SELL_REQUEST.poolAddress,
            dexType: FAILING_SELL_REQUEST.dexType,
            amount: FAILING_SELL_REQUEST.amount,
            direction: FAILING_SELL_REQUEST.direction
        };
        
        console.log('Quote request:', JSON.stringify(quoteRequest, null, 2));
        
        const quoteResponse = await axios.post(`${API_BASE_URL}/api/pump/quote`, quoteRequest);
        
        if (quoteResponse.data.success) {
            console.log('✅ Quote successful:');
            console.log(JSON.stringify(quoteResponse.data, null, 2));
        } else {
            console.log('❌ Quote failed:', quoteResponse.data.error);
            return;
        }
        
        // Step 2: Test the balance endpoint
        console.log('\n💰 Step 2: Testing balance endpoint...');
        const balanceRequest = {
            tokenAddress: FAILING_SELL_REQUEST.tokenAddress,
            walletAddress: FAILING_SELL_REQUEST.walletAddress
        };
        
        console.log('Balance request:', JSON.stringify(balanceRequest, null, 2));
        
        const balanceResponse = await axios.post(`${API_BASE_URL}/api/token/balance`, balanceRequest);
        
        if (balanceResponse.data.success) {
            console.log('✅ Balance check successful:');
            console.log(JSON.stringify(balanceResponse.data, null, 2));
            
            const tokenBalance = parseFloat(balanceResponse.data.data.tokenBalance);
            const requestedAmount = FAILING_SELL_REQUEST.amount;
            
            if (tokenBalance < requestedAmount) {
                console.log(`⚠️  WARNING: Insufficient balance!`);
                console.log(`Available: ${tokenBalance} tokens`);
                console.log(`Requested: ${requestedAmount} tokens`);
                console.log(`Shortfall: ${requestedAmount - tokenBalance} tokens`);
                
                // Adjust the sell amount to available balance
                if (tokenBalance > 0) {
                    console.log(`\n🔄 Adjusting sell amount to available balance: ${tokenBalance}`);
                    FAILING_SELL_REQUEST.amount = Math.floor(tokenBalance * 0.9); // Use 90% of available balance
                    console.log(`New sell amount: ${FAILING_SELL_REQUEST.amount} tokens`);
                } else {
                    console.log('❌ No tokens available to sell');
                    return;
                }
            } else {
                console.log('✅ Sufficient balance for sell transaction');
            }
        } else {
            console.log('❌ Balance check failed:', balanceResponse.data.error);
            return;
        }
        
        // Step 3: Test the actual swap endpoint
        console.log('\n💸 Step 3: Testing swap endpoint...');
        console.log('Final sell request:', JSON.stringify(FAILING_SELL_REQUEST, null, 2));
        
        const swapResponse = await axios.post(`${API_BASE_URL}/api/pump/swap`, FAILING_SELL_REQUEST);
        
        if (swapResponse.data.success) {
            console.log('🎉 Sell transaction successful!');
            console.log(JSON.stringify(swapResponse.data, null, 2));
        } else {
            console.log('❌ Sell transaction failed:', swapResponse.data.error);
            
            // Analyze the error
            if (swapResponse.data.error) {
                console.log('\n🔍 Error Analysis:');
                const error = swapResponse.data.error;
                
                if (error.includes('Insufficient')) {
                    console.log('- Error Type: Insufficient balance/funds');
                } else if (error.includes('slippage')) {
                    console.log('- Error Type: Slippage related');
                } else if (error.includes('pool')) {
                    console.log('- Error Type: Pool related');
                } else if (error.includes('transaction')) {
                    console.log('- Error Type: Transaction creation/execution');
                } else {
                    console.log('- Error Type: Unknown');
                }
            }
        }
        
    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
        
        if (error.response?.data) {
            console.log('Error response data:', JSON.stringify(error.response.data, null, 2));
        }
        
        if (error.response?.status) {
            console.log('HTTP Status:', error.response.status);
        }
    }
}

// Run the test
testSpecificSellRequest().catch(console.error);
