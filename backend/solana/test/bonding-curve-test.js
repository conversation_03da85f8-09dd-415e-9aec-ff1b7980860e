/**
 * Test Bonding Curve Analysis for PumpFun Tokens
 * Tests the new bonding curve progression information in quote responses
 */

const axios = require('axios');

// Test configuration
const API_BASE_URL = 'http://localhost:6001';

// Test cases for different bonding curve stages
const TEST_CASES = [
  {
    name: 'Your Test Token - Current Status',
    description: 'Check current bonding curve status of your test token',
    data: {
      tokenAddress: '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump',
      poolAddress: 'Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b',
      dexType: 'pumpfun',
      amount: 0.01,
      direction: 'buy'
    },
    expectedStage: 'Unknown - will determine from response'
  },
  {
    name: 'Small Trade Analysis',
    description: 'Test bonding curve analysis with small trade',
    data: {
      tokenAddress: '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump',
      poolAddress: 'Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b',
      dexType: 'pumpfun',
      amount: 0.001,
      direction: 'buy'
    },
    expectedStage: 'Should show same progression regardless of trade size'
  },
  {
    name: 'Sell Trade Analysis',
    description: 'Test bonding curve analysis with sell trade',
    data: {
      tokenAddress: '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump',
      poolAddress: 'Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b',
      dexType: 'pumpfun',
      amount: 1000, // 1000 tokens
      direction: 'sell'
    },
    expectedStage: 'Should show same progression for sell trades'
  },
  {
    name: 'Non-PumpFun Token',
    description: 'Test that bonding curve info is not included for non-PumpFun tokens',
    data: {
      tokenAddress: 'HNGtDJqUf6UjFAzBbZ2gtB16Cov3R8k6c2nQqG4Lpump',
      poolAddress: '5yRbZMHCRQCVnANsjk6BYviSDhHwFfew7mVQEdrX8W7w',
      dexType: 'pumpswap', // Not PumpFun
      amount: 0.01,
      direction: 'buy'
    },
    expectedStage: 'Should not include bonding curve info'
  }
];

/**
 * Test a single quote request and analyze bonding curve information
 */
async function testBondingCurveAnalysis(testCase) {
  console.log(`\n🧪 ${testCase.name}`);
  console.log(`📝 ${testCase.description}`);
  console.log('-'.repeat(60));
  
  try {
    console.log('📤 Request Data:');
    console.log(JSON.stringify(testCase.data, null, 2));
    
    const startTime = Date.now();
    const response = await axios.post(`${API_BASE_URL}/api/pump/quote`, testCase.data);
    const responseTime = Date.now() - startTime;
    
    if (response.data.success) {
      console.log(`\n✅ Quote successful (${responseTime}ms)`);
      const data = response.data.data;
      
      console.log('\n📊 Basic Quote Information:');
      console.log(`- Output Amount: ${data.outAmount}`);
      console.log(`- Price: ${data.price}`);
      console.log(`- Default Slippage: ${(data.slippage * 100).toFixed(2)}%`);
      console.log(`- Platform Fee: ${(data.fee * 100).toFixed(2)}%`);
      
      // Analyze bonding curve information
      if (data.bondingCurve) {
        console.log('\n🎯 Bonding Curve Analysis:');
        console.log(`- Progress: ${data.bondingCurve.progressPercentage}%`);
        console.log(`- Current SOL Raised: ${data.bondingCurve.currentSolRaised} SOL`);
        console.log(`- Graduation Threshold: ${data.bondingCurve.graduationThreshold} SOL`);
        console.log(`- Is Graduated: ${data.bondingCurve.isGraduated}`);
        console.log(`- Status: ${data.bondingCurve.status}`);
        console.log(`- Description: ${data.bondingCurve.description}`);
        
        // Calculate additional metrics
        const remainingSol = data.bondingCurve.graduationThreshold - data.bondingCurve.currentSolRaised;
        const progressStage = getBondingCurveStage(data.bondingCurve.progressPercentage);
        
        console.log('\n📈 Progress Analysis:');
        console.log(`- Stage: ${progressStage}`);
        console.log(`- Remaining SOL to graduation: ${remainingSol.toFixed(2)} SOL`);
        console.log(`- Progress ratio: ${(data.bondingCurve.currentSolRaised / data.bondingCurve.graduationThreshold * 100).toFixed(1)}%`);
        
        // Risk assessment based on bonding curve stage
        console.log('\n⚠️ Risk Assessment:');
        if (data.bondingCurve.isGraduated) {
          console.log('✅ LOW RISK: Token has graduated to AMM/Raydium');
        } else if (data.bondingCurve.progressPercentage >= 80) {
          console.log('🟡 MEDIUM RISK: Close to graduation, relatively stable');
        } else if (data.bondingCurve.progressPercentage >= 40) {
          console.log('🟠 MEDIUM-HIGH RISK: Mid-stage bonding curve');
        } else {
          console.log('🔴 HIGH RISK: Early stage bonding curve, high volatility expected');
        }
        
        // Trading implications
        console.log('\n💡 Trading Implications:');
        if (data.bondingCurve.progressPercentage >= 90) {
          console.log('- Consider higher slippage due to potential graduation');
          console.log('- Token may graduate during your trade');
          console.log('- Price may be more stable after graduation');
        } else if (data.bondingCurve.progressPercentage >= 50) {
          console.log('- Moderate liquidity expected');
          console.log('- Price movements may be significant');
          console.log('- Consider MEV protection for larger trades');
        } else {
          console.log('- Low liquidity, expect high slippage');
          console.log('- High price volatility possible');
          console.log('- Small trades recommended');
        }
        
        return { success: true, hasBondingCurve: true, data };
      } else {
        if (testCase.data.dexType === 'pumpfun') {
          console.log('\n⚠️ No bonding curve information found for PumpFun token');
          console.log('This may indicate an issue with the analysis or pool data');
        } else {
          console.log('\n✅ No bonding curve information (expected for non-PumpFun tokens)');
        }
        return { success: true, hasBondingCurve: false, data };
      }
      
    } else {
      console.log('❌ Quote Request Failed');
      console.log('Error:', response.data.error);
      return { success: false, error: response.data.error };
    }
    
  } catch (error) {
    console.log('❌ Request Error');
    if (error.response?.data) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data.error || error.response.data);
    } else {
      console.log('Network Error:', error.message);
    }
    return { success: false, error: error.message };
  }
}

/**
 * Get bonding curve stage description
 */
function getBondingCurveStage(progressPercentage) {
  if (progressPercentage >= 100) {
    return 'Graduated';
  } else if (progressPercentage >= 90) {
    return 'Pre-graduation';
  } else if (progressPercentage >= 70) {
    return 'Late stage';
  } else if (progressPercentage >= 40) {
    return 'Mid stage';
  } else if (progressPercentage >= 10) {
    return 'Early stage';
  } else {
    return 'Launch stage';
  }
}

/**
 * Compare bonding curve analysis across different trade types
 */
async function compareBondingCurveConsistency() {
  console.log('\n📊 Bonding Curve Consistency Analysis');
  console.log('='.repeat(60));
  console.log('Testing that bonding curve info is consistent across different trade types...');
  
  const tradeTypes = [
    { amount: 0.001, direction: 'buy', name: 'Small Buy' },
    { amount: 0.01, direction: 'buy', name: 'Medium Buy' },
    { amount: 1000, direction: 'sell', name: 'Token Sell' }
  ];
  
  const results = [];
  
  for (const trade of tradeTypes) {
    console.log(`\n💰 Testing ${trade.name}...`);
    
    const testData = {
      tokenAddress: '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump',
      poolAddress: 'Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b',
      dexType: 'pumpfun',
      amount: trade.amount,
      direction: trade.direction
    };
    
    try {
      const response = await axios.post(`${API_BASE_URL}/api/pump/quote`, testData);
      
      if (response.data.success && response.data.data.bondingCurve) {
        const bc = response.data.data.bondingCurve;
        results.push({
          name: trade.name,
          progressPercentage: bc.progressPercentage,
          currentSolRaised: bc.currentSolRaised,
          isGraduated: bc.isGraduated,
          status: bc.status
        });
        
        console.log(`✅ ${trade.name}: ${bc.progressPercentage}% progress, ${bc.currentSolRaised} SOL raised`);
      } else {
        console.log(`❌ ${trade.name}: Failed to get bonding curve info`);
      }
    } catch (error) {
      console.log(`❌ ${trade.name}: Error - ${error.message}`);
    }
  }
  
  // Analyze consistency
  if (results.length >= 2) {
    console.log('\n📈 Consistency Analysis:');
    console.log('Trade Type    | Progress | SOL Raised | Status');
    console.log('-'.repeat(50));
    
    results.forEach(result => {
      console.log(`${result.name.padEnd(12)} | ${result.progressPercentage.toString().padEnd(8)} | ${result.currentSolRaised.toString().padEnd(10)} | ${result.status}`);
    });
    
    // Check if all results are consistent
    const firstResult = results[0];
    const allConsistent = results.every(result => 
      Math.abs(result.progressPercentage - firstResult.progressPercentage) < 0.01 &&
      Math.abs(result.currentSolRaised - firstResult.currentSolRaised) < 0.01 &&
      result.status === firstResult.status
    );
    
    if (allConsistent) {
      console.log('\n✅ All bonding curve data is consistent across trade types');
    } else {
      console.log('\n⚠️ Bonding curve data varies across trade types (this may indicate an issue)');
    }
  }
}

/**
 * Main test function
 */
async function runBondingCurveTests() {
  console.log('🚀 Bonding Curve Analysis Testing');
  console.log('='.repeat(80));
  
  let totalTests = 0;
  let passedTests = 0;
  let testsWithBondingCurve = 0;
  
  // Test individual cases
  for (const testCase of TEST_CASES) {
    totalTests++;
    const result = await testBondingCurveAnalysis(testCase);
    
    if (result.success) {
      passedTests++;
      if (result.hasBondingCurve) {
        testsWithBondingCurve++;
      }
    }
  }
  
  // Test consistency across trade types
  await compareBondingCurveConsistency();
  
  // Summary
  console.log('\n' + '='.repeat(80));
  console.log('🏁 Test Summary');
  console.log('='.repeat(80));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Successful Quotes: ${passedTests}`);
  console.log(`Tests with Bonding Curve Info: ${testsWithBondingCurve}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests && testsWithBondingCurve > 0) {
    console.log('\n🎉 All tests passed! Bonding curve analysis is working correctly.');
    console.log('✅ Bonding curve progression information is being generated');
    console.log('✅ Progress percentages and graduation status are calculated');
    console.log('✅ Descriptive information helps users understand token stage');
  } else if (passedTests === totalTests) {
    console.log('\n⚠️ All quotes succeeded but some lack bonding curve information.');
    console.log('This may be expected for non-PumpFun tokens or during analysis failures.');
  } else {
    console.log('\n😞 Some tests failed. Please check the implementation.');
  }
  
  console.log('\n💡 Next Steps:');
  console.log('- Use bonding curve info to assess token risk and liquidity');
  console.log('- Adjust trading strategies based on graduation proximity');
  console.log('- Monitor progression over time to identify trending tokens');
}

// Run tests
if (require.main === module) {
  runBondingCurveTests().catch(console.error);
}

module.exports = { runBondingCurveTests, testBondingCurveAnalysis, compareBondingCurveConsistency };
