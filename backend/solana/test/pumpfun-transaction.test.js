/**
 * Test script for PumpFun transactions
 *
 * This script tests the PumpFun transaction creation, validation, and submission
 * with the new dynamic PDA derivation and improved blockhash handling.
 * Note: Retry functionality has been disabled as requested.
 */

const { Connection, PublicKey, Transaction } = require('@solana/web3.js');
const {
  validatePumpFunPDAs,
  preCreatePumpFunPDAs
} = require('../src/services/pumpFun/pda.validation');
const {
  updateTransactionBlockhash,
  simplifyTransaction
} = require('../src/services/transaction/transaction.utils');
const {
  fixPumpFunTransaction
} = require('../src/services/pumpFun/pumpFunUtils');
const {
  createSwapTransaction,
  executeSwap
} = require('../src/services/pumpFun/pump.service');

// Test configuration
const TEST_WALLET_ADDRESS = '********************************************';
const TEST_WALLET_ID = 'cbq2lb54zo7rtzv14i5sp75j';
const TEST_POOL_ADDRESS = '81oRNu8jP5oeVNQL8injYe9DSfK5CnJUruBq3qGZsCj8';
const TEST_TOKEN_ADDRESS = 'GxfZBiRyNbtPdPvxHyjnPKRJyR5mGJiCGY4wzKwUpump';

// Solana connection
const connection = new Connection(process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com');

/**
 * Test PDA validation
 */
async function testPDAValidation() {
  console.log('\n--- Testing PDA Validation ---');

  const poolAddress = new PublicKey(TEST_POOL_ADDRESS);
  const tokenMint = new PublicKey(TEST_TOKEN_ADDRESS);
  const userPublicKey = new PublicKey(TEST_WALLET_ADDRESS);

  try {
    // Validate PDAs
    const validationResults = await validatePumpFunPDAs(
      connection,
      poolAddress,
      tokenMint,
      userPublicKey
    );

    console.log('PDA validation results:', validationResults);

    // Check if any PDAs are invalid
    const invalidPDAs = Object.entries(validationResults)
      .filter(([_, isValid]) => !isValid)
      .map(([pda]) => pda);

    if (invalidPDAs.length > 0) {
      console.warn(`WARNING: The following PDAs are invalid: ${invalidPDAs.join(', ')}`);
    } else {
      console.log('All PDAs are valid!');
    }
  } catch (error) {
    console.error('Error validating PDAs:', error);
  }
}

/**
 * Test transaction simplification
 */
async function testTransactionSimplification() {
  console.log('\n--- Testing Transaction Simplification ---');

  try {
    // Create a swap request
    const swapRequest = {
      dexType: 'PumpFun',
      direction: 'Buy',
      poolAddress: TEST_POOL_ADDRESS,
      tokenAddress: TEST_TOKEN_ADDRESS,
      walletAddress: TEST_WALLET_ADDRESS,
      amount: '0.001', // 0.001 SOL
      slippage: 1 // 1% slippage
    };

    // Create a swap transaction
    console.log('Creating swap transaction...');
    const { transaction } = await createSwapTransaction(swapRequest, false, true);

    // Deserialize the transaction
    const decodedTransaction = Transaction.from(Buffer.from(transaction, 'base64'));
    console.log(`Original transaction has ${decodedTransaction.instructions.length} instructions`);

    // Simplify the transaction
    const simplifiedTransaction = simplifyTransaction(decodedTransaction);
    console.log(`Simplified transaction has ${simplifiedTransaction.instructions.length} instructions`);

    // Update the blockhash
    await updateTransactionBlockhash(simplifiedTransaction, connection, 'confirmed');
    console.log(`Updated transaction with fresh blockhash: ${simplifiedTransaction.recentBlockhash}`);

    // Validate the blockhash
    if (simplifiedTransaction.recentBlockhash) {
      const isValid = await connection.isBlockhashValid(simplifiedTransaction.recentBlockhash, { commitment: 'confirmed' });
      console.log(`Blockhash validation: ${isValid ? 'VALID' : 'INVALID'}`);
    }

    console.log('Transaction simplification test completed successfully');
  } catch (error) {
    console.error('Error testing transaction simplification:', error);
  }
}

/**
 * Test transaction creation and validation
 */
async function testTransactionCreation() {
  console.log('\n--- Testing Transaction Creation ---');

  try {
    // Create a swap request
    const swapRequest = {
      dexType: 'PumpFun',
      direction: 'Buy',
      poolAddress: TEST_POOL_ADDRESS,
      tokenAddress: TEST_TOKEN_ADDRESS,
      walletAddress: TEST_WALLET_ADDRESS,
      amount: '0.001', // 0.001 SOL
      slippage: 1 // 1% slippage
    };

    // Create a swap transaction
    console.log('Creating swap transaction...');
    const { transaction, quoteResult } = await createSwapTransaction(swapRequest, false, true);

    console.log('Quote result:', quoteResult);
    console.log('Transaction created successfully, length:', transaction.length);

    // Deserialize and validate the transaction
    const decodedTransaction = Transaction.from(Buffer.from(transaction, 'base64'));
    console.log(`Transaction has ${decodedTransaction.instructions.length} instructions`);

    // Check if the transaction has PumpFun instructions
    let hasPumpFunInstruction = false;
    for (const instruction of decodedTransaction.instructions) {
      if (instruction.programId.toString() === '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P') {
        hasPumpFunInstruction = true;
        break;
      }
    }

    console.log(`Transaction has PumpFun instructions: ${hasPumpFunInstruction}`);

    // Fix the transaction
    if (hasPumpFunInstruction) {
      const fixedTransaction = fixPumpFunTransaction(decodedTransaction);
      console.log('Transaction fixed successfully');
    }

    console.log('Transaction creation test completed successfully');
  } catch (error) {
    console.error('Error testing transaction creation:', error);
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('=== PumpFun Transaction Tests ===');

  await testPDAValidation();
  await testTransactionSimplification();
  await testTransactionCreation();

  console.log('\n=== Tests Complete ===');
}

// Run the tests
runTests().catch(console.error);
