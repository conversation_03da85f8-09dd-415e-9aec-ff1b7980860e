/**
 * Check Token Balance and Create Sell Transaction
 */

const axios = require('axios');

// Configuration
const API_BASE_URL = 'http://localhost:6001';
const WALLET_ADDRESS = '********************************************';
const WALLET_ID = 'cbq2lb54zo7rtzv14i5sp75j';
const TOKEN_ADDRESS = '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump';
const POOL_ADDRESS = 'Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b';

/**
 * Check current token balance by attempting a large sell quote
 */
async function checkTokenBalance() {
    console.log('🔍 Checking your current token balance...');
    console.log(`Wallet: ${WALLET_ADDRESS}`);
    console.log(`Token: ${TOKEN_ADDRESS}`);
    
    try {
        // Try to get a quote for selling a large amount to see what we actually have
        const quoteResponse = await axios.post(`${API_BASE_URL}/api/pump/quote`, {
            tokenAddress: TOKEN_ADDRESS,
            poolAddress: POOL_ADDRESS,
            dexType: 'pumpfun',
            amount: 1000000, // Try a large amount to see the max
            direction: 'sell'
        });

        if (quoteResponse.data.success) {
            console.log('✅ Quote successful - you have tokens to sell');
            console.log(`Expected SOL output for 1M tokens: ${quoteResponse.data.data.outAmount} SOL`);
            return true;
        } else {
            console.log('❌ Quote failed - you might not have tokens');
            return false;
        }
    } catch (error) {
        if (error.response?.data?.error?.includes('Insufficient token balance')) {
            console.log('❌ You have insufficient token balance or no tokens');
            
            // Extract balance information from error message if available
            const errorMsg = error.response.data.error;
            const balanceMatch = errorMsg.match(/User has ([\d.]+) tokens/);
            if (balanceMatch) {
                const currentBalance = parseFloat(balanceMatch[1]);
                console.log(`💰 Current Balance: ${currentBalance.toFixed(6)} tokens`);
                return currentBalance;
            }
            return 0;
        } else {
            console.error('Error checking balance:', error.response?.data || error.message);
            return false;
        }
    }
}

/**
 * Attempt to sell a specific amount of tokens
 */
async function sellTokens(amount) {
    console.log(`\n💸 Attempting to sell ${amount} tokens...`);
    
    const sellData = {
        tokenAddress: TOKEN_ADDRESS,
        poolAddress: POOL_ADDRESS,
        dexType: 'pumpfun',
        amount: amount,
        direction: 'sell',
        slippage: 0.10, // 10% slippage for better execution
        walletAddress: WALLET_ADDRESS,
        walletId: WALLET_ID,
        
        // MEV Protection (optional)
        mevProtection: true,
        bribeAmount: 1000000, // 0.001 SOL bribe
        priorityLevel: 'high',
        priorityFee: 100000
    };

    try {
        console.log('Sending sell request...');
        const response = await axios.post(`${API_BASE_URL}/api/pump/swap`, sellData);
        
        if (response.data.success) {
            console.log('✅ Sell transaction successful!');
            console.log(`- SOL received: ${response.data.data.outAmount} SOL`);
            console.log(`- Transaction signature: ${response.data.data.signature}`);
            console.log(`- Solscan URL: ${response.data.data.solscanUrl}`);
            
            if (response.data.data.bundleId) {
                console.log(`- Jito Bundle ID: ${response.data.data.bundleId}`);
                console.log(`- Jito URL: ${response.data.data.jitoUrl}`);
            }
            
            console.log(`- Execution method: ${response.data.data.executionMethod}`);
            console.log(`- MEV protected: ${response.data.data.mevProtected}`);
            
            return true;
        } else {
            console.log('❌ Sell transaction failed:', response.data.error);
            return false;
        }
    } catch (error) {
        console.error('❌ Sell transaction error:', error.response?.data?.error || error.message);
        return false;
    }
}

/**
 * Get a quote for selling tokens
 */
async function getSellQuote(amount) {
    console.log(`\n📊 Getting sell quote for ${amount} tokens...`);
    
    try {
        const response = await axios.post(`${API_BASE_URL}/api/pump/quote`, {
            tokenAddress: TOKEN_ADDRESS,
            poolAddress: POOL_ADDRESS,
            dexType: 'pumpfun',
            amount: amount,
            direction: 'sell'
        });

        if (response.data.success) {
            const data = response.data.data;
            console.log('✅ Sell Quote:');
            console.log(`- You would receive: ${data.outAmount} SOL`);
            console.log(`- Price per token: ${data.price} SOL`);
            console.log(`- Minimum output (with slippage): ${data.minOut} SOL`);
            console.log(`- Platform fee: ${data.fee * 100}%`);
            
            return data;
        } else {
            console.log('❌ Quote failed:', response.data.error);
            return null;
        }
    } catch (error) {
        console.error('Quote error:', error.response?.data?.error || error.message);
        return null;
    }
}

/**
 * Main function
 */
async function main() {
    console.log('🚀 Token Balance Check and Sell Tool');
    console.log('='.repeat(50));
    
    // Step 1: Check current balance
    const balance = await checkTokenBalance();
    
    if (balance === false) {
        console.log('❌ Could not determine token balance');
        return;
    }
    
    if (balance === 0) {
        console.log('❌ You have no tokens to sell');
        return;
    }
    
    if (typeof balance === 'number' && balance > 0) {
        console.log(`\n💰 You have ${balance.toFixed(6)} tokens`);
        
        // Step 2: Get sell quote for current balance
        const quote = await getSellQuote(balance);
        
        if (quote) {
            // Step 3: Ask if user wants to sell (for now, we'll sell automatically)
            console.log(`\n🤔 Do you want to sell ${balance.toFixed(6)} tokens for ~${quote.outAmount} SOL?`);
            console.log('Proceeding with sell transaction...');
            
            // Step 4: Execute sell
            const success = await sellTokens(balance);
            
            if (success) {
                console.log('\n🎉 Sell completed successfully!');
            } else {
                console.log('\n😞 Sell failed. Please check the error messages above.');
            }
        }
    } else {
        console.log('✅ You have tokens available for selling');
        console.log('Let me try to determine the exact amount...');
        
        // Try smaller amounts to find the actual balance
        const testAmounts = [100000, 50000, 10000, 5000, 1000, 500, 100, 50, 10];
        
        for (const testAmount of testAmounts) {
            console.log(`Testing sell quote for ${testAmount} tokens...`);
            const quote = await getSellQuote(testAmount);
            
            if (quote) {
                console.log(`✅ You can sell at least ${testAmount} tokens`);
                
                // Ask if user wants to sell this amount
                console.log(`\n🤔 Selling ${testAmount} tokens for ~${quote.outAmount} SOL`);
                const success = await sellTokens(testAmount);
                
                if (success) {
                    console.log('\n🎉 Sell completed successfully!');
                } else {
                    console.log('\n😞 Sell failed. Please check the error messages above.');
                }
                break;
            }
        }
    }
}

// Run the script
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { checkTokenBalance, sellTokens, getSellQuote };
