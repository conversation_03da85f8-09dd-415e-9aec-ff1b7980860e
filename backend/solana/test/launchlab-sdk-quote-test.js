/**
 * Test script for LaunchLab SDK-based quote implementation
 * Tests the new official SDK quote methods with wallet address support
 */

const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_POOL_ID = 'HMx4pdp2HsGCPqkXcBYWnxHdUgjn1HrPYWgQZs3rJg2P'; // Example pool ID
const TEST_WALLET = 'So11111111111111111111111111111111111111112'; // Example wallet

/**
 * Test LaunchLab quote endpoint with wallet address (new feature)
 */
async function testQuoteWithWallet() {
  console.log('\n=== Testing LaunchLab Quote with Wallet Address (SDK-based) ===');
  
  try {
    const quoteRequest = {
      poolId: TEST_POOL_ID,
      amount: '0.1', // 0.1 SOL
      direction: 'buy',
      walletAddress: TEST_WALLET // NEW: Wallet address for accurate fee calculation
    };

    console.log('Sending quote request with wallet:', {
      ...quoteRequest,
      walletAddress: quoteRequest.walletAddress.substring(0, 8) + '...'
    });
    
    const response = await axios.post(`${BASE_URL}/api/launchlab/quote`, quoteRequest);
    
    console.log('Quote response status:', response.status);
    console.log('Quote response data:', JSON.stringify(response.data, null, 2));
    
    // Verify response structure
    const data = response.data.data;
    if (data) {
      console.log('\n✅ SDK Quote Response Validation:');
      console.log('- Has inputMint:', !!data.inputMint);
      console.log('- Has outputMint:', !!data.outputMint);
      console.log('- Has inputAmount:', !!data.inputAmount);
      console.log('- Has outputAmount:', !!data.outputAmount);
      console.log('- Has fee (SDK calculated):', !!data.fee);
      console.log('- Has platformFee:', !!data.platformFee);
      console.log('- Has priceImpact:', !!data.priceImpact);
      console.log('- Has outputAmountHuman:', !!data.outputAmountHuman);
      console.log('- Has netOutputAmount:', !!data.netOutputAmount);
      
      // Check if SDK-based calculation provides more accurate values
      console.log('\n📊 SDK Quote Values:');
      console.log('- Input Amount (Human):', data.inputAmountHuman);
      console.log('- Output Amount (Human):', data.outputAmountHuman);
      console.log('- Fee (Human):', data.feeHuman);
      console.log('- Platform Fee (Human):', data.platformFeeHuman);
      console.log('- Price Impact:', data.priceImpactPercent);
    }
    
    return true;
  } catch (error) {
    console.error('SDK quote test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Test LaunchLab quote endpoint without wallet address (backward compatibility)
 */
async function testQuoteWithoutWallet() {
  console.log('\n=== Testing LaunchLab Quote without Wallet Address (Backward Compatibility) ===');
  
  try {
    const quoteRequest = {
      poolId: TEST_POOL_ID,
      amount: '0.1', // 0.1 SOL
      direction: 'buy'
      // No walletAddress parameter - should still work
    };

    console.log('Sending quote request without wallet:', quoteRequest);
    
    const response = await axios.post(`${BASE_URL}/api/launchlab/quote`, quoteRequest);
    
    console.log('Quote response status:', response.status);
    const data = response.data.data;
    
    if (data && data.outputAmountHuman) {
      console.log('✅ Backward compatibility maintained');
      console.log('- Output Amount (Human):', data.outputAmountHuman);
      console.log('- Fee (Human):', data.feeHuman);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Backward compatibility test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Test sell quote with SDK
 */
async function testSellQuoteWithSDK() {
  console.log('\n=== Testing LaunchLab Sell Quote with SDK ===');
  
  try {
    const quoteRequest = {
      poolId: TEST_POOL_ID,
      amount: '1000', // 1000 tokens
      direction: 'sell',
      walletAddress: TEST_WALLET
    };

    console.log('Sending sell quote request:', {
      ...quoteRequest,
      walletAddress: quoteRequest.walletAddress.substring(0, 8) + '...'
    });
    
    const response = await axios.post(`${BASE_URL}/api/launchlab/quote`, quoteRequest);
    
    console.log('Sell quote response status:', response.status);
    const data = response.data.data;
    
    if (data) {
      console.log('✅ SDK Sell Quote Values:');
      console.log('- Input Amount (Human):', data.inputAmountHuman);
      console.log('- Output Amount (Human):', data.outputAmountHuman);
      console.log('- Fee (Human):', data.feeHuman);
      console.log('- Platform Fee (Human):', data.platformFeeHuman);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('SDK sell quote test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Compare quote accuracy with and without wallet address
 */
async function compareQuoteAccuracy() {
  console.log('\n=== Comparing Quote Accuracy: With vs Without Wallet ===');
  
  try {
    const baseRequest = {
      poolId: TEST_POOL_ID,
      amount: '0.1',
      direction: 'buy'
    };

    // Quote without wallet
    const withoutWalletResponse = await axios.post(`${BASE_URL}/api/launchlab/quote`, baseRequest);
    const withoutWallet = withoutWalletResponse.data.data;

    // Quote with wallet
    const withWalletResponse = await axios.post(`${BASE_URL}/api/launchlab/quote`, {
      ...baseRequest,
      walletAddress: TEST_WALLET
    });
    const withWallet = withWalletResponse.data.data;

    console.log('📊 Accuracy Comparison:');
    console.log('Without Wallet:');
    console.log('  - Output Amount:', withoutWallet.outputAmountHuman);
    console.log('  - Fee:', withoutWallet.feeHuman);
    console.log('  - Platform Fee:', withoutWallet.platformFeeHuman);
    
    console.log('With Wallet (SDK-based):');
    console.log('  - Output Amount:', withWallet.outputAmountHuman);
    console.log('  - Fee:', withWallet.feeHuman);
    console.log('  - Platform Fee:', withWallet.platformFeeHuman);

    // Check if values are different (indicating more accurate calculation)
    const outputDifferent = withoutWallet.outputAmountHuman !== withWallet.outputAmountHuman;
    const feeDifferent = withoutWallet.feeHuman !== withWallet.feeHuman;
    
    console.log('\n🎯 Accuracy Indicators:');
    console.log('- Output amounts differ:', outputDifferent ? '✅ (More accurate with wallet)' : '⚠️ (Same)');
    console.log('- Fee calculations differ:', feeDifferent ? '✅ (More accurate with wallet)' : '⚠️ (Same)');
    
    return true;
  } catch (error) {
    console.error('Accuracy comparison failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Main test runner
 */
async function runSDKQuoteTests() {
  console.log('🚀 Starting LaunchLab SDK Quote Tests');
  console.log('Testing official Raydium SDK integration with wallet address support');
  
  let passedTests = 0;
  let totalTests = 0;
  
  // Test 1: Quote with wallet address (new feature)
  totalTests++;
  if (await testQuoteWithWallet()) {
    passedTests++;
  }
  
  // Test 2: Quote without wallet address (backward compatibility)
  totalTests++;
  if (await testQuoteWithoutWallet()) {
    passedTests++;
  }
  
  // Test 3: Sell quote with SDK
  totalTests++;
  if (await testSellQuoteWithSDK()) {
    passedTests++;
  }
  
  // Test 4: Compare accuracy
  totalTests++;
  if (await compareQuoteAccuracy()) {
    passedTests++;
  }
  
  console.log('\n=== SDK Quote Test Results ===');
  console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All SDK quote tests passed! Official Raydium SDK integration is working correctly.');
    console.log('✨ Features verified:');
    console.log('  - Official SDK quote methods');
    console.log('  - Wallet address support for accurate fees');
    console.log('  - Backward compatibility maintained');
    console.log('  - Both buy and sell quotes working');
  } else {
    console.log('❌ Some tests failed. Please check the SDK integration.');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runSDKQuoteTests().catch(console.error);
}

module.exports = {
  testQuoteWithWallet,
  testQuoteWithoutWallet,
  testSellQuoteWithSDK,
  compareQuoteAccuracy,
  runSDKQuoteTests
};
