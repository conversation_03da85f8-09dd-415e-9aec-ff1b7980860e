/**
 * Test Enhanced Parameter Format for Solana Trading API
 * Tests both new percentage-based format and legacy format for backward compatibility
 */

const axios = require('axios');

// Test configuration
const API_BASE_URL = 'http://localhost:6001';
const WALLET_ADDRESS = '********************************************';
const WALLET_ID = 'cbq2lb54zo7rtzv14i5sp75j';
const TOKEN_ADDRESS = '4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump';
const POOL_ADDRESS = 'Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b';

// Test cases for enhanced parameter format
const ENHANCED_FORMAT_TESTS = [
  {
    name: 'Small Trade - Enhanced Format',
    description: 'Test 0.001 SOL trade with percentage-based parameters',
    data: {
      tokenAddress: TOKEN_ADDRESS,
      poolAddress: POOL_ADDRESS,
      dexType: 'pumpfun',
      amount: 0.001,
      direction: 'buy',
      slippage: 10,           // 10% (instead of 0.10)
      priorityFee: 0.1,       // 0.1% of trade (instead of microLamports)
      bribeAmount: 5,         // 5% of trade (instead of lamports)
      walletAddress: WALLET_ADDRESS,
      walletId: WALLET_ID,
      mevProtection: true,
      priorityLevel: 'high'
    },
    expectedConversions: {
      slippage: 0.10,         // 10% → 0.10
      priorityFeeSOL: 0.000001, // 0.1% of 0.001 SOL
      bribeAmountSOL: 0.00005   // 5% of 0.001 SOL
    }
  },
  {
    name: 'Medium Trade - Enhanced Format',
    description: 'Test 0.01 SOL trade with optimized percentage parameters',
    data: {
      tokenAddress: TOKEN_ADDRESS,
      poolAddress: POOL_ADDRESS,
      dexType: 'pumpfun',
      amount: 0.01,
      direction: 'buy',
      slippage: 5,            // 5% (instead of 0.05)
      priorityFee: 0.01,      // 0.01% of trade
      bribeAmount: 1.5,       // 1.5% of trade
      walletAddress: WALLET_ADDRESS,
      walletId: WALLET_ID,
      mevProtection: true,
      priorityLevel: 'high'
    },
    expectedConversions: {
      slippage: 0.05,         // 5% → 0.05
      priorityFeeSOL: 0.000001, // 0.01% of 0.01 SOL
      bribeAmountSOL: 0.00015   // 1.5% of 0.01 SOL
    }
  },
  {
    name: 'Low Slippage - Enhanced Format',
    description: 'Test with very low slippage using decimal format',
    data: {
      tokenAddress: TOKEN_ADDRESS,
      poolAddress: POOL_ADDRESS,
      dexType: 'pumpfun',
      amount: 0.01,
      direction: 'buy',
      slippage: 0.5,          // 0.5% (decimal format, should stay as-is)
      priorityFee: 0.005,     // 0.005% of trade
      bribeAmount: 0.8,       // 0.8% of trade
      walletAddress: WALLET_ADDRESS,
      walletId: WALLET_ID,
      mevProtection: true,
      priorityLevel: 'medium'
    },
    expectedConversions: {
      slippage: 0.005,        // 0.5% → 0.005 (treated as decimal)
      priorityFeeSOL: 0.0000005, // 0.005% of 0.01 SOL
      bribeAmountSOL: 0.00008    // 0.8% of 0.01 SOL
    }
  }
];

// Test cases for legacy format (backward compatibility)
const LEGACY_FORMAT_TESTS = [
  {
    name: 'Legacy Format - Backward Compatibility',
    description: 'Test that old format still works',
    data: {
      tokenAddress: TOKEN_ADDRESS,
      poolAddress: POOL_ADDRESS,
      dexType: 'pumpfun',
      amount: 0.01,
      direction: 'buy',
      slippage: 0.10,         // Legacy decimal format
      priorityFee: 100000,    // Legacy microLamports format
      bribeAmount: 1500000,   // Legacy lamports format
      walletAddress: WALLET_ADDRESS,
      walletId: WALLET_ID,
      mevProtection: true,
      priorityLevel: 'high'
    },
    expectedConversions: {
      slippage: 0.10,         // Should stay as-is
      priorityFeeMicroLamports: 100000, // Should stay as-is
      bribeAmountLamports: 1500000      // Should stay as-is
    }
  }
];

/**
 * Test a single swap request and analyze parameter conversions
 */
async function testParameterConversion(testCase) {
  console.log(`\n🧪 ${testCase.name}`);
  console.log(`📝 ${testCase.description}`);
  console.log('-'.repeat(60));
  
  try {
    console.log('📤 Request Data:');
    console.log(JSON.stringify(testCase.data, null, 2));
    
    // First, get a quote to see parameter conversion
    const quoteResponse = await axios.post(`${API_BASE_URL}/api/pump/quote`, {
      tokenAddress: testCase.data.tokenAddress,
      poolAddress: testCase.data.poolAddress,
      dexType: testCase.data.dexType,
      amount: testCase.data.amount,
      direction: testCase.data.direction
    });
    
    if (quoteResponse.data.success) {
      console.log('✅ Quote successful');
      console.log(`💰 Expected output: ${quoteResponse.data.data.outAmount} tokens`);
    }
    
    // Now test the swap with enhanced parameters
    const swapResponse = await axios.post(`${API_BASE_URL}/api/pump/swap`, testCase.data);
    
    if (swapResponse.data.success) {
      console.log('\n✅ Swap Request Successful');
      const data = swapResponse.data.data;
      
      console.log('\n📊 Parameter Conversion Analysis:');
      console.log(`- Execution Method: ${data.executionMethod}`);
      console.log(`- MEV Protected: ${data.mevProtected}`);
      console.log(`- Tip Amount: ${data.tipAmount} lamports (${data.tipAmountSol} SOL)`);
      
      // Calculate actual conversions
      const actualBribePercentage = (data.tipAmount / 1_000_000_000) / testCase.data.amount * 100;
      
      console.log('\n🔄 Conversion Results:');
      console.log(`- Input slippage: ${testCase.data.slippage}`);
      console.log(`- Input priorityFee: ${testCase.data.priorityFee}`);
      console.log(`- Input bribeAmount: ${testCase.data.bribeAmount}`);
      console.log(`- Actual bribe percentage: ${actualBribePercentage.toFixed(2)}%`);
      console.log(`- Actual bribe SOL: ${data.tipAmountSol} SOL`);
      
      if (data.signature) {
        console.log(`\n🔗 Transaction: ${data.signature}`);
        console.log(`🔗 Solscan: ${data.solscanUrl}`);
      }
      
      if (data.bundleId) {
        console.log(`🔗 Jito Bundle: ${data.bundleId}`);
        console.log(`🔗 Jito Explorer: ${data.jitoUrl}`);
      }
      
      return { success: true, data };
    } else {
      console.log('❌ Swap Request Failed');
      console.log('Error:', swapResponse.data.error);
      return { success: false, error: swapResponse.data.error };
    }
    
  } catch (error) {
    console.log('❌ Request Error');
    if (error.response?.data) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data.error || error.response.data);
    } else {
      console.log('Network Error:', error.message);
    }
    return { success: false, error: error.message };
  }
}

/**
 * Test parameter validation
 */
async function testParameterValidation() {
  console.log('\n🛡️ Testing Parameter Validation');
  console.log('='.repeat(60));
  
  const invalidTests = [
    {
      name: 'Invalid Slippage (Too High)',
      data: { ...ENHANCED_FORMAT_TESTS[0].data, slippage: 60 }, // 60% > 50% max
      expectedError: 'slippage'
    },
    {
      name: 'Invalid Priority Fee (Too High)',
      data: { ...ENHANCED_FORMAT_TESTS[0].data, priorityFee: 15 }, // 15% > 10% max
      expectedError: 'priorityFee'
    },
    {
      name: 'Invalid Bribe Amount (Too High)',
      data: { ...ENHANCED_FORMAT_TESTS[0].data, bribeAmount: 25 }, // 25% > 20% max
      expectedError: 'bribeAmount'
    }
  ];
  
  for (const test of invalidTests) {
    console.log(`\n🧪 ${test.name}`);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/pump/swap`, test.data);
      console.log('❌ Expected validation error but request succeeded');
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ Validation error caught correctly');
        console.log(`Error: ${error.response.data.error}`);
      } else {
        console.log('❌ Unexpected error type');
        console.log(`Error: ${error.response?.data || error.message}`);
      }
    }
  }
}

/**
 * Main test function
 */
async function runEnhancedParameterTests() {
  console.log('🚀 Enhanced Parameter Format Testing');
  console.log('='.repeat(80));
  
  let totalTests = 0;
  let passedTests = 0;
  
  // Test enhanced format
  console.log('\n📈 Testing Enhanced Percentage-Based Format');
  for (const testCase of ENHANCED_FORMAT_TESTS) {
    totalTests++;
    const result = await testParameterConversion(testCase);
    if (result.success) passedTests++;
  }
  
  // Test legacy format
  console.log('\n🔄 Testing Legacy Format (Backward Compatibility)');
  for (const testCase of LEGACY_FORMAT_TESTS) {
    totalTests++;
    const result = await testParameterConversion(testCase);
    if (result.success) passedTests++;
  }
  
  // Test validation
  await testParameterValidation();
  
  // Summary
  console.log('\n' + '='.repeat(80));
  console.log('🏁 Test Summary');
  console.log('='.repeat(80));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${totalTests - passedTests}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! Enhanced parameter format is working correctly.');
    console.log('✅ Percentage-based parameters are being converted properly');
    console.log('✅ Backward compatibility with legacy format is maintained');
  } else {
    console.log('\n😞 Some tests failed. Please check the implementation.');
  }
}

// Run tests
if (require.main === module) {
  runEnhancedParameterTests().catch(console.error);
}

module.exports = { runEnhancedParameterTests, testParameterConversion };
