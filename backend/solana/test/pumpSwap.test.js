const axios = require('axios');

// Test configuration
const API_URL = 'http://localhost:6001';
const TEST_WALLET_ADDRESS = '********************************************';
const TEST_WALLET_ID = 'cbq2lb54zo7rtzv14i5sp75j';
const TEST_TOKEN_ADDRESS = 'HNGtDJqUf6UjFAzBbZ2gtB16Cov3R8k6c2nQqG4Lpump';
const TEST_POOL_ADDRESS = '5yRbZMHCRQCVnANsjk6BYviSDhHwFfew7mVQEdrX8W7w';

// Test PumpSwap quote first to get pool info
async function testPumpSwapPoolInfo() {
  try {
    console.log('Testing PumpSwap pool info via quote...');
    const response = await axios.post(`${API_URL}/api/pump/quote`, {
      poolAddress: TEST_POOL_ADDRESS,
      tokenAddress: TEST_TOKEN_ADDRESS,
      amount: 0.1,
      direction: 'buy',
      dexType: 'pumpswap'
    });
    console.log('PumpSwap pool info response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error testing PumpSwap pool info:', error.response?.data || error.message);
    throw error;
  }
}

// Test PumpSwap quote
async function testPumpSwapQuote(isBuy = true, amount = 0.1) {
  try {
    const direction = isBuy ? 'buy' : 'sell';
    console.log(`Testing PumpSwap ${direction} quote...`);
    const response = await axios.post(`${API_URL}/api/pump/quote`, {
      poolAddress: TEST_POOL_ADDRESS,
      tokenAddress: TEST_TOKEN_ADDRESS,
      amount,
      direction,
      dexType: 'pumpswap'
    });
    console.log('PumpSwap quote response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error testing PumpSwap quote:', error.response?.data || error.message);
    throw error;
  }
}

// Test PumpSwap swap
async function testPumpSwapSwap(isBuy = true, amount = 0.1) {
  try {
    const direction = isBuy ? 'buy' : 'sell';
    console.log(`Testing PumpSwap ${direction} swap...`);
    const response = await axios.post(`${API_URL}/api/pump/swap`, {
      poolAddress: TEST_POOL_ADDRESS,
      tokenAddress: TEST_TOKEN_ADDRESS,
      walletAddress: TEST_WALLET_ADDRESS,
      walletId: TEST_WALLET_ID,
      amount,
      direction,
      slippage: 0.01,
      dexType: 'pumpswap'
    });
    console.log('PumpSwap swap response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error testing PumpSwap swap:', error.response?.data || error.message);
    throw error;
  }
}

// Run all tests
async function runTests() {
  try {
    // Test PumpSwap pool info
    await testPumpSwapPoolInfo();

    // Test PumpSwap buy quote
    await testPumpSwapQuote(true, 0.1);

    // Test PumpSwap sell quote
    await testPumpSwapQuote(false, 10);

    // Test PumpSwap buy swap
    await testPumpSwapSwap(true, 0.1);

    // Test PumpSwap sell swap
    await testPumpSwapSwap(false, 10);

    console.log('All tests completed successfully!');
  } catch (error) {
    console.error('Test suite failed:', error);
  }
}

// Run the tests
runTests();
