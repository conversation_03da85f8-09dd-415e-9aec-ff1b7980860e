/**
 * Test script for LaunchLab controller fixes
 * Tests the missing parameters and correct response values
 */

const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_POOL_ID = 'HMx4pdp2HsGCPqkXcBYWnxHdUgjn1HrPYWgQZs3rJg2P'; // Example pool ID
const TEST_WALLET = 'So11111111111111111111111111111111111111112'; // Example wallet
const TEST_WALLET_ID = 'test-wallet-id';

/**
 * Test LaunchLab quote endpoint with new parameters
 */
async function testQuoteEndpoint() {
  console.log('\n=== Testing LaunchLab Quote Endpoint ===');
  
  try {
    const quoteRequest = {
      poolId: TEST_POOL_ID,
      amount: '0.1', // 0.1 SOL
      direction: 'buy',
      platformFees: 50 // 50 basis points (0.5%)
    };

    console.log('Sending quote request:', quoteRequest);
    
    const response = await axios.post(`${BASE_URL}/api/launchlab/quote`, quoteRequest);
    
    console.log('Quote response status:', response.status);
    console.log('Quote response data:', JSON.stringify(response.data, null, 2));
    
    // Verify response structure
    const data = response.data.data;
    if (data) {
      console.log('\n✅ Quote Response Validation:');
      console.log('- Has inputMint:', !!data.inputMint);
      console.log('- Has outputMint:', !!data.outputMint);
      console.log('- Has inputAmount:', !!data.inputAmount);
      console.log('- Has outputAmount:', !!data.outputAmount);
      console.log('- Has platformFee:', !!data.platformFee);
      console.log('- Has platformFeeHuman:', !!data.platformFeeHuman);
      console.log('- Has platformFeePercent:', !!data.platformFeePercent);
      console.log('- Has outputAmountHuman:', !!data.outputAmountHuman);
      console.log('- Has netOutputAmount:', !!data.netOutputAmount);
      console.log('- Has netOutputAmountHuman:', !!data.netOutputAmountHuman);
    }
    
    return true;
  } catch (error) {
    console.error('Quote test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Test LaunchLab swap endpoint with new parameters
 */
async function testSwapEndpoint() {
  console.log('\n=== Testing LaunchLab Swap Endpoint ===');
  
  try {
    const swapRequest = {
      poolId: TEST_POOL_ID,
      amount: '0.01', // 0.01 SOL
      walletAddress: TEST_WALLET,
      walletId: TEST_WALLET_ID,
      direction: 'buy',
      slippage: 0.05, // 5%
      priorityFee: 10000, // 10,000 microlamports
      computeUnitLimit: 200000, // 200k compute units
      platformFees: 25 // 25 basis points (0.25%)
    };

    console.log('Sending swap request:', {
      ...swapRequest,
      walletAddress: swapRequest.walletAddress.substring(0, 8) + '...',
      walletId: 'provided'
    });
    
    // Note: This will likely fail due to missing wallet credentials
    // but we can test the parameter validation
    const response = await axios.post(`${BASE_URL}/api/launchlab/swap`, swapRequest);
    
    console.log('Swap response status:', response.status);
    console.log('Swap response data:', JSON.stringify(response.data, null, 2));
    
    return true;
  } catch (error) {
    console.log('Swap test response (expected to fail without valid wallet):', 
      error.response?.status, error.response?.data?.error?.message || error.message);
    
    // Check if it's a validation error vs execution error
    if (error.response?.status === 400) {
      console.log('✅ Parameter validation working correctly');
      return true;
    } else if (error.response?.status === 500 && 
               error.response?.data?.error?.message?.includes('wallet') ||
               error.response?.data?.error?.message?.includes('Privy')) {
      console.log('✅ Parameters accepted, failed at execution (expected without valid wallet)');
      return true;
    }
    
    return false;
  }
}

/**
 * Test quote without platformFees parameter (should use default)
 */
async function testQuoteWithoutPlatformFees() {
  console.log('\n=== Testing Quote Without Platform Fees (Default) ===');
  
  try {
    const quoteRequest = {
      poolId: TEST_POOL_ID,
      amount: '0.1',
      direction: 'buy'
      // No platformFees parameter - should use config default
    };

    console.log('Sending quote request without platformFees:', quoteRequest);
    
    const response = await axios.post(`${BASE_URL}/api/launchlab/quote`, quoteRequest);
    
    console.log('Quote response status:', response.status);
    const data = response.data.data;
    
    if (data && data.platformFeePercent) {
      console.log('✅ Default platform fee applied:', data.platformFeePercent);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Default quote test failed:', error.response?.data || error.message);
    return false;
  }
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 Starting LaunchLab Controller Tests');
  console.log('Testing fixes for missing parameters and incorrect response values');
  
  let passedTests = 0;
  let totalTests = 0;
  
  // Test 1: Quote with platform fees
  totalTests++;
  if (await testQuoteEndpoint()) {
    passedTests++;
  }
  
  // Test 2: Swap with all parameters
  totalTests++;
  if (await testSwapEndpoint()) {
    passedTests++;
  }
  
  // Test 3: Quote with default platform fees
  totalTests++;
  if (await testQuoteWithoutPlatformFees()) {
    passedTests++;
  }
  
  console.log('\n=== Test Results ===');
  console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! LaunchLab controller fixes are working correctly.');
  } else {
    console.log('❌ Some tests failed. Please check the implementation.');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testQuoteEndpoint,
  testSwapEndpoint,
  testQuoteWithoutPlatformFees,
  runTests
};
