/**
 * Detect Jito-Enabled Transactions
 *
 * This script shows various ways to identify if a transaction
 * was executed through Jito MEV protection.
 */

const axios = require('axios');
const { Connection, PublicKey } = require('@solana/web3.js');

// Configuration
const API_BASE_URL = 'http://localhost:6001';
const SOLANA_RPC_URL = 'https://mainnet.helius-rpc.com/?api-key=52496df8-0e8c-472c-b15c-defef68bed70';

// Jito tip accounts (if transaction sends to these, it's Jito-enabled)
const JITO_TIP_ACCOUNTS = [
    '96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5',
    'HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe',
    'Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY',
    'ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49',
    'DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh',
    'ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt',
    'DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL',
    '********************************************'
];

/**
 * Method 1: Check API Response Fields
 */
function detectJitoFromApiResponse(response) {
    console.log('\n=== Method 1: API Response Analysis ===');
    
    if (!response.success) {
        console.log('❌ Transaction failed, cannot analyze');
        return { jitoEnabled: false, reason: 'Transaction failed' };
    }
    
    const data = response.data;
    
    // Check for Jito-specific fields
    const indicators = {
        bundleId: !!data.bundleId,
        jitoUrl: !!data.jitoUrl,
        mevProtected: !!data.mevProtected,
        tipAmount: data.tipAmount > 0,
        executionMethod: data.executionMethod === 'jito'
    };
    
    console.log('🔍 Jito Indicators:');
    Object.entries(indicators).forEach(([key, value]) => {
        console.log(`   ${value ? '✅' : '❌'} ${key}: ${value}`);
    });
    
    // Determine if Jito-enabled
    const jitoEnabled = indicators.bundleId || indicators.executionMethod;
    const mevAttempted = indicators.mevProtected && indicators.tipAmount;
    
    console.log('\n📊 Analysis:');
    if (jitoEnabled) {
        console.log('   🎉 JITO EXECUTION: Transaction executed via Jito bundle');
        console.log(`   📦 Bundle ID: ${data.bundleId}`);
        console.log(`   🔗 Jito Explorer: ${data.jitoUrl}`);
    } else if (mevAttempted) {
        console.log('   ⚠️ MEV ATTEMPTED: Jito was attempted but fell back to regular execution');
        console.log(`   💰 Tip Amount: ${data.tipAmount} lamports (${data.tipAmountSol} SOL)`);
        console.log(`   🔄 Execution Method: ${data.executionMethod}`);
    } else {
        console.log('   ℹ️ REGULAR EXECUTION: No MEV protection attempted');
    }
    
    return {
        jitoEnabled,
        mevAttempted,
        bundleId: data.bundleId,
        tipAmount: data.tipAmount,
        executionMethod: data.executionMethod
    };
}

/**
 * Method 2: Analyze Transaction On-Chain
 */
async function detectJitoFromTransaction(signature) {
    console.log('\n=== Method 2: On-Chain Transaction Analysis ===');
    
    try {
        const connection = new Connection(SOLANA_RPC_URL, 'confirmed');
        
        console.log(`🔍 Analyzing transaction: ${signature}`);
        
        // Get transaction details
        const transaction = await connection.getTransaction(signature, {
            maxSupportedTransactionVersion: 0
        });
        
        if (!transaction) {
            console.log('❌ Transaction not found');
            return { jitoEnabled: false, reason: 'Transaction not found' };
        }
        
        console.log('✅ Transaction found');
        console.log(`   📊 Status: ${transaction.meta.err ? 'Failed' : 'Success'}`);
        console.log(`   🔧 Instructions: ${transaction.transaction.message.instructions.length}`);
        
        // Analyze instructions for Jito tip transfers
        const instructions = transaction.transaction.message.instructions;
        const accountKeys = transaction.transaction.message.accountKeys;
        
        let jitoTipFound = false;
        let tipAmount = 0;
        let tipRecipient = null;
        
        for (let i = 0; i < instructions.length; i++) {
            const instruction = instructions[i];
            
            // Check if this is a system program transfer (program index 0 is usually system program)
            if (instruction.programIdIndex === 0) {
                // Get the accounts involved in this instruction
                const fromAccount = accountKeys[instruction.accounts[0]];
                const toAccount = accountKeys[instruction.accounts[1]];
                
                // Check if the recipient is a Jito tip account
                const isJitoTip = JITO_TIP_ACCOUNTS.includes(toAccount.toString());
                
                if (isJitoTip) {
                    jitoTipFound = true;
                    tipRecipient = toAccount.toString();
                    
                    // Try to extract tip amount from instruction data
                    if (instruction.data && instruction.data.length >= 4) {
                        // System program transfer instruction data format: [type(4 bytes), amount(8 bytes)]
                        const dataBuffer = Buffer.from(instruction.data);
                        if (dataBuffer.length >= 12) {
                            tipAmount = dataBuffer.readBigUInt64LE(4);
                        }
                    }
                    
                    console.log(`   🎯 Jito tip found!`);
                    console.log(`   💰 Tip recipient: ${tipRecipient}`);
                    console.log(`   💵 Tip amount: ${tipAmount.toString()} lamports`);
                    break;
                }
            }
        }
        
        // Check transaction logs for Jito-related messages
        const logs = transaction.meta.logMessages || [];
        const jitoLogs = logs.filter(log => 
            log.includes('jito') || 
            log.includes('bundle') || 
            log.includes('tip')
        );
        
        if (jitoLogs.length > 0) {
            console.log('   📋 Jito-related logs found:');
            jitoLogs.forEach(log => console.log(`      ${log}`));
        }
        
        console.log('\n📊 On-Chain Analysis:');
        if (jitoTipFound) {
            console.log('   ✅ JITO ENABLED: Transaction contains tip to Jito validator');
            console.log(`   💰 Tip: ${tipAmount.toString()} lamports to ${tipRecipient}`);
        } else {
            console.log('   ❌ NO JITO TIPS: No transfers to Jito tip accounts found');
        }
        
        return {
            jitoEnabled: jitoTipFound,
            tipAmount: tipAmount.toString(),
            tipRecipient,
            instructionCount: instructions.length,
            logs: jitoLogs
        };
        
    } catch (error) {
        console.error(`❌ Error analyzing transaction: ${error.message}`);
        return { jitoEnabled: false, error: error.message };
    }
}

/**
 * Method 3: Check Jito Explorer
 */
async function checkJitoExplorer(bundleId) {
    console.log('\n=== Method 3: Jito Explorer Check ===');
    
    if (!bundleId) {
        console.log('❌ No bundle ID provided');
        return { found: false, reason: 'No bundle ID' };
    }
    
    try {
        console.log(`🔍 Checking Jito explorer for bundle: ${bundleId}`);
        
        // Try to access Jito explorer API (if available)
        const jitoExplorerUrl = `https://explorer.jito.wtf/bundle/${bundleId}`;
        console.log(`🔗 Jito Explorer URL: ${jitoExplorerUrl}`);
        
        // Note: Jito explorer might not have a public API, but the URL existence indicates Jito execution
        return {
            found: true,
            explorerUrl: jitoExplorerUrl,
            bundleId
        };
        
    } catch (error) {
        console.error(`❌ Error checking Jito explorer: ${error.message}`);
        return { found: false, error: error.message };
    }
}

/**
 * Test with a real transaction
 */
async function testJitoDetection() {
    console.log('🔍 Testing Jito Detection Methods');
    console.log('=================================');
    
    // Test data for high priority (should trigger MEV protection)
    const testData = {
        "tokenAddress": "4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump",
        "poolAddress": "Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b",
        "dexType": "pumpfun",
        "amount": 0.01,
        "direction": "buy",
        "slippage": 0.10,
        "walletAddress": "********************************************",
        "walletId": "cbq2lb54zo7rtzv14i5sp75j",
        "priorityLevel": "high"
    };
    
    try {
        console.log('📤 Executing test transaction with priorityLevel=high...');
        
        const response = await axios.post(`${API_BASE_URL}/api/pump/swap`, testData, {
            timeout: 60000
        });
        
        if (response.data.success) {
            const signature = response.data.data.signature;
            const bundleId = response.data.data.bundleId;
            
            console.log(`✅ Transaction executed: ${signature}`);
            
            // Method 1: API Response Analysis
            const apiAnalysis = detectJitoFromApiResponse(response.data);
            
            // Method 2: On-Chain Analysis
            const onChainAnalysis = await detectJitoFromTransaction(signature);
            
            // Method 3: Jito Explorer Check
            const explorerCheck = await checkJitoExplorer(bundleId);
            
            // Summary
            console.log('\n=================================');
            console.log('🏁 JITO DETECTION SUMMARY');
            console.log('=================================');
            
            console.log('\n📊 Detection Results:');
            console.log(`   API Response: ${apiAnalysis.jitoEnabled ? '✅ Jito' : apiAnalysis.mevAttempted ? '⚠️ MEV Attempted' : '❌ Regular'}`);
            console.log(`   On-Chain: ${onChainAnalysis.jitoEnabled ? '✅ Jito Tips Found' : '❌ No Jito Tips'}`);
            console.log(`   Explorer: ${explorerCheck.found ? '✅ Bundle Found' : '❌ No Bundle'}`);
            
            console.log('\n🎯 How to Identify Jito Transactions:');
            console.log('   1. Check API response for bundleId field');
            console.log('   2. Look for executionMethod: "jito"');
            console.log('   3. Verify mevProtected: true and tipAmount > 0');
            console.log('   4. Analyze on-chain for transfers to Jito tip accounts');
            console.log('   5. Check if Jito explorer URL is provided');
            
        } else {
            console.log(`❌ Transaction failed: ${response.data.error}`);
        }
        
    } catch (error) {
        console.error(`❌ Test failed: ${error.response?.data?.error || error.message}`);
    }
}

/**
 * Create a simple detection function for production use
 */
function isJitoTransaction(apiResponse) {
    if (!apiResponse.success) return false;
    
    const data = apiResponse.data;
    
    // Primary indicators
    if (data.bundleId) return true;
    if (data.executionMethod === 'jito') return true;
    
    // Secondary indicators (MEV attempted)
    if (data.mevProtected && data.tipAmount > 0) {
        return 'attempted'; // MEV was attempted but may have fallen back
    }
    
    return false;
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        detectJitoFromApiResponse,
        detectJitoFromTransaction,
        checkJitoExplorer,
        isJitoTransaction,
        JITO_TIP_ACCOUNTS
    };
}

// Run the test if this script is executed directly
if (require.main === module) {
    testJitoDetection().catch(error => {
        console.error('Error running Jito detection test:', error);
        process.exit(1);
    });
}
