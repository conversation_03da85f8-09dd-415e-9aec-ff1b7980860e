const axios = require('axios');

/**
 * Test LaunchLab sell operation with slippage fix
 */
async function testLaunchLabSellSlippageFix() {
  console.log('🧪 Testing LaunchLab sell operation with slippage fix...');
  
  const sellRequest = {
    poolId: '4yDSc8nGAKUqL7bqDSxdxEtfBLTfvVcXoi4YUCmBMu1X', // Test pool
    amount: '1000', // Small amount of tokens to sell
    direction: 'sell',
    slippage: 0.05, // 5% slippage (should be sufficient)
    walletAddress: '********************************************',
    walletId: 'cbq2lb54zo7rtzv14i5sp75j',
    priorityFee: 5000,
    computeUnitLimit: 200000
  };

  try {
    console.log('Making LaunchLab sell request...');
    console.log('Request parameters:', {
      ...sellRequest,
      walletAddress: sellRequest.walletAddress.substring(0, 8) + '...',
      walletId: 'provided'
    });
    
    const response = await axios.post('http://localhost:6001/api/launchlab/swap', sellRequest, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60 second timeout
    });
    
    console.log('✅ SUCCESS: LaunchLab sell completed!');
    console.log('Response status:', response.status);
    console.log('Transaction details:', {
      txid: response.data.txid,
      inputAmount: response.data.inputAmountHuman,
      outputAmount: response.data.outputAmountHuman,
      success: response.data.success
    });
    
    return true;
    
  } catch (error) {
    console.error('❌ FAILED: LaunchLab sell test failed');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Error details:', JSON.stringify(error.response.data, null, 2));
      
      // Check if it's still a slippage error
      if (error.response.data.error && error.response.data.error.message) {
        const errorMessage = error.response.data.error.message;
        if (errorMessage.includes('ExceededSlippage') || errorMessage.includes('0x1774')) {
          console.error('🚨 SLIPPAGE ERROR STILL PRESENT - Fix did not work!');
          return false;
        } else {
          console.log('✅ SLIPPAGE FIX WORKING - Different error type (expected for test)');
          return true;
        }
      }
    } else if (error.request) {
      console.error('No response received:', error.message);
    } else {
      console.error('Request setup error:', error.message);
    }
    
    return false;
  }
}

/**
 * Test LaunchLab quote to verify slippage parameter is included
 */
async function testLaunchLabQuote() {
  console.log('\n📊 Testing LaunchLab quote with sell direction...');
  
  const quoteRequest = {
    poolId: '4yDSc8nGAKUqL7bqDSxdxEtfBLTfvVcXoi4YUCmBMu1X',
    amount: '1000',
    direction: 'sell',
    walletAddress: '********************************************'
  };

  try {
    const response = await axios.post('http://localhost:6001/api/launchlab/quote', quoteRequest, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
    
    console.log('✅ Quote successful');
    console.log('Quote details:', {
      inputAmount: response.data.inputAmountHuman,
      outputAmount: response.data.outputAmountHuman,
      priceImpact: response.data.priceImpactPercent
    });
    
    return true;
    
  } catch (error) {
    console.error('❌ Quote failed:', error.response?.data || error.message);
    return false;
  }
}

// Run the tests
async function runTests() {
  console.log('🚀 Starting LaunchLab slippage fix tests...\n');
  
  // Test 1: Quote
  const quoteSuccess = await testLaunchLabQuote();
  
  // Test 2: Sell operation
  const sellSuccess = await testLaunchLabSellSlippageFix();
  
  console.log('\n📋 Test Results:');
  console.log('Quote test:', quoteSuccess ? '✅ PASSED' : '❌ FAILED');
  console.log('Sell test:', sellSuccess ? '✅ PASSED' : '❌ FAILED');
  
  if (quoteSuccess && sellSuccess) {
    console.log('\n🎉 All tests passed! Slippage fix is working.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the logs above.');
  }
}

// Run the test
runTests().catch(console.error);
