/**
 * Test MEV Protection and Bribe Functionality
 *
 * This test verifies that MEV protection and bribe functionality works correctly
 * with the provided test data.
 */

const axios = require('axios');

// Test configuration
const API_BASE_URL = 'http://localhost:6001';

// Original test data (for MEV logic verification)
const TEST_DATA = {
    "tokenAddress": "4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump",
    "poolAddress": "Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b",
    "dexType": "pumpfun",
    "amount": 0.01,
    "direction": "buy",
    "slippage": 0.10,
    "walletAddress": "********************************************",
    "walletId": "cbq2lb54zo7rtzv14i5sp75j",
    "mevProtection": true,
    "bribeAmount": 5000000,
    "priorityLevel": "veryHigh",
    "priorityFee": 100000
};

// Smaller test data (for actual execution with limited wallet balance)
const SMALL_TEST_DATA = {
    "tokenAddress": "4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump",
    "poolAddress": "Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b",
    "dexType": "pumpfun",
    "amount": 0.001, // Much smaller amount
    "direction": "buy",
    "slippage": 0.10,
    "walletAddress": "********************************************",
    "walletId": "cbq2lb54zo7rtzv14i5sp75j",
    "mevProtection": true,
    "bribeAmount": 1000000, // Smaller bribe amount
    "priorityLevel": "high",
    "priorityFee": 100000
};

/**
 * Test the quote endpoint with MEV recommendations
 */
async function testQuoteWithMEV() {
    console.log('\n=== Testing Quote Endpoint with MEV Recommendations ===');

    try {
        const quoteData = {
            tokenAddress: TEST_DATA.tokenAddress,
            poolAddress: TEST_DATA.poolAddress,
            dexType: TEST_DATA.dexType,
            amount: TEST_DATA.amount,
            direction: TEST_DATA.direction
        };

        console.log('Sending quote request:', JSON.stringify(quoteData, null, 2));

        const response = await axios.post(`${API_BASE_URL}/api/pump/quote`, quoteData);

        console.log('Quote Response Status:', response.status);
        console.log('Quote Response Data:', JSON.stringify(response.data, null, 2));

        // Check if MEV recommendations are included
        if (response.data.success && response.data.data.mevProtection) {
            console.log('\n✅ MEV recommendations found in quote response');
            console.log(`- Recommended: ${response.data.data.mevProtection.recommended}`);
            console.log(`- Tip Amount: ${response.data.data.mevProtection.tipAmount} lamports`);
            console.log(`- Tip Amount SOL: ${response.data.data.mevProtection.tipAmountSol} SOL`);
            console.log(`- Reason: ${response.data.data.mevProtection.reason}`);
            console.log(`- Cost Percentage: ${response.data.data.mevProtection.costPercentage}%`);
        } else {
            console.log('❌ No MEV recommendations found in quote response');
        }

        return response.data;
    } catch (error) {
        console.error('Quote request failed:', error.response?.data || error.message);
        return null;
    }
}

/**
 * Test the swap endpoint with MEV protection
 */
async function testSwapWithMEV() {
    console.log('\n=== Testing Swap Endpoint with MEV Protection ===');
    console.log('Note: Using smaller amount due to wallet balance limitations');

    try {
        console.log('Sending swap request:', JSON.stringify(SMALL_TEST_DATA, null, 2));

        const response = await axios.post(`${API_BASE_URL}/api/pump/swap`, SMALL_TEST_DATA);

        console.log('Swap Response Status:', response.status);
        console.log('Swap Response Data:', JSON.stringify(response.data, null, 2));

        // Analyze the response to determine execution method
        if (response.data.success && response.data.data) {
            const data = response.data.data;

            console.log('\n=== Execution Analysis ===');
            console.log(`- Execution Method: ${data.executionMethod}`);
            console.log(`- MEV Protected: ${data.mevProtected}`);
            console.log(`- Tip Amount: ${data.tipAmount} lamports`);
            console.log(`- Tip Amount SOL: ${data.tipAmountSol} SOL`);

            if (data.signature) {
                console.log(`- Transaction Signature: ${data.signature}`);
                console.log(`- Solscan URL: ${data.solscanUrl}`);
            }

            if (data.bundleId) {
                console.log(`- Bundle ID: ${data.bundleId}`);
                console.log(`- Jito URL: ${data.jitoUrl}`);
            }

            // Verify MEV protection was activated
            if (data.executionMethod === 'jito' && data.mevProtected && data.bundleId) {
                console.log('\n✅ MEV Protection ACTIVATED - Transaction submitted to Jito');
                console.log(`✅ Bribe Amount: ${data.tipAmount} lamports (${data.tipAmountSol} SOL)`);
                return true;
            } else if (data.executionMethod === 'privy') {
                console.log('\n❌ MEV Protection NOT ACTIVATED - Using regular Privy execution');
                console.log('❌ This indicates the MEV logic failed and fell back to regular execution');

                // Check if MEV was supposed to be protected
                if (data.mevProtected) {
                    console.log('⚠️ Response shows mevProtected=true but executionMethod=privy');
                    console.log('⚠️ This suggests Jito execution failed and fell back to Privy');
                }
                return false;
            } else {
                console.log('\n⚠️ Unexpected execution method or MEV status');
                console.log(`⚠️ executionMethod: ${data.executionMethod}, mevProtected: ${data.mevProtected}`);
                return false;
            }
        } else {
            console.log('❌ Swap request failed or returned unexpected response');
            return false;
        }

    } catch (error) {
        console.error('Swap request failed:', error.response?.data || error.message);
        return false;
    }
}

/**
 * Test Jito service configuration
 */
async function testJitoConfiguration() {
    console.log('\n=== Testing Jito Service Configuration ===');

    try {
        // Test Jito RPC endpoint
        const jitoRpcUrl = process.env.JITO_RPC_URL || 'https://mainnet.block-engine.jito.wtf/api/v1/bundles';
        console.log(`Jito RPC URL: ${jitoRpcUrl}`);

        // Test if we can reach the Jito endpoint
        const testResponse = await axios.post(jitoRpcUrl, {
            jsonrpc: '2.0',
            id: 1,
            method: 'getInflightBundleStatuses',
            params: [[]]
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 5000
        });

        console.log('✅ Jito RPC endpoint is reachable');
        console.log('Response status:', testResponse.status);

    } catch (error) {
        console.log('❌ Jito RPC endpoint test failed:', error.message);
        if (error.response) {
            console.log('Response status:', error.response.status);
            console.log('Response data:', error.response.data);
        }
    }

    // Test platform keypair configuration
    try {
        const configResponse = await axios.get(`${API_BASE_URL}/api/health`);
        console.log('✅ API server is reachable');
    } catch (error) {
        console.log('❌ API server test failed:', error.message);
    }
}

/**
 * Test MEV logic directly
 */
async function testMEVLogic() {
    console.log('\n=== Testing MEV Logic Directly ===');

    // Simulate the logic from the controller
    const tradeValueSol = TEST_DATA.amount; // 0.01
    const slippageValue = TEST_DATA.slippage; // 0.10
    const mevProtection = TEST_DATA.mevProtection; // true
    const bribeAmount = TEST_DATA.bribeAmount; // 5000000

    console.log(`Trade Value: ${tradeValueSol} SOL`);
    console.log(`Slippage: ${slippageValue} (${slippageValue * 100}%)`);
    console.log(`MEV Protection Requested: ${mevProtection}`);
    console.log(`Bribe Amount: ${bribeAmount} lamports`);

    // Simulate controller logic
    const shouldUseMEV = mevProtection !== undefined
        ? mevProtection === true || mevProtection === 'true'
        : false; // Would call jitoService.isMEVProtectionRecommended

    const finalBribeAmount = bribeAmount || 0;

    console.log(`\nController Logic Results:`);
    console.log(`- shouldUseMEV: ${shouldUseMEV}`);
    console.log(`- finalBribeAmount: ${finalBribeAmount}`);
    console.log(`- Condition (shouldUseMEV && finalBribeAmount > 0): ${shouldUseMEV && finalBribeAmount > 0}`);

    if (shouldUseMEV && finalBribeAmount > 0) {
        console.log('✅ Controller should use MEV protection (Jito)');
    } else {
        console.log('❌ Controller should use regular execution (Privy)');
    }
}

/**
 * Main test function
 */
async function runMEVBribeTest() {
    console.log('🚀 Starting MEV Protection and Bribe Functionality Test');
    console.log('='.repeat(60));

    // Test 1: Jito configuration
    await testJitoConfiguration();

    // Test 2: Quote with MEV recommendations
    await testQuoteWithMEV();

    // Test 3: MEV logic analysis
    await testMEVLogic();

    // Test 4: Swap with MEV protection
    const mevWorking = await testSwapWithMEV();

    console.log('\n' + '='.repeat(60));
    console.log('🏁 Test Summary');
    console.log('='.repeat(60));

    if (mevWorking) {
        console.log('✅ MEV Protection and Bribe functionality is WORKING');
        console.log('✅ Transactions are being submitted to Jito with bribes');
    } else {
        console.log('❌ MEV Protection and Bribe functionality is NOT WORKING');
        console.log('❌ Transactions are falling back to regular Privy execution');
        console.log('\nPossible issues to investigate:');
        console.log('1. Check server logs for MEV protection decision logic');
        console.log('2. Verify Jito service configuration and RPC endpoint');
        console.log('3. Check enhanced swap service execution flow');
        console.log('4. Verify platform keypair configuration for Jito');
        console.log('5. Check if Jito bundle submission is failing and causing fallback');
    }
}

// Run the test
if (require.main === module) {
    runMEVBribeTest().catch(console.error);
}

module.exports = {
    runMEVBribeTest,
    testQuoteWithMEV,
    testSwapWithMEV,
    testMEVLogic,
    TEST_DATA
};
