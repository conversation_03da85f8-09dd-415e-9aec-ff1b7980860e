/**
 * Test sell functionality with actual available balance
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:6001';

async function testSellWithBalance() {
    console.log('Testing sell functionality with actual balance...');
    
    try {
        // First get the balance
        console.log('\n1. Getting current balance...');
        const balanceResponse = await axios.post(`${API_BASE_URL}/api/token/balance`, {
            tokenAddress: "********************************************",
            walletAddress: "********************************************"
        });
        
        console.log('Balance response:', JSON.stringify(balanceResponse.data, null, 2));
        
        if (!balanceResponse.data.success) {
            console.error('Failed to get balance');
            return;
        }
        
        const tokenBalance = parseFloat(balanceResponse.data.data.tokenBalance);
        console.log(`Available token balance: ${tokenBalance}`);
        
        if (tokenBalance <= 0) {
            console.log('No tokens available to sell');
            return;
        }
        
        // Use 90% of available balance to avoid precision issues
        const sellAmount = Math.floor(tokenBalance * 0.9 * 1000000) / 1000000; // Round to 6 decimals
        console.log(`Attempting to sell: ${sellAmount} tokens`);
        
        // Test quote with actual amount
        console.log('\n2. Testing quote with actual amount...');
        const quoteResponse = await axios.post(`${API_BASE_URL}/api/pump/quote`, {
            tokenAddress: "********************************************",
            poolAddress: "DrpCbEhfx2LyToPMNRLhDGhiWsovm2Zg46R3H7Egy6zM",
            dexType: "pumpfun",
            amount: sellAmount,
            direction: "sell"
        });
        
        console.log('Quote response:', JSON.stringify(quoteResponse.data, null, 2));
        
        if (!quoteResponse.data.success) {
            console.error('Quote failed');
            return;
        }
        
        // Test actual swap
        console.log('\n3. Testing actual swap...');
        const swapResponse = await axios.post(`${API_BASE_URL}/api/pump/swap`, {
            tokenAddress: "********************************************",
            poolAddress: "DrpCbEhfx2LyToPMNRLhDGhiWsovm2Zg46R3H7Egy6zM",
            dexType: "pumpfun",
            amount: sellAmount,
            direction: "sell",
            slippage: 0.1,
            walletAddress: "********************************************",
            walletId: "cbq2lb54zo7rtzv14i5sp75j",
            mevProtection: true,
            bribeAmount: 0.00005,
            priorityLevel: "high",
            priorityFee: 0.0002
        });
        
        console.log('Swap response:', JSON.stringify(swapResponse.data, null, 2));
        
        if (swapResponse.data.success) {
            console.log('✅ Sell transaction successful!');
        } else {
            console.log('❌ Sell transaction failed:', swapResponse.data.error);
        }
        
    } catch (error) {
        console.error('Test error:', error.response?.data || error.message);
    }
}

testSellWithBalance().catch(console.error);
