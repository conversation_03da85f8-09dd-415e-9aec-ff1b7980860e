/**
 * Test file for PDA derivation utilities
 *
 * This file tests the dynamic PDA derivation functions implemented in pda.utils.ts
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const {
  deriveAssociatedBondingCurveAccount,
  derivePumpFunCreatorVaultPDA,
  derivePumpSwapCreatorVaultPDA,
  derivePumpFunEventAuthorityPDA,
  derivePumpSwapEventAuthorityPDA,
  derivePumpFunGlobalConfigPDA,
  derivePumpSwapGlobalConfigPDA,
  fetchPumpFunFeeRecipient,
  fetchPumpSwapFeeRecipient
} = require('../src/services/pumpFun/pda.utils');

// Test wallet and token addresses
const TEST_WALLET_ADDRESS = '********************************************';
const TEST_TOKEN_ADDRESS = 'HNGtDJqUf6UjFAzBbZ2gtB16Cov3R8k6c2nQqG4Lpump';
const TEST_POOL_ADDRESS = '5yRbZMHCRQCVnANsjk6BYviSDhHwFfew7mVQEdrX8W7w';

// Solana connection
const connection = new Connection(process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com');

/**
 * Test the associated bonding curve account derivation
 */
async function testAssociatedBondingCurveDerivation() {
  console.log('\n--- Testing Associated Bonding Curve Account Derivation ---');

  const poolAddress = new PublicKey(TEST_POOL_ADDRESS);
  const tokenMint = new PublicKey(TEST_TOKEN_ADDRESS);

  const associatedBondingCurveAccount = deriveAssociatedBondingCurveAccount(poolAddress, tokenMint);
  console.log(`Derived associated bonding curve account: ${associatedBondingCurveAccount.toString()}`);

  // Verify the account exists on-chain
  try {
    const accountInfo = await connection.getAccountInfo(associatedBondingCurveAccount);
    console.log(`Account exists: ${accountInfo !== null}`);
    if (accountInfo) {
      console.log(`Account owner: ${accountInfo.owner.toString()}`);
      console.log(`Account data size: ${accountInfo.data.length} bytes`);
    }
  } catch (error) {
    console.error('Error verifying account:', error);
  }
}

/**
 * Test the PumpFun creator vault PDA derivation
 */
async function testPumpFunCreatorVaultDerivation() {
  console.log('\n--- Testing PumpFun Creator Vault PDA Derivation ---');

  const feeRecipient = new PublicKey('CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM');

  const creatorVault = derivePumpFunCreatorVaultPDA(feeRecipient);
  console.log(`Derived PumpFun creator vault PDA: ${creatorVault.toString()}`);

  // Verify the account exists on-chain
  try {
    const accountInfo = await connection.getAccountInfo(creatorVault);
    console.log(`Account exists: ${accountInfo !== null}`);
    if (accountInfo) {
      console.log(`Account owner: ${accountInfo.owner.toString()}`);
      console.log(`Account data size: ${accountInfo.data.length} bytes`);
    }
  } catch (error) {
    console.error('Error verifying account:', error);
  }
}

/**
 * Test the PumpSwap creator vault PDA derivation
 */
async function testPumpSwapCreatorVaultDerivation() {
  console.log('\n--- Testing PumpSwap Creator Vault PDA Derivation ---');

  const creatorVault = derivePumpSwapCreatorVaultPDA();
  console.log(`Derived PumpSwap creator vault PDA: ${creatorVault.toString()}`);

  // Verify the account exists on-chain
  try {
    const accountInfo = await connection.getAccountInfo(creatorVault);
    console.log(`Account exists: ${accountInfo !== null}`);
    if (accountInfo) {
      console.log(`Account owner: ${accountInfo.owner.toString()}`);
      console.log(`Account data size: ${accountInfo.data.length} bytes`);
    }
  } catch (error) {
    console.error('Error verifying account:', error);
  }
}

/**
 * Test the PumpFun event authority PDA derivation
 */
async function testPumpFunEventAuthorityDerivation() {
  console.log('\n--- Testing PumpFun Event Authority PDA Derivation ---');

  const eventAuthority = derivePumpFunEventAuthorityPDA();
  console.log(`Derived PumpFun event authority PDA: ${eventAuthority.toString()}`);

  // Verify the account exists on-chain
  try {
    const accountInfo = await connection.getAccountInfo(eventAuthority);
    console.log(`Account exists: ${accountInfo !== null}`);
    if (accountInfo) {
      console.log(`Account owner: ${accountInfo.owner.toString()}`);
      console.log(`Account data size: ${accountInfo.data.length} bytes`);
    }
  } catch (error) {
    console.error('Error verifying account:', error);
  }
}

/**
 * Test the PumpSwap event authority PDA derivation
 */
async function testPumpSwapEventAuthorityDerivation() {
  console.log('\n--- Testing PumpSwap Event Authority PDA Derivation ---');

  const eventAuthority = derivePumpSwapEventAuthorityPDA();
  console.log(`Derived PumpSwap event authority PDA: ${eventAuthority.toString()}`);

  // Verify the account exists on-chain
  try {
    const accountInfo = await connection.getAccountInfo(eventAuthority);
    console.log(`Account exists: ${accountInfo !== null}`);
    if (accountInfo) {
      console.log(`Account owner: ${accountInfo.owner.toString()}`);
      console.log(`Account data size: ${accountInfo.data.length} bytes`);
    }
  } catch (error) {
    console.error('Error verifying account:', error);
  }
}

/**
 * Test the fee recipient fetching
 */
async function testFeeRecipientFetching() {
  console.log('\n--- Testing Fee Recipient Fetching ---');

  try {
    const pumpFunFeeRecipient = await fetchPumpFunFeeRecipient(connection);
    console.log(`PumpFun fee recipient: ${pumpFunFeeRecipient.toString()}`);

    const pumpSwapFeeRecipient = await fetchPumpSwapFeeRecipient(connection);
    console.log(`PumpSwap fee recipient: ${pumpSwapFeeRecipient.toString()}`);
  } catch (error) {
    console.error('Error fetching fee recipients:', error);
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('=== PDA Derivation Tests ===');

  await testAssociatedBondingCurveDerivation();
  await testPumpFunCreatorVaultDerivation();
  await testPumpSwapCreatorVaultDerivation();
  await testPumpFunEventAuthorityDerivation();
  await testPumpSwapEventAuthorityDerivation();
  await testFeeRecipientFetching();

  console.log('\n=== Tests Complete ===');
}

// Run the tests
runTests().catch(console.error);
