/**
 * Simple test script to verify the addresses in our PumpFun service
 */

// Import required modules
const { PublicKey } = require('@solana/web3.js');

// Define the expected addresses
const EXPECTED_ADDRESSES = {
  // Solana system addresses
  SYSTEM_PROGRAM: '11111111111111111111111111111111',
  TOKEN_PROGRAM: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
  ASSOCIATED_TOKEN_PROGRAM: 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL',
  RENT_SYSVAR: 'SysvarRent111111111111111111111111111111111',
  WRAPPED_SOL: 'So11111111111111111111111111111111111111112',

  // PumpFun program addresses
  PUMP_FUN_PROGRAM_ID: '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P',
  GLOBAL_CONFIG: '4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf',
  EVENT_AUTHORITY: 'Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1',
  FEE_ACCOUNT: 'CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM',
  LIQUIDITY_MIGRATOR: '39azUYFWPz3VHgKCf3VChUwbpURdCHRxjWVowf5jUJjg'
};

// Import our implementation
try {
  // Try to load the compiled JavaScript files
  const pumpFunUtils = require('../dist/services/pumpFun/pumpFunUtils');
  const pdaUtils = require('../dist/services/pumpFun/pda.utils');

  console.log('\n--- Verifying PumpFun Addresses ---');

  // Check fee recipient
  const feeRecipient = pumpFunUtils.PUMP_FEE_RECIPIENT.toString();
  console.log(`Fee Recipient: ${feeRecipient}`);
  console.log(`Expected: ${EXPECTED_ADDRESSES.FEE_ACCOUNT}`);
  console.log(`Match: ${feeRecipient === EXPECTED_ADDRESSES.FEE_ACCOUNT ? 'YES ✅' : 'NO ❌'}`);

  // Check liquidity migrator
  const liquidityMigrator = pumpFunUtils.LIQUIDITY_MIGRATOR.toString();
  console.log(`\nLiquidity Migrator: ${liquidityMigrator}`);
  console.log(`Expected: ${EXPECTED_ADDRESSES.LIQUIDITY_MIGRATOR}`);
  console.log(`Match: ${liquidityMigrator === EXPECTED_ADDRESSES.LIQUIDITY_MIGRATOR ? 'YES ✅' : 'NO ❌'}`);

  // Check program ID
  const programId = pumpFunUtils.PUMPFUN_PROGRAM_ID.toString();
  console.log(`\nProgram ID: ${programId}`);
  console.log(`Expected: ${EXPECTED_ADDRESSES.PUMP_FUN_PROGRAM_ID}`);
  console.log(`Match: ${programId === EXPECTED_ADDRESSES.PUMP_FUN_PROGRAM_ID ? 'YES ✅' : 'NO ❌'}`);

  // Check token program
  const tokenProgram = pumpFunUtils.TOKEN_PROGRAM_ID.toString();
  console.log(`\nToken Program: ${tokenProgram}`);
  console.log(`Expected: ${EXPECTED_ADDRESSES.TOKEN_PROGRAM}`);
  console.log(`Match: ${tokenProgram === EXPECTED_ADDRESSES.TOKEN_PROGRAM ? 'YES ✅' : 'NO ❌'}`);

  // Check creator vault seed
  console.log('\n--- Verifying Creator Vault Seed ---');
  const creatorVaultSeed = 'creator-vault';
  console.log(`Creator Vault Seed: ${creatorVaultSeed}`);

  // Derive creator vault PDA
  const feeRecipientPubkey = new PublicKey(EXPECTED_ADDRESSES.FEE_ACCOUNT);
  const creatorVault = pdaUtils.derivePumpFunCreatorVaultPDA(feeRecipientPubkey).toString();
  console.log(`Derived Creator Vault PDA: ${creatorVault}`);

} catch (error) {
  console.error('Error loading modules:', error);
  console.log('\nTrying to load TypeScript files directly...');

  try {
    // Try using ts-node to load TypeScript files directly
    require('ts-node/register');
    const pumpFunUtils = require('../src/services/pumpFun/pumpFunUtils');
    const pdaUtils = require('../src/services/pumpFun/pda.utils');

    console.log('\n--- Verifying PumpFun Addresses (TypeScript) ---');

    // Check fee recipient
    const feeRecipient = pumpFunUtils.PUMP_FEE_RECIPIENT.toString();
    console.log(`Fee Recipient: ${feeRecipient}`);
    console.log(`Expected: ${EXPECTED_ADDRESSES.FEE_ACCOUNT}`);
    console.log(`Match: ${feeRecipient === EXPECTED_ADDRESSES.FEE_ACCOUNT ? 'YES ✅' : 'NO ❌'}`);

    // Check liquidity migrator
    const liquidityMigrator = pumpFunUtils.LIQUIDITY_MIGRATOR.toString();
    console.log(`\nLiquidity Migrator: ${liquidityMigrator}`);
    console.log(`Expected: ${EXPECTED_ADDRESSES.LIQUIDITY_MIGRATOR}`);
    console.log(`Match: ${liquidityMigrator === EXPECTED_ADDRESSES.LIQUIDITY_MIGRATOR ? 'YES ✅' : 'NO ❌'}`);

    // Check creator vault seed
    console.log('\n--- Verifying Creator Vault Seed ---');
    const feeRecipientPubkey = new PublicKey(EXPECTED_ADDRESSES.FEE_ACCOUNT);
    const creatorVault = pdaUtils.derivePumpFunCreatorVaultPDA(feeRecipientPubkey).toString();
    console.log(`Derived Creator Vault PDA: ${creatorVault}`);

  } catch (tsError) {
    console.error('Error loading TypeScript files:', tsError);
  }
}
