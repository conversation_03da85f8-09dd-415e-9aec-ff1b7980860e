/**
 * Test that mevProtection: false properly disables <PERSON><PERSON> and uses normal Privy validation
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:6001';

async function testMEVDisabled() {
    console.log('🚫 TESTING MEV PROTECTION DISABLED (mevProtection: false)');
    console.log('='.repeat(60));
    
    try {
        // First get the actual balance
        const balanceResponse = await axios.post(`${API_BASE_URL}/api/token/balance`, {
            tokenAddress: "a65PGnePgew3XKFM7R3Mu3DxnFykzTLtSdxRrzPpump",
            walletAddress: "********************************************"
        });
        
        if (!balanceResponse.data.success) {
            console.error('Balance check failed:', balanceResponse.data.error);
            return;
        }
        
        const tokenBalance = parseFloat(balanceResponse.data.data.tokenBalance);
        console.log(`Available token balance: ${tokenBalance} tokens`);
        
        if (tokenBalance <= 0) {
            console.log('❌ No tokens available for testing');
            return;
        }
        
        // Use 50% of available balance for testing
        const sellAmount = Math.floor(tokenBalance * 0.5 * 1000000) / 1000000;
        console.log(`Testing sell with ${sellAmount} tokens (50% of balance)`);
        
        // Test 1: Sell with mevProtection explicitly set to false
        console.log('\n🔄 STEP 1: Testing Sell with mevProtection: false');
        console.log('-'.repeat(50));
        
        const sellRequestNoMEV = {
            tokenAddress: "a65PGnePgew3XKFM7R3Mu3DxnFykzTLtSdxRrzPpump",
            poolAddress: "61LpLiUSyp9ACL6cDJ5fREKrk2628toxjTFXwuMM1vNR",
            dexType: "pumpfun",
            amount: sellAmount,
            direction: "sell",
            slippage: 0.1,
            walletAddress: "********************************************",
            walletId: "cbq2lb54zo7rtzv14i5sp75j",
            mevProtection: false, // Explicitly disable MEV protection
            bribeAmount: 0,
            priorityLevel: "low",
            priorityFee: 0.00001
        };
        
        console.log('Request payload:', JSON.stringify(sellRequestNoMEV, null, 2));
        
        const sellResponse = await axios.post(`${API_BASE_URL}/api/pump/swap`, sellRequestNoMEV, {
            timeout: 30000
        });
        
        console.log('Response:', JSON.stringify(sellResponse.data, null, 2));
        
        if (sellResponse.data.success) {
            console.log('\n✅ SELL WITH MEV DISABLED SUCCESSFUL!');
            console.log(`  - Transaction Signature: ${sellResponse.data.data.signature}`);
            console.log(`  - SOL Received: ${sellResponse.data.data.outAmount}`);
            console.log(`  - Execution Method: ${sellResponse.data.data.executionMethod}`);
            console.log(`  - MEV Protected: ${sellResponse.data.data.mevProtected}`);
            console.log(`  - Bundle ID: ${sellResponse.data.data.bundleId || 'None (expected)'}`);
            console.log(`  - Solscan URL: ${sellResponse.data.data.solscanUrl}`);
            
            // Verify that it used Privy execution, not Jito
            if (sellResponse.data.data.executionMethod === 'privy' && !sellResponse.data.data.bundleId) {
                console.log('\n🎉 VERIFICATION PASSED:');
                console.log('  ✅ Used Privy execution method');
                console.log('  ✅ No Jito bundle ID (as expected)');
                console.log('  ✅ MEV protection was properly disabled');
            } else {
                console.log('\n⚠️  VERIFICATION FAILED:');
                console.log(`  - Expected execution method: 'privy', got: '${sellResponse.data.data.executionMethod}'`);
                console.log(`  - Expected no bundle ID, got: '${sellResponse.data.data.bundleId}'`);
            }
            
            // Test 2: Compare with MEV enabled (if there are still tokens)
            console.log('\n🔄 STEP 2: Testing Sell with mevProtection: true (for comparison)');
            console.log('-'.repeat(50));
            
            // Wait a bit and check balance again
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const balanceResponse2 = await axios.post(`${API_BASE_URL}/api/token/balance`, {
                tokenAddress: "a65PGnePgew3XKFM7R3Mu3DxnFykzTLtSdxRrzPpump",
                walletAddress: "********************************************"
            });
            
            if (balanceResponse2.data.success) {
                const newBalance = parseFloat(balanceResponse2.data.data.tokenBalance);
                console.log(`Updated balance: ${newBalance} tokens`);
                
                if (newBalance > 0) {
                    const sellAmount2 = Math.floor(newBalance * 0.5 * 1000000) / 1000000;
                    console.log(`Testing MEV sell with ${sellAmount2} tokens`);
                    
                    const sellRequestWithMEV = {
                        tokenAddress: "a65PGnePgew3XKFM7R3Mu3DxnFykzTLtSdxRrzPpump",
                        poolAddress: "61LpLiUSyp9ACL6cDJ5fREKrk2628toxjTFXwuMM1vNR",
                        dexType: "pumpfun",
                        amount: sellAmount2,
                        direction: "sell",
                        slippage: 0.1,
                        walletAddress: "********************************************",
                        walletId: "cbq2lb54zo7rtzv14i5sp75j",
                        mevProtection: true, // Enable MEV protection
                        bribeAmount: 0.001, // Small bribe amount
                        priorityLevel: "medium",
                        priorityFee: 0.0005
                    };
                    
                    console.log('MEV Request payload:', JSON.stringify(sellRequestWithMEV, null, 2));
                    
                    try {
                        const mevSellResponse = await axios.post(`${API_BASE_URL}/api/pump/swap`, sellRequestWithMEV, {
                            timeout: 45000 // Longer timeout for MEV
                        });
                        
                        console.log('MEV Response:', JSON.stringify(mevSellResponse.data, null, 2));
                        
                        if (mevSellResponse.data.success) {
                            console.log('\n✅ SELL WITH MEV ENABLED SUCCESSFUL!');
                            console.log(`  - Transaction Signature: ${mevSellResponse.data.data.signature}`);
                            console.log(`  - SOL Received: ${mevSellResponse.data.data.outAmount}`);
                            console.log(`  - Execution Method: ${mevSellResponse.data.data.executionMethod}`);
                            console.log(`  - MEV Protected: ${mevSellResponse.data.data.mevProtected}`);
                            console.log(`  - Bundle ID: ${mevSellResponse.data.data.bundleId || 'None'}`);
                            
                            // Verify that it used Jito execution
                            if (mevSellResponse.data.data.executionMethod === 'jito' || mevSellResponse.data.data.bundleId) {
                                console.log('\n🎉 MEV VERIFICATION PASSED:');
                                console.log('  ✅ Used Jito execution method or has bundle ID');
                                console.log('  ✅ MEV protection was properly enabled');
                            } else {
                                console.log('\n⚠️  MEV VERIFICATION: Fell back to Privy (Jito may have failed)');
                                console.log(`  - Execution method: '${mevSellResponse.data.data.executionMethod}'`);
                                console.log(`  - Bundle ID: '${mevSellResponse.data.data.bundleId}'`);
                            }
                        } else {
                            console.log('\n❌ SELL WITH MEV ENABLED FAILED');
                            console.log(`  - Error: ${mevSellResponse.data.error}`);
                            
                            if (mevSellResponse.data.error.includes('timeout') || mevSellResponse.data.error.includes('Jito')) {
                                console.log('  - This is expected if Jito is having issues');
                                console.log('  - The important thing is that mevProtection: false worked');
                            }
                        }
                    } catch (mevError) {
                        console.log('\n❌ SELL WITH MEV ENABLED FAILED WITH EXCEPTION');
                        console.log(`  - Error: ${mevError.message}`);
                        
                        if (mevError.message.includes('timeout')) {
                            console.log('  - Jito timeout (expected if Jito is having issues)');
                            console.log('  - The important thing is that mevProtection: false worked');
                        }
                    }
                } else {
                    console.log('No tokens left for MEV comparison test');
                }
            }
            
        } else {
            console.log('\n❌ SELL WITH MEV DISABLED FAILED');
            console.log(`  - Error: ${sellResponse.data.error}`);
            
            // Analyze the error
            if (sellResponse.data.error.includes('Insufficient token balance')) {
                console.log('\n🔍 BALANCE ERROR ANALYSIS:');
                console.log('  - Issue: Token balance validation failed');
                console.log('  - This suggests the balance validation fixes are working');
                console.log('  - Try with a smaller amount');
            } else if (sellResponse.data.error.includes('Jito') || sellResponse.data.error.includes('bundle')) {
                console.log('\n🔍 JITO ERROR ANALYSIS:');
                console.log('  - Issue: Jito was still called despite mevProtection: false');
                console.log('  - This indicates the fix did not work properly');
            } else {
                console.log('\n🔍 OTHER ERROR ANALYSIS:');
                console.log('  - Issue: Unknown error type');
                console.log('  - Error:', sellResponse.data.error);
            }
        }
        
        console.log('\n📋 SUMMARY:');
        console.log('='.repeat(40));
        console.log('✅ Changes made to disable Jito when mevProtection: false:');
        console.log('  1. Updated pump.controller.ts to check mevProtection === false first');
        console.log('  2. Updated enhanced-swap.service.ts to force regular execution');
        console.log('  3. Updated determineMEVUsage() to respect explicit false values');
        console.log('');
        console.log('🎯 Expected behavior:');
        console.log('  - mevProtection: false → Use Privy execution, no Jito');
        console.log('  - mevProtection: true → Use Jito execution (if available)');
        console.log('  - mevProtection: undefined → Auto-recommend based on trade size');
        
    } catch (error) {
        console.error('Test failed:', error.message);
        if (error.response?.data) {
            console.error('Response:', JSON.stringify(error.response.data, null, 2));
        }
        
        if (error.code === 'ECONNREFUSED') {
            console.error('\n🔧 DIAGNOSIS: Solana service not running on port 6001');
        } else if (error.code === 'ETIMEDOUT') {
            console.error('\n🔧 DIAGNOSIS: Service timeout');
        }
    }
}

testMEVDisabled().catch(console.error);
