/**
 * Test Enhanced MEV Endpoints
 *
 * This test verifies that the new enhanced MEV endpoints are working correctly
 * and that MEV protection is properly activated.
 */

const axios = require('axios');

// Test configuration
const API_BASE_URL = 'http://localhost:6001';

// Test data
const TEST_DATA = {
    "tokenAddress": "4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump",
    "poolAddress": "Es36yqDKGdauwY9UU5CLzeHpHKuCfJhWGywLvBsFe58b",
    "dexType": "pumpfun",
    "amount": 0.01,
    "direction": "buy",
    "slippage": 0.10,
    "walletAddress": "********************************************",
    "walletId": "cbq2lb54zo7rtzv14i5sp75j",
    "mevProtection": true,
    "bribeAmount": 2000000, // 0.002 SOL
    "priorityLevel": "high",
    "maxMevTip": 5000000
};

/**
 * Test the enhanced MEV quote endpoint
 */
async function testEnhancedMevQuote() {
    console.log('\n=== Testing Enhanced MEV Quote Endpoint ===');
    
    try {
        const quoteData = {
            tokenAddress: TEST_DATA.tokenAddress,
            poolAddress: TEST_DATA.poolAddress,
            dexType: TEST_DATA.dexType,
            amount: TEST_DATA.amount,
            direction: TEST_DATA.direction
        };
        
        console.log('Sending enhanced MEV quote request:', JSON.stringify(quoteData, null, 2));
        
        const response = await axios.post(`${API_BASE_URL}/api/mev/quote`, quoteData, {
            timeout: 30000
        });
        
        console.log('Enhanced MEV Quote Response Status:', response.status);
        console.log('Enhanced MEV Quote Response Data:', JSON.stringify(response.data, null, 2));
        
        if (response.data.success && response.data.data.mevProtection) {
            console.log('✅ Enhanced MEV quote endpoint working');
            console.log(`- MEV Recommended: ${response.data.data.mevProtection.recommended}`);
            console.log(`- Tip Amount: ${response.data.data.mevProtection.tipAmount} lamports`);
            console.log(`- Tip Amount SOL: ${response.data.data.mevProtection.tipAmountSol} SOL`);
            return true;
        } else {
            console.log('❌ Enhanced MEV quote endpoint failed or missing MEV data');
            return false;
        }
        
    } catch (error) {
        console.error('❌ Enhanced MEV quote request failed:', error.response?.data || error.message);
        return false;
    }
}

/**
 * Test the enhanced MEV swap endpoint
 */
async function testEnhancedMevSwap() {
    console.log('\n=== Testing Enhanced MEV Swap Endpoint ===');
    
    try {
        console.log('Sending enhanced MEV swap request:', JSON.stringify(TEST_DATA, null, 2));
        
        const response = await axios.post(`${API_BASE_URL}/api/mev/swap`, TEST_DATA, {
            timeout: 60000 // Longer timeout for swaps
        });
        
        console.log('Enhanced MEV Swap Response Status:', response.status);
        console.log('Enhanced MEV Swap Response Data:', JSON.stringify(response.data, null, 2));
        
        if (response.data.success) {
            const data = response.data.data;
            console.log('✅ Enhanced MEV swap endpoint working');
            console.log(`- Execution Method: ${data.executionMethod}`);
            console.log(`- MEV Protected: ${data.mevProtected}`);
            console.log(`- Tip Amount: ${data.tipAmount} lamports`);
            console.log(`- Tip Amount SOL: ${data.tipAmountSol} SOL`);
            
            if (data.bundleId) {
                console.log(`- Bundle ID: ${data.bundleId}`);
                console.log(`- Jito URL: ${data.jitoUrl}`);
                console.log('🎉 JITO EXECUTION SUCCESSFUL!');
                return 'jito';
            } else if (data.signature) {
                console.log(`- Transaction Signature: ${data.signature}`);
                console.log(`- Solscan URL: ${data.solscanUrl}`);
                
                if (data.executionMethod === 'jito') {
                    console.log('⚠️ Jito execution claimed but no bundle ID returned');
                    return 'jito-partial';
                } else {
                    console.log('⚠️ Fallback to regular execution');
                    return 'fallback';
                }
            } else {
                console.log('❌ No transaction signature or bundle ID returned');
                return 'failed';
            }
        } else {
            console.log('❌ Enhanced MEV swap failed:', response.data.error);
            return 'failed';
        }
        
    } catch (error) {
        console.error('❌ Enhanced MEV swap request failed:', error.response?.data || error.message);
        return 'failed';
    }
}

/**
 * Test regular pump endpoint for comparison
 */
async function testRegularPumpEndpoint() {
    console.log('\n=== Testing Regular Pump Endpoint (for comparison) ===');
    
    try {
        const regularData = {
            tokenAddress: TEST_DATA.tokenAddress,
            poolAddress: TEST_DATA.poolAddress,
            dexType: TEST_DATA.dexType,
            amount: TEST_DATA.amount,
            direction: TEST_DATA.direction,
            slippage: TEST_DATA.slippage,
            walletAddress: TEST_DATA.walletAddress,
            walletId: TEST_DATA.walletId
        };
        
        console.log('Sending regular pump swap request:', JSON.stringify(regularData, null, 2));
        
        const response = await axios.post(`${API_BASE_URL}/api/pump/swap`, regularData, {
            timeout: 60000
        });
        
        console.log('Regular Pump Response Status:', response.status);
        console.log('Regular Pump Response Data:', JSON.stringify(response.data, null, 2));
        
        if (response.data.success) {
            console.log('✅ Regular pump endpoint working');
            console.log('- This should NOT have MEV protection');
            return true;
        } else {
            console.log('❌ Regular pump endpoint failed');
            return false;
        }
        
    } catch (error) {
        console.error('❌ Regular pump request failed:', error.response?.data || error.message);
        return false;
    }
}

/**
 * Main test function
 */
async function runEnhancedMevTests() {
    console.log('🚀 Starting Enhanced MEV Endpoints Test');
    console.log('============================================================');
    
    // Test enhanced MEV quote endpoint
    const quoteSuccess = await testEnhancedMevQuote();
    
    // Test enhanced MEV swap endpoint
    const swapResult = await testEnhancedMevSwap();
    
    // Test regular pump endpoint for comparison
    const regularSuccess = await testRegularPumpEndpoint();
    
    // Summary
    console.log('\n============================================================');
    console.log('🏁 Enhanced MEV Test Summary');
    console.log('============================================================');
    
    console.log(`Enhanced MEV Quote: ${quoteSuccess ? '✅ Working' : '❌ Failed'}`);
    console.log(`Enhanced MEV Swap: ${swapResult === 'jito' ? '✅ Jito Success' : 
                                     swapResult === 'jito-partial' ? '⚠️ Jito Partial' :
                                     swapResult === 'fallback' ? '⚠️ Fallback' : '❌ Failed'}`);
    console.log(`Regular Pump Endpoint: ${regularSuccess ? '✅ Working' : '❌ Failed'}`);
    
    if (swapResult === 'jito') {
        console.log('\n🎉 SUCCESS: Enhanced MEV endpoints are working and Jito execution is successful!');
    } else if (swapResult === 'fallback') {
        console.log('\n⚠️ PARTIAL: Enhanced MEV endpoints are working but falling back to regular execution');
        console.log('   This indicates Jito integration issues that need investigation');
    } else {
        console.log('\n❌ FAILURE: Enhanced MEV endpoints are not working properly');
    }
}

// Run the tests
runEnhancedMevTests().catch(error => {
    console.error('Error running enhanced MEV tests:', error);
    process.exit(1);
});
