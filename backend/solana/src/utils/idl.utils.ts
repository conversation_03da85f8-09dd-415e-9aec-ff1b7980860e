import { PublicKey } from '@solana/web3.js';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Interface for IDL instruction discriminator
 */
export interface IdlInstruction {
  name: string;
  discriminator: number[];
  accounts: any[];
  args: any[];
}

/**
 * Interface for IDL
 */
export interface Idl {
  address: string;
  metadata: {
    name: string;
    version: string;
    spec: string;
    description: string;
  };
  instructions: IdlInstruction[];
}

// Cache for loaded IDLs
const idlCache: Record<string, Idl> = {};

/**
 * Load an IDL from a file
 * @param idlPath Path to the IDL file
 * @returns Parsed IDL
 */
export function loadIdl(idlPath: string): Idl {
  if (idlCache[idlPath]) {
    return idlCache[idlPath];
  }

  try {
    const resolvedPath = path.resolve(idlPath);
    const idlContent = fs.readFileSync(resolvedPath, 'utf-8');
    const idl = JSON.parse(idlContent) as Idl;
    idlCache[idlPath] = idl;
    return idl;
  } catch (error) {
    console.error(`Error loading IDL from ${idlPath}:`, error);
    throw new Error(`Failed to load IDL from ${idlPath}`);
  }
}

/**
 * Get the program ID from an IDL
 * @param idl IDL object
 * @returns Program ID as PublicKey
 */
export function getProgramIdFromIdl(idl: Idl): PublicKey {
  return new PublicKey(idl.address);
}

/**
 * Get an instruction discriminator from an IDL
 * @param idl IDL object
 * @param instructionName Name of the instruction
 * @returns Discriminator as Buffer
 */
export function getInstructionDiscriminator(idl: Idl, instructionName: string): Buffer {
  const instruction = idl.instructions.find(instr => instr.name === instructionName);
  if (!instruction) {
    throw new Error(`Instruction '${instructionName}' not found in IDL`);
  }
  return Buffer.from(instruction.discriminator);
}

/**
 * Get the event authority seed from an IDL
 * @param idl IDL object
 * @returns Event authority seed as Buffer
 */
export function getEventAuthoritySeed(idl: Idl): Buffer {
  // Find an instruction that has an event_authority account
  const instructionWithEventAuthority = idl.instructions.find(instr =>
    instr.accounts.some(acc => acc.name === 'event_authority')
  );

  if (!instructionWithEventAuthority) {
    throw new Error('No instruction with event_authority found in IDL');
  }

  const eventAuthorityAccount = instructionWithEventAuthority.accounts.find(acc =>
    acc.name === 'event_authority'
  );

  if (!eventAuthorityAccount?.pda?.seeds?.[0]?.value) {
    throw new Error('Event authority seed not found in IDL');
  }

  return Buffer.from(eventAuthorityAccount.pda.seeds[0].value);
}

/**
 * Get the global config seed from an IDL
 * @param idl IDL object
 * @returns Global config seed as Buffer
 */
export function getGlobalConfigSeed(idl: Idl): Buffer {
  // For PumpSwap, the seed is 'global_config'
  if (idl.metadata.name === 'pump_amm') {
    return Buffer.from('global_config');
  }

  // For PumpFun, the seed is 'global'
  if (idl.metadata.name === 'pump') {
    return Buffer.from('global');
  }

  // Find an instruction that has a global_config account
  const instructionWithGlobalConfig = idl.instructions.find(instr =>
    instr.accounts.some(acc => acc.name === 'global_config')
  );

  if (!instructionWithGlobalConfig) {
    throw new Error('No instruction with global_config found in IDL');
  }

  const globalConfigAccount = instructionWithGlobalConfig.accounts.find(acc =>
    acc.name === 'global_config'
  );

  if (!globalConfigAccount?.pda?.seeds?.[0]?.value) {
    throw new Error('Global config seed not found in IDL');
  }

  return Buffer.from(globalConfigAccount.pda.seeds[0].value);
}

/**
 * Get the creator vault seed from an IDL
 * @param idl IDL object
 * @returns Creator vault seed as Buffer
 */
export function getCreatorVaultSeed(idl: Idl): Buffer {
  // For PumpSwap, the seed is 'creator_vault'
  if (idl.metadata.name === 'pump_amm') {
    return Buffer.from('creator_vault');
  }

  // For PumpFun, the seed is 'creator-vault'
  if (idl.metadata.name === 'pump') {
    return Buffer.from('creator-vault');
  }

  // Find an instruction that has a coin_creator_vault_authority account
  const instructionWithCreatorVault = idl.instructions.find(instr =>
    instr.accounts.some(acc => acc.name === 'coin_creator_vault_authority')
  );

  if (!instructionWithCreatorVault) {
    throw new Error('No instruction with coin_creator_vault_authority found in IDL');
  }

  const creatorVaultAccount = instructionWithCreatorVault.accounts.find(acc =>
    acc.name === 'coin_creator_vault_authority'
  );

  if (!creatorVaultAccount?.pda?.seeds?.[0]?.value) {
    throw new Error('Creator vault seed not found in IDL');
  }

  return Buffer.from(creatorVaultAccount.pda.seeds[0].value);
}

// Load IDLs at module initialization
export const PUMP_FUN_IDL = loadIdl('idl/pump_fun_idl.json');
export const PUMP_SWAP_IDL = loadIdl('idl/pump_swap_idl.json');

// Export program IDs
export const PUMP_FUN_PROGRAM_ID = getProgramIdFromIdl(PUMP_FUN_IDL);
export const PUMP_SWAP_PROGRAM_ID = getProgramIdFromIdl(PUMP_SWAP_IDL);

// Export discriminators
export const PUMP_FUN_BUY_DISCRIMINATOR = getInstructionDiscriminator(PUMP_FUN_IDL, 'buy');
export const PUMP_FUN_SELL_DISCRIMINATOR = getInstructionDiscriminator(PUMP_FUN_IDL, 'sell');
export const PUMP_SWAP_BUY_DISCRIMINATOR = getInstructionDiscriminator(PUMP_SWAP_IDL, 'buy');
export const PUMP_SWAP_SELL_DISCRIMINATOR = getInstructionDiscriminator(PUMP_SWAP_IDL, 'sell');

// Export seeds
export const PUMP_FUN_EVENT_AUTHORITY_SEED = getEventAuthoritySeed(PUMP_FUN_IDL);
export const PUMP_SWAP_EVENT_AUTHORITY_SEED = getEventAuthoritySeed(PUMP_SWAP_IDL);
export const PUMP_FUN_GLOBAL_CONFIG_SEED = getGlobalConfigSeed(PUMP_FUN_IDL);
export const PUMP_SWAP_GLOBAL_CONFIG_SEED = getGlobalConfigSeed(PUMP_SWAP_IDL);
export const PUMP_FUN_CREATOR_VAULT_SEED = getCreatorVaultSeed(PUMP_FUN_IDL);
export const PUMP_SWAP_CREATOR_VAULT_SEED = getCreatorVaultSeed(PUMP_SWAP_IDL);
