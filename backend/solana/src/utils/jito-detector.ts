/**
 * Jito Transaction Detection Utility
 * 
 * Simple utility functions to detect if a transaction was executed
 * through Jito MEV protection.
 */

export interface JitoDetectionResult {
  type: 'jito' | 'mev_attempted' | 'regular' | 'failed';
  jitoEnabled: boolean;
  mevAttempted: boolean;
  bundleId?: string;
  signature?: string;
  tipAmount?: number;
  tipAmountSol?: number;
  executionMethod?: string;
  jitoUrl?: string;
  solscanUrl?: string;
  reason?: string;
}

export interface SwapResponse {
  success: boolean;
  data?: {
    bundleId?: string;
    signature?: string;
    executionMethod?: string;
    mevProtected?: boolean;
    tipAmount?: number;
    tipAmountSol?: number;
    jitoUrl?: string;
    solscanUrl?: string;
  };
  error?: string;
}

/**
 * Detect Jito execution from API response
 */
export function detectJitoFromResponse(response: SwapResponse): JitoDetectionResult {
  // Handle failed transactions
  if (!response.success || !response.data) {
    return {
      type: 'failed',
      jitoEnabled: false,
      mevAttempted: false,
      reason: response.error || 'Transaction failed'
    };
  }

  const data = response.data;

  // ✅ CONFIRMED JITO EXECUTION
  if (data.bundleId) {
    return {
      type: 'jito',
      jitoEnabled: true,
      mevAttempted: true,
      bundleId: data.bundleId,
      tipAmount: data.tipAmount,
      tipAmountSol: data.tipAmountSol,
      executionMethod: data.executionMethod,
      jitoUrl: data.jitoUrl,
      reason: 'Transaction executed via Jito bundle'
    };
  }

  // Check for explicit Jito execution method
  if (data.executionMethod === 'jito') {
    return {
      type: 'jito',
      jitoEnabled: true,
      mevAttempted: true,
      signature: data.signature,
      tipAmount: data.tipAmount,
      tipAmountSol: data.tipAmountSol,
      executionMethod: data.executionMethod,
      solscanUrl: data.solscanUrl,
      reason: 'Transaction executed via Jito (no bundle ID returned)'
    };
  }

  // ⚠️ MEV ATTEMPTED (fell back to regular execution)
  if (data.mevProtected && data.tipAmount && data.tipAmount > 0) {
    return {
      type: 'mev_attempted',
      jitoEnabled: false,
      mevAttempted: true,
      signature: data.signature,
      tipAmount: data.tipAmount,
      tipAmountSol: data.tipAmountSol,
      executionMethod: data.executionMethod,
      solscanUrl: data.solscanUrl,
      reason: 'MEV protection attempted but fell back to regular execution'
    };
  }

  // ❌ REGULAR EXECUTION
  return {
    type: 'regular',
    jitoEnabled: false,
    mevAttempted: false,
    signature: data.signature,
    executionMethod: data.executionMethod,
    solscanUrl: data.solscanUrl,
    reason: 'Regular execution without MEV protection'
  };
}

/**
 * Simple boolean check for Jito execution
 */
export function isJitoTransaction(response: SwapResponse): boolean {
  const detection = detectJitoFromResponse(response);
  return detection.jitoEnabled;
}

/**
 * Check if MEV protection was attempted (regardless of success)
 */
export function isMevProtected(response: SwapResponse): boolean {
  const detection = detectJitoFromResponse(response);
  return detection.mevAttempted;
}

/**
 * Get human-readable transaction type
 */
export function getTransactionType(response: SwapResponse): string {
  const detection = detectJitoFromResponse(response);
  
  switch (detection.type) {
    case 'jito':
      return 'Jito MEV Protected';
    case 'mev_attempted':
      return 'MEV Protected (Fallback)';
    case 'regular':
      return 'Regular Transaction';
    case 'failed':
      return 'Failed Transaction';
    default:
      return 'Unknown';
  }
}

/**
 * Get transaction status with emoji
 */
export function getTransactionStatus(response: SwapResponse): string {
  const detection = detectJitoFromResponse(response);
  
  switch (detection.type) {
    case 'jito':
      return '🎉 Jito MEV Protection Active';
    case 'mev_attempted':
      return '⚠️ MEV Protection Applied (Fallback)';
    case 'regular':
      return 'ℹ️ Regular Execution';
    case 'failed':
      return '❌ Transaction Failed';
    default:
      return '❓ Unknown Status';
  }
}

/**
 * Check if request parameters would trigger MEV protection
 */
export function wouldTriggerMev(params: {
  priorityLevel?: string;
  mevProtection?: boolean;
  amount?: number;
  slippage?: number;
}): boolean {
  const { priorityLevel, mevProtection, amount, slippage } = params;

  // Explicit MEV protection
  if (mevProtection === true) return true;

  // High priority levels trigger MEV
  if (priorityLevel === 'high' || priorityLevel === 'veryHigh') return true;

  // Large trades might auto-trigger MEV (based on your business logic)
  if (amount && amount > 1.0) return true; // > 1 SOL

  // High slippage might trigger MEV
  if (slippage && slippage > 0.05) return true; // > 5%

  return false;
}

/**
 * Get expected tip amount based on priority level
 */
export function getExpectedTipAmount(priorityLevel: string, baseAmount: number = 500000): number {
  const multipliers = {
    low: 0.5,
    medium: 1.0,
    high: 1.5,
    veryHigh: 2.0
  };

  const multiplier = multipliers[priorityLevel as keyof typeof multipliers] || 1.0;
  return Math.floor(baseAmount * multiplier);
}

/**
 * Format tip amount for display
 */
export function formatTipAmount(lamports: number): string {
  const sol = lamports / 1_000_000_000;
  return `${lamports.toLocaleString()} lamports (${sol.toFixed(6)} SOL)`;
}

/**
 * Generate analytics data for transaction
 */
export function getAnalyticsData(response: SwapResponse): Record<string, any> {
  const detection = detectJitoFromResponse(response);
  
  return {
    transaction_type: detection.type,
    jito_enabled: detection.jitoEnabled,
    mev_attempted: detection.mevAttempted,
    execution_method: detection.executionMethod,
    tip_amount_lamports: detection.tipAmount || 0,
    tip_amount_sol: detection.tipAmountSol || 0,
    has_bundle_id: !!detection.bundleId,
    has_signature: !!detection.signature
  };
}

/**
 * Log transaction details (for debugging)
 */
export function logTransactionDetails(response: SwapResponse, label: string = 'Transaction'): void {
  const detection = detectJitoFromResponse(response);
  
  console.log(`${label} Details:`);
  console.log(`  Type: ${detection.type}`);
  console.log(`  Jito Enabled: ${detection.jitoEnabled}`);
  console.log(`  MEV Attempted: ${detection.mevAttempted}`);
  
  if (detection.bundleId) {
    console.log(`  Bundle ID: ${detection.bundleId}`);
  }
  
  if (detection.signature) {
    console.log(`  Signature: ${detection.signature}`);
  }
  
  if (detection.tipAmount) {
    console.log(`  Tip: ${formatTipAmount(detection.tipAmount)}`);
  }
  
  console.log(`  Reason: ${detection.reason}`);
}

// Export all Jito tip accounts for reference
export const JITO_TIP_ACCOUNTS = [
  '96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5',
  'HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe',
  'Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY',
  'ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49',
  'DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh',
  'ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt',
  'DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL',
  '3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT'
];
