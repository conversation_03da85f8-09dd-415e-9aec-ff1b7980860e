import axios from "axios";
import { Connection, Transaction, VersionedTransaction } from "@solana/web3.js";
import crypto from 'crypto';
import canonicalize from 'canonicalize';

/**
 * Privy Session Signer Utility
 * Handles transaction signing using Privy's session signer API
 * Migrated from radium-sdk/src/utils/privy-signer.ts
 */

export interface PrivySigningConfig {
  appId: string;
  appSecret: string;
  signingKey: string;
}

export interface PrivySignTransactionRequest {
  walletId: string;
  transaction: Transaction | VersionedTransaction;
  connection?: Connection;
}

export interface PrivySignTransactionResponse {
  signature: string;
  signedTransaction: Transaction | VersionedTransaction;
}

/**
 * Gets Privy credentials from environment variables
 */
function getPrivyCredentials(): PrivySigningConfig {
  const appId = process.env.PRIVY_APP_ID?.trim();
  const appSecret = process.env.PRIVY_APP_SECRET?.trim();
  const signingKey = process.env.PRIVY_SIGNING_KEY?.trim()?.replace('wallet-auth:', '');

  if (!appId || !appSecret || !signingKey) {
    throw new Error('Privy credentials missing. Set PRIVY_APP_ID, PRIVY_APP_SECRET, and PRIVY_SIGNING_KEY');
  }

  return { appId, appSecret, signingKey };
}

/**
 * JSON canonicalization using RFC 8785 standard for consistent string representation
 */
function canonicalizeJSON(obj: any): string {
  const result = canonicalize(obj);
  if (!result) {
    throw new Error('Failed to canonicalize JSON payload');
  }
  return result;
}

/**
 * Generates Privy authorization signature for API requests using ECDSA P-256
 */
function generatePrivySignature(url: string, body: any, signingKey: string, appId: string): string {
  try {
    // Create the payload according to Privy specifications
    const payload = {
      version: 1,
      method: 'POST',
      url,
      body,
      headers: {
        'privy-app-id': appId
      }
    };

    console.log('Generating Privy authorization signature', {
      url,
      appId,
      payloadKeys: Object.keys(payload)
    });

    // Canonicalize the payload using RFC 8785 standard
    const canonicalizedPayload = canonicalizeJSON(payload);
    console.log('Payload canonicalized', {
      length: canonicalizedPayload.length,
      preview: canonicalizedPayload.substring(0, 100) + '...'
    });

    // Format the signing key as PEM for ECDSA P-256
    const pemKey = `-----BEGIN PRIVATE KEY-----\n${signingKey}\n-----END PRIVATE KEY-----`;

    // Create private key object for ECDSA P-256
    const privateKey = crypto.createPrivateKey({
      key: pemKey,
      format: 'pem'
    });

    // Sign the canonicalized payload using ECDSA with SHA-256
    const signatureBuffer = crypto.sign('sha256', Buffer.from(canonicalizedPayload), privateKey);
    const signature = signatureBuffer.toString('base64');

    console.log('Privy signature generated successfully', {
      signatureLength: signature.length,
      signaturePreview: signature.substring(0, 20) + '...'
    });

    return signature;
  } catch (error) {
    console.error('Failed to generate Privy signature:', error);
    throw new Error(`Failed to generate Privy signature: ${(error as Error).message}`);
  }
}

/**
 * Updates transaction with fresh blockhash if connection is provided
 */
async function updateTransactionBlockhash(
  transaction: Transaction | VersionedTransaction,
  connection: Connection
): Promise<Transaction | VersionedTransaction> {
  try {
    // Get a fresh blockhash with finalized commitment
    const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash({
      commitment: 'finalized'
    });

    // Verify the blockhash is valid
    const isValid = await connection.isBlockhashValid(blockhash, { commitment: 'finalized' });
    console.log('Using blockhash', { blockhash, isValid, lastValidBlockHeight });

    if (transaction instanceof Transaction) {
      // Legacy transaction
      transaction.recentBlockhash = blockhash;
    } else {
      // VersionedTransaction - need to reconstruct with new blockhash
      // This is more complex for versioned transactions
      console.warn('Blockhash update for VersionedTransaction not implemented - using original');
    }

    return transaction;
  } catch (error) {
    console.warn('Error updating transaction blockhash', { error: (error as Error).message });
    return transaction; // Return original transaction if update fails
  }
}

/**
 * Signs and sends a transaction using Privy session signer
 */
export async function signAndSendTransactionWithPrivy(
  request: PrivySignTransactionRequest
): Promise<string> {
  try {
    const { walletId, transaction, connection } = request;

    if (!walletId) {
      throw new Error('Wallet ID is required for Privy session signing');
    }

    console.log('Signing transaction with Privy session signer', { walletId });

    // Get Privy credentials
    const { appId, appSecret, signingKey } = getPrivyCredentials();

    // Update transaction with fresh blockhash if connection provided
    let updatedTransaction = transaction;
    if (connection) {
      updatedTransaction = await updateTransactionBlockhash(transaction, connection);
    }

    // Serialize transaction to base64
    let serializedTransaction: string;
    if (updatedTransaction instanceof Transaction) {
      serializedTransaction = updatedTransaction.serialize({ requireAllSignatures: false }).toString('base64');
    } else {
      // VersionedTransaction
      serializedTransaction = Buffer.from(updatedTransaction.serialize()).toString('base64');
    }

    console.log('Transaction serialized for Privy', { length: serializedTransaction.length });

    // Prepare API request
    const baseUrl = 'https://api.privy.io';
    const apiEndpoint = `${baseUrl}/v1/wallets/${walletId}/rpc`;
    const basicAuth = Buffer.from(`${appId}:${appSecret}`).toString('base64');

    // Request body according to Privy API documentation
    const requestBody = {
      method: 'signAndSendTransaction',
      caip2: 'solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp', // Solana mainnet chain ID
      params: {
        transaction: serializedTransaction,
        encoding: 'base64'
      }
    };

    // Generate authorization signature
    const authSignature = generatePrivySignature(apiEndpoint, requestBody, signingKey, appId);

    console.log('Making request to Privy API', {
      endpoint: apiEndpoint,
      requestBodyKeys: Object.keys(requestBody),
      hasAuthSignature: !!authSignature,
      authSignatureLength: authSignature.length
    });

    // Make API request
    const response = await axios.post(
      apiEndpoint,
      requestBody,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${basicAuth}`,
          'privy-app-id': appId,
          'privy-authorization-signature': authSignature
        },
        timeout: 30000 // 30 second timeout
      }
    );

    console.log('Privy API response received', { status: response.status });

    // Extract transaction signature from response
    if (response.data && response.data.data && response.data.data.hash) {
      const signature = response.data.data.hash;
      console.log('Transaction signed and sent successfully via Privy', { signature });
      return signature;
    } else if (response.data && response.data.result) {
      const signature = response.data.result;
      console.log('Transaction signed and sent successfully via Privy', { signature });
      return signature;
    } else {
      console.error('Unexpected response format from Privy API', { response: response.data });
      throw new Error('Unexpected response format from Privy API');
    }

  } catch (error: any) {
    console.error('Privy sign transaction error:', error);

    // Handle specific Privy API errors
    if (error.response) {
      const status = error.response.status;
      const errorData = error.response.data;

      console.error('Privy API error response', {
        status,
        statusText: error.response.statusText,
        data: errorData,
        headers: error.response.headers
      });

      if (status === 404) {
        throw new Error(`Wallet ID not found in Privy: ${request.walletId}`);
      } else if (status === 401) {
        throw new Error('Privy API authentication failed: Check your app credentials and authorization signature');
      } else if (status === 400) {
        const errorMessage = errorData?.error?.message || errorData?.message || 'Invalid request format';
        throw new Error(`Privy API error: ${errorMessage}`);
      } else if (status === 403) {
        throw new Error('Privy API access forbidden: Check wallet permissions and authorization');
      } else {
        throw new Error(`Privy API error (${status}): ${errorData?.message || error.response.statusText}`);
      }
    }

    // Handle network or other errors
    if (error.code === 'ECONNREFUSED') {
      throw new Error('Failed to connect to Privy API: Connection refused');
    } else if (error.code === 'ETIMEDOUT') {
      throw new Error('Privy API request timed out');
    }

    throw new Error(`Failed to sign transaction with Privy: ${error.message}`);
  }
}

/**
 * Validates if Privy credentials are properly configured
 */
export function validatePrivyCredentials(): boolean {
  try {
    getPrivyCredentials();
    return true;
  } catch {
    return false;
  }
}
