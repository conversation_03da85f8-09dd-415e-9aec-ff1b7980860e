import Decimal from 'decimal.js';

/**
 * Utility functions for handling token decimals and conversions
 * Migrated from radium-sdk/src/utils/decimals.ts
 */

export interface TokenInfo {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  logoURI?: string;
}

/**
 * Format a decimal percentage as a user-friendly percentage string
 * @param decimalPercent - Decimal percentage (e.g., 0.041905986586425796)
 * @param maxDecimals - Maximum decimal places to show (default: 2)
 * @returns Formatted percentage string (e.g., "4.19%")
 */
export function formatPercentage(decimalPercent: number, maxDecimals: number = 2): string {
  const percentage = decimalPercent * 100;

  // Handle very small percentages
  if (percentage < 0.01 && percentage > 0) {
    return '<0.01%';
  }

  // Handle very large percentages
  if (percentage > 999.99) {
    return '>999%';
  }

  // Format with appropriate decimal places
  const formatted = percentage.toFixed(maxDecimals);

  // Remove trailing zeros
  const cleaned = parseFloat(formatted).toString();

  return `${cleaned}%`;
}

/**
 * Format basis points as a user-friendly percentage string
 * @param basisPoints - Basis points (e.g., 100 = 1%)
 * @param maxDecimals - Maximum decimal places to show (default: 2)
 * @returns Formatted percentage string (e.g., "1%")
 */
export function formatBasisPointsAsPercentage(basisPoints: number, maxDecimals: number = 2): string {
  const decimalPercent = basisPoints / 10000;
  return formatPercentage(decimalPercent, maxDecimals);
}

/**
 * Convert human-readable amount to base units (smallest denomination)
 * @param humanAmount - Human readable amount (e.g., "3.5")
 * @param decimals - Number of decimal places for the token
 * @returns Base units as string
 */
export function toBaseUnits(humanAmount: string | number, decimals: number): string {
  const amount = new Decimal(humanAmount);
  const multiplier = new Decimal(10).pow(decimals);
  return amount.mul(multiplier).toFixed(0);
}

/**
 * Convert base units to human-readable amount
 * @param baseUnits - Amount in base units (e.g., "30000")
 * @param decimals - Number of decimal places for the token
 * @returns Human readable amount as string
 */
export function toHumanAmount(baseUnits: string | number, decimals: number): string {
  const amount = new Decimal(baseUnits);
  const divisor = new Decimal(10).pow(decimals);
  return amount.div(divisor).toFixed();
}

/**
 * Format human amount with appropriate decimal places for display
 * @param humanAmount - Human readable amount
 * @param decimals - Number of decimal places for the token
 * @param maxDisplayDecimals - Maximum decimal places to show (default: 6)
 * @returns Formatted amount string
 */
export function formatHumanAmount(
  humanAmount: string | number,
  decimals: number,
  maxDisplayDecimals: number = 6
): string {
  const amount = new Decimal(humanAmount);
  const displayDecimals = Math.min(decimals, maxDisplayDecimals);

  // Remove trailing zeros and unnecessary decimal points
  return amount.toFixed(displayDecimals).replace(/\.?0+$/, '');
}

/**
 * Validate that an amount string represents a valid positive number
 * @param amount - Amount string to validate
 * @returns True if valid, false otherwise
 */
export function isValidAmount(amount: string): boolean {
  try {
    const decimal = new Decimal(amount);
    return decimal.isPositive() && decimal.isFinite();
  } catch {
    return false;
  }
}

/**
 * Calculate the minimum output amount considering slippage
 * @param outputAmount - Expected output amount in base units
 * @param slippageBps - Slippage in basis points (100 = 1%)
 * @returns Minimum output amount in base units
 */
export function calculateMinOutputAmount(outputAmount: string, slippageBps: number): string {
  const amount = new Decimal(outputAmount);
  const slippageMultiplier = new Decimal(1).minus(new Decimal(slippageBps).div(10000));
  return amount.mul(slippageMultiplier).toFixed(0);
}

/**
 * Calculate platform fee amount
 * @param inputAmount - Input amount in base units
 * @param platformFeeBps - Platform fee in basis points
 * @returns Platform fee amount in base units
 */
export function calculatePlatformFee(inputAmount: string, platformFeeBps: number): string {
  const amount = new Decimal(inputAmount);
  const feeMultiplier = new Decimal(platformFeeBps).div(10000);
  return amount.mul(feeMultiplier).toFixed(0);
}

/**
 * Parse user input amount and convert to base units
 * This function handles various input formats and validates the amount
 * @param userInput - User input amount (could be "3", "3.5", etc.)
 * @param tokenInfo - Token information including decimals
 * @returns Object with parsed amounts and validation status
 */
export function parseUserAmount(userInput: string, tokenInfo: TokenInfo): {
  isValid: boolean;
  humanAmount: string;
  baseUnits: string;
  error?: string;
} {
  try {
    // Clean and validate input
    const cleanInput = userInput.trim();
    if (!cleanInput || cleanInput === '0') {
      return {
        isValid: false,
        humanAmount: '0',
        baseUnits: '0',
        error: 'Amount must be greater than 0'
      };
    }

    // Validate decimal format
    if (!isValidAmount(cleanInput)) {
      return {
        isValid: false,
        humanAmount: '0',
        baseUnits: '0',
        error: 'Invalid amount format'
      };
    }

    const humanAmount = new Decimal(cleanInput).toFixed();
    const baseUnits = toBaseUnits(humanAmount, tokenInfo.decimals);

    // Validate that base units is a positive integer
    const baseUnitsDecimal = new Decimal(baseUnits);
    if (!baseUnitsDecimal.isPositive() || !baseUnitsDecimal.isInteger()) {
      return {
        isValid: false,
        humanAmount: '0',
        baseUnits: '0',
        error: 'Amount too small for token precision'
      };
    }

    return {
      isValid: true,
      humanAmount,
      baseUnits
    };
  } catch (error) {
    return {
      isValid: false,
      humanAmount: '0',
      baseUnits: '0',
      error: `Parse error: ${(error as Error).message}`
    };
  }
}

/**
 * Format token amount for display with symbol
 * @param amount - Amount in human-readable format
 * @param symbol - Token symbol
 * @param decimals - Token decimals for formatting
 * @returns Formatted string like "3.5 USDC"
 */
export function formatTokenAmount(amount: string, symbol: string, decimals: number): string {
  const formattedAmount = formatHumanAmount(amount, decimals);
  return `${formattedAmount} ${symbol}`;
}

/**
 * Calculate price impact percentage
 * @param inputAmount - Input amount in base units
 * @param outputAmount - Output amount in base units
 * @param inputDecimals - Input token decimals
 * @param outputDecimals - Output token decimals
 * @param marketPrice - Optional market price for comparison
 * @returns Price impact as percentage
 */
export function calculatePriceImpact(
  inputAmount: string,
  outputAmount: string,
  inputDecimals: number,
  outputDecimals: number,
  marketPrice?: number
): number {
  // This is a simplified calculation
  // In a real implementation, you would compare against market price
  const inputHuman = new Decimal(toHumanAmount(inputAmount, inputDecimals));
  const outputHuman = new Decimal(toHumanAmount(outputAmount, outputDecimals));

  // Mock calculation: assume 1% price impact for demonstration
  // Real implementation would fetch market data
  return 1.0;
}

/**
 * Validate token pair compatibility
 * @param inputToken - Input token info
 * @param outputToken - Output token info
 * @returns Validation result
 */
export function validateTokenPair(inputToken: TokenInfo, outputToken: TokenInfo): {
  isValid: boolean;
  error?: string;
} {
  if (inputToken.address === outputToken.address) {
    return {
      isValid: false,
      error: 'Input and output tokens cannot be the same'
    };
  }

  return { isValid: true };
}
