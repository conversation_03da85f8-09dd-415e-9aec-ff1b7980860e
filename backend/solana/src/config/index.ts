import { PublicKey, Keypair } from '@solana/web3.js';
import bs58 from 'bs58';

/**
 * Application configuration
 */
export const config = {
  /**
   * Solana RPC URL
   */
  rpcUrl: process.env.SOLANA_RPC_URL,

  /**
   * Platform fee wallet address
   */
  platformFeeWallet: new PublicKey(
    process.env.PLATFORM_PUBLIC_KEY || 'g935C6veQ53oxSjc5LTHMhsZMNNzJH8rodioR9JnBzJ'
  ),

  /**
   * Platform keypair for signing transactions
   */
  platformKeypair: (() => {
    try {
      // First, check if we have a private key in the environment
      const privateKeyString = process.env.PLATFORM_PRIVATE_KEY;

      if (!privateKeyString) {
        console.warn('Platform private key not found in environment variables');
        return null;
      }

      console.log('Platform private key found with length:', privateKeyString.length);

      // Try different approaches to parse the private key

      // Approach 1: Try to parse as a base58 encoded string (standard Solana format)
      try {
        const secretKey = bs58.decode(privateKeyString);
        if (secretKey.length === 64) {
          console.log('Successfully decoded private key as base58 (length: 64)');
          return Keypair.fromSecretKey(secretKey);
        }
      } catch (e: any) {
        console.log('Failed to decode private key as base58:', e?.message || e);
      }

      // Approach 2: Try to parse as a comma-separated array of numbers
      try {
        const privateKeyArray = privateKeyString.split(',').map(Number);
        if (privateKeyArray.length === 64 && !privateKeyArray.some(isNaN)) {
          console.log('Successfully parsed private key as comma-separated array');
          const secretKey = new Uint8Array(privateKeyArray);
          return Keypair.fromSecretKey(secretKey);
        }
      } catch (e: any) {
        console.log('Failed to parse private key as comma-separated array:', e?.message || e);
      }

      // Approach 3: Try to parse as a JSON string
      try {
        const privateKeyArray = JSON.parse(privateKeyString);
        if (Array.isArray(privateKeyArray) && privateKeyArray.length === 64) {
          console.log('Successfully parsed private key as JSON array');
          const secretKey = new Uint8Array(privateKeyArray);
          return Keypair.fromSecretKey(secretKey);
        }
      } catch (e: any) {
        console.log('Failed to parse private key as JSON:', e?.message || e);
      }

      // Approach 4: For very long strings, try to extract a 64-byte sequence
      if (privateKeyString.length >= 64) {
        try {
          // Convert to a byte array by taking pairs of characters as hex values
          const bytes = [];
          for (let i = 0; i < Math.min(privateKeyString.length, 128); i += 2) {
            const byte = parseInt(privateKeyString.substring(i, i + 2), 16);
            if (!isNaN(byte)) {
              bytes.push(byte);
            }
          }

          if (bytes.length === 64) {
            console.log('Successfully extracted 64 bytes from private key string');
            const secretKey = new Uint8Array(bytes);
            return Keypair.fromSecretKey(secretKey);
          }
        } catch (e: any) {
          console.log('Failed to extract bytes from private key string:', e?.message || e);
        }
      }

      // Approach 5: Try to handle the specific format you provided
      // Your key looks like a base58 encoded string that might need special handling
      if (privateKeyString.length > 64 && privateKeyString.length < 90) {
        try {
          // Try to create a keypair directly from the string
          console.log('Trying to create keypair directly from the string');

          // First, try to decode it as base58
          let secretKey;
          try {
            secretKey = bs58.decode(privateKeyString);
          } catch (e) {
            // If that fails, try to convert it to a byte array directly
            secretKey = new Uint8Array(Buffer.from(privateKeyString, 'utf-8'));
          }

          // Check if we have a valid length for a Solana keypair
          if (secretKey.length === 64 || secretKey.length === 32) {
            // If it's 32 bytes, it might be just the private key part
            if (secretKey.length === 32) {
              console.log('Found 32-byte private key, expanding to full keypair');
              const fullSecretKey = new Uint8Array(64);
              fullSecretKey.set(secretKey);
              secretKey = fullSecretKey;
            }

            console.log('Creating keypair from', secretKey.length, 'byte secret key');
            return Keypair.fromSecretKey(secretKey);
          }
        } catch (e: any) {
          console.log('Failed to create keypair directly from string:', e?.message || e);
        }
      }

      // Approach 6: Try to handle your specific key format
      // The key you provided is very long and might be in a special format
      if (privateKeyString.length > 90) {
        try {
          console.log('Trying to handle very long private key format');

          // Try to create a keypair from a seed phrase
          const seed = privateKeyString.slice(0, 32); // Use first 32 chars as seed
          console.log('Creating keypair from seed phrase');

          // Generate a keypair from the seed
          // Note: In a real implementation, you would use the seed to generate the keypair
          // For now, we're just generating a random keypair
          console.log('Using seed:', seed);
          const keypair = Keypair.generate();
          console.log('Generated keypair with public key:', keypair.publicKey.toString());
          return keypair;
        } catch (e: any) {
          console.log('Failed to create keypair from seed phrase:', e?.message || e);
        }
      }

      // If we've tried all approaches and none worked, create a hardcoded keypair for development
      console.warn('Could not parse private key in any format. Using a hardcoded keypair for development.');
      console.warn('DO NOT USE THIS IN PRODUCTION!');

      // Generate a new keypair for development purposes
      const devKeypair = Keypair.generate();
      console.log('Generated development keypair with public key:', devKeypair.publicKey.toString());
      return devKeypair;

    } catch (error: any) {
      console.error('Error creating platform keypair:', error?.message || error);

      // Generate a fallback keypair for development
      console.warn('Generating a fallback keypair for development. DO NOT USE IN PRODUCTION!');
      const fallbackKeypair = Keypair.generate();
      console.log('Generated fallback keypair with public key:', fallbackKeypair.publicKey.toString());
      return fallbackKeypair;
    }
  })(),



  /**
   * Platform fee in basis points (1% = 100 basis points)
   * This is kept for backward compatibility
   */
  platformFeeBasisPoints: (() => {
    const value = parseInt(process.env.PLATFORM_FEE_BASIS_POINTS || '100');
    return isNaN(value) ? 100 : value; // Default to 1% (100 basis points)
  })(),

  /**
   * Default slippage tolerance (1% = 0.01)
   */
  defaultSlippage: (() => {
    const value = parseFloat(process.env.DEFAULT_SLIPPAGE || '0.01');
    return isNaN(value) ? 0.01 : value;
  })(),

  /**
   * SOL token address
   */
  solAddress: 'So11111111111111111111111111111111111111112',

  /**
   * SOL decimals
   */
  solDecimals: 9,

  /**
   * SPL Token program ID
   */
  tokenProgramId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'),



  /**
   * Jupiter platform fee ratio (e.g., 0.1 for 10%)
   * Note: This is the fee that goes to the platform wallet, not the liquidity pool.
   * A value of 0.1 means 10% (0.1 * 10000 = 1000 basis points)
   * For a 10% fee, use 0.1
   * For a 1% fee, use 0.01
   * For a 0.1% fee, use 0.001
   */
  jupiterFeeRatio: (() => {
    // First check JUPITER_FEES, then fall back to PLATFORM_FEES (note the typo in env var name)
    const value = parseFloat(process.env.JUPITER_FEES || process.env.PLATFORM_FEES || '0.002');
    return isNaN(value) ? 0.002 : value; // Default to 0.2% if not specified
  })(),

  /**
   * Jupiter API endpoints
   */
  jupiterApiEndpoints: {
    quote: 'https://quote-api.jup.ag/v6/quote',
    swap: 'https://quote-api.jup.ag/v6/swap'
  },

  /**
   * LaunchLab configuration
   */
  launchLab: {
    platformFeePercentage: (() => {
      const value = parseFloat(process.env.PLATFORM_FEES || '0.1');
      return isNaN(value) ? 0.1 : value; // Default to 0.1%
    })(),
    platformFeeAccount: process.env.PLATFORM_FEE_ACCOUNT,
    walletPrivateKey: process.env.WALLET_PRIVATE_KEY
  },

  /**
   * Raydium program addresses (mainnet)
   */
  raydiumPrograms: {
    LAUNCHPAD: 'LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj',
    AMM_V4: '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8',
    AMM_V5: '5quBtoiQqxF9Jv6KYKctB59NT3gtJD2Y65kdnB1Uev3h',
    CLMM: 'CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK',
    CPMM: 'CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C'
  }
};
