import { Router } from 'express';
import jupiterRoutes from './jupiter/jupiter.route';
import pumpRoutes from './pumpFun/pump.route';
import launchLabRoutes from './launchlab/launchlab.route';
import privyTestRoutes from './test/privy-test.routes';
import tokenRoutes from './token.routes';

const router = Router();

// API Routes - Mount with explicit path
router.use('/api/jupiter', jupiterRoutes);
router.use('/api/pump', pumpRoutes);
router.use('/api/launchlab', launchLabRoutes);
router.use('/api/test/privy', privyTestRoutes);
router.use('/api/token', tokenRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
    console.log('Health check endpoint hit');
    res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Debug route
router.get('/debug', (req, res) => {
    console.log('Debug endpoint hit');
    res.status(200).json({ message: 'Debug endpoint working' });
});

export default router;
