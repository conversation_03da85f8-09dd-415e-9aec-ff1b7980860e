import { Router } from 'express';
import { getQuote, executeSwap } from '../../controllers/pumpFun/pump.controller';

const router = Router();

/**
 * @route   POST /api/pump/quote
 * @desc    Get a quote for a potential swap
 * @access  Public
 */
router.post('/quote', getQuote);

/**
 * @route   POST /api/pump/swap
 * @desc    Execute a swap
 * @access  Public
 */
router.post('/swap', executeSwap);

export default router;
