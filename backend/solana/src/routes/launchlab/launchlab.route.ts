import { Router } from 'express';
import { getQuote, executeSwap, getPoolInfo } from '../../controllers/launchlab/launchlab.controller';

const router = Router();

/**
 * @route   POST /api/launchlab/quote
 * @desc    Get a quote for LaunchLab trading
 * @access  Public
 * @body    {
 *   poolId: string,
 *   amount: string,
 *   direction: 'buy' | 'sell'
 * }
 */
router.post('/quote', getQuote);

/**
 * @route   POST /api/launchlab/swap
 * @desc    Execute a LaunchLab swap
 * @access  Public
 * @body    {
 *   poolId: string,
 *   amount: string,
 *   walletAddress: string,
 *   walletId: string,
 *   direction: 'buy' | 'sell',
 *   slippage: number,
 *   priorityFee?: number,
 *   computeUnitLimit?: number
 * }
 */
router.post('/swap', executeSwap);

/**
 * @route   GET /api/launchlab/pool/:poolId
 * @desc    Get LaunchLab pool information
 * @access  Public
 * @param   poolId - The LaunchLab pool ID
 */
router.get('/pool/:poolId', getPoolInfo);

export default router;
