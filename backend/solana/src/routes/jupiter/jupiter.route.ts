import { Router } from 'express';
import { getJupiterQuote, executeJupiterSwap } from '../../controllers/jupiter/jupiter.controller';

const router = Router();

/**
 * @route   POST /api/jupiter/quote
 * @desc    Get a quote for a potential Jupiter swap
 * @access  Public
 */
router.post('/quote', getJupiterQuote);

/**
 * @route   POST /api/jupiter/swap
 * @desc    Execute a Jupiter swap
 * @access  Public
 */
router.post('/swap', executeJupiterSwap);

export default router;
