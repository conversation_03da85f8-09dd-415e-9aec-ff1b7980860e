import { Router } from 'express';
import { 
  getTokenBalanceEndpoint, 
  tokenBalanceHealthCheck 
} from '../controllers/token/balance.controller';

/**
 * Token-related routes
 */
const router = Router();

/**
 * POST /api/token/balance
 * Get token balance for a wallet
 * 
 * Request body:
 * {
 *   "tokenAddress": "4dt9PSiWNY39LhJrpEa83Qz4hBSTK5wXwFEtqhxxpump",
 *   "walletAddress": "********************************************"
 * }
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "tokenBalance": "364034.253360",
 *     "rawBalance": "************",
 *     "decimals": 6,
 *     "solBalance": "0.006404",
 *     "tokenAccountAddress": "JB6ixpFuL8CCLvQi7xJCQCBVV7ixbkaj9DsSFprYkTn1",
 *     "hasTokenAccount": true
 *   }
 * }
 */
router.post('/balance', getTokenBalanceEndpoint);

/**
 * GET /api/token/health
 * Health check for token balance service
 */
router.get('/health', tokenBalanceHealthCheck);

export default router;
