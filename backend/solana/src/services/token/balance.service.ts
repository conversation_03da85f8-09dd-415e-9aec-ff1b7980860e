import { Connection, PublicKey } from '@solana/web3.js';
import { getAssociatedTokenAddress } from '@solana/spl-token';
import { TokenBalanceRequest } from '../../types/pump.types';

/**
 * Token balance service for checking SPL token balances
 */

/**
 * Check token balance for a given wallet and token
 * @param connection Solana connection
 * @param request Token balance request
 * @returns Token balance information
 */
export async function getTokenBalance(
  connection: Connection,
  request: TokenBalanceRequest
): Promise<{
  tokenBalance: string;
  rawBalance: string;
  decimals: number;
  solBalance: string;
  tokenAccountAddress: string;
  hasTokenAccount: boolean;
}> {
  console.log('Getting token balance for:', request);

  // Validate and create PublicKey objects
  let walletPublicKey: PublicKey;
  let tokenMintPublicKey: PublicKey | null = null;
  let isSolBalanceOnly = false;

  try {
    walletPublicKey = new PublicKey(request.walletAddress);
  } catch (error) {
    throw new Error(`Invalid wallet address: ${request.walletAddress}`);
  }
  
  // Check if we're only getting SOL balance
  if (!request.tokenAddress) {
    isSolBalanceOnly = true;
    console.log('Token address not provided. Will return only SOL balance.');
  } else {
    try {
      tokenMintPublicKey = new PublicKey(request.tokenAddress);
    } catch (error) {
      throw new Error(`Invalid token address: ${request.tokenAddress}`);
    }
  }

  // Default values for token-related fields
  let tokenBalance = '0';
  let rawBalance = '0';
  let decimals = 0;
  let tokenAccountAddress = '';
  let hasTokenAccount = false;
  
  // Only process token-specific logic if not just SOL balance request
  if (!isSolBalanceOnly && tokenMintPublicKey) {
    // Get associated token account address
    const tokenAcctAddress = await getAssociatedTokenAddress(
      tokenMintPublicKey,
      walletPublicKey
    );
    
    tokenAccountAddress = tokenAcctAddress.toString();
    console.log(`Token account address: ${tokenAccountAddress}`);

    // Check if token account exists
    const accountInfo = await connection.getAccountInfo(tokenAcctAddress);
    hasTokenAccount = accountInfo !== null;

    console.log(`Token account exists: ${hasTokenAccount}`);

    if (hasTokenAccount) {
    try {
      // Get token balance
      const balance = await connection.getTokenAccountBalance(tokenAcctAddress);
      
      tokenBalance = balance.value.uiAmountString || '0';
      rawBalance = balance.value.amount;
      decimals = balance.value.decimals;

      console.log(`Token balance: ${tokenBalance} (${rawBalance} raw, ${decimals} decimals)`);
    } catch (error: any) {
      console.error('Error getting token balance:', error);
      throw new Error(`Failed to get token balance: ${error.message}`);
    }
    } else {
      console.log('Token account does not exist, balance is 0');
    }
  } else {
    console.log('Skipping token balance check, only getting SOL balance');
    tokenAccountAddress = 'not_applicable';
  }

  // Get SOL balance
  let solBalance = '0';
  try {
    const solBalanceLamports = await connection.getBalance(walletPublicKey);
    solBalance = (solBalanceLamports / 1_000_000_000).toFixed(6);
    console.log(`SOL balance: ${solBalance} SOL`);
  } catch (error: any) {
    console.error('Error getting SOL balance:', error);
    // Don't throw error for SOL balance failure, just log it
    solBalance = '0';
  }

  return {
    tokenBalance,
    rawBalance,
    decimals,
    solBalance,
    tokenAccountAddress: tokenAccountAddress.toString(),
    hasTokenAccount
  };
}

/**
 * Validate token balance request parameters
 * @param request Token balance request
 * @returns Validation result
 */
export function validateTokenBalanceRequest(request: any): {
  isValid: boolean;
  error?: string;
} {
  if (!request) {
    return { isValid: false, error: 'Request body is required' };
  }

  if (!request.walletAddress) {
    return { isValid: false, error: 'walletAddress is required' };
  }

  if (typeof request.walletAddress !== 'string') {
    return { isValid: false, error: 'walletAddress must be a string' };
  }

  // Basic format validation for Solana addresses (should be base58 and ~44 characters)
  const addressRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;

  if (!addressRegex.test(request.walletAddress)) {
    return { isValid: false, error: 'walletAddress is not a valid Solana address format' };
  }
  
  // Only validate tokenAddress if it's provided
  if (request.tokenAddress) {
    if (typeof request.tokenAddress !== 'string') {
      return { isValid: false, error: 'tokenAddress must be a string' };
    }
    
    if (!addressRegex.test(request.tokenAddress)) {
      return { isValid: false, error: 'tokenAddress is not a valid Solana address format' };
    }
  }

  return { isValid: true };
}

/**
 * Format token balance for display
 * @param balance Token balance as string
 * @param decimals Number of decimals
 * @returns Formatted balance
 */
export function formatTokenBalance(balance: string, decimals: number): string {
  const numBalance = parseFloat(balance);
  
  if (numBalance === 0) {
    return '0';
  }

  // For very small amounts, show more decimal places
  if (numBalance < 0.001) {
    return numBalance.toFixed(Math.min(decimals, 9));
  }

  // For normal amounts, show up to 6 decimal places
  return numBalance.toFixed(6);
}

/**
 * Check if an address is a valid Solana public key
 * @param address Address to validate
 * @returns True if valid
 */
export function isValidSolanaAddress(address: string): boolean {
  try {
    new PublicKey(address);
    return true;
  } catch {
    return false;
  }
}
