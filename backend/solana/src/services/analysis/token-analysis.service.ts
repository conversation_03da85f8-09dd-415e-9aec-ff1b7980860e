/**
 * Token Analysis Service
 * Analyzes token characteristics to provide intelligent trading parameter recommendations
 */

import { Connection, PublicKey } from '@solana/web3.js';
import { PumpFunPoolData } from '../../types/pump.types';

/**
 * Token characteristics interface
 */
export interface TokenCharacteristics {
  age: 'new' | 'established' | 'mature'; // < 7 days, 7-30 days, > 30 days
  liquidity: 'low' | 'medium' | 'high'; // Based on pool size
  volatility: 'low' | 'medium' | 'high'; // Based on price movements
  popularity: 'low' | 'medium' | 'high'; // Based on trading activity
  riskLevel: 'low' | 'medium' | 'high'; // Overall risk assessment
}

/**
 * Network conditions interface
 */
export interface NetworkConditions {
  congestion: 'low' | 'medium' | 'high';
  averagePriorityFee: number; // in microLamports
  recommendedPriorityFee: number; // in microLamports
  mevActivity: 'low' | 'medium' | 'high';
}

/**
 * Parameter recommendations interface
 */
export interface ParameterRecommendations {
  slippage: number; // percentage (e.g., 8 for 8%)
  bribeAmount: number; // percentage of trade amount (e.g., 1.2 for 1.2%)
  priorityFee: number; // percentage of trade amount (e.g., 0.05 for 0.05%)
  reasoning: {
    slippage: string;
    bribeAmount: string;
    priorityFee: string;
  };
}

/**
 * Analyze token characteristics based on mint address and pool data
 */
export async function analyzeTokenCharacteristics(
  connection: Connection,
  tokenMint: PublicKey,
  poolData: PumpFunPoolData
): Promise<TokenCharacteristics> {
  try {
    console.log('Analyzing token characteristics for:', tokenMint.toString());

    // Analyze token age
    const age = await analyzeTokenAge(connection, tokenMint);

    // Analyze liquidity based on pool data
    const liquidity = analyzeLiquidity(poolData);

    // Analyze volatility (simplified - in production, would use historical data)
    const volatility = analyzeVolatility(poolData);

    // Analyze popularity (simplified - in production, would use trading volume data)
    const popularity = analyzePopularity(poolData);

    // Calculate overall risk level
    const riskLevel = calculateRiskLevel(age, liquidity, volatility, popularity);

    const characteristics: TokenCharacteristics = {
      age,
      liquidity,
      volatility,
      popularity,
      riskLevel
    };

    console.log('Token characteristics:', characteristics);
    return characteristics;

  } catch (error: any) {
    console.error('Error analyzing token characteristics:', error);

    // Return conservative defaults on error
    return {
      age: 'new',
      liquidity: 'low',
      volatility: 'high',
      popularity: 'low',
      riskLevel: 'high'
    };
  }
}

/**
 * Analyze token age based on mint creation time
 */
async function analyzeTokenAge(
  connection: Connection,
  tokenMint: PublicKey
): Promise<'new' | 'established' | 'mature'> {
  try {
    // Get token mint account info
    const accountInfo = await connection.getAccountInfo(tokenMint);

    if (!accountInfo) {
      return 'new'; // Default for unknown tokens
    }

    // In a production system, you would:
    // 1. Parse the mint account data to get creation timestamp
    // 2. Compare with current time to determine age
    // 3. Use transaction history to get more accurate creation time

    // For now, use a simplified heuristic based on account data
    const dataSize = accountInfo.data.length;
    const lamports = accountInfo.lamports;

    // Heuristic: Older tokens tend to have more complex data structures
    // and higher rent-exempt balances
    if (lamports > 2_000_000 && dataSize > 100) {
      return 'mature'; // > 30 days
    } else if (lamports > 1_500_000) {
      return 'established'; // 7-30 days
    } else {
      return 'new'; // < 7 days
    }

  } catch (error) {
    console.error('Error analyzing token age:', error);
    return 'new'; // Conservative default
  }
}

/**
 * Analyze liquidity based on pool reserves
 */
function analyzeLiquidity(poolData: PumpFunPoolData): 'low' | 'medium' | 'high' {
  if (!poolData.exists || !poolData.virtualSolReserves) {
    return 'low';
  }

  const solReserves = Number(poolData.virtualSolReserves) / 1_000_000_000; // Convert to SOL

  if (solReserves >= 100) {
    return 'high'; // >= 100 SOL
  } else if (solReserves >= 10) {
    return 'medium'; // 10-100 SOL
  } else {
    return 'low'; // < 10 SOL
  }
}

/**
 * Analyze volatility based on pool characteristics
 */
function analyzeVolatility(poolData: PumpFunPoolData): 'low' | 'medium' | 'high' {
  if (!poolData.exists || !poolData.virtualSolReserves || !poolData.virtualTokenReserves) {
    return 'high'; // Conservative default
  }

  // Calculate price impact for a 1 SOL trade
  const solReserves = Number(poolData.virtualSolReserves);
  const tokenReserves = Number(poolData.virtualTokenReserves);

  if (solReserves === 0 || tokenReserves === 0) {
    return 'high';
  }

  // Simple price impact calculation
  const tradeSize = 1_000_000_000; // 1 SOL in lamports
  const priceImpact = tradeSize / solReserves;

  if (priceImpact < 0.01) { // < 1% impact for 1 SOL
    return 'low';
  } else if (priceImpact < 0.05) { // 1-5% impact
    return 'medium';
  } else {
    return 'high'; // > 5% impact
  }
}

/**
 * Analyze popularity based on pool size and activity indicators
 */
function analyzePopularity(poolData: PumpFunPoolData): 'low' | 'medium' | 'high' {
  if (!poolData.exists || !poolData.realSolReserves) {
    return 'low';
  }

  const realSolReserves = Number(poolData.realSolReserves) / 1_000_000_000;

  // Heuristic: Popular tokens tend to have more real reserves
  if (realSolReserves >= 50) {
    return 'high';
  } else if (realSolReserves >= 5) {
    return 'medium';
  } else {
    return 'low';
  }
}

/**
 * Calculate overall risk level
 */
function calculateRiskLevel(
  age: string,
  liquidity: string,
  volatility: string,
  popularity: string
): 'low' | 'medium' | 'high' {
  let riskScore = 0;

  // Age factor (newer = riskier)
  if (age === 'new') riskScore += 3;
  else if (age === 'established') riskScore += 2;
  else riskScore += 1;

  // Liquidity factor (lower = riskier)
  if (liquidity === 'low') riskScore += 3;
  else if (liquidity === 'medium') riskScore += 2;
  else riskScore += 1;

  // Volatility factor
  if (volatility === 'high') riskScore += 3;
  else if (volatility === 'medium') riskScore += 2;
  else riskScore += 1;

  // Popularity factor (lower = riskier)
  if (popularity === 'low') riskScore += 3;
  else if (popularity === 'medium') riskScore += 2;
  else riskScore += 1;

  // Calculate risk level based on total score
  if (riskScore >= 10) {
    return 'high';
  } else if (riskScore >= 7) {
    return 'medium';
  } else {
    return 'low';
  }
}

/**
 * Get current network conditions
 */
export async function getNetworkConditions(connection: Connection): Promise<NetworkConditions> {
  try {
    console.log('Analyzing network conditions...');

    // Get recent performance samples to assess network congestion
    const performanceSamples = await connection.getRecentPerformanceSamples(5);

    let avgTps = 0;
    let avgSlotTime = 0;

    if (performanceSamples.length > 0) {
      avgTps = performanceSamples.reduce((sum, sample) => sum + sample.numTransactions, 0) / performanceSamples.length;
      avgSlotTime = performanceSamples.reduce((sum, sample) => sum + sample.samplePeriodSecs, 0) / performanceSamples.length;
    }

    // Determine congestion level
    let congestion: 'low' | 'medium' | 'high' = 'medium';
    if (avgTps > 3000) {
      congestion = 'high';
    } else if (avgTps > 1500) {
      congestion = 'medium';
    } else {
      congestion = 'low';
    }

    // Calculate priority fee recommendations based on congestion
    let averagePriorityFee = 100000; // Base 100k microLamports
    let recommendedPriorityFee = 100000;

    if (congestion === 'high') {
      averagePriorityFee = 500000;
      recommendedPriorityFee = 750000;
    } else if (congestion === 'medium') {
      averagePriorityFee = 200000;
      recommendedPriorityFee = 300000;
    }

    // Estimate MEV activity (simplified)
    const mevActivity: 'low' | 'medium' | 'high' = congestion === 'high' ? 'high' :
                                                   congestion === 'medium' ? 'medium' : 'low';

    const conditions: NetworkConditions = {
      congestion,
      averagePriorityFee,
      recommendedPriorityFee,
      mevActivity
    };

    console.log('Network conditions:', conditions);
    return conditions;

  } catch (error: any) {
    console.error('Error analyzing network conditions:', error);

    // Return conservative defaults
    return {
      congestion: 'medium',
      averagePriorityFee: 200000,
      recommendedPriorityFee: 300000,
      mevActivity: 'medium'
    };
  }
}

/**
 * Calculate intelligent parameter recommendations
 */
export function calculateParameterRecommendations(
  tokenCharacteristics: TokenCharacteristics,
  networkConditions: NetworkConditions,
  tradeAmountSol: number
): ParameterRecommendations {
  console.log('Calculating parameter recommendations...');
  console.log('Token characteristics:', tokenCharacteristics);
  console.log('Network conditions:', networkConditions);
  console.log('Trade amount:', tradeAmountSol, 'SOL');

  // Calculate dynamic slippage
  const slippage = calculateDynamicSlippage(tokenCharacteristics, tradeAmountSol);

  // Calculate dynamic bribe amount
  const bribeAmount = calculateDynamicBribeAmount(tokenCharacteristics, networkConditions, tradeAmountSol);

  // Calculate dynamic priority fee
  const priorityFee = calculateDynamicPriorityFee(networkConditions, tradeAmountSol);

  // Generate reasoning
  const reasoning = generateRecommendationReasoning(
    tokenCharacteristics,
    networkConditions,
    { slippage, bribeAmount, priorityFee }
  );

  const recommendations: ParameterRecommendations = {
    slippage,
    bribeAmount,
    priorityFee,
    reasoning
  };

  console.log('Parameter recommendations:', recommendations);
  return recommendations;
}

/**
 * Calculate dynamic slippage based on token characteristics
 */
function calculateDynamicSlippage(
  characteristics: TokenCharacteristics,
  tradeAmountSol: number
): number {
  let baseSlippage = 5; // Start with 5% base

  // Adjust based on liquidity
  if (characteristics.liquidity === 'low') {
    baseSlippage += 5; // +5% for low liquidity
  } else if (characteristics.liquidity === 'high') {
    baseSlippage -= 2; // -2% for high liquidity
  }

  // Adjust based on volatility
  if (characteristics.volatility === 'high') {
    baseSlippage += 3; // +3% for high volatility
  } else if (characteristics.volatility === 'low') {
    baseSlippage -= 2; // -2% for low volatility
  }

  // Adjust based on token age
  if (characteristics.age === 'new') {
    baseSlippage += 2; // +2% for new tokens
  } else if (characteristics.age === 'mature') {
    baseSlippage -= 1; // -1% for mature tokens
  }

  // Adjust based on trade size (larger trades need more slippage)
  if (tradeAmountSol >= 1) {
    baseSlippage += 2; // +2% for large trades
  } else if (tradeAmountSol >= 0.1) {
    baseSlippage += 1; // +1% for medium trades
  }

  // Ensure reasonable bounds (2% minimum, 20% maximum)
  return Math.max(2, Math.min(20, baseSlippage));
}

/**
 * Calculate dynamic bribe amount based on token and network conditions
 */
function calculateDynamicBribeAmount(
  characteristics: TokenCharacteristics,
  networkConditions: NetworkConditions,
  tradeAmountSol: number
): number {
  let baseBribe = 1.0; // Start with 1% base

  // Adjust based on popularity (popular tokens have more MEV risk)
  if (characteristics.popularity === 'high') {
    baseBribe += 0.5; // +0.5% for popular tokens
  } else if (characteristics.popularity === 'low') {
    baseBribe -= 0.3; // -0.3% for unpopular tokens
  }

  // Adjust based on MEV activity
  if (networkConditions.mevActivity === 'high') {
    baseBribe += 0.8; // +0.8% for high MEV activity
  } else if (networkConditions.mevActivity === 'low') {
    baseBribe -= 0.4; // -0.4% for low MEV activity
  }

  // Adjust based on trade size (larger trades are more attractive to MEV)
  if (tradeAmountSol >= 1) {
    baseBribe += 0.5; // +0.5% for large trades
  } else if (tradeAmountSol <= 0.001) {
    baseBribe -= 0.5; // -0.5% for very small trades
  }

  // Adjust based on risk level
  if (characteristics.riskLevel === 'high') {
    baseBribe += 0.3; // +0.3% for high-risk tokens
  }

  // Ensure reasonable bounds (0.2% minimum, 5% maximum)
  return Math.max(0.2, Math.min(5, baseBribe));
}

/**
 * Calculate dynamic priority fee based on network conditions
 */
function calculateDynamicPriorityFee(
  networkConditions: NetworkConditions,
  tradeAmountSol: number
): number {
  let baseFee = 0.01; // Start with 0.01% base

  // Adjust based on network congestion
  if (networkConditions.congestion === 'high') {
    baseFee += 0.05; // +0.05% for high congestion
  } else if (networkConditions.congestion === 'low') {
    baseFee -= 0.005; // -0.005% for low congestion
  }

  // Adjust based on trade size (larger trades can afford higher fees)
  if (tradeAmountSol >= 1) {
    baseFee += 0.02; // +0.02% for large trades
  } else if (tradeAmountSol <= 0.001) {
    baseFee -= 0.005; // -0.005% for very small trades
  }

  // Ensure reasonable bounds (0.001% minimum, 0.5% maximum)
  return Math.max(0.001, Math.min(0.5, baseFee));
}

/**
 * Generate human-readable reasoning for recommendations
 */
function generateRecommendationReasoning(
  characteristics: TokenCharacteristics,
  networkConditions: NetworkConditions,
  recommendations: { slippage: number; bribeAmount: number; priorityFee: number }
): { slippage: string; bribeAmount: string; priorityFee: string } {
  // Slippage reasoning
  let slippageReason = `${recommendations.slippage}% slippage recommended`;
  if (characteristics.liquidity === 'low') {
    slippageReason += ' due to low liquidity';
  } else if (characteristics.volatility === 'high') {
    slippageReason += ' due to high volatility';
  } else if (characteristics.age === 'new') {
    slippageReason += ' for new token safety';
  } else {
    slippageReason += ' for optimal execution';
  }

  // Bribe reasoning
  let bribeReason = `${recommendations.bribeAmount}% MEV protection`;
  if (characteristics.popularity === 'high') {
    bribeReason += ' for popular token with MEV risk';
  } else if (networkConditions.mevActivity === 'high') {
    bribeReason += ' due to high network MEV activity';
  } else {
    bribeReason += ' for standard MEV protection';
  }

  // Priority fee reasoning
  let priorityReason = `${recommendations.priorityFee}% priority fee`;
  if (networkConditions.congestion === 'high') {
    priorityReason += ' due to network congestion';
  } else if (networkConditions.congestion === 'low') {
    priorityReason += ' for normal network conditions';
  } else {
    priorityReason += ' for reliable execution';
  }

  return {
    slippage: slippageReason,
    bribeAmount: bribeReason,
    priorityFee: priorityReason
  };
}
