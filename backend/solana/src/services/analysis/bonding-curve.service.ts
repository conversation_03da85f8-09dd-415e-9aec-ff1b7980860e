/**
 * Bonding Curve Analysis Service
 * Analyzes PumpFun bonding curve progression and graduation status
 */

import { PumpFunPoolData } from '../../types/pump.types';

/**
 * Bonding curve information interface
 */
export interface BondingCurveInfo {
  progressPercentage: number;
  currentSolRaised: number;
  graduationThreshold: number;
  isGraduated: boolean;
  status: 'active' | 'graduated' | 'unknown';
  description: string;
}

/**
 * PumpFun bonding curve constants (CORRECTED - HYBRID APPROACH)
 */
export const PUMPFUN_CONSTANTS = {
  // Token supply constants (verified from on-chain global config)
  TOTAL_SUPPLY_RAW: 1_000_000_000_000_000n, // 1 billion tokens with 6 decimals
  RESERVED_TOKENS_RAW: 206_900_000_000_000n, // Reserved tokens with 6 decimals (20.69%)
  INITIAL_REAL_TOKEN_RESERVES_RAW: 793_100_000_000_000n, // totalSupply - reservedTokens with 6 decimals (79.31%)

  // Virtual reserves at start of bonding curve (verified from global config)
  INITIAL_VIRTUAL_SOL_RESERVES: 30_000_000_000, // 30 SOL in lamports
  INITIAL_VIRTUAL_TOKEN_RESERVES: 1_073_000_000_000_000, // ~1.073B tokens (with 6 decimals)

  // Graduation thresholds (CORRECTED based on precise pump.fun analysis)
  GRADUATION_MARKET_CAP_USD: 71961, // Market cap at graduation ($71,961)
  GRADUATION_THRESHOLD_SOL: 71.635, // SOL threshold for graduation (calculated from 94% = 67.337 SOL)

  // Estimated SOL price for market cap calculation
  get ESTIMATED_SOL_PRICE_USD() {
    return this.GRADUATION_MARKET_CAP_USD / this.GRADUATION_THRESHOLD_SOL; // ~$1004.55
  }
};

/**
 * Analyze bonding curve progression for a PumpFun token
 * @param poolData PumpFun pool data
 * @returns Bonding curve information
 */
export function analyzeBondingCurveProgression(poolData: PumpFunPoolData): BondingCurveInfo {
  console.log('Analyzing bonding curve progression...');
  console.log('Pool data:', {
    exists: poolData.exists,
    virtualSolReserves: poolData.virtualSolReserves,
    realSolReserves: poolData.realSolReserves,
    virtualTokenReserves: poolData.virtualTokenReserves
  });

  // Default values for unknown/invalid pools
  if (!poolData.exists || !poolData.virtualSolReserves || !poolData.realSolReserves || !poolData.realTokenReserves) {
    console.log('Pool data incomplete, returning default bonding curve info');
    return {
      progressPercentage: 0,
      currentSolRaised: 0,
      graduationThreshold: PUMPFUN_CONSTANTS.GRADUATION_MARKET_CAP_USD,
      isGraduated: false,
      status: 'unknown',
      description: 'Unable to determine bonding curve status - pool data unavailable'
    };
  }

  // Convert reserves to proper units
  const virtualSolReservesSOL = Number(poolData.virtualSolReserves) / 1_000_000_000;
  const realSolReservesSOL = Number(poolData.realSolReserves) / 1_000_000_000;
  const realTokenReservesRaw = BigInt(poolData.realTokenReserves); // Convert to BigInt for precise calculation

  console.log('Converted reserves:', {
    virtualSolReservesSOL,
    realSolReservesSOL,
    realTokenReservesRaw
  });

  // Check if token has graduated (moved to AMM/Raydium)
  const isGraduated = checkGraduationStatus(poolData);

  if (isGraduated) {
    return {
      progressPercentage: 100,
      currentSolRaised: realSolReservesSOL, // Use actual SOL amount
      graduationThreshold: PUMPFUN_CONSTANTS.GRADUATION_MARKET_CAP_USD,
      isGraduated: true,
      status: 'graduated',
      description: 'Token has completed bonding curve and graduated to AMM/Raydium'
    };
  }

  // Calculate bonding curve progression using the correct TOKEN-based formula
  const progression = calculateBondingCurveProgress(
    realSolReservesSOL,
    realTokenReservesRaw
  );

  console.log('Bonding curve progression calculated:', progression);
  return progression;
}

/**
 * Check if token has graduated from bonding curve
 * @param poolData PumpFun pool data
 * @returns True if graduated
 */
function checkGraduationStatus(poolData: PumpFunPoolData): boolean {
  // Check if we have the required data
  if (!poolData.realSolReserves) {
    return false;
  }

  // Primary graduation check: SOL reserves reach graduation threshold (CORRECTED)
  const realSolReservesSOL = Number(poolData.realSolReserves) / 1_000_000_000;
  const graduationThresholdSOL = PUMPFUN_CONSTANTS.GRADUATION_THRESHOLD_SOL;

  // Token graduates when SOL reserves reach the graduation threshold
  if (realSolReservesSOL >= graduationThresholdSOL) {
    console.log(`Token graduated: Real SOL reserves (${realSolReservesSOL.toFixed(3)} SOL) exceed graduation threshold (${graduationThresholdSOL} SOL)`);
    return true;
  }

  // Additional check: If the pool data explicitly indicates completion
  if (poolData.isGraduated === true) {
    console.log('Token graduated: Pool data indicates graduation');
    return true;
  }

  console.log(`Token not graduated: Real SOL reserves (${realSolReservesSOL.toFixed(3)} SOL) below graduation threshold (${graduationThresholdSOL} SOL)`);
  return false;
}

/**
 * Calculate bonding curve progress percentage using the correct SOL-based formula
 * @param realSolReservesSOL Real SOL reserves in SOL
 * @param realTokenReservesRaw Real token reserves in raw format (with 6 decimals) - for reference only
 * @returns Bonding curve information
 */
function calculateBondingCurveProgress(
  realSolReservesSOL: number,
  realTokenReservesRaw: bigint
): BondingCurveInfo {
  console.log('=== SOL-BASED BONDING CURVE CALCULATION ===');
  console.log('Input data:');
  console.log(`- Real SOL Reserves: ${realSolReservesSOL} SOL`);
  console.log(`- Real Token Reserves (raw): ${realTokenReservesRaw.toLocaleString()}`);
  console.log('');

  // PumpFun bonding curve mechanics (CORRECTED - SOL-BASED):
  // - Progress is calculated based on SOL accumulated in the bonding curve
  // - Formula: Progress = (realSolReserves / graduationThresholdSOL) * 100
  // - Token graduates when SOL reserves reach the graduation threshold (~68.9 SOL)

  const graduationThresholdSOL = PUMPFUN_CONSTANTS.GRADUATION_THRESHOLD_SOL;

  // Calculate progress based on SOL reserves (CORRECT FORMULA)
  const progressPercentage = Math.max(0, Math.min(100,
    (realSolReservesSOL / graduationThresholdSOL) * 100
  ));

  console.log('Progress calculation:');
  console.log(`- Formula: (realSolReserves / graduationThresholdSOL) * 100`);
  console.log(`- Progress: (${realSolReservesSOL} / ${graduationThresholdSOL}) * 100`);
  console.log(`- Progress: ${progressPercentage.toFixed(2)}%`);
  console.log('');

  // Calculate remaining SOL needed for graduation
  const remainingSolNeeded = Math.max(graduationThresholdSOL - realSolReservesSOL, 0);

  // Generate description based on SOL-based progress
  let description: string;
  if (progressPercentage >= 95) {
    description = `${progressPercentage.toFixed(2)}% through bonding curve, very close to graduation! Only ${remainingSolNeeded.toFixed(3)} SOL needed to reach $${PUMPFUN_CONSTANTS.GRADUATION_MARKET_CAP_USD.toLocaleString()} market cap`;
  } else if (progressPercentage >= 80) {
    description = `${progressPercentage.toFixed(2)}% through bonding curve, approaching graduation. ${remainingSolNeeded.toFixed(3)} SOL needed to reach $${PUMPFUN_CONSTANTS.GRADUATION_MARKET_CAP_USD.toLocaleString()} market cap`;
  } else if (progressPercentage >= 50) {
    description = `${progressPercentage.toFixed(2)}% through bonding curve, halfway to graduation. ${remainingSolNeeded.toFixed(3)} SOL needed to reach $${PUMPFUN_CONSTANTS.GRADUATION_MARKET_CAP_USD.toLocaleString()} market cap`;
  } else if (progressPercentage >= 20) {
    description = `${progressPercentage.toFixed(2)}% through bonding curve, early stage. ${remainingSolNeeded.toFixed(3)} SOL needed to reach $${PUMPFUN_CONSTANTS.GRADUATION_MARKET_CAP_USD.toLocaleString()} market cap`;
  } else {
    description = `${progressPercentage.toFixed(2)}% through bonding curve, very early stage. ${remainingSolNeeded.toFixed(3)} SOL needed to reach $${PUMPFUN_CONSTANTS.GRADUATION_MARKET_CAP_USD.toLocaleString()} market cap`;
  }

  return {
    progressPercentage: Math.round(progressPercentage * 100) / 100, // Round to 2 decimal places
    currentSolRaised: Math.round(realSolReservesSOL * 100) / 100,
    graduationThreshold: PUMPFUN_CONSTANTS.GRADUATION_MARKET_CAP_USD,
    isGraduated: false,
    status: 'active',
    description
  };
}

/**
 * Get bonding curve stage description
 * @param progressPercentage Progress percentage
 * @returns Stage description
 */
export function getBondingCurveStage(progressPercentage: number): string {
  if (progressPercentage >= 100) {
    return 'Graduated';
  } else if (progressPercentage >= 90) {
    return 'Pre-graduation';
  } else if (progressPercentage >= 70) {
    return 'Late stage';
  } else if (progressPercentage >= 40) {
    return 'Mid stage';
  } else if (progressPercentage >= 10) {
    return 'Early stage';
  } else {
    return 'Launch stage';
  }
}

/**
 * Calculate estimated time to graduation (simplified)
 * @param currentProgress Current progress percentage
 * @param recentVolumeSOL Recent trading volume in SOL (if available)
 * @returns Estimated time description
 */
export function estimateTimeToGraduation(
  currentProgress: number,
  recentVolumeSOL?: number
): string {
  if (currentProgress >= 100) {
    return 'Already graduated';
  }

  const remainingProgress = 100 - currentProgress;

  if (!recentVolumeSOL || recentVolumeSOL <= 0) {
    return 'Unable to estimate - insufficient volume data';
  }

  // Simple estimation based on recent volume
  // This is a rough approximation and would need more sophisticated modeling in production
  const estimatedDays = remainingProgress / (recentVolumeSOL * 10); // Very rough estimate

  if (estimatedDays < 1) {
    return 'Less than 1 day at current pace';
  } else if (estimatedDays < 7) {
    return `~${Math.round(estimatedDays)} days at current pace`;
  } else if (estimatedDays < 30) {
    return `~${Math.round(estimatedDays / 7)} weeks at current pace`;
  } else {
    return 'Several months at current pace';
  }
}

/**
 * Get bonding curve health indicators
 * @param bondingCurveInfo Bonding curve information
 * @param virtualSolReservesSOL Virtual SOL reserves
 * @returns Health indicators
 */
export function getBondingCurveHealthIndicators(
  bondingCurveInfo: BondingCurveInfo,
  virtualSolReservesSOL: number
): {
  liquidity: 'low' | 'medium' | 'high';
  momentum: 'slow' | 'steady' | 'fast';
  risk: 'low' | 'medium' | 'high';
} {
  // Liquidity assessment based on virtual reserves
  let liquidity: 'low' | 'medium' | 'high';
  if (virtualSolReservesSOL > 20) {
    liquidity = 'high';
  } else if (virtualSolReservesSOL > 10) {
    liquidity = 'medium';
  } else {
    liquidity = 'low';
  }

  // Momentum assessment based on progress
  let momentum: 'slow' | 'steady' | 'fast';
  if (bondingCurveInfo.progressPercentage > 80) {
    momentum = 'fast';
  } else if (bondingCurveInfo.progressPercentage > 30) {
    momentum = 'steady';
  } else {
    momentum = 'slow';
  }

  // Risk assessment (early stage = higher risk)
  let risk: 'low' | 'medium' | 'high';
  if (bondingCurveInfo.progressPercentage > 70) {
    risk = 'low';
  } else if (bondingCurveInfo.progressPercentage > 30) {
    risk = 'medium';
  } else {
    risk = 'high';
  }

  return { liquidity, momentum, risk };
}
