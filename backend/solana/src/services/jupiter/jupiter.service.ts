import { Connection, PublicKey, Transaction, VersionedTransaction } from '@solana/web3.js';
import { getMint } from '@solana/spl-token';
import axios from 'axios';
import {
  JupiterQuoteRequest,
  JupiterQuoteResponse,
  JupiterSwapRequest,
  JupiterSwapResponse,
  JupiterQuoteResult
} from '../../types/jupiter.types';
import { QuoteResult, SwapDirection } from '../../types/pump.types';
import { config } from '../../config';
import { signAndSendTransactionWithPrivy } from '../privy/proper-privy.service';

// Constants
const SOL_MINT = 'So11111111111111111111111111111111111111112';
const SOL_DECIMALS = 9;

/**
 * Get a quote from Jupiter API
 * @param inputMint Input token mint address
 * @param outputMint Output token mint address
 * @param amount Amount to swap (in input token's native units)
 * @param slippageBps Slippage tolerance in basis points (e.g., 100 for 1%)
 * @param platformFeeBps Platform fee in basis points
 * @returns Jupiter quote response
 */
export async function getJupiterQuote(
  inputMint: string,
  outputMint: string,
  amount: number,
  slippageBps: number,
  platformFeeBps: number
): Promise<JupiterQuoteResponse> {
  try {
    console.log(`Getting Jupiter quote for ${amount} ${inputMint} to ${outputMint} with ${slippageBps} bps slippage`);

    // Construct the quote request URL with query parameters
    const url = new URL(config.jupiterApiEndpoints.quote);
    url.searchParams.append('inputMint', inputMint);
    url.searchParams.append('outputMint', outputMint);
    url.searchParams.append('amount', amount.toString());
    url.searchParams.append('slippageBps', slippageBps.toString());

    // Add platform fee if specified
    if (platformFeeBps > 0) {
      url.searchParams.append('platformFeeBps', platformFeeBps.toString());
    }

    console.log(`Jupiter quote URL: ${url.toString()}`);

    // Make the request to Jupiter API
    const response = await axios.get<JupiterQuoteResponse>(url.toString());

    console.log('Jupiter quote response received:', JSON.stringify(response.data));

    // Validate the response
    if (!response.data || !response.data.outAmount) {
      throw new Error(`Invalid Jupiter quote response: ${JSON.stringify(response.data)}`);
    }

    return response.data;
  } catch (error: any) {
    console.error('Error getting Jupiter quote:', error);
    throw new Error(`Failed to get Jupiter quote: ${error.message || error}`);
  }
}

/**
 * Create a swap transaction using Jupiter API
 * @param quoteResponse Jupiter quote response
 * @param userPublicKey User's public key
 * @returns Jupiter swap response
 */
export async function createJupiterSwapTransaction(
  quoteResponse: JupiterQuoteResponse,
  userPublicKey: string
): Promise<JupiterSwapResponse> {
  try {
    console.log(`Creating Jupiter swap transaction for user: ${userPublicKey}`);

    // Prepare the swap request
    const swapRequest: JupiterSwapRequest = {
      quoteResponse,
      userPublicKey,
      wrapAndUnwrapSol: true,
      dynamicComputeUnitLimit: true,
      // Add priority fee for better transaction success rate
      prioritizationFeeLamports: {
        priorityLevelWithMaxLamports: {
          maxLamports: 1000000, // 0.001 SOL
          priorityLevel: 'high'
        }
      }
    };

    // Make the request to Jupiter API
    const response = await axios.post<JupiterSwapResponse>(
      config.jupiterApiEndpoints.swap,
      swapRequest,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('Jupiter swap response received');

    if (!response.data.swapTransaction) {
      throw new Error('No swap transaction in Jupiter response');
    }

    return response.data;
  } catch (error: any) {
    console.error('Error creating Jupiter swap transaction:', error);
    throw new Error(`Failed to create Jupiter swap transaction: ${error.message || error}`);
  }
}

/**
 * Process a Jupiter quote response into a standardized quote result
 * @param quoteResponse Jupiter quote response
 * @param inputDecimals Input token decimals
 * @param outputDecimals Output token decimals
 * @returns Standardized quote result
 */
export function processJupiterQuote(
  quoteResponse: JupiterQuoteResponse,
  inputDecimals: number,
  outputDecimals: number
): JupiterQuoteResult {
  try {
    console.log('Processing Jupiter quote:', JSON.stringify(quoteResponse));

    // Validate the response
    if (!quoteResponse.outAmount) {
      throw new Error(`Invalid Jupiter quote response: missing outAmount`);
    }

    // Parse the input and output amounts
    // Use inAmount if available, otherwise fall back to a default value
    const inputAmountStr = quoteResponse.inAmount || '0';
    if (!inputAmountStr) {
      throw new Error(`Invalid Jupiter quote response: missing inAmount`);
    }

    const inputAmountRaw = BigInt(inputAmountStr);
    const outputAmountRaw = BigInt(quoteResponse.outAmount);

    // Convert to decimal values
    const inputAmountDecimal = Number(inputAmountRaw) / 10 ** inputDecimals;
    const outAmount = Number(outputAmountRaw) / 10 ** outputDecimals;

    // Calculate price (ratio of input to output)
    const price = inputAmountDecimal / outAmount;

    return {
      outAmount,
      price,
      outputAmountRaw,
      inputAmountRaw,
      priceImpactPct: typeof quoteResponse.priceImpactPct === 'string'
        ? parseFloat(quoteResponse.priceImpactPct)
        : (quoteResponse.priceImpactPct || 0),
      quoteResponse
    };
  } catch (error: any) {
    console.error('Error processing Jupiter quote:', error);
    throw new Error(`Failed to process Jupiter quote: ${error.message || error}`);
  }
}

/**
 * Execute a Jupiter swap with Privy session signing
 * @param transaction Base64 encoded transaction
 * @param connection Solana connection
 * @param walletAddress User's wallet address
 * @param walletId User's wallet ID for Privy session signing
 * @returns Transaction signature
 */
export async function executeJupiterSwapWithPrivy(
  transaction: string,
  connection: Connection,
  walletAddress: string,
  walletId?: string
): Promise<string> {
  try {
    console.log(`Executing Jupiter swap for wallet: ${walletAddress}`);

    if (!walletId) {
      throw new Error('Wallet ID is required for Privy session signing');
    }

    // Decode the transaction
    const transactionBuffer = Buffer.from(transaction, 'base64');

    // Check if it's a versioned transaction
    if (transactionBuffer[0] === 0x80) {
      console.log('Processing versioned transaction with Privy session signing');

      try {
        // For versioned transactions, we need to deserialize and sign differently
        const versionedTransaction = VersionedTransaction.deserialize(transactionBuffer);

        // Set the fee payer to the platform wallet
        if (!config.platformKeypair) {
          throw new Error('Platform keypair not found. Please check your environment variables.');
        }

        // Add the platform keypair as a signer for gas fees
        versionedTransaction.sign([config.platformKeypair]);

        // Serialize the transaction to base64 for Privy
        const serializedTx = Buffer.from(versionedTransaction.serialize()).toString('base64');
        
        // Use Privy service to sign and send the transaction
        console.log(`Using Privy service to sign and send versioned transaction for wallet ID: ${walletId}`);
        const signature = await signAndSendTransactionWithPrivy(
          walletId,
          serializedTx,
          connection,
          'high', // Jupiter transactions use high priority by default
          true // Wait for confirmation
        );

        console.log(`Jupiter swap executed successfully with Privy. Signature: ${signature}`);
        return signature;
      } catch (error: any) {
        console.error('Error processing versioned transaction with Privy:', error);

        // If direct versioned transaction signing fails, try converting to legacy transaction
        console.log('Trying alternative approach for versioned transaction...');

        try {
          // Get the latest blockhash for the transaction
          const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');

          // Create a new transaction with the same instructions
          const legacyTransaction = new Transaction();
          legacyTransaction.recentBlockhash = blockhash;
          legacyTransaction.lastValidBlockHeight = lastValidBlockHeight;

          // Check if platform keypair exists
          if (!config.platformKeypair) {
            throw new Error('Platform keypair not found. Please check your environment variables.');
          }

          // Set the fee payer to the platform wallet
          legacyTransaction.feePayer = config.platformKeypair.publicKey;

          // Try to extract instructions from the versioned transaction
          const versionedTransaction = VersionedTransaction.deserialize(transactionBuffer);
          const message = versionedTransaction.message;

          // Add all the instructions from the message to the legacy transaction
          // This is a simplified approach and may not work for all versioned transactions
          for (let i = 0; i < message.compiledInstructions.length; i++) {
            const compiledInstruction = message.compiledInstructions[i];

            // Convert compiled instruction to regular instruction
            const instruction = {
              programId: message.staticAccountKeys[compiledInstruction.programIdIndex],
              keys: compiledInstruction.accountKeyIndexes.map(index => ({
                pubkey: message.staticAccountKeys[index],
                isSigner: message.isAccountSigner(index),
                isWritable: message.isAccountWritable(index)
              })),
              data: Buffer.from(compiledInstruction.data)
            };

            legacyTransaction.add(instruction);
          }

          // Partially sign with the platform keypair for gas fees
          legacyTransaction.partialSign(config.platformKeypair);

          // Use Privy service to sign and send the transaction
          console.log(`Using Privy service to sign and send legacy transaction for wallet ID: ${walletId}`);
          const serializedTx = legacyTransaction.serialize({ requireAllSignatures: false }).toString('base64');
          const signature = await signAndSendTransactionWithPrivy(
            walletId,
            serializedTx,
            connection,
            'high', // Jupiter transactions use high priority by default
            true // Wait for confirmation
          );

          console.log(`Jupiter swap executed successfully with alternative approach. Signature: ${signature}`);
          return signature;
        } catch (conversionError: any) {
          console.error('Error converting versioned transaction to legacy:', conversionError);
          throw new Error(`Failed to process versioned transaction: ${error.message}, conversion error: ${conversionError.message}`);
        }
      }
    } else {
      console.log('Processing legacy transaction with Privy session signing');

      // For legacy transactions, deserialize and sign with Privy
      const legacyTransaction = Transaction.from(transactionBuffer);

      // Get the latest blockhash for the transaction
      const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');
      legacyTransaction.recentBlockhash = blockhash;
      legacyTransaction.lastValidBlockHeight = lastValidBlockHeight;

      // Set the fee payer to the platform wallet
      if (!config.platformKeypair) {
        throw new Error('Platform keypair not found. Please check your environment variables.');
      }
      legacyTransaction.feePayer = config.platformKeypair.publicKey;

      // Partially sign with the platform keypair for gas fees
      legacyTransaction.partialSign(config.platformKeypair);

      // Serialize the transaction to base64 for Privy
      const serializedTx = legacyTransaction.serialize({ requireAllSignatures: false }).toString('base64');
      
      // Use Privy service to sign and send the transaction
      console.log(`Using Privy service to sign and send legacy transaction for wallet ID: ${walletId}`);
      const signature = await signAndSendTransactionWithPrivy(
        walletId,
        serializedTx,
        connection,
        'high', // Jupiter transactions use high priority by default
        true // Wait for confirmation
      );

      console.log(`Jupiter swap executed successfully with Privy. Signature: ${signature}`);
      return signature;
    }
  } catch (error: any) {
    console.error('Error executing Jupiter swap with Privy:', error);
    throw new Error(`Failed to execute Jupiter swap: ${error.message || error}`);
  }
}

/**
 * Get token decimals
 * @param connection Solana connection
 * @param mintAddress Token mint address
 * @returns Token decimals
 */
export async function getTokenDecimals(
  connection: Connection,
  mintAddress: string
): Promise<number> {
  try {
    // Check if it's SOL
    if (mintAddress === SOL_MINT) {
      return SOL_DECIMALS;
    }

    // Get the token mint info
    const mintInfo = await getMint(connection, new PublicKey(mintAddress));
    return mintInfo.decimals;
  } catch (error: any) {
    console.error(`Error getting token decimals for ${mintAddress}:`, error);
    throw new Error(`Failed to get token decimals: ${error.message || error}`);
  }
}
