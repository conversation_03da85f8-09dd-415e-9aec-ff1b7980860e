import axios from "axios";
import { Connection, Transaction, VersionedTransaction } from "@solana/web3.js";
import crypto from 'crypto';
import canonicalize from 'canonicalize';

/**
 * Signs and sends a transaction with the Privy session signer
 * @param walletId The wallet ID for Privy session signing
 * @param serializedTransaction The transaction to sign, already serialized as base64
 * @param connection Optional Solana connection for blockhash management and confirmation
 * @param priorityLevel Priority level for timeout determination
 * @param waitForConfirmation Whether to wait for transaction confirmation (default: true)
 * @returns The transaction signature
 *
 * UPDATED: Added transaction confirmation logic to fix priority level response inconsistency
 */
export async function signAndSendTransactionWithPrivy(
  walletId: string,
  serializedTransaction: string,
  connection?: Connection,
  priorityLevel: 'low' | 'medium' | 'high' | 'veryHigh' = 'medium',
  waitForConfirmation: boolean = true
): Promise<string> {
  try {
    if (!walletId) {
      throw new Error('Wallet ID is required for Privy session signing');
    }

    console.log(`Transaction serialized for Privy, length: ${serializedTransaction.length}`);

    // Get Privy credentials from environment variables
    const appId = process.env.PRIVY_APP_ID?.trim();
    const appSecret = process.env.PRIVY_APP_SECRET?.trim();
    const signingKey = process.env.PRIVY_SIGNING_KEY?.trim()?.replace('wallet-auth:', '');

    if (!appId || !appSecret || !signingKey) {
      throw new Error('Privy credentials missing. Set PRIVY_APP_ID, PRIVY_APP_SECRET, and PRIVY_SIGNING_KEY');
    }

    // If a connection is provided, we can update the transaction with a fresh blockhash
    if (connection) {
      try {
        // Deserialize the transaction
        const transaction = Transaction.from(Buffer.from(serializedTransaction, 'base64'));

        // Get a fresh blockhash with finalized commitment
        // This is more reliable for transaction submission
        const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash({
          commitment: 'finalized'
        });

        // Update the transaction
        transaction.recentBlockhash = blockhash;

        // Verify the blockhash is valid
        const isValid = await connection.isBlockhashValid(blockhash, { commitment: 'finalized' });
        console.log(`Using blockhash: ${blockhash}, valid: ${isValid}, last valid block height: ${lastValidBlockHeight}`);

        // IMPORTANT: Do not modify the transaction structure after this point
        // Only update the blockhash to ensure signature verification

        // Re-serialize the transaction
        serializedTransaction = transaction.serialize({ requireAllSignatures: false }).toString('base64');

        console.log(`Updated transaction with fresh blockhash: ${blockhash}`);
      } catch (error) {
        console.warn('Error updating transaction blockhash:', error);
        // Continue with the original transaction
      }
    }

    console.log('All Privy credentials found:');
    console.log('- App ID:', appId);
    console.log('- App Secret length:', appSecret.length);
    console.log('- Signing Key length:', signingKey.length);

    // Prepare headers with basic authentication
    const basicAuth = Buffer.from(`${appId}:${appSecret}`).toString('base64');
    const baseUrl = 'https://api.privy.io';

    // Function to generate Privy authorization signature
    function generatePrivySignature(url: string, body: any, signingKey: string): string {
      try {
        // Create the payload according to Privy specifications
        const payload = {
          version: 1,
          method: 'POST',
          url,
          body,
          headers: {
            'privy-app-id': appId
          }
        };

        console.log('Generating Privy authorization signature');

        // Canonicalize the payload according to RFC 8785 for consistent string representation
        const canonicalizedPayload = canonicalize(payload) as string;
        if (!canonicalizedPayload) {
          throw new Error('Failed to canonicalize payload');
        }

        console.log('Canonicalized payload length:', canonicalizedPayload.length);

        // Format the signing key as PEM
        const pemKey = `-----BEGIN PRIVATE KEY-----
${signingKey}
-----END PRIVATE KEY-----`;

        // Create signing object and update with payload
        const sign = crypto.createSign('sha256');
        sign.update(canonicalizedPayload);

        // Sign with the private key
        const privateKeyOptions = {
          key: pemKey,
          format: 'pem' as const,
          type: 'pkcs8' as const
        };

        // Generate and return signature
        const signature = sign.sign(privateKeyOptions, 'base64');
        console.log('Signature generated successfully, length:', signature.length);
        return signature;
      } catch (error) {
        console.error('Error generating Privy signature:', error);
        throw new Error(`Failed to generate Privy signature: ${(error as Error).message}`);
      }
    }

    // Format the request according to Privy API documentation
    // Use the standard wallet endpoint for transaction signing
    const apiEndpoint = `${baseUrl}/v1/wallets/${walletId}/rpc`;

    // Follow Privy API format based on official documentation
    const requestBody = {
      method: 'signAndSendTransaction',
      caip2: 'solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp', // Solana mainnet chain ID
      params: {
        transaction: serializedTransaction,
        encoding: 'base64'
      }
    };

    console.log('Using Privy transaction signing endpoint instead of RPC');

    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    console.log('Making request to Privy API with proper JSON-RPC format');

    try {
      // Log the full request details for debugging
      console.log('Request URL:', apiEndpoint);
      console.log('Headers:', {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${basicAuth.substring(0, 10)}...`,
        'privy-app-id': appId
      });

      // Add detailed logging for credential verification
      console.log('Verifying Privy credentials...');
      console.log('App ID length:', appId.length);
      console.log('App Secret first 5 chars:', appSecret.substring(0, 5) + '...');
      console.log('Wallet ID being used:', walletId);

      // Generate the Privy authorization signature
      const signature = generatePrivySignature(apiEndpoint, requestBody, signingKey);

      // Retry logic disabled as requested
      let privySignature = signature; // Use a different variable name to avoid const reassignment

      try {
        // Create client for this specific request with all required headers
        const response = await axios.post(
          apiEndpoint,
          requestBody,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Basic ${basicAuth}`,
              'privy-app-id': appId,
              'privy-authorization-signature': privySignature
            }
          }
        );

        console.log('Privy request sent with proper authorization signature.');
        console.log('Privy API response status:', response.status);

        // Extract signature from RPC response format
        console.log('Privy API response data:', JSON.stringify(response.data, null, 2));

        // Process the response to extract the transaction signature
        let signature: string;

        if (response.data && response.data.result) {
          console.log('Received successful response from Privy RPC API');
          signature = response.data.result;
        } else if (response.data && response.data.result && response.data.result.signature) {
          console.log('Received signature in RPC result format');
          signature = response.data.result.signature;
        } else if (response.data && response.data.signature) {
          console.log('Received signature in alternate format');
          signature = response.data.signature;
        } else if (response.data && response.data.data && response.data.data.hash) {
          // New format from Privy API
          console.log('Received signature in new Privy format');
          signature = response.data.data.hash;
        } else {
          console.error('Unexpected response format:', JSON.stringify(response.data, null, 2));
          throw new Error('Unexpected response format from Privy API');
        }

        console.log(`Transaction submitted successfully with signature: ${signature}`);

        // If confirmation is not required, return signature immediately
        if (!waitForConfirmation || !connection) {
          console.log('Skipping transaction confirmation check');
          return signature;
        }

        // Wait for transaction confirmation based on priority level
        console.log(`Waiting for transaction confirmation with priority level: ${priorityLevel}`);
        const confirmedSignature = await waitForTransactionConfirmation(
          connection,
          signature,
          priorityLevel
        );

        return confirmedSignature;
      } catch (error) {
        console.error('Privy API call failed:', error);
        throw error;
      }
    } catch (error: any) {
      console.log('Privy API request failed:', error.message);
      console.log('Error details:');
      if (error.response) {
        console.log('Status:', error.response.status);
        console.log('Response data:', error.response.data);

        // Handle specific error types with more detailed messages
        if (error.response.status === 404) {
          throw new Error(`Wallet ID not found in Privy: ${walletId}`);
        } else if (error.response.status === 401) {
          throw new Error('Privy API authentication failed: Check your app credentials');
        } else if (error.response.status === 400) {
          // Log the full error response object for detailed debugging
          console.log('DETAILED ERROR RESPONSE:', JSON.stringify(error.response.data, null, 2));

          // Extract the error message in a more structured way
          let errorMessage = 'Invalid request format';
          if (error.response.data?.error?.message) {
            errorMessage = error.response.data.error.message;
            console.log('Error message:', errorMessage);
            console.log('Error code:', error.response.data.error.code);

            // For transaction signature verification failures, provide more helpful error message
            if (error.response.data.error.code === 'transaction_broadcast_failure' &&
                errorMessage.includes('signature verification failure')) {
              console.log('SIGNATURE VERIFICATION FAILURE DETECTED');

              // Provide a more detailed error message with troubleshooting steps
              throw new Error(
                'Transaction signature verification failed. This is likely due to one of the following issues:\n' +
                '1. The transaction was modified after serialization\n' +
                '2. The fee payer is not correctly set to the user wallet\n' +
                '3. The PDAs in the transaction are not correctly derived\n' +
                '4. The blockhash is stale or invalid\n\n' +
                'Please check the transaction structure and try again.'
              );
            }

            // For transaction simulation failures, extract the Solana error logs
            if (errorMessage.includes('Transaction simulation failed')) {
              console.log('SOLANA PROGRAM ERROR:', errorMessage);

              // Extract and format program logs if available
              const logMatch = errorMessage.match(/Logs: (.+)/i);
              if (logMatch && logMatch[1]) {
                const logs = logMatch[1].split(' -- ');
                console.log('PROGRAM LOGS:');
                logs.forEach((log, i) => console.log(`${i}:`, log));
              }
            }
          } else if (error.response.data?.error) {
            errorMessage = JSON.stringify(error.response.data.error);
          }

          console.log(`Detailed 400 error: ${JSON.stringify(error.response.data)}`);
          throw new Error(`Privy API error (400): ${errorMessage}`);
        }
      }

      throw new Error(`Privy API error: ${error.message}`);
    }
  } catch (error) {
    console.error('Privy signing error:', error);
    throw new Error(`Failed to sign transaction with Privy: ${(error as Error).message}`);
  }
}

/**
 * Wait for transaction confirmation with priority-level-specific timeouts
 * @param connection Solana connection
 * @param signature Transaction signature to confirm
 * @param priorityLevel Priority level for timeout determination
 * @returns Confirmed signature or throws error
 */
async function waitForTransactionConfirmation(
  connection: Connection,
  signature: string,
  priorityLevel: 'low' | 'medium' | 'high' | 'veryHigh'
): Promise<string> {
  // Priority-level-specific timeout settings (in milliseconds)
  const timeoutSettings = {
    low: 90000,      // 90 seconds for low priority
    medium: 45000,   // 45 seconds for medium priority
    high: 30000,     // 30 seconds for high priority
    veryHigh: 15000  // 15 seconds for very high priority
  };

  const timeout = timeoutSettings[priorityLevel];
  const startTime = Date.now();

  console.log(`Waiting for confirmation of transaction ${signature} with ${timeout}ms timeout (${priorityLevel} priority)`);

  // Create a promise that resolves when transaction is confirmed or rejects on timeout
  return new Promise(async (resolve, reject) => {
    const timeoutId = setTimeout(() => {
      reject(new Error(
        `Transaction confirmation timeout after ${timeout}ms for ${priorityLevel} priority transaction. ` +
        `Signature: ${signature}. The transaction may still be processing on the blockchain.`
      ));
    }, timeout);

    // Poll for transaction confirmation
    const checkConfirmation = async () => {
      try {
        const status = await connection.getSignatureStatus(signature, {
          searchTransactionHistory: true
        });

        if (status.value === null) {
          // Transaction not found yet, continue polling
          console.log(`Transaction ${signature} not found yet, continuing to poll...`);
          setTimeout(checkConfirmation, 2000); // Check every 2 seconds
          return;
        }

        if (status.value.err) {
          // Transaction failed
          clearTimeout(timeoutId);
          console.error(`Transaction ${signature} failed:`, status.value.err);
          reject(new Error(`Transaction failed: ${JSON.stringify(status.value.err)}`));
          return;
        }

        if (status.value.confirmationStatus === 'confirmed' || status.value.confirmationStatus === 'finalized') {
          // Transaction confirmed successfully
          clearTimeout(timeoutId);
          const elapsedTime = Date.now() - startTime;
          console.log(`Transaction ${signature} confirmed successfully in ${elapsedTime}ms (${status.value.confirmationStatus})`);
          resolve(signature);
          return;
        }

        // Transaction is still processing, continue polling
        console.log(`Transaction ${signature} status: ${status.value.confirmationStatus}, continuing to poll...`);
        setTimeout(checkConfirmation, 2000); // Check every 2 seconds

      } catch (error) {
        console.error(`Error checking transaction status for ${signature}:`, error);
        // Continue polling on error, don't fail immediately
        setTimeout(checkConfirmation, 2000);
      }
    };

    // Start checking immediately
    checkConfirmation();
  });
}
