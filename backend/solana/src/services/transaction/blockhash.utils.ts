import { Connection, Transaction } from '@solana/web3.js';

/**
 * Blockhash management utilities
 *
 * This file contains utility functions for properly managing blockhashes
 * in Solana transactions, especially when using Privy session signing.
 */

// Constants
const BLOCKHASH_TIMEOUT = 60000; // 60 seconds
const MAX_RETRY_ATTEMPTS = 3;

/**
 * Get a fresh blockhash with the specified commitment level
 *
 * @param connection Solana connection
 * @param commitment Commitment level (default: 'confirmed')
 * @returns The blockhash and last valid block height
 */
export async function getFreshBlockhash(
  connection: Connection,
  commitment: 'confirmed' | 'finalized' = 'confirmed'
): Promise<{ blockhash: string; lastValidBlockHeight: number }> {
  try {
    console.log(`Getting fresh blockhash with ${commitment} commitment...`);
    const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash(commitment);

    // Validate the blockhash
    const isValid = await connection.isBlockhashValid(blockhash, { commitment });
    if (!isValid) {
      console.warn(`Blockhash ${blockhash} is not valid with ${commitment} commitment`);

      // Try again with a different commitment level
      if (commitment === 'finalized') {
        console.log('Retrying with confirmed commitment...');
        return getFreshBlockhash(connection, 'confirmed');
      }
    }

    console.log(`Got fresh blockhash: ${blockhash}, last valid block height: ${lastValidBlockHeight}`);
    return { blockhash, lastValidBlockHeight };
  } catch (error) {
    console.error('Error getting fresh blockhash:', error);
    throw new Error(`Failed to get fresh blockhash: ${(error as Error).message}`);
  }
}

/**
 * Update a transaction with a fresh blockhash
 *
 * @param transaction Transaction to update
 * @param connection Solana connection
 * @param commitment Commitment level (default: 'confirmed')
 * @returns The updated transaction
 */
export async function updateTransactionBlockhash(
  transaction: Transaction,
  connection: Connection,
  commitment: 'confirmed' | 'finalized' = 'confirmed'
): Promise<Transaction> {
  const { blockhash, lastValidBlockHeight } = await getFreshBlockhash(connection, commitment);

  // Update the transaction
  transaction.recentBlockhash = blockhash;
  transaction.lastValidBlockHeight = lastValidBlockHeight;

  return transaction;
}

/**
 * Execute a transaction without retry logic (retry disabled as requested)
 *
 * @param operation Function that executes the transaction
 * @param connection Solana connection
 * @param transaction Transaction to execute
 * @returns The result of the operation
 */
export async function executeWithBlockhashRetry<T>(
  operation: (tx: Transaction) => Promise<T>,
  connection: Connection,
  transaction: Transaction
): Promise<T> {
  try {
    // Execute the operation without retries
    return await operation(transaction);
  } catch (error) {
    console.error('Transaction execution failed:', error);
    throw error;
  }
}
