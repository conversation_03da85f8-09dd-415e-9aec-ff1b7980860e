import {
  Connection,
  PublicKey,
  Transaction,
  TransactionInstruction,
  SystemProgram,
  Keypair
} from '@solana/web3.js';
import {
  getAssociatedTokenAddress,
  createAssociatedTokenAccountInstruction,
  TOKEN_PROGRAM_ID
} from '@solana/spl-token';
import { updateTransactionBlockhash, getFreshBlockhash } from './blockhash.utils';

// Re-export for convenience
export { updateTransactionBlockhash, getFreshBlockhash };

/**
 * Transaction utilities
 *
 * This file contains utility functions for managing Solana transactions,
 * especially for simplifying complex transactions and pre-creating accounts.
 */

/**
 * Check if an associated token account exists
 *
 * @param connection Solana connection
 * @param owner Account owner
 * @param mint Token mint
 * @returns Whether the account exists
 */
export async function checkIfTokenAccountExists(
  connection: Connection,
  owner: PublicKey,
  mint: PublicKey
): Promise<boolean> {
  try {
    const tokenAccount = await getAssociatedTokenAddress(mint, owner, true);
    const accountInfo = await connection.getAccountInfo(tokenAccount);
    return accountInfo !== null;
  } catch (error) {
    console.error('Error checking token account:', error);
    return false;
  }
}

/**
 * Create an associated token account if it doesn't exist
 * No retry logic (retry disabled as requested)
 *
 * @param connection Solana connection
 * @param payer Account that will pay for the creation
 * @param owner Account owner
 * @param mint Token mint
 * @returns The transaction signature if a new account was created, null if the account already exists
 */
export async function createTokenAccountIfNeeded(
  connection: Connection,
  payer: Keypair,
  owner: PublicKey,
  mint: PublicKey
): Promise<string | null> {
  try {
    // Check if the account already exists
    const exists = await checkIfTokenAccountExists(connection, owner, mint);
    if (exists) {
      console.log('Token account already exists');
      return null;
    }

    // Create the account
    const tokenAccount = await getAssociatedTokenAddress(mint, owner, true);
    const instruction = createAssociatedTokenAccountInstruction(
      payer.publicKey,
      tokenAccount,
      owner,
      mint
    );

    // Create and send the transaction
    const transaction = new Transaction().add(instruction);
    await updateTransactionBlockhash(transaction, connection);

    // Sign and send the transaction - no retry
    const signature = await connection.sendTransaction(transaction, [payer]);
    console.log(`Created token account ${tokenAccount.toString()}, signature: ${signature}`);

    // Wait for confirmation
    await connection.confirmTransaction(signature);

    return signature;
  } catch (error) {
    console.error('Error creating token account:', error);
    throw error;
  }
}

/**
 * Simplify a transaction by removing unnecessary instructions
 *
 * @param transaction Transaction to simplify
 * @returns The simplified transaction
 */
export function simplifyTransaction(transaction: Transaction): Transaction {
  // Create a new transaction with the same blockhash and fee payer
  const simplifiedTransaction = new Transaction();
  simplifiedTransaction.recentBlockhash = transaction.recentBlockhash;
  simplifiedTransaction.feePayer = transaction.feePayer;

  // Filter out unnecessary compute budget instructions
  const essentialInstructions = transaction.instructions.filter(instruction => {
    // Keep all non-compute budget instructions
    if (!instruction.programId.equals(new PublicKey('ComputeBudget111111111111111111111111111111'))) {
      return true;
    }

    // Only keep compute budget instructions that are essential
    // For example, keep setComputeUnitLimit but remove setComputeUnitPrice if not needed
    const isEssential = instruction.data.length > 0 && instruction.data[0] === 0; // 0 = setComputeUnitLimit
    return isEssential;
  });

  // Add the essential instructions to the new transaction
  essentialInstructions.forEach(instruction => {
    simplifiedTransaction.add(instruction);
  });

  return simplifiedTransaction;
}

/**
 * Split a complex transaction into multiple simpler transactions
 *
 * @param transaction Complex transaction to split
 * @returns Array of simpler transactions
 */
export function splitTransaction(transaction: Transaction): Transaction[] {
  const transactions: Transaction[] = [];

  // Group instructions by program ID
  const instructionsByProgram: { [programId: string]: TransactionInstruction[] } = {};

  transaction.instructions.forEach(instruction => {
    const programId = instruction.programId.toString();
    if (!instructionsByProgram[programId]) {
      instructionsByProgram[programId] = [];
    }
    instructionsByProgram[programId].push(instruction);
  });

  // Create a transaction for each program's instructions
  Object.entries(instructionsByProgram).forEach(([programId, instructions]) => {
    const tx = new Transaction();
    tx.recentBlockhash = transaction.recentBlockhash;
    tx.feePayer = transaction.feePayer;

    instructions.forEach(instruction => {
      tx.add(instruction);
    });

    transactions.push(tx);
  });

  return transactions;
}
