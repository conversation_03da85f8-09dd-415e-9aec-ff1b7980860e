# LaunchLab Service Integration

This document describes the LaunchLab service integration that provides quote and swap API functionality for Raydium LaunchLab operations.

## Overview

The LaunchLab service has been migrated from the `radium-sdk` folder and integrated into the main project structure following the same architectural patterns used by PumpFun and Jupiter services.

## Architecture

### Files Structure
```
src/
├── types/launchlab.types.ts          # Type definitions
├── services/launchlab/
│   ├── launchlab.service.ts          # Core service implementation
│   └── README.md                     # This file
├── controllers/launchlab/
│   └── launchlab.controller.ts       # HTTP request handlers
└── routes/launchlab/
    └── launchlab.route.ts            # Route definitions
```

### Key Components

1. **LaunchLabService**: Core service that handles Raydium SDK interactions
2. **LaunchLabController**: HTTP request/response handling with validation
3. **LaunchLabRoutes**: Express route definitions
4. **Types**: TypeScript interfaces for type safety

## API Endpoints

### 1. Get Quote
**POST** `/api/launchlab/quote`

Get a price quote for LaunchLab trading.

**Request Body:**
```json
{
  "poolId": "string",     // LaunchLab pool ID (44 characters)
  "amount": "string",     // Amount to trade
  "direction": "buy|sell" // Trade direction
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "inputMint": "string",
    "outputMint": "string",
    "inputAmount": "string",
    "outputAmount": "string",
    "priceImpact": 0.01,
    "fee": "string",
    "platformFee": "string",
    "netOutputAmount": "string",
    "newPrice": "string",
    "inputAmountHuman": "1.0",
    "outputAmountHuman": "1000.0",
    "feeHuman": "0.0",
    "platformFeeHuman": "1.0",
    "netOutputAmountHuman": "999.0",
    "priceImpactPercent": "1.00%",
    "newPriceHuman": "0.001000000",
    "platformFeePercent": "0.1%"
  },
  "timestamp": 1234567890
}
```

### 2. Execute Swap
**POST** `/api/launchlab/swap`

Execute a LaunchLab swap transaction.

**Request Body:**
```json
{
  "poolId": "string",           // LaunchLab pool ID
  "amount": "string",           // Amount to trade
  "walletAddress": "string",    // User wallet address
  "walletId": "string",         // Privy wallet identifier
  "direction": "buy|sell",      // Trade direction
  "slippage": 0.01,            // Slippage tolerance (0-1)
  "priorityFee": 200000,       // Optional: Priority fee in microlamports
  "computeUnitLimit": 300000   // Optional: Compute unit limit
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "txid": "string",
    "inputAmount": "string",
    "outputAmount": "string",
    "fee": "string",
    "priceImpact": 0.01,
    "newPrice": "string",
    "inputAmountHuman": "1.0",
    "outputAmountHuman": "1000.0",
    "feeHuman": "0.0",
    "priceImpactPercent": "1.00%",
    "newPriceHuman": "0.001000000"
  },
  "timestamp": 1234567890
}
```

### 3. Get Pool Info
**GET** `/api/launchlab/pool/:poolId`

Get detailed information about a LaunchLab pool.

**Response:**
```json
{
  "success": true,
  "data": {
    "poolId": "string",
    "tokenMint": "string",
    "quoteMint": "string",
    "status": "active|migrated",
    "supply": "string",
    "totalSellA": "string",
    "totalFundRaisingB": "string",
    "currentFundRaised": "string",
    "realA": "string",
    "realB": "string",
    "migrationProgress": 75.5,
    "currentPrice": "string",
    "marketCap": "string",
    "tokenName": "string",
    "tokenSymbol": "string",
    "tokenDescription": "string",
    "supplyHuman": "1000000.000000",
    "totalSellAHuman": "800000.000000",
    "totalFundRaisingBHuman": "1000.000000000",
    "currentFundRaisedHuman": "755.000000000",
    "realAHuman": "200000.000000",
    "realBHuman": "755.000000000",
    "currentPriceHuman": "0.003775000",
    "marketCapHuman": "3775.000000000"
  },
  "timestamp": 1234567890
}
```

## Features

### Core Functionality
- **Quote API**: Get price quotes for buy/sell operations
- **Swap API**: Execute buy/sell transactions with Privy integration
- **Pool Information**: Retrieve detailed pool data and metadata

### Technical Features
- **Privy Integration**: Seamless wallet integration for transaction signing
- **Error Handling**: Comprehensive error handling with specific error codes
- **Validation**: Input validation for all parameters
- **Caching**: Pool information caching for performance
- **Type Safety**: Full TypeScript support with detailed type definitions

### Architectural Consistency
- Follows the same patterns as existing PumpFun and Jupiter services
- Consistent error handling and response formats
- Modular structure with separation of concerns
- Production-ready code with proper logging

## Dependencies

- `@raydium-io/raydium-sdk-v2`: Raydium SDK for LaunchLab operations
- `@solana/web3.js`: Solana blockchain interactions
- `@solana/spl-token`: SPL token operations
- `bs58`: Base58 encoding/decoding
- `bn.js`: Big number operations

## Configuration

The service uses the existing configuration from `src/config/index.ts`:
- RPC URL for Solana connection
- Platform fee configuration
- Default slippage settings

## Error Handling

The service includes comprehensive error handling with specific error codes:
- `LAUNCHLAB_INITIALIZATION_FAILED`
- `LAUNCHLAB_QUOTE_FAILED`
- `LAUNCHLAB_SWAP_FAILED`
- `LAUNCHLAB_POOL_NOT_FOUND`
- `LAUNCHLAB_POOL_MIGRATED`
- `LAUNCHLAB_INVALID_AMOUNT`
- `LAUNCHLAB_INVALID_DIRECTION`
- `LAUNCHLAB_PRIVY_NOT_CONFIGURED`
- `LAUNCHLAB_WALLET_NOT_CONFIGURED`

## Usage Example

```typescript
import { launchLabService } from './services/launchlab/launchlab.service';

// Get a quote
const quote = await launchLabService.getQuote({
  poolId: 'PoolIdHere...',
  amount: '1.0',
  direction: 'buy'
});

// Execute a swap
const swap = await launchLabService.executeSwap({
  poolId: 'PoolIdHere...',
  amount: '1.0',
  walletAddress: 'WalletAddressHere...',
  walletId: 'PrivyWalletId',
  direction: 'buy',
  slippage: 0.01
});
```

## Migration Notes

This service was migrated from the `radium-sdk` folder with the following changes:
- Adapted to match existing project architecture
- Simplified for API-only functionality (no UI components)
- Integrated with existing Privy service
- Added comprehensive TypeScript types
- Implemented consistent error handling
- Added proper logging and validation

The migration focused specifically on quote and swap API functionality as requested.
