import {
  Connection,
  PublicKey,
  VersionedTransaction,
  Transaction,
  TransactionInstruction,
  ComputeBudgetProgram,
  SystemProgram
} from '@solana/web3.js';
import {
  getAssociatedTokenAddress,
  createAssociatedTokenAccountIdempotentInstruction
} from '@solana/spl-token';
import {
  Raydium
} from '@raydium-io/raydium-sdk-v2';
import Decimal from 'decimal.js';
import BN from 'bn.js';
import { signAndSendTransactionWithPrivy } from '../../utils/privy-signer';
import {
  toHumanAmount,
  parseUserAmount,
  formatPercentage
} from '../../utils/decimals';

import {
  LaunchLabQuoteRequest,
  LaunchLabQuoteResponse,
  LaunchLabSwapRequest,
  LaunchLabTradeResponse,
  LaunchLabPoolInfo,
  PlatformConfig,
  ILaunchLabService
} from '../../types/launchlab.types';
import { config } from '../../config';

/**
 * LaunchLab Service for Raydium LaunchLab operations
 * Focuses on quote and swap API functionality only
 */
export class LaunchLabService implements ILaunchLabService {
  private connection: Connection;
  private raydium: Raydium | null = null;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  // Cache for platform configurations and pool data
  private platformCache: Map<string, PlatformConfig> = new Map();
  private poolCache: Map<string, LaunchLabPoolInfo> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private readonly INITIALIZATION_TIMEOUT = 30000; // 30 seconds

  // LaunchLab program constants
  private readonly RAYDIUM_PROGRAMS = config.raydiumPrograms;

  constructor() {
    // Create optimized connection
    this.connection = new Connection(config.rpcUrl || 'https://api.mainnet-beta.solana.com', {
      commitment: 'confirmed',
      httpHeaders: {
        'Content-Type': 'application/json',
      },
      fetch: (url, options) => {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        return fetch(url, {
          ...options,
          signal: controller.signal,
        }).finally(() => {
          clearTimeout(timeoutId);
        });
      },
    });

    console.log('LaunchLab service initialized', {
      rpcUrl: config.rpcUrl?.includes('helius') ? 'Helius RPC' : 'Custom RPC',
      network: 'mainnet-beta'
    });
  }

  /**
   * Initialize the Raydium SDK for LaunchLab operations
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this.performInitialization();

    try {
      await this.initializationPromise;
    } finally {
      this.initializationPromise = null;
    }
  }

  /**
   * Perform the actual initialization
   */
  private async performInitialization(): Promise<void> {
    const startTime = Date.now();

    try {
      // Initialize Raydium SDK with minimal configuration
      this.raydium = await Raydium.load({
        connection: this.connection,
        owner: undefined, // Will be set per operation
        disableLoadToken: false,
      });

      this.isInitialized = true;

      const duration = Date.now() - startTime;
      console.log('LaunchLab SDK initialized successfully', {
        duration,
        network: 'mainnet-beta'
      });

    } catch (error) {
      this.isInitialized = false;
      console.error('Failed to initialize LaunchLab SDK:', error);
      throw new Error(`Failed to initialize LaunchLab SDK: ${(error as Error).message}`);
    }
  }

  /**
   * Get quote for LaunchLab trading
   */
  async getQuote(request: LaunchLabQuoteRequest): Promise<LaunchLabQuoteResponse> {
    try {
      await this.ensureInitialized();

      if (!this.raydium) {
        throw new Error('Raydium SDK not initialized');
      }

      // Get pool info and validate
      const poolInfo = await this.getPoolInfo(request.poolId);

      if (poolInfo.status !== 'active') {
        throw new Error('Pool has migrated to regular AMM - use regular swap endpoints');
      }

      // Determine input/output mints based on direction
      const solMint = 'So11111111111111111111111111111111111111112';
      const inputMint = request.direction === 'buy' ? solMint : poolInfo.tokenMint;
      const outputMint = request.direction === 'buy' ? poolInfo.tokenMint : solMint;

      // Get token metadata
      const [inputTokenInfo, outputTokenInfo] = await Promise.all([
        this.raydium.token.getTokenInfo(inputMint),
        this.raydium.token.getTokenInfo(outputMint)
      ]);

      // Parse input amount using real token decimals
      const parsedAmount = parseUserAmount(request.amount, {
        decimals: inputTokenInfo.decimals,
        symbol: inputTokenInfo.symbol,
        address: inputMint,
        name: inputTokenInfo.name
      } as any);

      if (!parsedAmount.isValid) {
        throw new Error(parsedAmount.error || 'Invalid input amount');
      }

      // Calculate quote using official Raydium SDK methods
      const { outputAmount, priceImpact, fee } = await this.calculateQuoteUsingOfficialSDK(
        request.poolId,
        parsedAmount.baseUnits,
        request.direction,
        request.walletAddress
      );

      // Extract platform fee portion from the total fee for display purposes
      const platformFeePercentage = config.launchLab.platformFeePercentage;
      const platformFeeRate = Math.floor(platformFeePercentage * 100); // Convert to basis points * 100
      const PROTOCOL_FEE_RATE = 2500; // 0.25% protocol fee
      const totalFeeRate = PROTOCOL_FEE_RATE + platformFeeRate; // Protocol fee + platform fee

      // Calculate platform fee portion of the total fee
      const platformFeeAmount = Math.floor(parseFloat(fee) * (platformFeeRate / totalFeeRate));
      const platformFee = platformFeeAmount.toString();

      // Net output amount is the actual output (fees already deducted from input)
      const netOutputAmount = outputAmount;

      // Calculate new price after trade
      const newPrice = await this.calculateNewPrice(request.poolId, parsedAmount.baseUnits, request.direction);

      const response: LaunchLabQuoteResponse = {
        inputMint,
        outputMint,
        inputAmount: parsedAmount.baseUnits,
        outputAmount,
        priceImpact,
        fee,
        platformFee,
        netOutputAmount,
        newPrice,

        // Human-readable fields
        inputAmountHuman: toHumanAmount(parsedAmount.baseUnits, inputTokenInfo.decimals),
        outputAmountHuman: toHumanAmount(outputAmount, outputTokenInfo.decimals),
        feeHuman: toHumanAmount(fee, inputTokenInfo.decimals),
        platformFeeHuman: toHumanAmount(platformFee, outputTokenInfo.decimals),
        netOutputAmountHuman: toHumanAmount(netOutputAmount, outputTokenInfo.decimals),
        priceImpactPercent: formatPercentage(priceImpact),
        newPriceHuman: toHumanAmount(newPrice, 9),
        platformFeePercent: `${platformFeePercentage}%`,
      };

      console.log('Quote calculated successfully', {
        direction: request.direction,
        inputAmount: response.inputAmountHuman,
        outputAmount: response.outputAmountHuman
      });

      return response;

    } catch (error) {
      console.error('Failed to get LaunchLab quote:', error);
      throw error;
    }
  }

  /**
   * Execute swap operation
   */
  async executeSwap(request: LaunchLabSwapRequest): Promise<LaunchLabTradeResponse> {
    try {
      await this.ensureInitialized();

      if (!this.raydium) {
        throw new Error('Raydium SDK not initialized');
      }

      // Set the owner for this operation
      await this.setOwnerForOperation(request.walletAddress);

      // Get pool info and validate
      const poolInfo = await this.getPoolInfo(request.poolId);

      if (poolInfo.status !== 'active') {
        throw new Error('Pool has migrated to regular AMM - use regular swap endpoints');
      }

      console.log('Executing LaunchLab swap', {
        poolId: request.poolId,
        direction: request.direction,
        amount: request.amount,
        walletAddress: request.walletAddress
      });

      if (request.direction === 'buy') {
        return await this.executeBuySwap(request, poolInfo);
      } else {
        return await this.executeSellSwap(request, poolInfo);
      }

    } catch (error) {
      console.error('Failed to execute LaunchLab swap:', error);
      throw error;
    }
  }

  /**
   * Get LaunchLab pool information
   */
  async getPoolInfo(poolId: string): Promise<LaunchLabPoolInfo> {
    try {
      await this.ensureInitialized();

      // Check cache first
      const cacheKey = `pool_${poolId}`;
      const now = Date.now();
      if (this.poolCache.has(cacheKey) && this.cacheExpiry.get(cacheKey)! > now) {
        return this.poolCache.get(cacheKey)!;
      }

      if (!this.raydium) {
        throw new Error('Raydium SDK not initialized');
      }

      // Get pool data from Raydium SDK
      const poolInfo = await this.raydium.launchpad.getRpcPoolInfo({ poolId: new PublicKey(poolId) });

      // Process pool data and create response
      const result = await this.processPoolInfo(poolInfo, poolId);

      // Cache the result
      this.poolCache.set(cacheKey, result);
      this.cacheExpiry.set(cacheKey, now + this.CACHE_DURATION);

      return result;

    } catch (error) {
      console.error('Failed to get LaunchLab pool info:', error);
      throw error;
    }
  }

  /**
   * Cleanup method
   */
  async cleanup(): Promise<void> {
    try {
      console.log('Cleaning up LaunchLab service resources');

      // Clear caches
      this.platformCache.clear();
      this.poolCache.clear();
      this.cacheExpiry.clear();

      // Reset initialization state
      this.isInitialized = false;
      this.initializationPromise = null;
      this.raydium = null;

      console.log('LaunchLab service cleanup completed');
    } catch (error) {
      console.error('Error during LaunchLab service cleanup:', error);
    }
  }

  /**
   * Private helper methods
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  /**
   * Set the owner for the current operation
   */
  private async setOwnerForOperation(walletAddress: string): Promise<void> {
    try {
      if (!this.raydium) {
        throw new Error('Raydium SDK not initialized');
      }

      const ownerPublicKey = new PublicKey(walletAddress);

      // Reinitialize with the owner if needed
      if (typeof (this.raydium as any).setOwner === 'function') {
        await (this.raydium as any).setOwner(ownerPublicKey);
      } else {
        await this.reinitializeWithOwner(ownerPublicKey);
      }

      console.log('Owner set successfully for LaunchLab operation');
    } catch (error) {
      console.error('Failed to set owner for operation:', error);
      throw error;
    }
  }

  /**
   * Reinitialize SDK with owner
   */
  private async reinitializeWithOwner(owner: PublicKey): Promise<void> {
    try {
      this.raydium = await Raydium.load({
        connection: this.connection,
        owner,
        disableLoadToken: false,
      });
    } catch (error) {
      console.error('Failed to reinitialize SDK with owner:', error);
      throw error;
    }
  }

  /**
   * Parse user amount to base units
   */
  private parseUserAmount(amount: string, decimals: number): string {
    try {
      const numAmount = parseFloat(amount);
      if (isNaN(numAmount) || numAmount <= 0) {
        throw new Error('Invalid amount');
      }
      return Math.floor(numAmount * Math.pow(10, decimals)).toString();
    } catch (error) {
      throw new Error(`Invalid amount: ${amount}`);
    }
  }

  /**
   * Convert base units to human readable amount
   */
  private toHumanAmount(baseUnits: string, decimals: number): string {
    try {
      const amount = parseFloat(baseUnits) / Math.pow(10, decimals);
      return amount.toFixed(decimals);
    } catch (error) {
      return '0';
    }
  }

  /**
   * Calculate quote using official Raydium SDK LaunchLab methods
   */
  private async calculateQuoteUsingOfficialSDK(
    poolId: string,
    inputAmount: string,
    direction: 'buy' | 'sell',
    walletAddress?: string
  ): Promise<{ outputAmount: string; priceImpact: number; fee: string }> {
    try {
      if (!this.raydium) {
        throw new Error('Raydium SDK not initialized');
      }

      // Set wallet address for accurate fee calculation if provided
      if (walletAddress) {
        try {
          await this.setOwnerForOperation(walletAddress);
          console.log('Set wallet address for quote calculation:', walletAddress.substring(0, 8) + '...');
        } catch (error) {
          console.warn('Failed to set wallet address for quote, using default calculation:', error);
        }
      }

      // Get pool RPC info for SDK calculations
      const poolRpcInfo = await this.raydium.launchpad.getRpcPoolInfo({
        poolId: new PublicKey(poolId)
      });

      // Get platform info for accurate fee calculation
      const platformData = await this.connection.getAccountInfo(poolRpcInfo.platformId);
      if (!platformData) {
        throw new Error('Platform configuration not found');
      }

      // Calculate platform fee rate using environment configuration
      const platformFeePercentage = config.launchLab.platformFeePercentage;
      const platformFeeRate = Math.floor(platformFeePercentage * 100); // Convert to basis points * 100

      let outputAmount: string;
      let priceImpact: number;
      let fee: string;

      if (direction === 'buy') {
        // Use official Raydium SDK buyToken method for accurate quote calculation
        try {
          const { extInfo } = await this.raydium.launchpad.buyToken({
            programId: new PublicKey(this.RAYDIUM_PROGRAMS.LAUNCHPAD),
            mintA: new PublicKey(poolRpcInfo.mintA.toString()),
            slippage: new BN(100), // 1% slippage for quote calculation
            configInfo: poolRpcInfo.configInfo,
            platformFeeRate: new BN(platformFeeRate),
            txVersion: 'V0' as any,
            buyAmount: new BN(inputAmount),
            computeBudgetConfig: undefined, // No compute budget for quotes
          });

          outputAmount = extInfo.outAmount.toString();

          // Calculate fee based on input amount and platform fee rate
          const PROTOCOL_FEE_RATE = 2500; // 0.25% protocol fee
          const totalFeeRate = PROTOCOL_FEE_RATE + platformFeeRate;
          const FEE_RATE_DENOMINATOR = new Decimal(1000000);
          const inputAmountDecimal = new Decimal(inputAmount);
          const feeAmount = inputAmountDecimal.mul(totalFeeRate).div(FEE_RATE_DENOMINATOR);
          fee = feeAmount.ceil().toString();

          // Calculate price impact (simplified for quote)
          priceImpact = 0.01; // 1% default for quotes

        } catch (sdkError) {
          console.warn('SDK buyToken failed for quote, using fallback calculation:', sdkError);
          throw sdkError;
        }
      } else {
        // Use official Raydium SDK sellToken method for accurate quote calculation
        try {
          const { extInfo } = await this.raydium.launchpad.sellToken({
            programId: new PublicKey(this.RAYDIUM_PROGRAMS.LAUNCHPAD),
            mintA: new PublicKey(poolRpcInfo.mintA.toString()),
            slippage: new BN(100), // 1% slippage for quote calculation
            configInfo: poolRpcInfo.configInfo,
            platformFeeRate: new BN(platformFeeRate),
            txVersion: 'V0' as any,
            sellAmount: new BN(inputAmount),
            computeBudgetConfig: undefined, // No compute budget for quotes
          });

          outputAmount = extInfo?.outAmount?.toString() || '0';

          // Calculate fee based on input amount and platform fee rate
          const PROTOCOL_FEE_RATE = 2500; // 0.25% protocol fee
          const totalFeeRate = PROTOCOL_FEE_RATE + platformFeeRate;
          const FEE_RATE_DENOMINATOR = new Decimal(1000000);
          const inputAmountDecimal = new Decimal(inputAmount);
          const feeAmount = inputAmountDecimal.mul(totalFeeRate).div(FEE_RATE_DENOMINATOR);
          fee = feeAmount.ceil().toString();

          // Calculate price impact (simplified for quote)
          priceImpact = 0.01; // 1% default for quotes

        } catch (sdkError) {
          console.warn('SDK sellToken failed for quote, using fallback calculation:', sdkError);
          throw sdkError;
        }
      }

      console.log('Official SDK quote calculation completed', {
        poolId: poolId.substring(0, 8) + '...',
        direction,
        inputAmount,
        outputAmount,
        fee,
        priceImpact: `${(priceImpact * 100).toFixed(2)}%`,
        walletProvided: !!walletAddress
      });

      return {
        outputAmount,
        priceImpact: Math.abs(priceImpact),
        fee
      };

    } catch (error) {
      console.error('Error calculating quote using SDK:', error);
      // Fallback to simple calculation with basic fee
      const inputAmountDecimal = new Decimal(inputAmount);
      const basicFeeRate = new Decimal(5000); // 0.5% default fee
      const FEE_RATE_DENOMINATOR = new Decimal(1000000);
      const feeAmount = inputAmountDecimal.mul(basicFeeRate).div(FEE_RATE_DENOMINATOR);
      const inputAfterFee = inputAmountDecimal.sub(feeAmount.ceil());

      const mockOutputAmount = direction === 'buy'
        ? Math.floor(parseFloat(inputAfterFee.toString()) * 1000).toString()
        : Math.floor(parseFloat(inputAfterFee.toString()) / 1000).toString();

      return {
        outputAmount: mockOutputAmount,
        priceImpact: 0.01,
        fee: feeAmount.ceil().toString()
      };
    }
  }

  /**
   * Calculate new price after trade
   */
  private async calculateNewPrice(
    poolId: string,
    inputAmount: string,
    direction: 'buy' | 'sell'
  ): Promise<string> {
    // Mock implementation - in real scenario, this would calculate based on bonding curve
    return '0.001'; // Mock price
  }

  /**
   * Process pool info from SDK response
   */
  private async processPoolInfo(poolInfo: any, poolId: string): Promise<LaunchLabPoolInfo> {
    // Determine pool status
    const status = poolInfo.status === 0 ? 'active' : 'migrated';

    // Calculate migration progress
    const migrationProgress = this.calculateMigrationProgress(
      poolInfo.realB.toString(),
      poolInfo.totalFundRaisingB.toString()
    );

    // Calculate current price
    const currentPrice = this.calculateCurrentPrice(
      poolInfo.realA.toString(),
      poolInfo.realB.toString()
    );

    // Get token metadata
    const tokenMint = poolInfo.mintA.toString();
    const quoteMint = poolInfo.mintB.toString();

    let tokenMetadata;
    try {
      tokenMetadata = await this.raydium!.token.getTokenInfo(tokenMint);
    } catch (error) {
      tokenMetadata = {
        symbol: tokenMint.substring(0, 8),
        name: `Token ${tokenMint.substring(0, 8)}`,
        decimals: 6
      };
    }

    return {
      poolId,
      tokenMint,
      quoteMint,
      status,
      supply: poolInfo.supply.toString(),
      totalSellA: poolInfo.totalSellA.toString(),
      totalFundRaisingB: poolInfo.totalFundRaisingB.toString(),
      currentFundRaised: poolInfo.realB.toString(),
      realA: poolInfo.realA.toString(),
      realB: poolInfo.realB.toString(),
      migrationProgress,
      currentPrice,
      marketCap: (parseFloat(currentPrice) * parseFloat(poolInfo.supply.toString())).toString(),

      // Token metadata
      tokenName: tokenMetadata.name,
      tokenSymbol: tokenMetadata.symbol,
      tokenDescription: `${tokenMetadata.name} launched on Raydium LaunchLab`,

      // Human-readable fields
      supplyHuman: this.toHumanAmount(poolInfo.supply.toString(), tokenMetadata.decimals),
      totalSellAHuman: this.toHumanAmount(poolInfo.totalSellA.toString(), tokenMetadata.decimals),
      totalFundRaisingBHuman: this.toHumanAmount(poolInfo.totalFundRaisingB.toString(), 9),
      currentFundRaisedHuman: this.toHumanAmount(poolInfo.realB.toString(), 9),
      realAHuman: this.toHumanAmount(poolInfo.realA.toString(), tokenMetadata.decimals),
      realBHuman: this.toHumanAmount(poolInfo.realB.toString(), 9),
      currentPriceHuman: this.toHumanAmount(currentPrice, 9),
      marketCapHuman: this.toHumanAmount((parseFloat(currentPrice) * parseFloat(poolInfo.supply.toString())).toString(), 9)
    };
  }

  /**
   * Calculate migration progress
   */
  private calculateMigrationProgress(currentFundRaised: string, totalFundRaising: string): number {
    try {
      const current = parseFloat(currentFundRaised);
      const total = parseFloat(totalFundRaising);
      return total > 0 ? Math.min((current / total) * 100, 100) : 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Calculate current price
   */
  private calculateCurrentPrice(realA: string, realB: string): string {
    try {
      const tokenReserves = parseFloat(realA);
      const solReserves = parseFloat(realB);
      return tokenReserves > 0 ? (solReserves / tokenReserves).toString() : '0';
    } catch (error) {
      return '0';
    }
  }

  /**
   * Enhance transaction with proper instruction ordering and missing instructions
   */
  private async enhanceTransactionWithFeesAndBudget(
    transaction: VersionedTransaction | Transaction | any,
    request: LaunchLabSwapRequest,
    inputAmount: string,
    direction: 'buy' | 'sell'
  ): Promise<VersionedTransaction | Transaction> {
    try {
      const instructions: TransactionInstruction[] = [];
      const userWallet = new PublicKey(request.walletAddress);

      // 1. FIRST: SetComputeUnitLimit (must come before SetComputeUnitPrice)
      if (request.priorityFee && request.priorityFee > 0) {
        const computeUnitLimit = request.computeUnitLimit || 200000; // Default 200k CU
        const computeUnitLimitInstruction = ComputeBudgetProgram.setComputeUnitLimit({
          units: computeUnitLimit
        });
        instructions.push(computeUnitLimitInstruction);

        // 2. SECOND: SetComputeUnitPrice (priority fee)
        const computeUnitPriceInstruction = ComputeBudgetProgram.setComputeUnitPrice({
          microLamports: request.priorityFee
        });
        instructions.push(computeUnitPriceInstruction);

        console.log('Added compute budget instructions in correct order', {
          priorityFee: request.priorityFee,
          computeUnitLimit,
          direction
        });
      }

      // 3. THIRD: Associated Token Account creation (createIdempotent)
      if (direction === 'buy') {
        // For buy operations, we need to ensure the user's token account exists
        // Get the token mint from the pool info
        const poolInfo = await this.getPoolInfo(request.poolId);
        const tokenMint = new PublicKey(poolInfo.tokenMint);

        // Calculate the associated token account address
        const userTokenAccount = await getAssociatedTokenAddress(
          tokenMint,
          userWallet
        );

        // Create the associated token account instruction (idempotent - won't fail if exists)
        const createTokenAccountInstruction = createAssociatedTokenAccountIdempotentInstruction(
          userWallet, // payer
          userTokenAccount, // associated token account
          userWallet, // owner
          tokenMint // mint
        );
        instructions.push(createTokenAccountInstruction);

        console.log('Added createAssociatedTokenAccountIdempotent instruction', {
          tokenMint: tokenMint.toString(),
          userTokenAccount: userTokenAccount.toString(),
          direction
        });
      }

      // 4. Get original transaction instructions and analyze them
      let originalInstructions: TransactionInstruction[] = [];
      let tokenInitializeInstructions: TransactionInstruction[] = [];
      let swapInstructions: TransactionInstruction[] = [];

      if (transaction instanceof Transaction) {
        originalInstructions = transaction.instructions;
      } else if (transaction && transaction.instructions && Array.isArray(transaction.instructions)) {
        originalInstructions = transaction.instructions;
      } else if (transaction && transaction.message && transaction.message.compiledInstructions) {
        // For VersionedTransaction, we'll return the original for now
        // In production, you'd need to decompile and rebuild
        console.log('VersionedTransaction detected - returning original with note about enhancement needed');
        return transaction;
      }

      // 5. Separate Token Program initializeAccount instructions from swap instructions
      // This ensures createIdempotent comes AFTER initializeAccount instructions
      originalInstructions.forEach(instruction => {
        // Check if this is a Token Program initializeAccount instruction
        if (instruction.programId.equals(new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA')) &&
            instruction.data.length > 0 && instruction.data[0] === 18) { // 18 = InitializeAccount instruction
          tokenInitializeInstructions.push(instruction);
          console.log('Found Token Program initializeAccount instruction');
        } else {
          swapInstructions.push(instruction);
        }
      });

      // 6. CRITICAL: Platform fee transfer instruction calculation and verification
      let platformFeeInstruction: TransactionInstruction | null = null;

      if (config.platformFeeWallet && config.launchLab.platformFeePercentage > 0) {
        const platformFeePercentage = config.launchLab.platformFeePercentage;

        // Enhanced platform fee calculation for both buy and sell operations
        let platformFeeLamports = 0;

        if (direction === 'buy') {
          // For buy operations: charge platform fee on SOL input
          const inputAmountLamports = parseFloat(inputAmount);
          platformFeeLamports = Math.floor(inputAmountLamports * (platformFeePercentage / 100));

          console.log('Buy operation platform fee calculation', {
            inputAmountSOL: inputAmountLamports / 1e9,
            inputAmountLamports,
            platformFeePercentage,
            platformFeeLamports,
            platformFeeSOL: platformFeeLamports / 1e9
          });

        } else if (direction === 'sell') {
          // For sell operations: charge platform fee on expected SOL output
          // We need to estimate the SOL output to calculate the platform fee
          try {
            // Get a rough estimate of SOL output for platform fee calculation
            const poolInfo = await this.getPoolInfo(request.poolId);
            const tokenMetadata = await this.raydium!.token.getTokenInfo(poolInfo.tokenMint);

            // Simple estimation: use current price * token amount
            const currentPricePerToken = parseFloat(poolInfo.currentPrice);
            const tokenAmountHuman = parseFloat(inputAmount) / Math.pow(10, tokenMetadata.decimals);
            const estimatedSOLOutput = tokenAmountHuman * currentPricePerToken;
            const estimatedSOLOutputLamports = Math.floor(estimatedSOLOutput * 1e9);

            platformFeeLamports = Math.floor(estimatedSOLOutputLamports * (platformFeePercentage / 100));

            console.log('Sell operation platform fee calculation', {
              tokenAmountHuman,
              currentPricePerToken,
              estimatedSOLOutput,
              estimatedSOLOutputLamports,
              platformFeePercentage,
              platformFeeLamports,
              platformFeeSOL: platformFeeLamports / 1e9
            });

          } catch (error) {
            console.warn('Failed to calculate platform fee for sell operation, skipping platform fee', {
              error: error instanceof Error ? error.message : String(error)
            });
            platformFeeLamports = 0;
          }
        }

        if (platformFeeLamports > 0) {
          platformFeeInstruction = SystemProgram.transfer({
            fromPubkey: userWallet,
            toPubkey: config.platformFeeWallet,
            lamports: platformFeeLamports
          });

          console.log('✅ PLATFORM FEE VERIFICATION: Platform fee transfer instruction created', {
            platformFeeLamports,
            platformFeeSOL: platformFeeLamports / 1e9,
            platformFeePercentage,
            direction,
            platformFeeWallet: config.platformFeeWallet.toString(),
            instructionCreated: true
          });
        } else {
          console.log('❌ PLATFORM FEE VERIFICATION: No platform fee instruction created', {
            platformFeeLamports,
            platformFeePercentage,
            direction,
            reason: 'Platform fee amount is 0 or calculation failed'
          });
        }
      } else {
        console.log('❌ PLATFORM FEE VERIFICATION: Platform fee not configured', {
          hasPlatformFeeWallet: !!config.platformFeeWallet,
          platformFeePercentage: config.launchLab.platformFeePercentage,
          direction
        });
      }

      // 7. CORRECT INSTRUCTION ORDERING: Build final instruction sequence
      const finalInstructions: TransactionInstruction[] = [
        // 1. Compute budget instructions (SetComputeUnitLimit, SetComputeUnitPrice)
        ...instructions,

        // 2. Token Program initializeAccount instructions (if any)
        ...tokenInitializeInstructions,

        // 3. Associated Token Account createIdempotent (already in instructions for buy operations)
        // Note: createIdempotent is already added to instructions array above for buy operations

        // 4. Swap instructions (LaunchLab program instructions)
        ...swapInstructions,

        // 5. LAST: Platform fee transfer instruction (must be the final instruction)
        ...(platformFeeInstruction ? [platformFeeInstruction] : [])
      ];

      console.log('✅ INSTRUCTION ORDERING VERIFICATION: Final instruction sequence', {
        totalInstructions: finalInstructions.length,
        computeBudgetInstructions: request.priorityFee ? 2 : 0,
        tokenInitializeInstructions: tokenInitializeInstructions.length,
        tokenAccountCreation: direction === 'buy' ? 1 : 0,
        swapInstructions: swapInstructions.length,
        platformFeeInstruction: platformFeeInstruction ? 1 : 0,
        direction,
        instructionBreakdown: {
          '1_compute_budget': request.priorityFee ? 2 : 0,
          '2_token_initialize': tokenInitializeInstructions.length,
          '3_token_account_creation': direction === 'buy' ? 1 : 0,
          '4_swap_instructions': swapInstructions.length,
          '5_platform_fee_transfer': platformFeeInstruction ? 1 : 0
        }
      });

      // 8. Create enhanced transaction
      if (transaction instanceof Transaction) {
        const enhancedTransaction = new Transaction();
        enhancedTransaction.recentBlockhash = transaction.recentBlockhash;
        enhancedTransaction.feePayer = transaction.feePayer;
        enhancedTransaction.lastValidBlockHeight = transaction.lastValidBlockHeight;

        // Add all instructions in the correct order
        finalInstructions.forEach(ix => enhancedTransaction.add(ix));

        console.log('Enhanced transaction with verified correct instruction ordering', {
          totalInstructions: finalInstructions.length,
          direction,
          platformFeeAdded: !!platformFeeInstruction
        });

        return enhancedTransaction;
      } else {
        // Handle other transaction types
        const legacyTransaction = transaction as Transaction;
        const enhancedTransaction = new Transaction();
        enhancedTransaction.recentBlockhash = legacyTransaction.recentBlockhash;
        enhancedTransaction.feePayer = legacyTransaction.feePayer;
        enhancedTransaction.lastValidBlockHeight = legacyTransaction.lastValidBlockHeight;

        finalInstructions.forEach(ix => enhancedTransaction.add(ix));
        return enhancedTransaction;
      }

    } catch (error) {
      console.error('Failed to enhance transaction with fees and budget:', error);

      // Return original transaction if enhancement fails
      console.warn('Failed to enhance transaction, using original', {
        error: error instanceof Error ? error.message : String(error)
      });
      return transaction;
    }
  }

  /**
   * Execute buy swap
   */
  private async executeBuySwap(
    request: LaunchLabSwapRequest,
    poolInfo: LaunchLabPoolInfo
  ): Promise<LaunchLabTradeResponse> {
    // Parse SOL amount to spend
    const parsedAmount = parseUserAmount(request.amount, {
      decimals: 9,
      symbol: 'SOL',
      address: 'So11111111111111111111111111111111111111112',
      name: 'Solana'
    } as any);

    if (!parsedAmount.isValid) {
      throw new Error(parsedAmount.error || 'Invalid SOL amount');
    }

    // Get pool RPC info
    const poolRpcInfo = await this.raydium!.launchpad.getRpcPoolInfo({
      poolId: new PublicKey(request.poolId)
    });

    // Calculate platform fee rate using environment configuration
    const platformFeePercentage = config.launchLab.platformFeePercentage;
    const platformFeeRate = Math.floor(platformFeePercentage * 100); // Convert to basis points * 100 format

    // Create buy transaction without compute budget config (we handle it manually)
    const { transaction, extInfo } = await this.raydium!.launchpad.buyToken({
      programId: new PublicKey(this.RAYDIUM_PROGRAMS.LAUNCHPAD),
      mintA: new PublicKey(poolInfo.tokenMint),
      slippage: new BN(Math.floor(request.slippage * 10000)),
      configInfo: poolRpcInfo.configInfo,
      platformFeeRate: new BN(platformFeeRate),
      txVersion: 'V0' as any,
      buyAmount: new BN(parsedAmount.baseUnits),
      computeBudgetConfig: undefined, // We handle compute budget manually in enhanceTransaction
    });

    // Enhance transaction with compute budget instructions and platform fee transfer
    const enhancedTransaction = await this.enhanceTransactionWithFeesAndBudget(
      transaction,
      request,
      parsedAmount.baseUnits,
      'buy'
    );

    // Execute enhanced transaction with Privy
    const txid = await signAndSendTransactionWithPrivy({
      walletId: request.walletId,
      transaction: enhancedTransaction as any,
      connection: this.connection
    });

    const outputAmount = extInfo.outAmount.toString();

    // Get token metadata for proper decimal handling
    const tokenMetadata = await this.raydium!.token.getTokenInfo(poolInfo.tokenMint);

    return {
      txid,
      inputAmount: parsedAmount.baseUnits,
      outputAmount,
      fee: '0',
      priceImpact: 0.01,
      newPrice: poolInfo.currentPrice,

      // Human-readable fields with correct decimals
      inputAmountHuman: toHumanAmount(parsedAmount.baseUnits, 9), // SOL has 9 decimals
      outputAmountHuman: toHumanAmount(outputAmount, tokenMetadata.decimals), // Use actual token decimals
      feeHuman: '0',
      priceImpactPercent: '1.00%',
      newPriceHuman: toHumanAmount(poolInfo.currentPrice, 9)
    };
  }

  /**
   * Execute sell swap
   */
  private async executeSellSwap(
    request: LaunchLabSwapRequest,
    poolInfo: LaunchLabPoolInfo
  ): Promise<LaunchLabTradeResponse> {
    // Get token metadata
    const tokenMetadata = await this.raydium!.token.getTokenInfo(poolInfo.tokenMint);

    // Parse token amount to sell
    const parsedAmount = parseUserAmount(request.amount, {
      decimals: tokenMetadata.decimals,
      symbol: tokenMetadata.symbol,
      address: poolInfo.tokenMint,
      name: tokenMetadata.name
    } as any);

    if (!parsedAmount.isValid) {
      throw new Error(parsedAmount.error || 'Invalid token amount');
    }

    // Get pool RPC info
    const poolRpcInfo = await this.raydium!.launchpad.getRpcPoolInfo({
      poolId: new PublicKey(request.poolId)
    });

    // Calculate platform fee rate using environment configuration
    const platformFeePercentage = config.launchLab.platformFeePercentage;
    const platformFeeRate = Math.floor(platformFeePercentage * 100); // Convert to basis points * 100 format

    // Create sell transaction without compute budget config (we handle it manually)
    const slippageBps = Math.floor(request.slippage * 10000); // Convert to basis points
    console.log('LaunchLab sell transaction parameters', {
      slippage: request.slippage,
      slippageBps,
      sellAmount: parsedAmount.baseUnits,
      platformFeeRate,
      direction: 'sell'
    });

    const { transaction, extInfo } = await this.raydium!.launchpad.sellToken({
      programId: new PublicKey(this.RAYDIUM_PROGRAMS.LAUNCHPAD),
      mintA: new PublicKey(poolInfo.tokenMint),
      slippage: new BN(slippageBps), // Add missing slippage parameter
      configInfo: poolRpcInfo.configInfo,
      platformFeeRate: new BN(platformFeeRate),
      txVersion: 'V0' as any,
      sellAmount: new BN(parsedAmount.baseUnits),
      computeBudgetConfig: undefined, // We handle compute budget manually in enhanceTransaction
    });

    // Enhance transaction with compute budget instructions and platform fee transfer
    const enhancedTransaction = await this.enhanceTransactionWithFeesAndBudget(
      transaction,
      request,
      parsedAmount.baseUnits,
      'sell'
    );

    // Execute enhanced transaction with Privy
    const txid = await signAndSendTransactionWithPrivy({
      walletId: request.walletId,
      transaction: enhancedTransaction as any,
      connection: this.connection
    });

    const outputAmount = extInfo?.outAmount?.toString() || '0';

    return {
      txid,
      inputAmount: parsedAmount.baseUnits,
      outputAmount,
      fee: '0',
      priceImpact: 0.01,
      newPrice: poolInfo.currentPrice,

      // Human-readable fields with correct decimals
      inputAmountHuman: toHumanAmount(parsedAmount.baseUnits, tokenMetadata.decimals), // Use actual token decimals
      outputAmountHuman: toHumanAmount(outputAmount, 9), // SOL has 9 decimals
      feeHuman: '0',
      priceImpactPercent: '1.00%',
      newPriceHuman: toHumanAmount(poolInfo.currentPrice, 9)
    };
  }
}

// Export singleton instance
export const launchLabService = new LaunchLabService();
