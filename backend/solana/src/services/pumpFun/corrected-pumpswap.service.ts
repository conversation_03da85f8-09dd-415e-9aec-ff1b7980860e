import { Connection, PublicKey, Transaction } from '@solana/web3.js';
import { SwapRequest, SwapDirection, QuoteResult } from '../../types/pump.types';
import { config } from '../../config';
import { 
  checkPumpSwapPool, 
  calculatePumpSwapQuote, 
  createPumpSwapSwapTransaction 
} from './pumpSwap.service';
import { getMint } from '@solana/spl-token';

/**
 * Corrected PumpSwap service with Privy wallet signing and proper fee handling
 * This service mirrors the corrected PumpFun implementation for consistency
 */

/**
 * Execute a PumpSwap swap with Privy wallet signing (same approach as corrected PumpFun)
 * @param swapRequest Swap request
 * @param executeTransaction Whether to execute the transaction or just create it
 * @returns Transaction signature and quote result
 */
export async function executeCorrectedPumpSwapSwap(
  swapRequest: SwapRequest,
  executeTransaction: boolean = true
): Promise<{
  signature?: string;
  transaction?: string;
  quoteResult: QuoteResult;
}> {
  console.log('🔄 [CORRECTED PUMPSWAP] Starting corrected PumpSwap execution with Privy signing...');
  console.log(`🔄 [CORRECTED PUMPSWAP] Execute transaction: ${executeTransaction}`);
  console.log(`🔄 [CORRECTED PUMPSWAP] Direction: ${swapRequest.direction}`);
  console.log(`🔄 [CORRECTED PUMPSWAP] Amount: ${swapRequest.amount}`);

  if (!config.rpcUrl) {
    throw new Error('SOLANA_RPC_URL environment variable is not set');
  }

  const connection = new Connection(config.rpcUrl, 'confirmed');
  let transaction: Transaction;
  let quoteResult: QuoteResult;

  // Get token decimals
  const tokenMint = new PublicKey(swapRequest.tokenAddress);
  const mintInfo = await getMint(connection, tokenMint);
  const tokenDecimals = mintInfo.decimals;

  console.log(`🔄 [CORRECTED PUMPSWAP] Token decimals: ${tokenDecimals}`);

  // Get user wallet public key
  const userWallet = new PublicKey(swapRequest.walletAddress);

  // Check user's SOL balance before proceeding
  const userBalance = await connection.getBalance(userWallet);
  console.log(`🔄 [CORRECTED PUMPSWAP] User SOL balance: ${userBalance} lamports (${userBalance / 1e9} SOL)`);

  if (swapRequest.direction === SwapDirection.Buy) {
    // For buy: SOL -> Token
    console.log('🔄 [CORRECTED PUMPSWAP] Processing BUY transaction...');

    // Check pool and get quote
    const poolAddress = new PublicKey(swapRequest.poolAddress);
    const poolData = await checkPumpSwapPool(connection, poolAddress);

    if (!poolData.exists) {
      throw new Error(`PumpSwap pool not found: ${poolAddress.toString()}`);
    }

    // Calculate quote using PumpSwap algorithm
    quoteResult = await calculatePumpSwapQuote(
      connection,
      poolData,
      swapRequest.amount,
      true, // isBuy
      tokenDecimals,
      (swapRequest.slippage || 0.05) * 100 // Convert to percentage
    );

    console.log(`🔄 [CORRECTED PUMPSWAP] Buy quote - Input: ${swapRequest.amount} SOL, Output: ${quoteResult.outAmount} tokens`);

    // Calculate required balance for buy transaction
    const swapAmountLamports = BigInt(Math.floor(swapRequest.amount * 1e9)); // Convert SOL to lamports
    const platformFeeLamports = (swapAmountLamports * BigInt(100)) / BigInt(10000); // 1% platform fee
    const ataCreationCost = BigInt(2039280); // Cost to create Associated Token Account
    const transactionFees = BigInt(10000); // Estimated transaction fees
    const bufferAmount = BigInt(5000000); // Buffer for wrapped SOL operations
    const totalRequiredLamports = swapAmountLamports + platformFeeLamports + ataCreationCost + transactionFees + bufferAmount;

    console.log(`🔄 [CORRECTED PUMPSWAP] Balance check for buy transaction:`);
    console.log(`  - Swap amount: ${swapAmountLamports} lamports (${swapRequest.amount} SOL)`);
    console.log(`  - Platform fee: ${platformFeeLamports} lamports`);
    console.log(`  - ATA creation: ${ataCreationCost} lamports`);
    console.log(`  - Transaction fees: ${transactionFees} lamports`);
    console.log(`  - Buffer: ${bufferAmount} lamports`);
    console.log(`  - Total required: ${totalRequiredLamports} lamports (${Number(totalRequiredLamports) / 1e9} SOL)`);
    console.log(`  - User balance: ${userBalance} lamports (${userBalance / 1e9} SOL)`);

    if (BigInt(userBalance) < totalRequiredLamports) {
      const shortfall = totalRequiredLamports - BigInt(userBalance);
      const shortfallSOL = Number(shortfall) / 1e9;
      const requiredSOL = Number(totalRequiredLamports) / 1e9;

      throw new Error(
        `Insufficient SOL balance for PumpSwap transaction. ` +
        `Required: ${requiredSOL.toFixed(6)} SOL, Available: ${(userBalance / 1e9).toFixed(6)} SOL. ` +
        `Please add ${shortfallSOL.toFixed(6)} SOL to your wallet. ` +
        `Note: Small swaps require significant SOL for account creation (~0.002 SOL) and fees.`
      );
    }

    // Create transaction with user as fee payer (for Privy signing)
    transaction = await createPumpSwapSwapTransaction(
      connection,
      poolAddress,
      tokenMint,
      userWallet, // User pays fees (will be signed via Privy)
      config.platformFeeWallet, // Platform fee wallet
      swapRequest.amount,
      true, // isBuy
      swapRequest.slippage || 0.05,
      quoteResult,
      poolData,
      tokenDecimals
    );

  } else {
    // For sell: Token -> SOL
    console.log('🔄 [CORRECTED PUMPSWAP] Processing SELL transaction...');

    // Check pool and get quote
    const poolAddress = new PublicKey(swapRequest.poolAddress);
    const poolData = await checkPumpSwapPool(connection, poolAddress);

    if (!poolData.exists) {
      throw new Error(`PumpSwap pool not found: ${poolAddress.toString()}`);
    }

    // Calculate quote using PumpSwap algorithm
    quoteResult = await calculatePumpSwapQuote(
      connection,
      poolData,
      swapRequest.amount,
      false, // isBuy = false for sell
      tokenDecimals,
      (swapRequest.slippage || 0.05) * 100 // Convert to percentage
    );

    console.log(`🔄 [CORRECTED PUMPSWAP] Sell quote - Input: ${swapRequest.amount} tokens, Output: ${quoteResult.outAmount} SOL`);

    // STEP 1: Validate user's token balance before proceeding
    console.log('=== TOKEN BALANCE VALIDATION ===');
    console.log(`Checking token balance for user: ${userWallet.toString()}`);
    console.log(`Token mint: ${tokenMint.toString()}`);
    console.log(`Requested sell amount: ${swapRequest.amount} tokens`);

    try {
      // Get user's token account
      const { getAssociatedTokenAddress } = await import('@solana/spl-token');
      const userTokenAccount = await getAssociatedTokenAddress(tokenMint, userWallet);
      console.log(`User token account: ${userTokenAccount.toString()}`);

      // Check if token account exists
      const tokenAccountInfo = await connection.getAccountInfo(userTokenAccount);
      if (!tokenAccountInfo) {
        throw new Error(`Token account does not exist for user ${userWallet.toString()}. User has no tokens to sell.`);
      }

      // Get the user's current token balance
      const tokenBalance = await connection.getTokenAccountBalance(userTokenAccount);
      const currentBalance = parseFloat(tokenBalance.value.uiAmountString || '0');

      console.log(`Current token balance: ${currentBalance} tokens`);
      console.log(`Token balance (raw): ${tokenBalance.value.amount}`);
      console.log(`Token decimals: ${tokenBalance.value.decimals}`);

      // Validate that user has sufficient tokens with tolerance for floating-point precision
      const tolerance = 0.000001; // 1 microtoken tolerance
      if (currentBalance < (swapRequest.amount - tolerance)) {
        const shortfall = swapRequest.amount - currentBalance;
        const errorMessage = `Insufficient token balance: User has ${currentBalance} tokens, requested to sell ${swapRequest.amount} tokens. Shortfall: ${shortfall.toFixed(6)} tokens.`;
        console.error(errorMessage);
        throw new Error(errorMessage);
      }

      // If the amount is very close to the balance (within tolerance), use the exact balance
      if (Math.abs(currentBalance - swapRequest.amount) <= tolerance && currentBalance < swapRequest.amount) {
        console.log(`Adjusting sell amount from ${swapRequest.amount} to exact balance ${currentBalance} (within tolerance)`);
        swapRequest.amount = currentBalance;
      }

      console.log(`✅ Token balance validation passed: User has ${currentBalance} tokens, selling ${swapRequest.amount} tokens`);
      console.log(`Remaining balance after sell: ${(currentBalance - swapRequest.amount).toFixed(6)} tokens`);

    } catch (error: any) {
      console.error('Token balance validation failed:', error);
      throw error;
    }

    // Calculate required balance for sell transaction
    const tokenValueInSol = swapRequest.amount * quoteResult.price; // SOL value of tokens being sold
    const platformFeeLamports = BigInt(Math.floor(tokenValueInSol * 1e9 * 0.01)); // 1% platform fee
    const ataCreationCost = BigInt(2039280); // Cost for wrapped SOL account if needed
    const transactionFees = BigInt(10000); // Estimated transaction fees
    const totalRequiredLamports = platformFeeLamports + ataCreationCost + transactionFees;

    console.log(`🔄 [CORRECTED PUMPSWAP] Balance check for sell transaction:`);
    console.log(`  - Token value: ${tokenValueInSol} SOL`);
    console.log(`  - Platform fee: ${platformFeeLamports} lamports`);
    console.log(`  - ATA creation: ${ataCreationCost} lamports`);
    console.log(`  - Transaction fees: ${transactionFees} lamports`);
    console.log(`  - Total required: ${totalRequiredLamports} lamports (${Number(totalRequiredLamports) / 1e9} SOL)`);
    console.log(`  - User balance: ${userBalance} lamports (${userBalance / 1e9} SOL)`);

    if (BigInt(userBalance) < totalRequiredLamports) {
      const shortfall = totalRequiredLamports - BigInt(userBalance);
      const shortfallSOL = Number(shortfall) / 1e9;
      const requiredSOL = Number(totalRequiredLamports) / 1e9;

      throw new Error(
        `Insufficient SOL balance for PumpSwap sell transaction. ` +
        `Required: ${requiredSOL.toFixed(6)} SOL, Available: ${(userBalance / 1e9).toFixed(6)} SOL. ` +
        `Please add ${shortfallSOL.toFixed(6)} SOL to your wallet for transaction fees and account creation.`
      );
    }

    // Create transaction with user as fee payer (for Privy signing)
    transaction = await createPumpSwapSwapTransaction(
      connection,
      poolAddress,
      tokenMint,
      userWallet, // User pays fees (will be signed via Privy)
      config.platformFeeWallet, // Platform fee wallet
      swapRequest.amount,
      false, // isBuy = false for sell
      swapRequest.slippage || 0.05,
      quoteResult,
      poolData,
      tokenDecimals
    );
  }

  // Get fresh blockhash
  const { blockhash } = await connection.getLatestBlockhash('finalized');
  transaction.recentBlockhash = blockhash;

  const operationType = swapRequest.direction === SwapDirection.Buy ? 'buy' : 'sell';
  console.log('🔄 [CORRECTED PUMPSWAP] Transaction created successfully');
  console.log(`🔄 [CORRECTED PUMPSWAP] - Instructions: ${transaction.instructions.length} (PumpSwap ${operationType})`);
  console.log('🔄 [CORRECTED PUMPSWAP] - Fee payer:', transaction.feePayer?.toString());
  console.log('🔄 [CORRECTED PUMPSWAP] - Recent blockhash:', transaction.recentBlockhash);

  // If executeTransaction is false, just return the transaction for external execution (e.g., Jito)
  if (!executeTransaction) {
    const serializedTransaction = transaction.serialize({ requireAllSignatures: false }).toString('base64');
    console.log('🔄 [CORRECTED PUMPSWAP] Transaction created for external execution');
    return { transaction: serializedTransaction, quoteResult };
  }

  // Since PumpSwap requires user to be a signer, we need to use Privy session signing
  if (!swapRequest.walletId) {
    throw new Error('Wallet ID is required for PumpSwap transactions with Privy session signing');
  }

  // User will sign via Privy, platform covers gas costs through reimbursement
  console.log('🔄 [CORRECTED PUMPSWAP] User will sign via Privy, platform covers gas costs through reimbursement');
  // Import the Privy signing function
  const { signAndSendTransactionWithPrivy } = await import('../privy/proper-privy.service');

  // Serialize transaction for Privy (user will sign, user pays gas but platform reimburses)
  const serializedTransaction = transaction.serialize({ requireAllSignatures: false }).toString('base64');
  console.log('🔄 [CORRECTED PUMPSWAP] Transaction serialized for Privy session signing');

  // Sign and send with Privy (user signs and pays gas, platform reimburses off-chain)
  // IMPORTANT: Don't pass connection to prevent blockhash modification in Privy service
  console.log(`🔄 [CORRECTED PUMPSWAP] Signing transaction with Privy for wallet: ${swapRequest.walletAddress}`);
  const signature = await signAndSendTransactionWithPrivy(swapRequest.walletId, serializedTransaction);

  console.log('🔄 [CORRECTED PUMPSWAP] Transaction signed and sent successfully with Privy:', signature);
  return { signature, quoteResult };
}

/**
 * Get PumpSwap pool status and quote information
 * @param swapRequest Swap request
 * @returns Quote result and pool status
 */
export async function getPumpSwapQuoteAndStatus(
  swapRequest: SwapRequest
): Promise<{
  quoteResult: QuoteResult;
  poolStatus: any;
}> {
  console.log('🔄 [CORRECTED PUMPSWAP] Getting quote and pool status...');

  if (!config.rpcUrl) {
    throw new Error('SOLANA_RPC_URL environment variable is not set');
  }

  const connection = new Connection(config.rpcUrl, 'confirmed');
  
  // Get token decimals
  const tokenMint = new PublicKey(swapRequest.tokenAddress);
  const mintInfo = await getMint(connection, tokenMint);
  const tokenDecimals = mintInfo.decimals;

  // Check pool and get quote
  const poolAddress = new PublicKey(swapRequest.poolAddress);
  const poolData = await checkPumpSwapPool(connection, poolAddress);
  
  if (!poolData.exists) {
    throw new Error(`PumpSwap pool not found: ${poolAddress.toString()}`);
  }

  // Calculate quote
  const quoteResult = await calculatePumpSwapQuote(
    connection,
    poolData,
    swapRequest.amount,
    swapRequest.direction === SwapDirection.Buy,
    tokenDecimals,
    (swapRequest.slippage || 0.05) * 100 // Convert to percentage
  );

  const poolStatus = {
    exists: poolData.exists,
    reserves: poolData.reserves,
    // Add other relevant pool information
  };

  console.log('🔄 [CORRECTED PUMPSWAP] Quote and status retrieved successfully');
  
  return {
    quoteResult,
    poolStatus
  };
}
