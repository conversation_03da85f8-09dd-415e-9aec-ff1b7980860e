import { Connection, PublicKey } from '@solana/web3.js';
import { PUMP_FUN_PROGRAM_ID, PUMP_SWAP_PROGRAM_ID } from '../../utils/idl.utils';
import { getAssociatedTokenAddress } from '@solana/spl-token';

/**
 * PDA Derivation Utilities
 *
 * This file contains utility functions for deriving Program Derived Addresses (PDAs)
 * used in PumpFun and PumpSwap operations. These functions replace hardcoded values
 * with dynamic derivation based on on-chain data.
 *
 * UPDATED: Fixed PDA derivation to use correct seed formats for Privy compatibility
 */

// Constants for PDA derivation
export const ASSOCIATED_TOKEN_PROGRAM_ID = new PublicKey('ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL');
export const TOKEN_PROGRAM_ID = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');

/**
 * Derive the associated bonding curve account for a token
 *
 * This follows the Solana PDA derivation logic:
 * - Seeds: [bondingCurve, TOKEN_PROGRAM_ID, tokenMint]
 * - Program ID: ASSOCIATED_TOKEN_PROGRAM_ID
 *
 * @param bondingCurve The bonding curve address
 * @param tokenMint The token mint address
 * @returns The associated bonding curve account address
 */
export function deriveAssociatedBondingCurveAccount(
  bondingCurve: PublicKey,
  tokenMint: PublicKey
): PublicKey {
  const [associatedAccount] = PublicKey.findProgramAddressSync(
    [
      bondingCurve.toBuffer(),
      TOKEN_PROGRAM_ID.toBuffer(),
      tokenMint.toBuffer()
    ],
    ASSOCIATED_TOKEN_PROGRAM_ID
  );

  console.log(`Derived associated bonding curve account: ${associatedAccount.toString()}`);
  return associatedAccount;
}

/**
 * Get the associated token account for a user
 *
 * This uses the standard SPL token associated account derivation
 *
 * @param tokenMint The token mint address
 * @param owner The owner's wallet address
 * @returns The associated token account address
 */
export async function getAssociatedUserAccount(
  tokenMint: PublicKey,
  owner: PublicKey
): Promise<PublicKey> {
  const associatedAccount = await getAssociatedTokenAddress(tokenMint, owner);
  console.log(`Derived associated token account: ${associatedAccount.toString()}`);
  return associatedAccount;
}

/**
 * Derive the bonding curve PDA for PumpFun (following reference implementation)
 *
 * @param tokenMint The token mint address
 * @returns The bonding curve PDA
 */
export function derivePumpFunBondingCurvePDA(tokenMint: PublicKey): PublicKey {
  const [bondingCurve] = PublicKey.findProgramAddressSync(
    [Buffer.from('bonding-curve'), tokenMint.toBuffer()],
    PUMP_FUN_PROGRAM_ID
  );

  console.log(`Derived PumpFun bonding curve PDA: ${bondingCurve.toString()}`);
  return bondingCurve;
}

/**
 * Get the associated bonding curve account (not a PDA, but an associated token account)
 *
 * @param tokenMint The token mint address
 * @returns The associated bonding curve account
 */
export async function getPumpFunAssociatedBondingCurve(tokenMint: PublicKey): Promise<PublicKey> {
  const bondingCurve = derivePumpFunBondingCurvePDA(tokenMint);
  const associatedAccount = await getAssociatedTokenAddress(tokenMint, bondingCurve, true);

  console.log(`Derived associated bonding curve account: ${associatedAccount.toString()}`);
  return associatedAccount;
}

/**
 * Get creator from bonding curve state (following reference implementation)
 *
 * @param connection Solana connection
 * @param bondingCurve The bonding curve address
 * @param fallbackCreator Fallback creator if unable to fetch from chain
 * @returns The creator public key
 */
export async function getCreatorFromBondingCurve(
  connection: Connection,
  bondingCurve: PublicKey,
  fallbackCreator: PublicKey
): Promise<PublicKey> {
  try {
    const accountInfo = await connection.getAccountInfo(bondingCurve);
    if (accountInfo && accountInfo.data) {
      // Parse the bonding curve state to get the creator
      // Skip discriminator (8 bytes) + virtualTokenReserves(8) + virtualSolReserves(8) + realTokenReserves(8) + realSolReserves(8) + tokenTotalSupply(8) + complete(1)
      const creatorOffset = 8 + 8 + 8 + 8 + 8 + 8 + 1; // 49 bytes
      if (accountInfo.data.length >= creatorOffset + 32) {
        const creatorBytes = accountInfo.data.slice(creatorOffset, creatorOffset + 32);
        const creator = new PublicKey(creatorBytes);
        console.log(`Found creator from bonding curve: ${creator.toString()}`);
        return creator;
      }
    }
  } catch (error) {
    console.warn('Could not fetch creator from bonding curve, using fallback');
  }

  console.log(`Using fallback creator: ${fallbackCreator.toString()}`);
  return fallbackCreator;
}

/**
 * Derive the creator vault PDA for PumpFun (following reference implementation)
 *
 * @param creator The creator address (from bonding curve state)
 * @returns The creator vault PDA
 */
export function derivePumpFunCreatorVaultPDA(creator: PublicKey): PublicKey {
  const [creatorVault] = PublicKey.findProgramAddressSync(
    [Buffer.from('creator-vault'), creator.toBuffer()],
    PUMP_FUN_PROGRAM_ID
  );

  console.log(`Derived PumpFun creator vault PDA: ${creatorVault.toString()}`);
  return creatorVault;
}

/**
 * Derive the creator vault PDA for PumpSwap
 *
 * @returns The creator vault PDA
 */
export function derivePumpSwapCreatorVaultPDA(): PublicKey {
  const [creatorVault] = PublicKey.findProgramAddressSync(
    [Buffer.from('creator_vault')], // Note: uses underscore, not hyphen
    PUMP_SWAP_PROGRAM_ID
  );

  console.log(`Derived PumpSwap creator vault PDA: ${creatorVault.toString()}`);
  return creatorVault;
}

/**
 * Derive the coin creator vault authority PDA for PumpSwap
 * This is derived from the coin creator address from the pool data
 *
 * @param coinCreator The coin creator address from the pool
 * @returns The coin creator vault authority PDA
 */
export function derivePumpSwapCoinCreatorVaultAuthorityPDA(coinCreator: PublicKey): PublicKey {
  const [coinCreatorVaultAuthority] = PublicKey.findProgramAddressSync(
    [Buffer.from('creator_vault'), coinCreator.toBuffer()], // 'creator_vault' + coin_creator
    PUMP_SWAP_PROGRAM_ID
  );

  console.log(`Derived PumpSwap coin creator vault authority PDA: ${coinCreatorVaultAuthority.toString()}`);
  console.log(`  - Coin creator: ${coinCreator.toString()}`);
  return coinCreatorVaultAuthority;
}

/**
 * Derive the event authority PDA for PumpFun
 *
 * @returns The event authority PDA
 */
export function derivePumpFunEventAuthorityPDA(): PublicKey {
  const [eventAuthority] = PublicKey.findProgramAddressSync(
    [Buffer.from('__event_authority')],
    PUMP_FUN_PROGRAM_ID
  );

  console.log(`Derived PumpFun event authority PDA: ${eventAuthority.toString()}`);
  return eventAuthority;
}

/**
 * Derive the event authority PDA for PumpSwap
 *
 * @returns The event authority PDA
 */
export function derivePumpSwapEventAuthorityPDA(): PublicKey {
  const [eventAuthority] = PublicKey.findProgramAddressSync(
    [Buffer.from('__event_authority')],
    PUMP_SWAP_PROGRAM_ID
  );

  console.log(`Derived PumpSwap event authority PDA: ${eventAuthority.toString()}`);
  return eventAuthority;
}

/**
 * Derive the global config PDA for PumpFun
 *
 * @returns The global config PDA
 */
export function derivePumpFunGlobalConfigPDA(): PublicKey {
  const [globalConfig] = PublicKey.findProgramAddressSync(
    [Buffer.from('global')],
    PUMP_FUN_PROGRAM_ID
  );

  console.log(`Derived PumpFun global config PDA: ${globalConfig.toString()}`);
  return globalConfig;
}

/**
 * Derive the global config PDA for PumpSwap
 *
 * @returns The global config PDA
 */
export function derivePumpSwapGlobalConfigPDA(): PublicKey {
  const [globalConfig] = PublicKey.findProgramAddressSync(
    [Buffer.from('global_config')], // Note: uses underscore, not hyphen
    PUMP_SWAP_PROGRAM_ID
  );

  console.log(`Derived PumpSwap global config PDA: ${globalConfig.toString()}`);
  return globalConfig;
}

/**
 * Get all required PDAs for a PumpFun transaction
 *
 * @param tokenMint The token mint address
 * @param userWallet The user's wallet address
 * @param poolAddress Optional pool address for deriving associated bonding curve
 * @param connection Optional Solana connection for fetching on-chain data
 * @returns Object containing all required PDAs
 */
export async function getAllPumpFunPDAs(
  tokenMint: PublicKey,
  userWallet: PublicKey,
  connection?: Connection,
  poolAddress?: PublicKey
): Promise<{
  associatedTokenAccount: PublicKey;
  creatorVault: PublicKey;
  eventAuthority: PublicKey;
  globalConfig: PublicKey;
  associatedBondingCurve: PublicKey;
}> {
  // Get fee recipient (either from on-chain or fallback)
  const feeRecipient = connection
    ? await fetchPumpFunFeeRecipient(connection)
    : new PublicKey('CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM');

  // Get associated token account (standard SPL token account)
  const associatedTokenAccount = await getAssociatedTokenAddress(tokenMint, userWallet);

  // Get creator vault with correct seed
  const creatorVault = derivePumpFunCreatorVaultPDA(feeRecipient);

  // Get event authority
  const eventAuthority = derivePumpFunEventAuthorityPDA();

  // Get global config
  const globalConfig = derivePumpFunGlobalConfigPDA();

  // Get associated bonding curve (for pool)
  const poolAddr = poolAddress || new PublicKey('81oRNu8jP5oeVNQL8injYe9DSfK5CnJUruBq3qGZsCj8');
  const associatedBondingCurve = deriveAssociatedBondingCurveAccount(poolAddr, tokenMint);

  return {
    associatedTokenAccount,
    creatorVault,
    eventAuthority,
    globalConfig,
    associatedBondingCurve
  };
}

/**
 * Fetch the fee recipient address for PumpFun from on-chain data
 *
 * @param connection Solana connection
 * @returns The fee recipient address
 */
export async function fetchPumpFunFeeRecipient(connection: Connection): Promise<PublicKey> {
  try {
    // For now, return the known fee recipient address
    // In a future update, this could fetch and parse the global config account data
    return new PublicKey('CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM');
  } catch (error) {
    console.error('Error fetching PumpFun fee recipient:', error);
    // Fall back to the known fee recipient address
    return new PublicKey('CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM');
  }
}

/**
 * Fetch the protocol fee recipient address for PumpSwap from on-chain data
 *
 * @param connection Solana connection
 * @returns The protocol fee recipient address
 */
export async function fetchPumpSwapFeeRecipient(connection: Connection): Promise<PublicKey> {
  try {
    const globalConfigPDA = derivePumpSwapGlobalConfigPDA();
    console.log(`🔍 [PDA] Fetching PumpSwap fee recipient from global config: ${globalConfigPDA.toString()}`);

    // Fetch the account data
    const accountInfo = await connection.getAccountInfo(globalConfigPDA);
    if (!accountInfo) {
      console.error('❌ [ERROR] PumpSwap global config account not found');
      throw new Error('PumpSwap global config account not found');
    }

    console.log(`✅ [SUCCESS] Global config account found, data length: ${accountInfo.data.length} bytes`);

    // Parse the account data to extract the fee recipient
    // The global config account contains the protocol fee recipient at a specific offset
    // Based on analysis of successful transactions, let's try to parse it correctly

    if (accountInfo.data.length >= 40) {
      // Try to extract the protocol fee recipient from the global config data
      // The exact offset may vary, but let's try common positions
      try {
        // Try offset 8 (after discriminator)
        const feeRecipientBytes = accountInfo.data.slice(8, 40);
        const protocolFeeRecipient = new PublicKey(feeRecipientBytes);
        console.log(`✅ [SUCCESS] Parsed protocol fee recipient from global config: ${protocolFeeRecipient.toString()}`);
        return protocolFeeRecipient;
      } catch (parseError) {
        console.warn(`⚠️ [WARNING] Failed to parse fee recipient from global config: ${parseError}`);
      }
    }

    // FIXED: Use the correct PumpSwap protocol fee recipient from successful transaction analysis
    // This is the owner of the protocol fee recipient token account: 94qWNrtmfn42h3ZjUZwWvK1MEo9uVmmrBPd2hpNjYDjb
    const protocolFeeRecipient = new PublicKey('62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV');
    console.log(`✅ [SUCCESS] Using verified PumpSwap protocol fee recipient: ${protocolFeeRecipient.toString()}`);

    return protocolFeeRecipient;
  } catch (error) {
    console.error('❌ [ERROR] Error fetching PumpSwap fee recipient:', error);
    // Fall back to the verified protocol fee recipient from successful transaction
    const fallbackRecipient = new PublicKey('62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV');
    console.log(`🔄 [FALLBACK] Using fallback PumpSwap protocol fee recipient: ${fallbackRecipient.toString()}`);
    return fallbackRecipient;
  }
}
