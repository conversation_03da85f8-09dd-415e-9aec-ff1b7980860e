import { Connection, PublicKey } from '@solana/web3.js';
import {
  deriveAssociatedBondingCurveAccount,
  derivePumpFunCreatorVaultPDA,
  derivePumpFunEventAuthorityPDA,
  derivePumpFunGlobalConfigPDA,
  fetchPumpFunFeeRecipient,
  TOKEN_PROGRAM_ID,
  getAssociatedUserAccount,
  getAllPumpFunPDAs
} from './pda.utils';
import { PUMP_FUN_PROGRAM_ID } from '../../utils/idl.utils';
import { getAssociatedTokenAddress } from '@solana/spl-token';

/**
 * PDA Validation Utilities
 *
 * This file contains utility functions for validating PDAs before using them
 * in transactions, ensuring they exist on-chain and have the expected ownership.
 *
 * UPDATED: Fixed PDA validation to use correct seed formats for Privy compatibility
 */

/**
 * Validate that a PDA exists on-chain
 *
 * @param connection Solana connection
 * @param pda PDA to validate
 * @param expectedOwner Expected owner of the PDA (optional)
 * @returns Whether the PDA exists and has the expected owner
 */
export async function validatePDA(
  connection: Connection,
  pda: PublicKey,
  expectedOwner?: PublicKey
): Promise<boolean> {
  try {
    console.log(`Validating PDA: ${pda.toString()}`);

    // Get the account info
    const accountInfo = await connection.getAccountInfo(pda);

    // Check if the account exists
    if (!accountInfo) {
      console.warn(`PDA ${pda.toString()} does not exist on-chain`);
      return false;
    }

    // Check the owner if expected
    if (expectedOwner && !accountInfo.owner.equals(expectedOwner)) {
      console.warn(`PDA ${pda.toString()} has unexpected owner: ${accountInfo.owner.toString()}, expected: ${expectedOwner.toString()}`);
      return false;
    }

    console.log(`PDA ${pda.toString()} is valid`);
    return true;
  } catch (error) {
    console.error(`Error validating PDA ${pda.toString()}:`, error);
    return false;
  }
}

/**
 * Validate all PDAs used in a PumpFun transaction
 *
 * UPDATED: Using standard SPL token associated account instead of custom PDA
 *
 * @param connection Solana connection
 * @param poolAddress Pool address
 * @param tokenMint Token mint
 * @param userPublicKey User public key
 * @returns Object containing validation results for each PDA
 */
export async function validatePumpFunPDAs(
  connection: Connection,
  poolAddress: PublicKey,
  tokenMint: PublicKey,
  userPublicKey: PublicKey
): Promise<{
  globalConfig: boolean;
  associatedBondingCurve: boolean;
  associatedTokenAccount: boolean;
  creatorVault: boolean;
  eventAuthority: boolean;
}> {
  // Get all PDAs using our updated utility function
  const pdas = await getAllPumpFunPDAs(tokenMint, userPublicKey, connection);

  // Validate each PDA
  const globalConfigValid = await validatePDA(connection, pdas.globalConfig, PUMP_FUN_PROGRAM_ID);
  const associatedBondingCurveValid = await validatePDA(connection, pdas.associatedBondingCurve, TOKEN_PROGRAM_ID);

  // Use standard SPL token associated account instead of custom PDA
  const associatedTokenAccountValid = await validatePDA(
    connection,
    pdas.associatedTokenAccount,
    TOKEN_PROGRAM_ID
  );

  const creatorVaultValid = await validatePDA(connection, pdas.creatorVault, PUMP_FUN_PROGRAM_ID);
  const eventAuthorityValid = await validatePDA(connection, pdas.eventAuthority, PUMP_FUN_PROGRAM_ID);

  return {
    globalConfig: globalConfigValid,
    associatedBondingCurve: associatedBondingCurveValid,
    associatedTokenAccount: associatedTokenAccountValid,
    creatorVault: creatorVaultValid,
    eventAuthority: eventAuthorityValid
  };
}

/**
 * Get the associated token account for a user
 *
 * UPDATED: Using standard SPL token associated account instead of custom PDA
 *
 * @param tokenMint Token mint
 * @param userPublicKey User public key
 * @returns The associated token account
 */
export async function getAssociatedTokenAccountForUser(
  tokenMint: PublicKey,
  userPublicKey: PublicKey
): Promise<PublicKey> {
  return await getAssociatedTokenAddress(tokenMint, userPublicKey);
}

/**
 * Pre-create necessary PDAs for a PumpFun transaction
 *
 * @param connection Solana connection
 * @param poolAddress Pool address
 * @param tokenMint Token mint
 * @param userPublicKey User public key
 * @returns Object containing validation results after pre-creation
 */
export async function preCreatePumpFunPDAs(
  connection: Connection,
  poolAddress: PublicKey,
  tokenMint: PublicKey,
  userPublicKey: PublicKey
): Promise<{
  globalConfig: boolean;
  associatedBondingCurve: boolean;
  associatedTokenAccount: boolean;
  creatorVault: boolean;
  eventAuthority: boolean;
}> {
  // First validate all PDAs
  const validationResults = await validatePumpFunPDAs(
    connection,
    poolAddress,
    tokenMint,
    userPublicKey
  );

  // If any PDAs are invalid, we would create them here
  // However, most PDAs should be created by the program itself
  // For now, we just return the validation results

  return validationResults;
}
