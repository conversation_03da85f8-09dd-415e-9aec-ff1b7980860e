# PumpFun Integration

## Overview

This module provides integration with the PumpFun protocol on Solana, handling token swaps and liquidity pool interactions. The implementation has been consolidated into a single, well-documented utility file for better maintainability and reliability.

## Code Consolidation

To improve code organization and maintainability, all PumpFun-related functionality has been consolidated into `pumpFunUtils.ts`. This consolidation addresses several issues with the previous implementation and provides a more robust solution.

### Consolidated Functionality

- **Account Structure Management**
  - Ensures correct account ordering for Anchor compatibility
  - Maintains critical positioning of SystemProgram and TokenProgram accounts
  - Handles all necessary account validations

- **Transaction Processing**
  - Creates and validates buy/sell instructions
  - Fixes transaction account structures when needed
  - Handles instruction replacement for Anchor compatibility

- **Utilities**
  - Pool token account derivation
  - Instruction validation
  - Error handling and reporting

## How to Use

### Installation

All necessary functionality is included in the `pumpFunUtils.ts` file. No additional installation is required.

### Basic Usage

```typescript
import { 
  createCorrectBuyInstruction,
  createCorrectSellInstruction,
  fixPumpFunTransaction,
  validatePumpFunInstruction,
  PUMPFUN_PROGRAM_ID,
  TOKEN_PROGRAM_ID
} from './pumpFunUtils';

// Example: Create a buy instruction
const buyIx = await createCorrectBuyInstruction(
  poolAddress,
  tokenMint,
  userTokenAccount,
  userPublicKey,
  amount,
  maxSolCost
);

// Example: Fix an existing transaction
const fixedTx = fixPumpFunTransaction(originalTransaction);

// Example: Validate an instruction
const { valid, errors } = validatePumpFunInstruction(instruction);
```

## Issue Resolution

### Fixed Issues

1. **Account Structure Mismatch**
   - Resolved issues with incorrect account ordering in Anchor program calls
   - Fixed SystemProgram and TokenProgram positioning in account lists
   - Eliminated duplicate account references

2. **Transaction Serialization**
   - Ensured consistent transaction serialization
   - Fixed issues where transaction fixes were being overwritten

3. **Code Organization**
   - Consolidated multiple files into a single, well-documented utility
   - Improved type safety and error handling
   - Added comprehensive documentation

## Migration Guide

The following files have been consolidated into `pumpFunUtils.ts` and can be safely removed:

- `anchorFix.ts` - Functionality moved to `pumpFunUtils.ts`
- `pumpFunCorrect.ts` - Consolidated into `pumpFunUtils.ts`
- `pumpFunCorrectNew.ts` - Consolidated into `pumpFunUtils.ts`
- `pumpFunFix.ts` - Functionality moved to `pumpFunUtils.ts`

Update all imports to use the new consolidated file.

## Best Practices

1. **Always Validate Instructions**
   ```typescript
   const { valid, errors } = validatePumpFunInstruction(instruction);
   if (!valid) {
     console.error('Invalid instruction:', errors);
   }
   ```

2. **Use the Provided Fix Function**
   ```typescript
   const fixedTx = fixPumpFunTransaction(originalTransaction);
   ```

3. **Check for Updates**
   - This implementation may need updates if the PumpFun program changes
   - Monitor the official PumpFun documentation for changes

## Implementation Notes

The main issue addressed by this consolidation was the account structure in PumpFun transactions. The Anchor program expects specific accounts at specific indices:

- System Program must be at index 8
- Token Program must be at index 9

The error in the original implementation was due to incorrect account ordering or duplication.
