import BN from 'bn.js';
import { config } from '../../config';

/**
 * Constants
 */
export const SOL_DECIMALS = config.solDecimals;
export const SOL_ADDRESS = config.solAddress;
export const PLATFORM_FEE_BASIS_POINTS = config.platformFeeBasisPoints;

// Hardcoded Swap Calculation Fees (fees retained by the liquidity pool)
export const PUMPFUN_SWAP_FEE_RATIO = 0.01; // 1% fixed fee for PumpFun pools
export const PUMPSWAP_SWAP_FEE_RATIO = 0.0025; // 0.25% fixed fee for PumpSwap pools

// Platform Fees (fees that go to our platform wallet) - hardcoded values
export const PUMPFUN_PLATFORM_FEE_RATIO = 0.01; // 1% platform fee for PumpFun
export const PUMPSWAP_PLATFORM_FEE_RATIO = 0.01; // 1% platform fee for PumpSwap

/**
 * Read a BigUInt64 from a buffer at the specified offset
 * @param buffer The buffer to read from
 * @param offset The offset to read at
 * @returns The read BigInt value
 */
export function readBigUInt64LE(buffer: Buffer | Uint8Array, offset: number): bigint {
  const first = buffer[offset];
  const last = buffer[offset + 7];

  if (first === undefined || last === undefined) {
    throw new Error(`Buffer too small (${buffer.length}) for BigUInt64LE at offset ${offset}`);
  }

  // Create a DataView from the buffer to read values without using deprecated methods
  let dataView: DataView;

  if (Buffer.isBuffer(buffer)) {
    // If it's a Buffer, get the underlying ArrayBuffer
    dataView = new DataView(buffer.buffer, buffer.byteOffset + offset, 8);
  } else {
    // If it's a Uint8Array
    dataView = new DataView(buffer.buffer, buffer.byteOffset + offset, 8);
  }

  // Read the values using DataView (which works with any ArrayBuffer view)
  const lo = dataView.getUint32(0, true); // true for little-endian
  const hi = dataView.getUint32(4, true); // true for little-endian

  return (BigInt(hi) * BigInt(4294967296)) + BigInt(lo);
}

/**
 * Calculate bonding curve output for PumpFun pools
 * @param virtualSOL Virtual SOL reserves
 * @param virtualTokens Virtual token reserves
 * @param amount Amount to swap
 * @param isBuy Whether this is a buy or sell
 * @param feeRatio Fee ratio (e.g., 0.01 for 1%)
 * @returns The output amount as a BigInt
 */
export function calculateBondingCurveOutput(
  virtualSOL: bigint,
  virtualTokens: bigint,
  amount: bigint,
  isBuy: boolean,
  feeRatio: number
): bigint {
  const virtualSOLBN = new BN(virtualSOL.toString());
  const virtualTokensBN = new BN(virtualTokens.toString());
  const amountBN = new BN(amount.toString());

  if (isBuy) {
    const k = virtualSOLBN.mul(virtualTokensBN);
    const newVirtualSOL = virtualSOLBN.add(amountBN);
    const newVirtualTokens = k.div(newVirtualSOL);
    const outputTokens = virtualTokensBN.sub(newVirtualTokens);
    const feeBN = outputTokens.muln(Math.floor(feeRatio * 10000)).divn(10000);
    const outputAfterFees = outputTokens.sub(feeBN);
    return BigInt(outputAfterFees.toString());
  } else {
    const k = virtualSOLBN.mul(virtualTokensBN);
    const newVirtualTokens = virtualTokensBN.add(amountBN);
    const newVirtualSOL = k.div(newVirtualTokens);
    const outputSOL = virtualSOLBN.sub(newVirtualSOL);
    const feeBN = outputSOL.muln(Math.floor(feeRatio * 10000)).divn(10000);
    const outputAfterFees = outputSOL.sub(feeBN);
    return BigInt(outputAfterFees.toString());
  }
}

/**
 * Format a number with specified decimal places
 * @param num Number to format
 * @param decimals Number of decimal places
 * @returns Formatted number as string
 */
export function formatNumber(num: number, decimals: number = 6): string {
  return num.toFixed(decimals);
}

/**
 * Format token amount for display
 * @param amount Token amount
 * @param isToken Whether this is a token amount (true) or SOL amount (false)
 * @returns Formatted token amount
 */
export function formatTokenAmount(amount: number): number {
  // For token amounts, preserve decimal places for small amounts
  // Use 6 decimal places for precision, but remove trailing zeros
  return parseFloat(amount.toFixed(6));
}

/**
 * Format SOL amount for display
 * @param amount SOL amount
 * @returns Formatted SOL amount
 */
export function formatSolAmount(amount: number): number {
  // For SOL amounts, we typically want to show 6 decimal places
  return parseFloat(amount.toFixed(6));
}

/**
 * Format price for display
 * @param price Price value
 * @returns Formatted price
 */
export function formatPrice(price: number): number {
  if (price < 0.000001) {
    // For very small prices, use scientific notation with 9 significant digits
    return parseFloat(price.toExponential(9));
  } else if (price < 0.001) {
    // For small prices, show 9 decimal places
    return parseFloat(price.toFixed(9));
  } else if (price < 1) {
    // For medium prices, show 6 decimal places
    return parseFloat(price.toFixed(6));
  } else {
    // For large prices, show 4 decimal places
    return parseFloat(price.toFixed(4));
  }
}

/**
 * Calculate platform fee amount in lamports
 * @param amountLamports Amount in lamports
 * @param dexType The type of DEX (pumpfun or pumpswap)
 * @returns Fee amount in lamports
 */
export function calculatePlatformFee(amountLamports: bigint, dexType: 'pumpfun' | 'pumpswap' = 'pumpfun'): bigint {
  // Use the appropriate platform fee ratio based on the DEX type
  const feeRatio = dexType === 'pumpfun' ? PUMPFUN_PLATFORM_FEE_RATIO : PUMPSWAP_PLATFORM_FEE_RATIO;

  // Convert fee ratio to basis points (e.g., 0.1 -> 1000 basis points)
  const feeBasisPoints = Math.floor(feeRatio * 10000);

  return (amountLamports * BigInt(feeBasisPoints)) / BigInt(10000);
}

/**
 * Convert lamports to SOL
 * @param lamports Amount in lamports
 * @returns Amount in SOL
 */
export function lamportsToSol(lamports: bigint): number {
  return Number(lamports) / 10 ** SOL_DECIMALS;
}

/**
 * Convert SOL to lamports
 * @param sol Amount in SOL
 * @returns Amount in lamports
 */
export function solToLamports(sol: number): bigint {
  return BigInt(Math.floor(sol * 10 ** SOL_DECIMALS));
}

/**
 * Convert token amount to raw amount based on decimals
 * @param amount Token amount
 * @param decimals Token decimals
 * @returns Raw token amount
 */
export function tokenToRaw(amount: number, decimals: number): bigint {
  return BigInt(Math.floor(amount * 10 ** decimals));
}

/**
 * Convert raw token amount to token amount based on decimals
 * @param rawAmount Raw token amount
 * @param decimals Token decimals
 * @returns Token amount
 */
export function rawToToken(rawAmount: bigint, decimals: number): number {
  return Number(rawAmount) / 10 ** decimals;
}

/**
 * Generate an Anchor instruction discriminator from a method name
 * @param methodName The method name to generate a discriminator for
 * @returns The 8-byte discriminator as a Buffer
 */
export function generateAnchorDiscriminator(methodName: string): Buffer {
  // Use the node:crypto module to create a SHA256 hash
  const crypto = require('crypto');

  // Create a SHA256 hash of the method name
  const hash = crypto.createHash('sha256').update(methodName).digest();

  // Return the first 8 bytes of the hash
  return Buffer.from(hash.slice(0, 8));
}
