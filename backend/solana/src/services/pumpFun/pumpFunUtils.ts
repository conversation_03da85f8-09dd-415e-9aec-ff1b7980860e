import { PublicKey, TransactionInstruction, Transaction, SystemProgram, Connection } from '@solana/web3.js';
import { BN } from '@coral-xyz/anchor';
import { getAssociatedTokenAddress, createAssociatedTokenAccountIdempotentInstruction } from '@solana/spl-token';
import { config } from '../../config';
import {
  PUMP_FUN_PROGRAM_ID,
  PUMP_FUN_BUY_DISCRIMINATOR,
  PUMP_FUN_SELL_DISCRIMINATOR,
  PUMP_FUN_EVENT_AUTHORITY_SEED,
  PUMP_FUN_GLOBAL_CONFIG_SEED
} from '../../utils/idl.utils';
import {
  deriveAssociatedBondingCurveAccount,
  derivePumpFunBondingCurvePDA,
  getPumpFunAssociatedBondingCurve,
  getCreatorFromBondingCurve,
  derivePumpFunCreatorVaultPDA,
  derivePumpFunEventAuthorityPDA,
  derivePumpFunGlobalConfigPDA,
  fetchPump<PERSON>un<PERSON>eeR<PERSON><PERSON><PERSON>,
  TOKEN_PROGRAM_ID as PDA_TOKEN_PROGRAM_ID,
  getAllPumpFunPDAs
} from './pda.utils';

/**
 * PumpFun Utilities
 *
 * This consolidated file contains all the necessary functionality for working with PumpFun liquidity pools.
 * It includes correct implementations for both Buy and Sell instructions that properly handle Anchor's
 * account structure requirements, as well as utility functions to fix transactions.
 *
 * The key issue addressed in this file is ensuring the correct ordering of accounts in the Anchor program
 * instructions, particularly the positioning of the System Program and Token Program accounts.
 *
 * UPDATED: Fixed PDA derivation to use correct seed formats for Privy compatibility
 */

// =============================================================================
// Constants
// =============================================================================

// Using the program ID from the IDL
// For backward compatibility
export const PUMPFUN_PROGRAM_ID = PUMP_FUN_PROGRAM_ID;

// Hard-coded addresses matching the pool's program ID (based on program logs)
export const PUMP_FEE_RECIPIENT = new PublicKey('CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM');

// Liquidity Migrator address
export const LIQUIDITY_MIGRATOR = new PublicKey('39azUYFWPz3VHgKCf3VChUwbpURdCHRxjWVowf5jUJjg');

// Derive event authority using the seed from the IDL
// FIXED: Using the correct seed format for event authority
export const EVENT_AUTHORITY = derivePumpFunEventAuthorityPDA();

// Explicitly define the SPL Token Program ID to ensure consistency
export const TOKEN_PROGRAM_ID = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');

// Use discriminators from the IDL
export const BUY_DISCRIMINATOR = PUMP_FUN_BUY_DISCRIMINATOR;
export const SELL_DISCRIMINATOR = PUMP_FUN_SELL_DISCRIMINATOR;

// =============================================================================
// Core Functions
// =============================================================================

/**
 * Create an associated token account instruction if needed
 * Following the reference implementation pattern
 */
export function createAssociatedTokenAccountInstruction(
  tokenMint: PublicKey,
  userPublicKey: PublicKey,
  userTokenAccount: PublicKey
): TransactionInstruction {
  return createAssociatedTokenAccountIdempotentInstruction(
    userPublicKey, // payer
    userTokenAccount, // associated token account
    userPublicKey, // owner
    tokenMint // mint
  );
}

/**
 * Validate PumpFun instruction for Privy compatibility
 * This ensures our new implementation works with Privy session signing
 */
export function validatePumpFunInstructionForPrivy(
  instruction: TransactionInstruction,
  userWallet: PublicKey
): { valid: boolean; errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];

  console.log('Validating PumpFun instruction for Privy compatibility...');

  // Check program ID
  if (instruction.programId.toString() !== PUMP_FUN_PROGRAM_ID.toString()) {
    errors.push(`Invalid program ID: expected ${PUMP_FUN_PROGRAM_ID.toString()}, got ${instruction.programId.toString()}`);
  }

  // Check instruction data length (should be 24 bytes: 8 discriminator + 16 data)
  if (instruction.data.length !== 24) {
    warnings.push(`Instruction data length is ${instruction.data.length} bytes, expected 24 bytes`);
  }

  // Check account count (should be 12 accounts)
  if (instruction.keys.length !== 12) {
    warnings.push(`Account count is ${instruction.keys.length}, expected 12 accounts`);
  }

  // Validate critical account positions
  if (instruction.keys.length >= 7) {
    // User wallet should be at position 6 and marked as signer
    if (!instruction.keys[6].pubkey.equals(userWallet)) {
      errors.push(`User wallet mismatch at position 6: expected ${userWallet.toString()}, got ${instruction.keys[6].pubkey.toString()}`);
    }
    if (!instruction.keys[6].isSigner) {
      errors.push('User wallet at position 6 is not marked as signer');
    }
    if (!instruction.keys[6].isWritable) {
      warnings.push('User wallet at position 6 is not marked as writable');
    }
  }

  // Validate system program at position 7
  if (instruction.keys.length >= 8) {
    const systemProgram = new PublicKey('********************************');
    if (!instruction.keys[7].pubkey.equals(systemProgram)) {
      errors.push(`System program mismatch at position 7: expected ${systemProgram.toString()}, got ${instruction.keys[7].pubkey.toString()}`);
    }
    if (instruction.keys[7].isSigner) {
      warnings.push('System program at position 7 should not be marked as signer');
    }
  }

  // Validate token program at position 8
  if (instruction.keys.length >= 9) {
    if (!instruction.keys[8].pubkey.equals(TOKEN_PROGRAM_ID)) {
      errors.push(`Token program mismatch at position 8: expected ${TOKEN_PROGRAM_ID.toString()}, got ${instruction.keys[8].pubkey.toString()}`);
    }
  }

  const valid = errors.length === 0;

  console.log(`PumpFun instruction validation result: ${valid ? 'VALID' : 'INVALID'}`);
  if (errors.length > 0) {
    console.log('Validation errors:', errors);
  }
  if (warnings.length > 0) {
    console.log('Validation warnings:', warnings);
  }

  return { valid, errors, warnings };
}

/**
 * Get the token account address for a pool
 * @param poolAddress Pool address
 * @param tokenMint Token mint address (not used in PumpFun's implementation, but kept for API compatibility)
 * @returns The PDA for the pool's token account
 */
export function getPoolTokenAccount(poolAddress: PublicKey, tokenMint?: PublicKey): PublicKey {
  // For known pools, use hardcoded values from observed transactions
  if (poolAddress.toString() === '81oRNu8jP5oeVNQL8injYe9DSfK5CnJUruBq3qGZsCj8') {
    // This is the observed token account from the transaction explorer for GxfZBiRyNbtPdPvxHyjnPKRJyR5mGJiCGY4wzKwUpump
    return new PublicKey('8ya7z8WgFtZmL5Nx3xqskX6VY9oMLJu7nMESkC45LEaR');
  }

  // Based on PumpFun program's actual implementation, the correct seed is 'bonding_token_account'
  const [vaultAccount] = PublicKey.findProgramAddressSync(
    [Buffer.from('bonding_token_account'), poolAddress.toBuffer()],
    PUMPFUN_PROGRAM_ID
  );

  console.log('Derived PDA token account for pool:', vaultAccount.toString());
  return vaultAccount;
}

/**
 * Create a buy instruction for the PumpFun program following the reference implementation
 * This follows the exact account ordering from the working reference
 */
export async function createCorrectBuyInstruction(
  poolAddress: PublicKey,
  tokenMint: PublicKey,
  userTokenAccount: PublicKey, // Not used directly, but kept for API compatibility
  userPublicKey: PublicKey,
  tokenAmount: bigint,
  maxSolCost: bigint,
  connection?: Connection
): Promise<TransactionInstruction> {
  console.log('Creating PumpFun buy instruction with:');
  console.log('Token amount:', tokenAmount.toString());
  console.log('Max SOL cost:', maxSolCost.toString());

  // Create instruction data: 8-byte discriminator + 8-byte amount + 8-byte maxCost (16 bytes total)
  const dataBuffer = Buffer.alloc(16);
  dataBuffer.writeBigUInt64LE(tokenAmount, 0);
  dataBuffer.writeBigUInt64LE(maxSolCost, 8);

  const buyInstructionData = Buffer.concat([BUY_DISCRIMINATOR, dataBuffer]);
  console.log('Buy instruction data (hex):', buyInstructionData.toString('hex'));

  // Get required addresses following reference implementation
  const bondingCurve = derivePumpFunBondingCurvePDA(tokenMint);
  const associatedBondingCurve = await getPumpFunAssociatedBondingCurve(tokenMint);
  const userTokenAccountAddr = await getAssociatedTokenAddress(tokenMint, userPublicKey);

  // Get creator from bonding curve state (or fallback to user)
  const creator = connection
    ? await getCreatorFromBondingCurve(connection, bondingCurve, userPublicKey)
    : userPublicKey;

  const creatorVault = derivePumpFunCreatorVaultPDA(creator);
  const eventAuthority = derivePumpFunEventAuthorityPDA();
  const globalConfig = derivePumpFunGlobalConfigPDA();
  const feeRecipient = await fetchPumpFunFeeRecipient(connection || new Connection('https://api.mainnet-beta.solana.com'));

  // Create accounts array following the reference implementation account order
  const keys = [
    // 1. Global state account (readonly)
    { pubkey: globalConfig, isSigner: false, isWritable: false },

    // 2. Fee recipient account (mutable)
    { pubkey: feeRecipient, isSigner: false, isWritable: true },

    // 3. Token mint account (readonly)
    { pubkey: tokenMint, isSigner: false, isWritable: false },

    // 4. Bonding curve account (mutable)
    { pubkey: bondingCurve, isSigner: false, isWritable: true },

    // 5. Associated bonding curve account (mutable)
    { pubkey: associatedBondingCurve, isSigner: false, isWritable: true },

    // 6. User token account (mutable)
    { pubkey: userTokenAccountAddr, isSigner: false, isWritable: true },

    // 7. User wallet (signer and SOL source, mutable)
    { pubkey: userPublicKey, isSigner: true, isWritable: true },

    // 8. System program (readonly)
    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },

    // 9. Token program (readonly)
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },

    // 10. Rent sysvar (readonly)
    { pubkey: new PublicKey('SysvarRent********************************1'), isSigner: false, isWritable: false },

    // 11. Event authority (readonly)
    { pubkey: eventAuthority, isSigner: false, isWritable: false },

    // 12. Program ID itself (readonly)
    { pubkey: PUMP_FUN_PROGRAM_ID, isSigner: false, isWritable: false }
  ];

  console.log('Final account structure:');
  keys.forEach((key, index) => {
    console.log(`${index}: ${key.pubkey.toString()} (signer: ${key.isSigner}, writable: ${key.isWritable})`);
  });

  return new TransactionInstruction({
    programId: PUMP_FUN_PROGRAM_ID,
    keys,
    data: buyInstructionData
  });
}

/**
 * Create a sell instruction for the PumpFun program following the reference implementation
 * This follows the exact account ordering from the working reference
 */
export async function createCorrectSellInstruction(
  poolAddress: PublicKey,
  tokenMint: PublicKey,
  _userTokenAccount: PublicKey, // Not used but kept for API compatibility
  userPublicKey: PublicKey,
  tokenAmount: bigint,
  minSolOutput: bigint,
  connection?: Connection
): Promise<TransactionInstruction> {
  console.log('Creating PumpFun sell instruction with:');
  console.log('Token amount:', tokenAmount.toString());
  console.log('Min SOL output:', minSolOutput.toString());

  // Create instruction data: 8-byte discriminator + 8-byte amount + 8-byte minOutput (16 bytes total)
  const dataBuffer = Buffer.alloc(16);
  dataBuffer.writeBigUInt64LE(tokenAmount, 0);
  dataBuffer.writeBigUInt64LE(minSolOutput, 8);

  const sellInstructionData = Buffer.concat([SELL_DISCRIMINATOR, dataBuffer]);
  console.log('Sell instruction data (hex):', sellInstructionData.toString('hex'));

  // Get required addresses following reference implementation
  const bondingCurve = derivePumpFunBondingCurvePDA(tokenMint);
  const associatedBondingCurve = await getPumpFunAssociatedBondingCurve(tokenMint);
  const userTokenAccountAddr = await getAssociatedTokenAddress(tokenMint, userPublicKey);

  // Get creator from bonding curve state (or fallback to user)
  const creator = connection
    ? await getCreatorFromBondingCurve(connection, bondingCurve, userPublicKey)
    : userPublicKey;

  const creatorVault = derivePumpFunCreatorVaultPDA(creator);
  const eventAuthority = derivePumpFunEventAuthorityPDA();
  const globalConfig = derivePumpFunGlobalConfigPDA();
  const feeRecipient = await fetchPumpFunFeeRecipient(connection || new Connection('https://api.mainnet-beta.solana.com'));

  // Create accounts array following the reference implementation account order
  const keys = [
    // 1. Global state account (readonly)
    { pubkey: globalConfig, isSigner: false, isWritable: false },

    // 2. Fee recipient account (mutable)
    { pubkey: feeRecipient, isSigner: false, isWritable: true },

    // 3. Token mint account (readonly)
    { pubkey: tokenMint, isSigner: false, isWritable: false },

    // 4. Bonding curve account (mutable)
    { pubkey: bondingCurve, isSigner: false, isWritable: true },

    // 5. Associated bonding curve account (mutable)
    { pubkey: associatedBondingCurve, isSigner: false, isWritable: true },

    // 6. User token account (mutable)
    { pubkey: userTokenAccountAddr, isSigner: false, isWritable: true },

    // 7. User wallet (signer and SOL source, mutable)
    { pubkey: userPublicKey, isSigner: true, isWritable: true },

    // 8. System program (readonly)
    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },

    // 9. Token program (readonly)
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },

    // 10. Rent sysvar (readonly)
    { pubkey: new PublicKey('SysvarRent********************************1'), isSigner: false, isWritable: false },

    // 11. Event authority (readonly)
    { pubkey: eventAuthority, isSigner: false, isWritable: false },

    // 12. Program ID itself (readonly)
    { pubkey: PUMP_FUN_PROGRAM_ID, isSigner: false, isWritable: false }
  ];

  console.log('Final sell account structure:');
  keys.forEach((key, index) => {
    console.log(`${index}: ${key.pubkey.toString()} (signer: ${key.isSigner}, writable: ${key.isWritable})`);
  });

  return new TransactionInstruction({
    programId: PUMP_FUN_PROGRAM_ID,
    keys,
    data: sellInstructionData
  });
}

// =============================================================================
// Transaction Fixing Utilities
// =============================================================================

/**
 * Specialized function to fix the token program ID issue in PumpFun transactions
 *
 * Based on extensive debugging, this ensures the account structure matches exactly
 * what the PumpFun program expects. Uses dynamic PDA derivation instead of hardcoded values.
 *
 * UPDATED: Fixed PDA derivation to use correct seed formats for Privy compatibility
 */
export function fixPumpFunTransaction(transaction: Transaction): Transaction {
  console.log('Applying specialized PumpFun transaction fix...');

  // Iterate through all instructions in the transaction
  for (const ix of transaction.instructions) {
    // Check if this is a PumpFun program instruction
    if (ix.programId.toString() === PUMPFUN_PROGRAM_ID.toString()) {
      console.log(`Found PumpFun instruction with ${ix.keys.length} accounts`);

      // Find the user wallet (should be at index 6)
      let userWallet = null;
      if (ix.keys.length > 6) {
        userWallet = ix.keys[6].pubkey;
        console.log(`User wallet at index 6: ${userWallet.toString()}`);
      }

      if (!userWallet) {
        console.warn('Could not find user wallet in transaction, skipping fix');
        continue;
      }

      // Find the token mint (should be at index 2)
      let tokenMint = null;
      if (ix.keys.length > 2) {
        tokenMint = ix.keys[2].pubkey;
        console.log(`Token mint at index 2: ${tokenMint.toString()}`);
      }

      if (!tokenMint) {
        console.warn('Could not find token mint in transaction, skipping fix');
        continue;
      }

      // Get pool address from the transaction (should be at index 3)
      let poolAddress = null;
      if (ix.keys.length > 3) {
        poolAddress = ix.keys[3].pubkey;
        console.log(`Pool address at index 3: ${poolAddress.toString()}`);
      }

      // Get all PDAs asynchronously - we'll use a promise and resolve it immediately
      // since we can't make this function async without changing all callers
      // Convert null to undefined for type safety
      getAllPumpFunPDAs(
        tokenMint as PublicKey,
        userWallet as PublicKey,
        undefined,
        poolAddress || undefined
      )
        .then(pdas => {
          console.log('Derived PDAs successfully:', pdas);

          // Update the associated token account in the transaction
          if (ix.keys.length > 5) {
            console.log(`Account at index 5 is currently: ${ix.keys[5].pubkey.toString()}`);

            // Force it to be the associated token account
            ix.keys[5] = {
              pubkey: pdas.associatedTokenAccount,
              isSigner: false,
              isWritable: true
            };

            console.log(`Fixed: Account at index 5 is now: ${ix.keys[5].pubkey.toString()} (Associated Token Account)`);
          }

          // Update the creator vault in the transaction
          if (ix.keys.length > 8) {
            console.log(`Account at index 8 is currently: ${ix.keys[8].pubkey.toString()}`);

            // Force it to be the creator vault
            ix.keys[8] = {
              pubkey: pdas.creatorVault,
              isSigner: false,
              isWritable: true
            };

            console.log(`Fixed: Account at index 8 is now: ${ix.keys[8].pubkey.toString()} (Creator Vault)`);
          }

          // Update the event authority in the transaction
          if (ix.keys.length > 10) {
            console.log(`Account at index 10 is currently: ${ix.keys[10].pubkey.toString()}`);

            // Force it to be the event authority
            ix.keys[10] = {
              pubkey: pdas.eventAuthority,
              isSigner: false,
              isWritable: false
            };

            console.log(`Fixed: Account at index 10 is now: ${ix.keys[10].pubkey.toString()} (Event Authority)`);
          }
        })
        .catch(error => {
          console.error('Error getting PDAs:', error);
        });

      // Use the event authority and creator vault from our utility functions
      const eventAuthority = EVENT_AUTHORITY;
      console.log('Using event authority PDA:', eventAuthority.toString());

      // Get the fee recipient (using the known value since we don't have a connection object here)
      const feeRecipient = new PublicKey('CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM');

      // Use the creator vault from our utility function
      const creatorVault = derivePumpFunCreatorVaultPDA(feeRecipient);
      console.log('Using creator vault PDA:', creatorVault.toString());

      // Get the associated token account for the user
      getAssociatedTokenAddress(tokenMint, userWallet)
        .then(associatedTokenAccount => {
          console.log(`Associated token account for user: ${associatedTokenAccount.toString()}`);

          // Ensure the associated token account (index 5) is the correct SPL token account
          if (ix.keys.length > 5) {
            console.log(`Account at index 5 is currently: ${ix.keys[5].pubkey.toString()}`);

            // Force it to be the associated token account
            ix.keys[5] = {
              pubkey: associatedTokenAccount,
              isSigner: false,
              isWritable: true
            };

            console.log(`Fixed: Account at index 5 is now: ${ix.keys[5].pubkey.toString()} (Associated Token Account)`);
          }
        })
        .catch(error => {
          console.error('Error getting associated token account:', error);
        });

      // Critical fix: ensure the 8th account (index 7) is the System Program
      if (ix.keys.length > 7) {
        console.log(`Account at index 7 is currently: ${ix.keys[7].pubkey.toString()}`);

        // Force it to be the System Program ID
        ix.keys[7] = {
          pubkey: SystemProgram.programId,
          isSigner: false,
          isWritable: false
        };

        console.log(`Fixed: Account at index 7 is now: ${ix.keys[7].pubkey.toString()}`);
      }

      // Check if this is a buy instruction (discriminator starts with 66063d12)
      const isBuyInstruction = Buffer.from(ix.data).slice(0, 4).toString('hex') === '66063d12';

      if (isBuyInstruction) {
        // For buy instructions, token program is at index 9
        if (ix.keys.length > 9) {
          console.log(`Account at index 9 is currently: ${ix.keys[9].pubkey.toString()}`);

          // Force it to be the Token Program ID
          ix.keys[9] = {
            pubkey: TOKEN_PROGRAM_ID,
            isSigner: false,
            isWritable: false
          };

          console.log(`Fixed: Account at index 9 is now: ${ix.keys[9].pubkey.toString()}`);
        }

        // Ensure creator vault is at index 8
        if (ix.keys.length > 8) {
          console.log(`Account at index 8 is currently: ${ix.keys[8].pubkey.toString()}`);

          // Force it to be the creator vault
          ix.keys[8] = {
            pubkey: creatorVault,
            isSigner: false,
            isWritable: true
          };

          console.log(`Fixed: Account at index 8 is now: ${ix.keys[8].pubkey.toString()}`);
        }
      } else {
        // For sell instructions, creator vault is at index 8 and token program at index 9
        if (ix.keys.length > 8) {
          console.log(`Account at index 8 is currently: ${ix.keys[8].pubkey.toString()}`);

          // Force it to be the creator vault
          ix.keys[8] = {
            pubkey: creatorVault,
            isSigner: false,
            isWritable: true
          };

          console.log(`Fixed: Account at index 8 is now: ${ix.keys[8].pubkey.toString()}`);
        }

        if (ix.keys.length > 9) {
          console.log(`Account at index 9 is currently: ${ix.keys[9].pubkey.toString()}`);

          // Force it to be the Token Program ID
          ix.keys[9] = {
            pubkey: TOKEN_PROGRAM_ID,
            isSigner: false,
            isWritable: false
          };

          console.log(`Fixed: Account at index 9 is now: ${ix.keys[9].pubkey.toString()}`);
        }
      }

      // Ensure event authority is at the correct position
      if (ix.keys.length > 10) {
        console.log(`Account at index 10 is currently: ${ix.keys[10].pubkey.toString()}`);

        // Force it to be the event authority
        ix.keys[10] = {
          pubkey: eventAuthority,
          isSigner: false,
          isWritable: false
        };

        console.log(`Fixed: Account at index 10 is now: ${ix.keys[10].pubkey.toString()}`);
      }

      // Log the final account structure for verification
      console.log('Final PumpFun instruction account structure:');
      ix.keys.forEach((account, idx) => {
        console.log(`Account ${idx}: ${account.pubkey.toString()} (isSigner: ${account.isSigner}, isWritable: ${account.isWritable})`);
      });
    }
  }

  // Return the modified transaction
  // No serialization needed - we've directly modified the original transaction
  return transaction;
}

/**
 * Replaces a PumpFun instruction in a transaction with a corrected version
 * This directly replaces the instruction instead of modifying account lists
 */
export function replacePumpFunInstructionInTransaction(
  transaction: any,
  poolAddress: PublicKey,
  tokenMint: PublicKey,
  userWallet: PublicKey,
  userTokenAccount: PublicKey,
  amount: bigint,
  maxSolCost: bigint
): boolean {
  // Find the PumpFun instruction in the transaction
  const pumpFunInstructionIndex = transaction.instructions.findIndex(
    (ix: any) => ix.programId.toString() === PUMPFUN_PROGRAM_ID.toString()
  );

  if (pumpFunInstructionIndex === -1) {
    console.error('No PumpFun instruction found in transaction');
    return false;
  }

  // Get the original instruction to extract account pubkeys
  const originalInstruction = transaction.instructions[pumpFunInstructionIndex];

  // Get the global account from the original instruction (first account)
  const globalAccount = originalInstruction.keys[0].pubkey;

  // Get the fee recipient from the original instruction (second account)
  const feeRecipient = originalInstruction.keys[1].pubkey;

  // Associated account is at index 4
  const associatedAccount = originalInstruction.keys[4].pubkey;

  // Vault account is at index 5
  const vaultAccount = originalInstruction.keys[5].pubkey;

  console.log('Creating fixed Anchor instruction with proper account ordering');
  console.log(`Token amount: ${amount}`);
  console.log(`Max SOL cost: ${maxSolCost}`);

  // Create a brand new instruction with the correct account structure
  const fixedInstruction = createAnchorCorrectBuyInstruction(
    globalAccount,
    feeRecipient,
    tokenMint,
    poolAddress,
    associatedAccount,
    vaultAccount,
    userWallet,
    userTokenAccount,
    amount,
    maxSolCost
  );

  // Replace the original instruction with our fixed one
  transaction.instructions[pumpFunInstructionIndex] = fixedInstruction;

  console.log('Successfully replaced PumpFun instruction with fixed version');
  console.log('New instruction account structure:');
  fixedInstruction.keys.forEach((acc, idx) => {
    console.log(`Account ${idx}: ${acc.pubkey.toString()} (isSigner: ${acc.isSigner}, isWritable: ${acc.isWritable})`);
  });

  return true;
}

/**
 * Creates a completely new PumpFun buy instruction with the proper account layout
 * This ensures the token program is at the correct position expected by Anchor
 * Uses dynamic PDA derivation instead of hardcoded values
 */
export function createAnchorCorrectBuyInstruction(
  globalAccount: PublicKey,
  feeRecipient: PublicKey,
  tokenMint: PublicKey,
  poolAccount: PublicKey,
  associatedAccount: PublicKey,
  _vaultAccount: PublicKey, // Not used but kept for API compatibility
  userWallet: PublicKey,
  _userTokenAccount: PublicKey, // Not used but kept for API compatibility
  amount: bigint,
  maxSolCost: bigint
): TransactionInstruction {
  // Dynamically derive the creator vault and event authority PDAs
  const creatorVault = derivePumpFunCreatorVaultPDA(feeRecipient);
  const eventAuthority = derivePumpFunEventAuthorityPDA();

  console.log('Using creator vault PDA:', creatorVault.toString());
  console.log('Using event authority PDA:', eventAuthority.toString());

  // Buy discriminator (first 8 bytes of sha256 hash of "global:buy")
  const discriminator = Buffer.from('66063d1201daebea', 'hex');

  // Create the data buffer: 8-byte discriminator + 8-byte token amount + 8-byte max SOL cost
  const data = Buffer.alloc(24);
  discriminator.copy(data, 0);
  data.writeBigUInt64LE(amount, 8);
  data.writeBigUInt64LE(maxSolCost, 16);

  // Derive the associated_user PDA using the user's wallet address
  const [associatedUserPDA] = PublicKey.findProgramAddressSync(
    [Buffer.from('associated_user'), userWallet.toBuffer()],
    PUMPFUN_PROGRAM_ID
  );
  console.log('Using associated user PDA:', associatedUserPDA.toString());

  // Create the account metas in EXACTLY the order required by Anchor
  const keys = [
    // 1. Global account
    { pubkey: globalAccount, isSigner: false, isWritable: false },

    // 2. Fee recipient account
    { pubkey: feeRecipient, isSigner: false, isWritable: true },

    // 3. Token mint
    { pubkey: tokenMint, isSigner: false, isWritable: false },

    // 4. Pool account
    { pubkey: poolAccount, isSigner: false, isWritable: true },

    // 5. Associated account
    { pubkey: associatedAccount, isSigner: false, isWritable: true },

    // 6. Associated user PDA (owned by PumpFun program)
    { pubkey: associatedUserPDA, isSigner: false, isWritable: true },

    // 7. User wallet (signer and SOL source)
    { pubkey: userWallet, isSigner: true, isWritable: true },

    // 8. System program - CRITICAL POSITION
    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },

    // 9. Creator vault (mutable)
    { pubkey: creatorVault, isSigner: false, isWritable: true },

    // 10. Token program - CRITICAL POSITION
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },

    // 11. Event authority (readonly)
    { pubkey: eventAuthority, isSigner: false, isWritable: false },

    // 12. Program ID itself (readonly) - MUST be the last account
    { pubkey: PUMPFUN_PROGRAM_ID, isSigner: false, isWritable: false }
  ];

  // Create and return the instruction
  return new TransactionInstruction({
    programId: PUMPFUN_PROGRAM_ID,
    keys,
    data
  });
}

// =============================================================================
// Validator Functions
// =============================================================================

/**
 * Validates that a PumpFun instruction has the correct account structure
 * This can be used to verify transactions before sending them
 * Uses dynamic PDA derivation instead of hardcoded values
 *
 * UPDATED: Fixed PDA derivation to use correct seed formats for Privy compatibility
 */
export function validatePumpFunInstruction(instruction: TransactionInstruction): { valid: boolean, errors: string[] } {
  const errors: string[] = [];

  // Check that the program ID is correct
  if (instruction.programId.toString() !== PUMPFUN_PROGRAM_ID.toString()) {
    errors.push(`Invalid program ID: expected ${PUMPFUN_PROGRAM_ID}, got ${instruction.programId}`);
  }

  // Check if this is a buy instruction (discriminator starts with 66063d12)
  // Note: We're not using this variable directly, but it's useful for debugging
  const instructionType = Buffer.from(instruction.data).slice(0, 4).toString('hex') === '66063d12' ? 'buy' : 'sell';
  console.log(`Validating ${instructionType} instruction`);

  // Check that there are exactly 12 accounts
  if (instruction.keys.length !== 12) {
    errors.push(`Invalid account count: expected 12, got ${instruction.keys.length}`);
  }

  // Critical positions checks
  if (instruction.keys.length > 7) {
    const systemProgram = instruction.keys[7].pubkey.toString();
    if (systemProgram !== SystemProgram.programId.toString()) {
      errors.push(`Invalid System Program at index 7: expected ${SystemProgram.programId}, got ${systemProgram}`);
    }
  }

  // Get the fee recipient (using the known value since we don't have a connection object here)
  const feeRecipient = new PublicKey('CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM');

  // Use the creator vault from our utility function
  const creatorVaultForValidation = derivePumpFunCreatorVaultPDA(feeRecipient);

  // Check creator vault position
  if (instruction.keys.length > 8) {
    const creatorVaultAccount = instruction.keys[8].pubkey.toString();
    if (creatorVaultAccount !== creatorVaultForValidation.toString()) {
      errors.push(`Invalid Creator Vault at index 8: expected ${creatorVaultForValidation}, got ${creatorVaultAccount}`);
    }
  }

  // Check token program position (same for both buy and sell)
  if (instruction.keys.length > 9) {
    const tokenProgram = instruction.keys[9].pubkey.toString();
    if (tokenProgram !== TOKEN_PROGRAM_ID.toString()) {
      errors.push(`Invalid Token Program at index 9: expected ${TOKEN_PROGRAM_ID}, got ${tokenProgram}`);
    }
  }

  // Use the event authority from our constant
  const eventAuthorityForValidation = EVENT_AUTHORITY;

  // Check event authority position
  if (instruction.keys.length > 10) {
    const eventAuthorityAccount = instruction.keys[10].pubkey.toString();
    if (eventAuthorityAccount !== eventAuthorityForValidation.toString()) {
      errors.push(`Invalid Event Authority at index 10: expected ${eventAuthorityForValidation}, got ${eventAuthorityAccount}`);
    }
  }

  // Check if we can find the user wallet (should be at index 6)
  if (instruction.keys.length > 6) {
    // Log the user wallet for debugging
    console.log(`User wallet at index 6: ${instruction.keys[6].pubkey.toString()}`);

    // Check if the user wallet is a signer
    if (!instruction.keys[6].isSigner) {
      errors.push(`User wallet at index 6 must be a signer`);
    }

    // Check if the user wallet is writable
    if (!instruction.keys[6].isWritable) {
      errors.push(`User wallet at index 6 must be writable`);
    }

    // If we have a token mint (index 2), we can check the associated token account
    if (instruction.keys.length > 2 && instruction.keys.length > 5) {
      // Log the token mint for debugging
      console.log(`Token mint at index 2: ${instruction.keys[2].pubkey.toString()}`);

      // We can't validate the associated token account here because getAssociatedTokenAddress is async
      // But we can log a warning
      console.log(`Cannot validate associated token account at index 5 in validatePumpFunInstruction (async operation)`);
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}
