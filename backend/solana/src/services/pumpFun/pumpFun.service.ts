import { Connection, PublicKey, Transaction, SystemProgram, TransactionInstruction, ComputeBudgetProgram } from '@solana/web3.js';
import { createAssociatedTokenAccountInstruction, getAssociatedTokenAddress } from '@solana/spl-token';
import { PumpFunPoolData, QuoteR<PERSON>ult, PoolStatus } from '../../types/pump.types';
// Import the corrected implementation that uses proper Anchor instruction format
import { createCorrectBuyInstruction, createCorrectSellInstruction, PUMPFUN_PROGRAM_ID } from './pumpFunUtils';
import {
  readBigUInt64LE,
  calculateBondingCurveOutput,
  SOL_DECIMALS,
  solToLamports,
  tokenToRaw,
  rawToToken,
  calculatePlatformFee,
  PUMPFUN_SWAP_FEE_RATIO
} from './utils.service';
import { config } from '../../config';

// Constants are now imported from pumpFunUtils.ts

/**
 * Check if a PumpFun pool exists and get its data
 * @param connection Solana connection
 * @param poolAddress Pool address
 * @returns Pool data
 */
export async function checkPumpFunPool(
  connection: Connection,
  poolAddress: PublicKey
): Promise<PumpFunPoolData> {
  try {
    const accountInfo = await connection.getAccountInfo(poolAddress);

    if (!accountInfo) {
      console.log('❌ No PumpFun pool found at the provided address');
      return { exists: false };
    }

    console.log('✅ PumpFun pool FOUND!');
    console.log(`🔸 Owner: ${accountInfo.owner.toString()}`);
    console.log(`🔸 Data Size: ${accountInfo.data.length} bytes`);

    try {
      // Validate account data
      if (!accountInfo.data || accountInfo.data.length < 41) { // 8 (discriminator) + 5*8 (bigints) + 1 (complete flag)
        console.error(`❌ Invalid PumpFun pool data format: Data too short (${accountInfo.data.length} bytes)`);
        return { exists: false };
      }

      const data = accountInfo.data;
      let offset = 8; // skip discriminator

      try {
        const virtualTokenReserves = readBigUInt64LE(data, offset);
        offset += 8;

        const virtualSolReserves = readBigUInt64LE(data, offset);
        offset += 8;

        const realTokenReserves = readBigUInt64LE(data, offset);
        offset += 8;

        const realSolReserves = readBigUInt64LE(data, offset);
        offset += 8;

        const tokenTotalSupply = readBigUInt64LE(data, offset);
        offset += 8;

        // Validate pool values
        if (virtualTokenReserves === BigInt(0) || virtualSolReserves === BigInt(0)) {
          console.warn('⚠️ PumpFun pool has zero reserves');
        }

        const complete = data[offset] === 1;
        const feeRatio = PUMPFUN_SWAP_FEE_RATIO; // Use the hardcoded 1% fee ratio for PumpFun pools

        console.log('PumpFun pool details:');
        console.log(`- Virtual token reserves: ${virtualTokenReserves}`);
        console.log(`- Virtual SOL reserves: ${virtualSolReserves}`);
        console.log(`- Real token reserves: ${realTokenReserves}`);
        console.log(`- Real SOL reserves: ${realSolReserves}`);
        console.log(`- Token total supply: ${tokenTotalSupply}`);
        console.log(`- Complete: ${complete}`);

        return {
          exists: true,
          isGraduated: complete,
          virtualTokenReserves,
          virtualSolReserves,
          realTokenReserves,
          realSolReserves,
          tokenTotalSupply,
          feeRatio,
          owner: accountInfo.owner
        };
      } catch (parseError) {
        console.error(`❌ Error parsing PumpFun pool data at offset ${offset}: ${(parseError as Error).message}`);
        console.error(`Failed to parse at offset ${offset}`);
        return { exists: false };
      }
    } catch (error) {
      console.error(`❌ Error processing PumpFun pool data: ${(error as Error).message}`);
      console.error(error);
      return { exists: false };
    }
  } catch (error) {
    console.log(`❌ Error fetching PumpFun pool: ${(error as Error).message}`);
    return { exists: false };
  }
}

/**
 * Calculate a quote for a PumpFun pool
 * @param amount Amount to swap
 * @param isBuy Whether this is a buy or sell
 * @param poolData Pool data
 * @param tokenDecimals Token decimals
 * @returns Quote result
 */
export function calculateDirectQuote(
  amount: number,
  isBuy: boolean,
  poolData: PumpFunPoolData,
  tokenDecimals: number
): QuoteResult {
  if (!poolData.virtualSolReserves || !poolData.virtualTokenReserves || !poolData.feeRatio) {
    throw new Error('Invalid pool data for quote calculation');
  }

  let inputAmount: bigint;
  let outputAmount: bigint;
  let price: number;

  if (isBuy) {
    inputAmount = solToLamports(amount);
    outputAmount = calculateBondingCurveOutput(
      poolData.virtualSolReserves,
      poolData.virtualTokenReserves,
      inputAmount,
      true,
      poolData.feeRatio
    );
    const outputAmountFloat = rawToToken(outputAmount, tokenDecimals);
    price = amount / outputAmountFloat;
    return { outAmount: outputAmountFloat, price, outputAmountRaw: outputAmount };
  } else {
    inputAmount = tokenToRaw(amount, tokenDecimals);
    outputAmount = calculateBondingCurveOutput(
      poolData.virtualSolReserves,
      poolData.virtualTokenReserves,
      inputAmount,
      false,
      poolData.feeRatio
    );
    const outputAmountFloat = rawToToken(outputAmount, SOL_DECIMALS);
    price = outputAmountFloat / amount;
    return { outAmount: outputAmountFloat, price, outputAmountRaw: outputAmount };
  }
}

/**
 * Get the status of a PumpFun pool
 * @param poolData Pool data
 * @returns Pool status
 */
export function getPumpFunPoolStatus(poolData: PumpFunPoolData): PoolStatus {
  if (!poolData.exists) {
    return PoolStatus.Unknown;
  }

  if (poolData.isGraduated) {
    return PoolStatus.Graduated;
  }

  return PoolStatus.Active;
}

/**
 * Create a swap transaction for a PumpFun pool
 * @param connection Solana connection
 * @param poolAddress Pool address
 * @param tokenMint Token mint address
 * @param userPublicKey User's public key
 * @param platformFeeWallet Platform fee wallet address
 * @param amount Amount to swap
 * @param isBuy Whether this is a buy or sell
 * @param slippage Slippage tolerance (e.g., 0.01 for 1%)
 * @param quoteResult Quote result
 * @param poolData Pool data
 * @returns Transaction
 */
export async function createPumpFunSwapTransaction(
  connection: Connection,
  poolAddress: PublicKey,
  tokenMint: PublicKey,
  userPublicKey: PublicKey,
  platformFeeWallet: PublicKey,
  amount: number,
  isBuy: boolean,
  slippage: number,
  quoteResult: QuoteResult,
  poolData?: PumpFunPoolData,
  tokenDecimals: number = 9, // Default to 9 decimals if not provided
  useUserAsFeePayer: boolean = false // Set to true when using Privy for signing
): Promise<Transaction> {
  if (!poolData?.owner) {
    throw new Error('Pool owner not found');
  }

  // Get the associated token account for the user
  const userTokenAccount = await getAssociatedTokenAddress(tokenMint, userPublicKey);

  // Check if the user token account exists, and if not, add an instruction to create it
  let createAtaIx: TransactionInstruction | null = null;
  try {
    const accountInfo = await connection.getAccountInfo(userTokenAccount);
    if (!accountInfo) {
      console.log('User token account does not exist, creating it...');
      createAtaIx = createAssociatedTokenAccountInstruction(
        userPublicKey,
        userTokenAccount,
        userPublicKey,
        tokenMint
      );
    } else {
      console.log('User token account exists');
    }
  } catch (error) {
    console.log('Error checking user token account, assuming it needs to be created');
    createAtaIx = createAssociatedTokenAccountInstruction(
      userPublicKey,
      userTokenAccount,
      userPublicKey,
      tokenMint
    );
  }

  const transaction = new Transaction();

  // Get the platform keypair from config
  const platformKeypair = config.platformKeypair;
  if (!platformKeypair) {
    throw new Error('Platform keypair not found. Please check your environment variables.');
  }

  // Set the platform as the fee payer
  const platformPublicKey = platformKeypair.publicKey;

  // Validate input parameters
  if (slippage < 0 || slippage > 1) {
    throw new Error('Slippage must be between 0 and 1');
  }

  if (amount <= 0) {
    throw new Error('Swap amount must be greater than zero');
  }

  // We no longer need to derive PDAs here as they are properly derived in the createCorrectBuyInstruction
  // and createCorrectSellInstruction functions

  // We no longer need to define accounts here as they are properly defined in the createCorrectBuyInstruction
  // and createCorrectSellInstruction functions

  // Prepare transaction data based on swap direction
  if (isBuy) {
    // ==================== BUY OPERATION (SOL → Token) ====================
    console.log(`Creating PumpFun BUY transaction for ${amount} SOL`);

    // Convert SOL amount to lamports for on-chain operations
    const buyAmountLamports = solToLamports(amount);

    // Calculate platform fee based on input amount (standardized approach)
    const platformFeeLamports = calculatePlatformFee(buyAmountLamports, 'pumpfun');
    console.log(`Platform fee: ${platformFeeLamports} lamports`);

    // For buy transactions in Anchor, we need:
    // 1. Expected token output amount (from quote)
    // 2. Maximum SOL to spend (input + slippage)

    // Calculate expected token output (already in quoteResult.outputAmountRaw)
    const expectedTokenOutput = quoteResult.outputAmountRaw;
    console.log(`Expected token output: ${expectedTokenOutput} raw tokens`);

    // Calculate maximum SOL to spend with slippage buffer
    const maxSolToSpend = BigInt(Math.ceil(Number(buyAmountLamports) * (1 + slippage)));
    console.log(`Maximum SOL to spend (with ${slippage * 100}% slippage): ${maxSolToSpend} lamports`);

    // Create platform fee instruction (SOL transfer)
    const platformFeeInstruction = SystemProgram.transfer({
      fromPubkey: userPublicKey,
      toPubkey: platformFeeWallet,
      lamports: platformFeeLamports,
    });

    // Derive the associated_user PDA for this user
    const [associatedUserPDA] = PublicKey.findProgramAddressSync(
      [Buffer.from('associated_user'), userPublicKey.toBuffer()],
      PUMPFUN_PROGRAM_ID
    );
    console.log('Using associated user PDA:', associatedUserPDA.toString());

    // Create an instruction to initialize the associated_user PDA account
    // This is needed because the PumpFun program expects this account to be initialized
    const initAssociatedUserInstruction = SystemProgram.createAccount({
      fromPubkey: userPublicKey,
      newAccountPubkey: associatedUserPDA,
      space: 0, // Minimal space needed
      lamports: await connection.getMinimumBalanceForRentExemption(0),
      programId: PUMPFUN_PROGRAM_ID
    });

    // Create swap instruction using direct implementation that matches explorer transactions
    console.log('Creating buy instruction with token amount:', expectedTokenOutput.toString());
    console.log('Max SOL to spend:', maxSolToSpend.toString());

    const swapInstruction = await createCorrectBuyInstruction(
      poolAddress,
      tokenMint,
      userTokenAccount,
      userPublicKey,
      expectedTokenOutput, // Amount of tokens to buy
      maxSolToSpend // Maximum SOL to spend
    );

    // Add Compute Budget Instructions first (based on Solscan example)
    // 1. Set compute unit limit to 100,000
    const computeUnitLimitIx = ComputeBudgetProgram.setComputeUnitLimit({
      units: 100000
    });

    // 2. Set compute unit price to 200,000 micro lamports (much more reasonable than 10M)
    const computeUnitPriceIx = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports: 200000
    });

    // Add instructions in proper order (exactly matching Solscan transaction structure)
    // 1. Compute budget instructions MUST come first
    transaction.add(computeUnitLimitIx);
    transaction.add(computeUnitPriceIx);
    // 2. Platform fee instruction
    transaction.add(platformFeeInstruction);

    // 3. Initialize the associated_user PDA account
    try {
      // Check if the associated user PDA account already exists
      const associatedUserInfo = await connection.getAccountInfo(associatedUserPDA);
      if (!associatedUserInfo) {
        console.log('Associated user PDA account does not exist, creating it...');
        transaction.add(initAssociatedUserInstruction);
      } else {
        console.log('Associated user PDA account already exists');
      }
    } catch (error) {
      console.log('Error checking associated user PDA account, assuming it needs to be created');
      transaction.add(initAssociatedUserInstruction);
    }

    // 4. Create token account if needed
    if (createAtaIx) {
      console.log('Adding instruction to create user token account');
      transaction.add(createAtaIx);
    }

    // 4. Swap instruction
    console.log('Swap instruction accounts:');
    swapInstruction.keys.forEach((account, idx) => {
      console.log(`Account ${idx}: ${account.pubkey.toString()} (isSigner: ${account.isSigner}, isWritable: ${account.isWritable})`);
    });
    transaction.add(swapInstruction);
  } else {
    // ==================== SELL OPERATION (Token → SOL) ====================
    console.log(`Creating PumpFun SELL transaction for ${amount} tokens`);

    // STEP 1: Validate user's token balance before proceeding
    console.log('=== TOKEN BALANCE VALIDATION ===');
    console.log(`Checking token balance for user: ${userPublicKey.toString()}`);
    console.log(`Token mint: ${tokenMint.toString()}`);
    console.log(`User token account: ${userTokenAccount.toString()}`);
    console.log(`Requested sell amount: ${amount} tokens`);

    try {
      // Check if the user's token account exists
      const tokenAccountInfo = await connection.getAccountInfo(userTokenAccount);

      if (!tokenAccountInfo) {
        const errorMessage = `Token account does not exist for user ${userPublicKey.toString()}. User has no tokens to sell.`;
        console.error(errorMessage);
        throw new Error(`Insufficient token balance: User has 0 tokens, requested to sell ${amount} tokens. Shortfall: ${amount} tokens.`);
      }

      // Get the user's current token balance
      const tokenBalance = await connection.getTokenAccountBalance(userTokenAccount);
      const currentBalance = parseFloat(tokenBalance.value.uiAmountString || '0');

      console.log(`Current token balance: ${currentBalance} tokens`);
      console.log(`Token balance (raw): ${tokenBalance.value.amount}`);
      console.log(`Token decimals: ${tokenBalance.value.decimals}`);

      // Validate that user has sufficient tokens with tolerance for floating-point precision
      const tolerance = 0.000001; // 1 microtoken tolerance
      if (currentBalance < (amount - tolerance)) {
        const shortfall = amount - currentBalance;
        const errorMessage = `Insufficient token balance: User has ${currentBalance} tokens, requested to sell ${amount} tokens. Shortfall: ${shortfall.toFixed(6)} tokens.`;
        console.error(errorMessage);
        throw new Error(errorMessage);
      }

      // If the amount is very close to the balance (within tolerance), use the exact balance
      if (Math.abs(currentBalance - amount) <= tolerance && currentBalance < amount) {
        console.log(`Adjusting sell amount from ${amount} to exact balance ${currentBalance} (within tolerance)`);
        amount = currentBalance;
      }

      console.log(`✅ Token balance validation passed: User has ${currentBalance} tokens, selling ${amount} tokens`);
      console.log(`Remaining balance after sell: ${(currentBalance - amount).toFixed(6)} tokens`);

    } catch (error: any) {
      if (error.message && error.message.includes('Insufficient token balance')) {
        throw error; // Re-throw our custom error
      }
      // Handle other errors (network issues, etc.)
      console.error('Error validating token balance:', error);
      throw new Error(`Failed to validate token balance: ${error?.message || 'Unknown error'}`);
    }

    // Convert token amount to raw value for on-chain operations
    const sellAmountTokens = tokenToRaw(amount, tokenDecimals);

    // Calculate platform fee based on input value (standardized approach)
    // Calculate equivalent SOL value of tokens for fee calculation
    const tokenValueInSol = amount * quoteResult.price; // price = SOL per token
    const platformFeeLamports = calculatePlatformFee(solToLamports(tokenValueInSol), 'pumpfun');
    console.log(`Platform fee: ${platformFeeLamports} lamports`);

    // For sell transactions in Anchor, we need:
    // 1. Token amount to sell (input amount)
    // 2. Minimum SOL to receive (output with slippage protection)

    // Token amount to sell is already computed as sellAmountTokens
    console.log(`Token amount to sell: ${sellAmountTokens} raw tokens`);

    // Calculate minimum SOL output with slippage protection
    const minSolOutput = BigInt(Math.floor(Number(quoteResult.outputAmountRaw) * (1 - slippage)));
    console.log(`Minimum SOL output (with ${slippage * 100}% slippage): ${minSolOutput} lamports`);

    // Create platform fee instruction (SOL transfer)
    const platformFeeInstruction = SystemProgram.transfer({
      fromPubkey: userPublicKey,
      toPubkey: platformFeeWallet,
      lamports: platformFeeLamports,
    });

    // Derive the associated_user PDA for this user
    const [associatedUserPDA] = PublicKey.findProgramAddressSync(
      [Buffer.from('associated_user'), userPublicKey.toBuffer()],
      PUMPFUN_PROGRAM_ID
    );
    console.log('Using associated user PDA:', associatedUserPDA.toString());

    // Create an instruction to initialize the associated_user PDA account
    // This is needed because the PumpFun program expects this account to be initialized
    const initAssociatedUserInstruction = SystemProgram.createAccount({
      fromPubkey: userPublicKey,
      newAccountPubkey: associatedUserPDA,
      space: 0, // Minimal space needed
      lamports: await connection.getMinimumBalanceForRentExemption(0),
      programId: PUMPFUN_PROGRAM_ID
    });

    // Create swap instruction using direct implementation that matches explorer transactions
    console.log('Creating sell instruction with token amount:', sellAmountTokens.toString());
    console.log('Min SOL output:', minSolOutput.toString());

    const swapInstruction = await createCorrectSellInstruction(
      poolAddress,
      tokenMint,
      userTokenAccount,
      userPublicKey,
      sellAmountTokens, // Amount of tokens to sell
      minSolOutput // Minimum SOL output
    );

    // Add Compute Budget Instructions first (based on Solscan example)
    // 1. Set compute unit limit to 100,000
    const computeUnitLimitIx = ComputeBudgetProgram.setComputeUnitLimit({
      units: 100000
    });

    // 2. Set compute unit price to 200,000 micro lamports (much more reasonable than 10M)
    const computeUnitPriceIx = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports: 200000
    });

    // Add instructions in proper order (exactly matching Solscan transaction structure)
    // 1. Compute budget instructions MUST come first
    transaction.add(computeUnitLimitIx);
    transaction.add(computeUnitPriceIx);
    // 2. Platform fee instruction
    transaction.add(platformFeeInstruction);

    // 3. Initialize the associated_user PDA account
    try {
      // Check if the associated user PDA account already exists
      const associatedUserInfo = await connection.getAccountInfo(associatedUserPDA);
      if (!associatedUserInfo) {
        console.log('Associated user PDA account does not exist, creating it...');
        transaction.add(initAssociatedUserInstruction);
      } else {
        console.log('Associated user PDA account already exists');
      }
    } catch (error) {
      console.log('Error checking associated user PDA account, assuming it needs to be created');
      transaction.add(initAssociatedUserInstruction);
    }

    // 4. Create token account if needed
    if (createAtaIx) {
      console.log('Adding instruction to create user token account in sell flow');
      transaction.add(createAtaIx);
    }
    // 3. Swap instruction
    console.log('Swap instruction accounts:');
    swapInstruction.keys.forEach((account, idx) => {
      console.log(`Account ${idx}: ${account.pubkey.toString()} (isSigner: ${account.isSigner}, isWritable: ${account.isWritable})`);
    });
    transaction.add(swapInstruction);
  }

  // We've moved the transaction creation logic to the pumpFunAnchor.ts helper module
  // with correct Anchor discriminators and account structure

  // Set the fee payer based on the useUserAsFeePayer parameter
  if (useUserAsFeePayer) {
    // When using Privy for signing, the user's wallet must be the fee payer
    transaction.feePayer = userPublicKey;
    console.log(`Using user wallet as fee payer for transaction: ${userPublicKey.toString()}`);
  } else {
    // Default behavior: use platform wallet as fee payer
    transaction.feePayer = platformPublicKey;
    console.log(`Using platform wallet as fee payer for transaction: ${platformPublicKey.toString()}`);
  }

  // Don't set blockhash or sign here - this will be done in signAndSendTransactionWithPrivy
  // This way we avoid signature verification issues when the blockhash is updated

  return transaction;
}
