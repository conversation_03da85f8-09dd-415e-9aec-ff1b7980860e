import { Connection, PublicKey } from '@solana/web3.js';
import { Program, AnchorProvider, BN } from '@coral-xyz/anchor';
import { PUMP_SWAP_PROGRAM_ID } from '../../utils/idl.utils';

/**
 * SDK Utility Functions for PumpSwap Integration
 *
 * This file provides utility functions that mirror the @pump-swap-sdk approach
 * for proper Anchor program integration and mathematical calculations.
 */

/**
 * Pool interface matching the SDK's Pool type
 */
export interface Pool {
  poolBump: number;
  index: number;
  creator: PublicKey;
  baseMint: PublicKey;
  quoteMint: PublicKey;
  lpMint: PublicKey;
  poolBaseTokenAccount: PublicKey;
  poolQuoteTokenAccount: PublicKey;
  lpSupply: BN;
  coinCreator: PublicKey;
}

/**
 * Global config interface for fee rates
 */
export interface GlobalConfig {
  lpFeeBasisPoints: BN;
  protocolFeeBasisPoints: BN;
  coinCreatorFeeBasisPoints: BN;
  protocolFeeRecipients: PublicKey[];
  admin: PublicKey;
}

/**
 * Buy calculation result interface
 */
export interface BuyBaseInputResult {
  uiQuote: BN;
  maxQuote: BN;
  internalQuoteAmountIn: BN;
}

/**
 * Buy quote input calculation result interface (SOL input -> tokens output)
 */
export interface BuyQuoteInputResult {
  baseAmountOut: BN;
  minBaseAmountOut: BN;
  internalBaseAmountOut: BN;
}

/**
 * Sell calculation result interface
 */
export interface SellBaseInputResult {
  uiQuote: BN;
  minQuote: BN;
  internalQuoteAmountOut: BN;
}

/**
 * Ceiling division function from SDK
 * @param a Numerator
 * @param b Denominator
 * @returns Ceiling of a/b
 */
export function ceilDiv(a: BN, b: BN): BN {
  if (b.isZero()) {
    throw new Error("Cannot divide by zero.");
  }
  return a.add(b.subn(1)).div(b);
}

/**
 * Fee calculation function from SDK
 * @param amount Amount to calculate fee on
 * @param basisPoints Fee in basis points
 * @returns Fee amount
 */
export function fee(amount: BN, basisPoints: BN): BN {
  return ceilDiv(amount.mul(basisPoints), new BN(10_000));
}

/**
 * Create a minimal Anchor program instance for account decoding
 * @param connection Solana connection
 * @param programId Program ID
 * @returns Anchor program instance
 */
export function createPumpAmmProgram(connection: Connection, programId: PublicKey = PUMP_SWAP_PROGRAM_ID) {
  // Create a minimal provider for account decoding
  const provider = new AnchorProvider(connection, null as any, {});

  // Create a minimal program instance with the IDL structure we need
  // Include the discriminators from the actual IDL file for proper account decoding
  const program = new Program(
    {
      version: "0.1.0",
      name: "pump_amm",
      address: programId.toString(),
      instructions: [],
      accounts: [
        {
          name: "Pool",
          discriminator: [241, 154, 109, 4, 17, 177, 109, 188],
          type: {
            kind: "struct",
            fields: [
              { name: "poolBump", type: "u8" },
              { name: "index", type: "u16" },
              { name: "creator", type: "pubkey" },
              { name: "baseMint", type: "pubkey" },
              { name: "quoteMint", type: "pubkey" },
              { name: "lpMint", type: "pubkey" },
              { name: "poolBaseTokenAccount", type: "pubkey" },
              { name: "poolQuoteTokenAccount", type: "pubkey" },
              { name: "lpSupply", type: "u64" },
              { name: "coinCreator", type: "pubkey" }
            ]
          }
        },
        {
          name: "GlobalConfig",
          discriminator: [149, 8, 156, 202, 160, 252, 176, 217],
          type: {
            kind: "struct",
            fields: [
              { name: "lpFeeBasisPoints", type: "u64" },
              { name: "protocolFeeBasisPoints", type: "u64" },
              { name: "coinCreatorFeeBasisPoints", type: "u64" },
              { name: "protocolFeeRecipients", type: { vec: "pubkey" } },
              { name: "admin", type: "pubkey" }
            ]
          }
        }
      ],
      types: [],
      events: [],
      errors: [],
      metadata: {
        address: programId.toString()
      }
    } as any,
    provider
  );

  return program;
}

/**
 * Buy calculation using SDK's algorithm
 * @param base Base amount to buy
 * @param slippage Slippage percentage (e.g., 1 for 1%)
 * @param baseReserve Pool base reserve
 * @param quoteReserve Pool quote reserve
 * @param lpFeeBps LP fee in basis points
 * @param protocolFeeBps Protocol fee in basis points
 * @param coinCreatorFeeBps Coin creator fee in basis points
 * @param coinCreator Coin creator public key
 * @returns Buy calculation result
 */
export function buyBaseInputInternal(
  base: BN,
  slippage: number,
  baseReserve: BN,
  quoteReserve: BN,
  lpFeeBps: BN,
  protocolFeeBps: BN,
  coinCreatorFeeBps: BN,
  coinCreator: PublicKey,
): BuyBaseInputResult {
  // Basic validations
  if (baseReserve.isZero() || quoteReserve.isZero()) {
    throw new Error("Invalid input: 'baseReserve' or 'quoteReserve' cannot be zero.");
  }
  if (base.gt(baseReserve)) {
    throw new Error("Cannot buy more base tokens than the pool reserves.");
  }

  // Calculate quote amount needed using constant product formula
  const numerator = quoteReserve.mul(base);
  const denominator = baseReserve.sub(base);

  if (denominator.isZero()) {
    throw new Error("Pool would be depleted; denominator is zero.");
  }

  const quoteAmountIn = ceilDiv(numerator, denominator);

  // Calculate fees
  const lpFee = fee(quoteAmountIn, lpFeeBps);
  const protocolFee = fee(quoteAmountIn, protocolFeeBps);
  const coinCreatorFee = PublicKey.default.equals(coinCreator)
    ? new BN(0)
    : fee(quoteAmountIn, coinCreatorFeeBps);

  const totalQuote = quoteAmountIn
    .add(lpFee)
    .add(protocolFee)
    .add(coinCreatorFee);

  // Calculate max quote with slippage
  const precision = new BN(1_000_000_000);
  const slippageFactorFloat = (1 + slippage / 100) * 1_000_000_000;
  const slippageFactor = new BN(Math.floor(slippageFactorFloat));
  const maxQuote = totalQuote.mul(slippageFactor).div(precision);

  return {
    uiQuote: totalQuote,
    maxQuote,
    internalQuoteAmountIn: quoteAmountIn,
  };
}

/**
 * Calculate buy quote using SOL input (quote input) - how many tokens can be bought with a specific amount of SOL
 * This is the correct function to use when the user specifies how much SOL they want to spend
 *
 * @param quoteAmountIn Amount of SOL to spend (in lamports)
 * @param slippage Slippage percentage (e.g., 1 for 1%)
 * @param baseReserve Current token reserve in the pool
 * @param quoteReserve Current SOL reserve in the pool
 * @param lpFeeBps LP fee in basis points
 * @param protocolFeeBps Protocol fee in basis points
 * @param coinCreatorFeeBps Coin creator fee in basis points
 * @param coinCreator Coin creator public key
 * @returns Buy calculation result with tokens output
 */
export function buyQuoteInputInternal(
  quoteAmountIn: BN,
  slippage: number,
  baseReserve: BN,
  quoteReserve: BN,
  lpFeeBps: BN,
  protocolFeeBps: BN,
  coinCreatorFeeBps: BN,
  coinCreator: PublicKey,
): BuyQuoteInputResult {
  console.log(`🧮 [SDK] buyQuoteInputInternal calculation (SOL input -> tokens output):`);
  console.log(`  - Quote amount in (SOL to spend): ${quoteAmountIn.toString()}`);
  console.log(`  - Base reserve: ${baseReserve.toString()}`);
  console.log(`  - Quote reserve: ${quoteReserve.toString()}`);
  console.log(`  - LP fee: ${lpFeeBps.toString()} basis points`);
  console.log(`  - Protocol fee: ${protocolFeeBps.toString()} basis points`);
  console.log(`  - Coin creator fee: ${coinCreatorFeeBps.toString()} basis points`);

  // Validate inputs
  if (quoteAmountIn.isZero()) {
    throw new Error("Invalid input: 'quoteAmountIn' cannot be zero.");
  }
  if (baseReserve.isZero() || quoteReserve.isZero()) {
    throw new Error("Invalid input: 'baseReserve' or 'quoteReserve' cannot be zero.");
  }

  // Calculate fees first
  const lpFee = fee(quoteAmountIn, lpFeeBps);
  const protocolFee = fee(quoteAmountIn, protocolFeeBps);
  const coinCreatorFee = PublicKey.default.equals(coinCreator)
    ? new BN(0)
    : fee(quoteAmountIn, coinCreatorFeeBps);

  const totalFees = lpFee.add(protocolFee).add(coinCreatorFee);
  console.log(`  - Total fees: ${totalFees.toString()}`);

  // Calculate the effective quote amount after fees
  const effectiveQuoteAmountIn = quoteAmountIn.sub(totalFees);
  console.log(`  - Effective quote amount in (after fees): ${effectiveQuoteAmountIn.toString()}`);

  if (effectiveQuoteAmountIn.isZero() || effectiveQuoteAmountIn.isNeg()) {
    throw new Error("Invalid calculation: fees exceed input amount");
  }

  // Calculate the base amount out using constant product formula
  // For buying tokens: baseAmountOut = (baseReserve * effectiveQuoteAmountIn) / (quoteReserve + effectiveQuoteAmountIn)
  const numerator = baseReserve.mul(effectiveQuoteAmountIn);
  const denominator = quoteReserve.add(effectiveQuoteAmountIn);

  if (denominator.isZero()) {
    throw new Error("Invalid calculation: denominator cannot be zero");
  }

  const baseAmountOut = numerator.div(denominator);
  console.log(`  - Base amount out (tokens received): ${baseAmountOut.toString()}`);

  // Apply slippage to get minimum base amount out
  const precision = new BN(1_000_000_000);
  const slippageFactorFloat = (1 - slippage / 100) * 1_000_000_000;
  const slippageFactor = new BN(Math.floor(slippageFactorFloat));
  const minBaseAmountOut = baseAmountOut.mul(slippageFactor).div(precision);
  console.log(`  - Min base amount out (with slippage): ${minBaseAmountOut.toString()}`);

  return {
    baseAmountOut: baseAmountOut,
    minBaseAmountOut: minBaseAmountOut,
    internalBaseAmountOut: baseAmountOut
  };
}

/**
 * Sell calculation using SDK's algorithm
 * @param base Base amount to sell
 * @param slippage Slippage percentage (e.g., 1 for 1%)
 * @param baseReserve Pool base reserve
 * @param quoteReserve Pool quote reserve
 * @param lpFeeBps LP fee in basis points
 * @param protocolFeeBps Protocol fee in basis points
 * @param coinCreatorFeeBps Coin creator fee in basis points
 * @param coinCreator Coin creator public key
 * @returns Sell calculation result
 */
export function sellBaseInputInternal(
  base: BN,
  slippage: number,
  baseReserve: BN,
  quoteReserve: BN,
  lpFeeBps: BN,
  protocolFeeBps: BN,
  coinCreatorFeeBps: BN,
  coinCreator: PublicKey,
): SellBaseInputResult {
  // Basic validations
  if (baseReserve.isZero() || quoteReserve.isZero()) {
    throw new Error("Invalid input: 'baseReserve' or 'quoteReserve' cannot be zero.");
  }

  // Calculate quote amount out using constant product formula
  const numerator = quoteReserve.mul(base);
  const denominator = baseReserve.add(base);
  const quoteAmountOut = numerator.div(denominator);

  // Calculate fees
  const lpFee = fee(quoteAmountOut, lpFeeBps);
  const protocolFee = fee(quoteAmountOut, protocolFeeBps);
  const coinCreatorFee = PublicKey.default.equals(coinCreator)
    ? new BN(0)
    : fee(quoteAmountOut, coinCreatorFeeBps);

  // Subtract fees to get the actual user receive
  const finalQuote = quoteAmountOut
    .sub(lpFee)
    .sub(protocolFee)
    .sub(coinCreatorFee);

  if (finalQuote.isNeg()) {
    throw new Error("Fees exceed total output; final quote is negative.");
  }

  // Calculate minQuote with slippage
  const precision = new BN(1_000_000_000);
  const slippageFactorFloat = (1 - slippage / 100) * 1_000_000_000;
  const slippageFactor = new BN(Math.floor(slippageFactorFloat));
  const minQuote = finalQuote.mul(slippageFactor).div(precision);

  return {
    uiQuote: finalQuote,
    minQuote,
    internalQuoteAmountOut: quoteAmountOut,
  };
}
