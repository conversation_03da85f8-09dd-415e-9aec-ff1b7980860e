import { Connection, Keypair, PublicKey, Transaction, VersionedTransaction } from '@solana/web3.js';
import { AnchorProvider, Wallet, Program, Idl } from '@coral-xyz/anchor';
import { config } from '../../config';
import { signAndSendTransactionWithPrivy } from '../privy/proper-privy.service';

/**
 * Custom wallet implementation that works with our platform keypair and Privy session signing
 */
class PlatformWallet implements Wallet {
  readonly payer: Keypair;

  constructor(
    private readonly platformKeypair: Keypair,
    private readonly walletId?: string
  ) {
    if (!platformKeypair) {
      throw new Error('Platform keypair is required for PlatformWallet');
    }
    this.payer = platformKeypair;
  }

  /**
   * Get the public key of the wallet
   */
  get publicKey(): PublicKey {
    return this.platformKeypair.publicKey;
  }

  /**
   * Sign a transaction using the platform keypair
   * If Privy service and wallet ID are provided, will use Privy session signing
   * @param tx Transaction to sign
   * @returns Signed transaction
   */
  async signTransaction<T extends Transaction | VersionedTransaction>(tx: T): Promise<T> {
    // Handle different transaction types
    if (tx instanceof Transaction) {
      // First, sign with the platform keypair (for gas fees)
      tx.partialSign(this.platformKeypair);

      // If wallet ID is provided, use Privy session signing
      if (this.walletId) {
        try {
          // Serialize the transaction to base64 for Privy
          const serializedTx = tx.serialize({ requireAllSignatures: false }).toString('base64');
          
          // Use Privy service to sign and send the transaction
          console.log(`Using Privy service to sign transaction for wallet ID: ${this.walletId}`);
          const signature = await signAndSendTransactionWithPrivy(this.walletId, serializedTx);
          
          console.log('Transaction signed with Privy, signature:', signature);
          // The transaction is already sent by signAndSendTransactionWithPrivy
          // We just need to return the original transaction with the platform signature
        } catch (error) {
          console.error('Error signing transaction with Privy:', error);
          throw new Error(`Failed to sign transaction with Privy: ${(error as Error).message}`);
        }
      }
    } else if (tx instanceof VersionedTransaction) {
      // For VersionedTransaction, we need to implement signing logic
      if (this.walletId) {
        try {
          // Serialize the versioned transaction to base64 for Privy
          const serializedTx = Buffer.from(tx.serialize()).toString('base64');
          
          // Use Privy service to sign and send the transaction
          console.log(`Using Privy service to sign versioned transaction for wallet ID: ${this.walletId}`);
          const signature = await signAndSendTransactionWithPrivy(this.walletId, serializedTx);
          
          console.log('Versioned transaction signed with Privy, signature:', signature);
        } catch (error) {
          console.error('Error signing versioned transaction with Privy:', error);
          throw new Error(`Failed to sign versioned transaction with Privy: ${(error as Error).message}`);
        }
      } else {
        console.warn('VersionedTransaction signing requires a wallet ID for Privy');
      }
    }

    // Return the transaction
    return tx;
  }

  /**
   * Sign multiple transactions using the platform keypair
   * @param txs Transactions to sign
   * @returns Signed transactions
   */
  async signAllTransactions<T extends Transaction | VersionedTransaction>(txs: T[]): Promise<T[]> {
    return Promise.all(txs.map(tx => this.signTransaction(tx)));
  }
}

/**
 * Get an Anchor provider configured for Solana transactions
 * @param walletId Optional wallet ID for Privy session signing
 * @returns AnchorProvider instance
 */
export function getProvider(walletId?: string): AnchorProvider {
  // Check if RPC URL is configured
  if (!config.rpcUrl) {
    throw new Error('SOLANA_RPC_URL environment variable is not set');
  }

  // Create a connection with finalized commitment
  const connection = new Connection(config.rpcUrl, 'finalized');

  // Get the platform keypair for gas fees
  const platformKeypair = config.platformKeypair;
  if (!platformKeypair) {
    throw new Error('Platform keypair not found. Please check your environment variables.');
  }

  // Create a wallet instance
  let wallet: Wallet;

  if (walletId) {
    // If wallet ID is provided, create a wallet with Privy session signing
    wallet = new PlatformWallet(platformKeypair, walletId);
    console.log('Created Anchor provider with Privy session signing for wallet ID:', walletId);
  } else {
    // Otherwise, create a wallet with just the platform keypair
    wallet = new PlatformWallet(platformKeypair);
    console.log('Created Anchor provider with platform keypair only');
  }

  // Create and return the provider
  return new AnchorProvider(
    connection,
    wallet,
    {
      preflightCommitment: 'finalized',
      commitment: 'finalized'
    }
  );
}

/**
 * Create a Program instance with our custom provider
 * @param idl The IDL for the program
 * @param programId The program ID
 * @param walletId Optional wallet ID for Privy session signing
 * @returns Program instance
 */
export function createProgram<T extends Idl = Idl>(
  idl: T,
  walletId?: string
): Program<T> {
  // Get our custom provider
  const provider = getProvider(walletId);

  // Create the program with our provider
  return new Program<T>(idl, provider);
}
