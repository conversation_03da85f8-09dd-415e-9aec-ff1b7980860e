/**
 * Parameter conversion utilities for enhanced user experience
 * Converts percentage-based inputs to internal Solana units while maintaining backward compatibility
 */

/**
 * Convert slippage parameter to decimal format
 * @param slippage User input (can be percentage like 10 or decimal like 0.10)
 * @returns Decimal slippage (e.g., 0.10 for 10%)
 */
export function convertSlippageToDecimal(slippage: number): number {
  // If slippage is > 1, treat as percentage (e.g., 10 = 10%)
  // If slippage is <= 1, treat as decimal (e.g., 0.10 = 10%)
  if (slippage > 1) {
    return slippage / 100;
  }
  return slippage;
}

/**
 * Convert priority fee parameter to microLamports
 * @param priorityFee User input (can be SOL amount, percentage of trade, or microLamports)
 * @param tradeAmountSol Trade amount in SOL
 * @returns Priority fee in microLamports
 */
export function convertPriorityFeeToMicroLamports(
  priorityFee: number,
  tradeAmountSol: number
): number {
  // Determine the input format based on value ranges:
  // - If priorityFee < 1, treat as SOL amount (e.g., 0.0003 SOL)
  // - If priorityFee >= 1 and < 100, treat as percentage of trade (e.g., 5%)
  // - If priorityFee >= 100, treat as microLamports (backward compatibility)

  if (priorityFee < 1) {
    // Treat as SOL amount - convert directly to microLamports
    const feeAmountLamports = priorityFee * 1_000_000_000;

    // Convert to microLamports assuming ~100,000 compute units for typical transactions
    const computeUnits = 100_000;
    const microLamports = Math.floor((feeAmountLamports * 1_000_000) / computeUnits);

    // Ensure minimum viable priority fee
    return Math.max(microLamports, 1000); // Minimum 1000 microLamports
  } else if (priorityFee < 100) {
    // Calculate as percentage of trade amount
    const feeAmountSol = (priorityFee / 100) * tradeAmountSol;
    const feeAmountLamports = feeAmountSol * 1_000_000_000;

    // Convert to microLamports assuming ~100,000 compute units for typical transactions
    const computeUnits = 100_000;
    const microLamports = Math.floor((feeAmountLamports * 1_000_000) / computeUnits);

    // Ensure minimum viable priority fee
    return Math.max(microLamports, 1000); // Minimum 1000 microLamports
  }

  // Return as-is (backward compatibility for microLamports)
  return Math.floor(priorityFee);
}

/**
 * Convert bribe amount parameter to lamports
 * @param bribeAmount User input (can be SOL amount, percentage of trade, or lamports)
 * @param tradeAmountSol Trade amount in SOL
 * @returns Bribe amount in lamports
 */
export function convertBribeAmountToLamports(
  bribeAmount: number,
  tradeAmountSol: number
): number {
  // Determine the input format based on value ranges:
  // - If bribeAmount < 1, treat as SOL amount (e.g., 0.0001 SOL)
  // - If bribeAmount >= 1 and < 1000, treat as percentage of trade (e.g., 5%)
  // - If bribeAmount >= 1000, treat as lamports (backward compatibility)

  if (bribeAmount < 1) {
    // Treat as SOL amount - convert directly to lamports
    const bribeAmountLamports = Math.floor(bribeAmount * 1_000_000_000);

    // Set reasonable bounds
    const minBribe = 100_000; // 0.0001 SOL minimum
    const maxBribe = 50_000_000; // 0.05 SOL maximum

    return Math.max(minBribe, Math.min(bribeAmountLamports, maxBribe));
  } else if (bribeAmount < 1000) {
    // Calculate as percentage of trade amount
    const bribeAmountSol = (bribeAmount / 100) * tradeAmountSol;
    const bribeAmountLamports = Math.floor(bribeAmountSol * 1_000_000_000);

    // Set reasonable bounds
    const minBribe = 100_000; // 0.0001 SOL minimum
    const maxBribe = 50_000_000; // 0.05 SOL maximum

    return Math.max(minBribe, Math.min(bribeAmountLamports, maxBribe));
  }

  // Return as-is (backward compatibility for lamports)
  return Math.floor(bribeAmount);
}

/**
 * Validate slippage parameter
 * @param slippage Slippage value
 * @returns Validation result
 */
export function validateSlippage(slippage: number): { isValid: boolean; error?: string } {
  if (typeof slippage !== 'number' || isNaN(slippage)) {
    return { isValid: false, error: 'Slippage must be a number' };
  }
  
  if (slippage < 0) {
    return { isValid: false, error: 'Slippage cannot be negative' };
  }
  
  // Convert to decimal for validation
  const decimalSlippage = convertSlippageToDecimal(slippage);
  
  if (decimalSlippage > 0.5) { // 50% max
    return { isValid: false, error: 'Slippage cannot exceed 50%' };
  }
  
  return { isValid: true };
}

/**
 * Validate priority fee parameter
 * @param priorityFee Priority fee value
 * @param tradeAmountSol Trade amount in SOL
 * @returns Validation result
 */
export function validatePriorityFee(
  priorityFee: number,
  tradeAmountSol: number
): { isValid: boolean; error?: string } {
  if (typeof priorityFee !== 'number' || isNaN(priorityFee)) {
    return { isValid: false, error: 'Priority fee must be a number' };
  }

  if (priorityFee < 0) {
    return { isValid: false, error: 'Priority fee cannot be negative' };
  }

  // If it's a SOL amount (< 1), validate SOL range
  if (priorityFee < 1) {
    if (priorityFee > 0.1) { // 0.1 SOL max
      return { isValid: false, error: 'Priority fee in SOL cannot exceed 0.1 SOL' };
    }
  } else if (priorityFee < 100) {
    // If it's a percentage (1-99), validate percentage range
    if (priorityFee > 10) { // 10% max
      return { isValid: false, error: 'Priority fee percentage cannot exceed 10%' };
    }
  } else {
    // If it's microLamports, validate reasonable range
    if (priorityFee > 10_000_000) { // 10M microLamports max
      return { isValid: false, error: 'Priority fee in microLamports cannot exceed 10,000,000' };
    }
  }

  return { isValid: true };
}

/**
 * Validate bribe amount parameter
 * @param bribeAmount Bribe amount value
 * @param tradeAmountSol Trade amount in SOL
 * @returns Validation result
 */
export function validateBribeAmount(
  bribeAmount: number,
  tradeAmountSol: number
): { isValid: boolean; error?: string } {
  if (typeof bribeAmount !== 'number' || isNaN(bribeAmount)) {
    return { isValid: false, error: 'Bribe amount must be a number' };
  }

  if (bribeAmount < 0) {
    return { isValid: false, error: 'Bribe amount cannot be negative' };
  }

  // If it's a SOL amount (< 1), validate SOL range
  if (bribeAmount < 1) {
    if (bribeAmount > 0.1) { // 0.1 SOL max
      return { isValid: false, error: 'Bribe amount in SOL cannot exceed 0.1 SOL' };
    }
  } else if (bribeAmount < 1000) {
    // If it's a percentage (1-999), validate percentage range
    if (bribeAmount > 20) { // 20% max
      return { isValid: false, error: 'Bribe amount percentage cannot exceed 20%' };
    }
  } else {
    // If it's lamports, validate reasonable range
    if (bribeAmount > 100_000_000) { // 0.1 SOL max
      return { isValid: false, error: 'Bribe amount in lamports cannot exceed 100,000,000 (0.1 SOL)' };
    }
  }

  return { isValid: true };
}

/**
 * Get parameter format information for API documentation
 * @param parameterName Parameter name
 * @returns Format information
 */
export function getParameterFormatInfo(parameterName: string): {
  description: string;
  examples: { old: any; new: any; description: string }[];
} {
  switch (parameterName) {
    case 'slippage':
      return {
        description: 'Slippage tolerance. Can be specified as percentage (>1) or decimal (≤1)',
        examples: [
          { old: 0.10, new: 10, description: '10% slippage' },
          { old: 0.05, new: 5, description: '5% slippage' },
          { old: 0.002, new: 0.2, description: '0.2% slippage' }
        ]
      };
    
    case 'priorityFee':
      return {
        description: 'Priority fee. Can be specified as percentage of trade (<100) or microLamports (≥100)',
        examples: [
          { old: 100000, new: 0.01, description: '0.01% of trade amount' },
          { old: 200000, new: 0.02, description: '0.02% of trade amount' },
          { old: 500000, new: 500000, description: '500,000 microLamports (backward compatible)' }
        ]
      };
    
    case 'bribeAmount':
      return {
        description: 'Bribe amount for MEV protection. Can be specified as percentage of trade (<1000) or lamports (≥1000)',
        examples: [
          { old: 5000000, new: 1.5, description: '1.5% of trade amount' },
          { old: 1000000, new: 0.5, description: '0.5% of trade amount' },
          { old: 10000000, new: 10000000, description: '10,000,000 lamports (backward compatible)' }
        ]
      };
    
    default:
      return {
        description: 'Unknown parameter',
        examples: []
      };
  }
}
