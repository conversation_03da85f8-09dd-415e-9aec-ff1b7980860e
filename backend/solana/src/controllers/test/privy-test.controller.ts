import { Request, Response } from 'express';
import { <PERSON>, <PERSON>Key, SystemProgram, Transaction } from '@solana/web3.js';
import { config } from '../../config';
import { signAndSendTransactionWithPrivy } from '../../services/privy/proper-privy.service';
// No need to import getFreshBlockhash anymore

/**
 * Test controller for Privy session signing
 * Creates a simple SOL transfer transaction to test Privy session signing
 */

/**
 * Test Privy session signing with a simple SOL transfer
 * @param req Request
 * @param res Response
 */
export async function testPrivySigningWithSimpleTransaction(req: Request, res: Response): Promise<void> {
  try {
    const { walletAddress, walletId, amount = 0.000001 } = req.body;

    if (!walletAddress) {
      res.status(400).json({ success: false, error: 'Wallet address is required' });
      return;
    }

    if (!walletId) {
      res.status(400).json({ success: false, error: 'Wallet ID is required for Privy session signing' });
      return;
    }

    // Create a connection to the Solana network
    if (!config.rpcUrl) {
      res.status(500).json({ success: false, error: 'SOLANA_RPC_URL environment variable is not set' });
      return;
    }

    console.log('Creating simple SOL transfer transaction for testing Privy session signing');
    console.log('Wallet Address:', walletAddress);
    console.log('Wallet ID:', walletId);
    console.log('Amount:', amount, 'SOL');

    const connection = new Connection(config.rpcUrl, 'confirmed');

    // Create a simple transaction that transfers a small amount of SOL
    // from the user's wallet to a known address (platform fee wallet)
    const transaction = new Transaction();

    // Set the fee payer to the user's wallet
    const userPublicKey = new PublicKey(walletAddress);
    transaction.feePayer = userPublicKey;

    // Get a fresh blockhash with finalized commitment
    // This is more reliable for transaction submission
    const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash({
      commitment: 'finalized'
    });
    transaction.recentBlockhash = blockhash;
    console.log('Using blockhash:', blockhash);
    console.log('Last valid block height:', lastValidBlockHeight);

    // Verify the blockhash is valid
    const isValid = await connection.isBlockhashValid(blockhash, { commitment: 'finalized' });
    console.log('Blockhash valid:', isValid);

    // Create a simple transfer instruction
    // Transfer a very small amount of SOL to avoid actual fund movement
    const lamports = Math.floor(amount * 1_000_000_000); // Convert SOL to lamports
    const destinationAddress = new PublicKey('g935C6veQ53oxSjc5LTHMhsZMNNzJH8rodioR9JnBzJ'); // Platform fee wallet

    const transferInstruction = SystemProgram.transfer({
      fromPubkey: userPublicKey,
      toPubkey: destinationAddress,
      lamports: lamports
    });

    // Add the instruction to the transaction
    transaction.add(transferInstruction);

    console.log('Transaction created with 1 instruction:');
    console.log('- Transfer', amount, 'SOL from', userPublicKey.toString(), 'to', destinationAddress.toString());

    // Serialize the transaction
    const serializedTransaction = transaction.serialize({ requireAllSignatures: false }).toString('base64');
    console.log('Transaction serialized, length:', serializedTransaction.length);

    // Sign and send the transaction with Privy
    console.log('Signing and sending transaction with Privy...');
    const signature = await signAndSendTransactionWithPrivy(walletId, serializedTransaction, connection);
    console.log('Transaction signed and sent with Privy, signature:', signature);

    // Return the signature
    res.status(200).json({
      success: true,
      signature,
      message: 'Simple SOL transfer transaction signed and sent successfully'
    });
  } catch (error) {
    console.error('Error in testPrivySigningWithSimpleTransaction:', error);
    res.status(500).json({
      success: false,
      error: (error as Error).message || 'Unknown error'
    });
  }
}
