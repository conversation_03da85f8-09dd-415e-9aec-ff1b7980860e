import { Request, Response } from 'express';
import {
  QuoteRequest,
  QuoteResponse,
  SwapDirection,
  DexType
} from '../../types/pump.types';
import { getSwapQuote } from '../../services/pumpFun/pump.service';
import { 
  executeEnhancedSwap, 
  getMEVRecommendation,
  EnhancedSwapRequest 
} from '../../services/mev/enhanced-swap.service';
import { config } from '../../config';
import { formatTokenAmount, formatSolAmount, formatPrice } from '../../services/pumpFun/utils.service';

/**
 * Enhanced pump controller with MEV protection and bribe capabilities
 */

/**
 * Get a quote with MEV protection recommendations
 */
export async function getEnhancedQuote(req: Request, res: Response): Promise<void> {
  try {
    const { tokenAddress, poolAddress, dexType, amount, direction } = req.body;

    // Validate request (same as original)
    if (!tokenAddress || !poolAddress || !amount || amount <= 0) {
      res.status(400).json({
        success: false,
        error: 'Invalid request parameters. Required: tokenAddress, poolAddress, amount'
      });
      return;
    }

    if (!Object.values(DexType).includes(dexType as DexType)) {
      res.status(400).json({
        success: false,
        error: `Invalid dexType. Must be one of: ${Object.values(DexType).join(', ')}`
      });
      return;
    }

    if (!Object.values(SwapDirection).includes(direction as SwapDirection)) {
      res.status(400).json({
        success: false,
        error: `Invalid direction. Must be one of: ${Object.values(SwapDirection).join(', ')}`
      });
      return;
    }

    const quoteRequest: QuoteRequest = {
      tokenAddress,
      poolAddress,
      dexType: dexType as DexType,
      amount: parseFloat(amount),
      direction: direction as SwapDirection
    };

    // Get regular quote
    console.log('Getting enhanced quote with MEV recommendations');
    const { quoteResult } = await getSwapQuote(quoteRequest);

    // Calculate slippage and fee
    const slippage = config.defaultSlippage;
    const fee = config.platformFeeBasisPoints / 10000;

    // Get MEV protection recommendation
    const mevRecommendation = getMEVRecommendation(
      parseFloat(amount),
      slippage
    );

    // Calculate minimum output with slippage
    const minOut = quoteResult.outAmount * (1 - slippage);

    // Format the values for the response
    const formattedOutAmount = direction === SwapDirection.Buy
      ? formatTokenAmount(quoteResult.outAmount)
      : formatSolAmount(quoteResult.outAmount);

    const formattedPrice = formatPrice(quoteResult.price);
    const formattedMinOut = direction === SwapDirection.Buy
      ? formatTokenAmount(minOut)
      : formatSolAmount(minOut);

    // Enhanced response with MEV recommendations
    const response = {
      success: true,
      data: {
        // Regular quote data
        outAmount: formattedOutAmount,
        price: formattedPrice,
        slippage,
        fee,
        minOut: formattedMinOut,
        
        // MEV protection recommendations
        mevProtection: {
          recommended: mevRecommendation.recommended,
          tipAmount: mevRecommendation.tipAmount,
          tipAmountSol: mevRecommendation.tipAmount / 1_000_000_000,
          reason: mevRecommendation.reason,
          costPercentage: mevRecommendation.costPercentage,
          
          // Priority level recommendations
          priorityLevels: {
            low: Math.floor(mevRecommendation.tipAmount * 0.5),
            medium: mevRecommendation.tipAmount,
            high: Math.floor(mevRecommendation.tipAmount * 1.5),
            veryHigh: Math.floor(mevRecommendation.tipAmount * 2.0)
          }
        }
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting enhanced quote:', error);
    res.status(500).json({
      success: false,
      error: (error as Error).message || 'An error occurred while getting the quote'
    });
  }
}

/**
 * Execute a swap with MEV protection and bribe options
 */
export async function executeEnhancedSwapEndpoint(req: Request, res: Response): Promise<void> {
  try {
    console.log('Executing enhanced swap with request:', JSON.stringify(req.body));

    const { 
      tokenAddress, 
      poolAddress, 
      dexType, 
      amount, 
      direction, 
      slippage, 
      walletAddress, 
      walletId,
      // New MEV protection parameters
      mevProtection,
      bribeAmount,
      priorityLevel,
      maxMevTip
    } = req.body;

    // Validate request (same as original)
    if (!tokenAddress || !poolAddress || !amount || amount <= 0 || !walletAddress) {
      res.status(400).json({
        success: false,
        error: 'Invalid request parameters. Required: tokenAddress, poolAddress, amount, walletAddress'
      });
      return;
    }

    if (!Object.values(DexType).includes(dexType as DexType)) {
      res.status(400).json({
        success: false,
        error: `Invalid dexType. Must be one of: ${Object.values(DexType).join(', ')}`
      });
      return;
    }

    if (!Object.values(SwapDirection).includes(direction as SwapDirection)) {
      res.status(400).json({
        success: false,
        error: `Invalid direction. Must be one of: ${Object.values(SwapDirection).join(', ')}`
      });
      return;
    }

    // Validate MEV parameters
    if (bribeAmount && (bribeAmount < 0 || bribeAmount > 10_000_000)) { // Max 0.01 SOL
      res.status(400).json({
        success: false,
        error: 'Invalid bribeAmount. Must be between 0 and 10,000,000 lamports (0.01 SOL)'
      });
      return;
    }

    if (priorityLevel && !['low', 'medium', 'high', 'veryHigh'].includes(priorityLevel)) {
      res.status(400).json({
        success: false,
        error: 'Invalid priorityLevel. Must be one of: low, medium, high, veryHigh'
      });
      return;
    }

    // Parse slippage
    const slippageValue = slippage ? parseFloat(slippage) : config.defaultSlippage;
    if (slippageValue < 0 || slippageValue > 1) {
      res.status(400).json({
        success: false,
        error: 'Invalid slippage. Must be between 0 and 1'
      });
      return;
    }

    // Create enhanced swap request
    const enhancedSwapRequest: EnhancedSwapRequest = {
      tokenAddress,
      poolAddress,
      dexType: dexType as DexType,
      amount: parseFloat(amount),
      direction: direction as SwapDirection,
      slippage: slippageValue,
      walletAddress,
      walletId,
      // MEV protection options
      mevProtection: mevProtection === true || mevProtection === 'true',
      bribeAmount: bribeAmount ? parseInt(bribeAmount) : undefined,
      priorityLevel: priorityLevel || 'medium',
      maxMevTip: maxMevTip ? parseInt(maxMevTip) : undefined
    };

    console.log('Creating enhanced swap transaction with request:', JSON.stringify(enhancedSwapRequest));

    try {
      // Execute the enhanced swap
      const result = await executeEnhancedSwap(enhancedSwapRequest);
      console.log('Enhanced swap executed successfully');

      // Format the values for the response
      const formattedOutAmount = direction === SwapDirection.Buy
        ? formatTokenAmount(result.quoteResult.outAmount)
        : formatSolAmount(result.quoteResult.outAmount);

      const formattedPrice = formatPrice(result.quoteResult.price);

      // Create response
      const response = {
        success: true,
        data: {
          // Transaction details
          signature: result.signature,
          bundleId: result.bundleId,
          outAmount: formattedOutAmount,
          price: formattedPrice,
          
          // MEV protection details
          mevProtected: result.mevProtected,
          tipAmount: result.tipAmount,
          tipAmountSol: result.tipAmount ? result.tipAmount / 1_000_000_000 : 0,
          executionMethod: result.executionMethod,
          
          // URLs
          solscanUrl: result.signature ? `https://solscan.io/tx/${result.signature}` : undefined,
          jitoUrl: result.bundleId ? `https://explorer.jito.wtf/bundle/${result.bundleId}` : undefined
        }
      };

      console.log(`Enhanced swap executed successfully. Method: ${result.executionMethod}`);
      if (result.signature) {
        console.log(`View on Solscan: https://solscan.io/tx/${result.signature}`);
      }
      if (result.bundleId) {
        console.log(`View on Jito: https://explorer.jito.wtf/bundle/${result.bundleId}`);
      }

      res.status(200).json(response);
    } catch (innerError: any) {
      console.error('Error in enhanced swap execution:', innerError);
      res.status(500).json({
        success: false,
        error: innerError.message || 'An error occurred while executing the enhanced swap'
      });
    }
  } catch (error: any) {
    console.error('Error executing enhanced swap:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'An error occurred while executing the enhanced swap'
    });
  }
}
