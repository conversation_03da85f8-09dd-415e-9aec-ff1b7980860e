import { Request, Response } from 'express';
import { Connection, PublicKey } from '@solana/web3.js';
import {
  QuoteRequest,
  QuoteResponse,
  SwapRequest,
  SwapResponse,
  DexType,
  SwapDirection
} from '../../types/pump.types';
import { getSwapQuote, executeSwapWithPrivy } from '../../services/pumpFun/pump.service';
import { checkPumpFunPool } from '../../services/pumpFun/pumpFun.service';
import { config } from '../../config';
import { formatTokenAmount, formatSolAmount, formatPrice } from '../../services/pumpFun/utils.service';
import { jitoService } from '../../services/jito/jito.service';
import { getMEVRecommendation, executeEnhancedSwap } from '../../services/mev/enhanced-swap.service';
import {
  convertSlippageToDecimal,
  convertPriorityFeeToMicroLamports,
  convertBribeAmountToLamports,
  validateSlip<PERSON>,
  validate<PERSON>riorityFee,
  validateBribeAmount
} from '../../services/common/parameter-converter.service';
import {
  analyzeTokenCharacteristics,
  getNetworkConditions,
  calculateParameterRecommendations
} from '../../services/analysis/token-analysis.service';
import {
  analyzeBondingCurveProgression
} from '../../services/analysis/bonding-curve.service';

/**
 * Get a quote for a potential swap
 * @param req Express request
 * @param res Express response
 */
export async function getQuote(req: Request, res: Response): Promise<void> {
  try {
    const { tokenAddress, poolAddress, dexType, amount, direction } = req.body;

    // Validate request
    if (!tokenAddress || !poolAddress || !amount || amount <= 0) {
      res.status(400).json({
        success: false,
        error: 'Invalid request parameters. Required: tokenAddress, poolAddress, amount'
      });
      return;
    }

    // Validate dexType
    if (!Object.values(DexType).includes(dexType as DexType)) {
      res.status(400).json({
        success: false,
        error: `Invalid dexType. Must be one of: ${Object.values(DexType).join(', ')}`
      });
      return;
    }

    // Validate direction
    if (!Object.values(SwapDirection).includes(direction as SwapDirection)) {
      res.status(400).json({
        success: false,
        error: `Invalid direction. Must be one of: ${Object.values(SwapDirection).join(', ')}`
      });
      return;
    }

    const quoteRequest: QuoteRequest = {
      tokenAddress,
      poolAddress,
      dexType: dexType as DexType,
      amount: parseFloat(amount),
      direction: direction as SwapDirection
    };

    // Get quote and pool data
    console.log('Getting quote and analyzing token characteristics...');
    const { quoteResult, poolStatus } = await getSwapQuote(quoteRequest);

    // Calculate slippage and fee
    const slippage = config.defaultSlippage;
    const fee = config.platformFeeBasisPoints / 10000;

    // Calculate minimum output with slippage
    const minOut = quoteResult.outAmount * (1 - slippage);

    // Get MEV protection recommendation
    const mevRecommendation = getMEVRecommendation(
      parseFloat(amount),
      slippage
    );

    // Generate intelligent parameter recommendations and bonding curve analysis
    let recommendations;
    let bondingCurveInfo;

    try {
      console.log('Generating intelligent analysis...');

      // Create connection for analysis
      const connection = new Connection(config.rpcUrl || 'https://api.mainnet-beta.solana.com', 'confirmed');
      const tokenMint = new PublicKey(tokenAddress);
      const poolAddress = new PublicKey(quoteRequest.poolAddress);

      // Enhanced analysis for PumpFun tokens
      if (dexType === DexType.PumpFun) {
        console.log('Performing PumpFun-specific analysis...');
        const poolData = await checkPumpFunPool(connection, poolAddress);

        // Analyze bonding curve progression
        console.log('Analyzing bonding curve progression...');
        bondingCurveInfo = analyzeBondingCurveProgression(poolData);
        console.log('✅ Generated bonding curve analysis');

        // Analyze token characteristics for parameter recommendations
        console.log('Analyzing token characteristics...');
        const tokenCharacteristics = await analyzeTokenCharacteristics(connection, tokenMint, poolData);

        // Get current network conditions
        console.log('Analyzing network conditions...');
        const networkConditions = await getNetworkConditions(connection);

        // Calculate intelligent recommendations
        console.log('Calculating parameter recommendations...');
        recommendations = calculateParameterRecommendations(
          tokenCharacteristics,
          networkConditions,
          parseFloat(amount)
        );

        console.log('✅ Generated intelligent parameter recommendations');
      } else {
        console.log('⚠️ Enhanced analysis only available for PumpFun tokens currently');
      }
    } catch (analysisError: any) {
      console.error('Error generating enhanced analysis:', analysisError);
      console.log('Continuing without enhanced analysis...');
      // Continue without enhanced analysis rather than failing the entire quote
    }

    // Debug the quote result before formatting
    console.log('🔍 [CONTROLLER] Quote result before formatting:');
    console.log(`  - outAmount: ${quoteResult.outAmount} (type: ${typeof quoteResult.outAmount})`);
    console.log(`  - price: ${quoteResult.price} (type: ${typeof quoteResult.price})`);
    console.log(`  - outputAmountRaw: ${quoteResult.outputAmountRaw?.toString() || 'undefined'}`);
    console.log(`  - minOut calculated: ${minOut} (type: ${typeof minOut})`);

    // Validate quote result before formatting
    if (quoteResult.outAmount === 0 || quoteResult.outAmount === null || quoteResult.outAmount === undefined) {
      console.error('❌ [CONTROLLER] Invalid outAmount in quote result');
      throw new Error('Quote calculation returned zero or invalid output amount');
    }

    if (quoteResult.price === null || quoteResult.price === undefined || isNaN(quoteResult.price)) {
      console.error('❌ [CONTROLLER] Invalid price in quote result');
      throw new Error('Quote calculation returned invalid price');
    }

    // Format the values for the response
    const formattedOutAmount = direction === SwapDirection.Buy
      ? formatTokenAmount(quoteResult.outAmount)
      : formatSolAmount(quoteResult.outAmount);

    const formattedPrice = formatPrice(quoteResult.price);
    const formattedMinOut = direction === SwapDirection.Buy
      ? formatTokenAmount(minOut)
      : formatSolAmount(minOut);

    console.log('✅ [CONTROLLER] Formatted values:');
    console.log(`  - formattedOutAmount: ${formattedOutAmount}`);
    console.log(`  - formattedPrice: ${formattedPrice}`);
    console.log(`  - formattedMinOut: ${formattedMinOut}`);

    // Enhanced response with MEV recommendations and intelligent parameter suggestions (backward compatible)
    const response: QuoteResponse = {
      success: true,
      data: {
        outAmount: formattedOutAmount,
        price: formattedPrice,
        slippage,
        fee,
        minOut: formattedMinOut,

        // New MEV protection information (optional, doesn't break existing clients)
        mevProtection: {
          recommended: mevRecommendation.recommended,
          tipAmount: mevRecommendation.tipAmount,
          tipAmountSol: mevRecommendation.tipAmount / 1_000_000_000,
          reason: mevRecommendation.reason,
          costPercentage: mevRecommendation.costPercentage,

          // Priority level recommendations
          priorityLevels: {
            low: Math.floor(mevRecommendation.tipAmount * 0.5),
            medium: mevRecommendation.tipAmount,
            high: Math.floor(mevRecommendation.tipAmount * 1.5),
            veryHigh: Math.floor(mevRecommendation.tipAmount * 2.0)
          }
        },

        // New intelligent parameter recommendations (optional, doesn't break existing clients)
        ...(recommendations && {
          recommendations: {
            slippage: recommendations.slippage,
            bribeAmount: recommendations.bribeAmount,
            priorityFee: recommendations.priorityFee,
            reasoning: recommendations.reasoning
          }
        }),

        // New bonding curve information (optional, PumpFun only, doesn't break existing clients)
        ...(bondingCurveInfo && {
          bondingCurve: {
            progressPercentage: bondingCurveInfo.progressPercentage,
            currentSolRaised: bondingCurveInfo.currentSolRaised,
            graduationThreshold: bondingCurveInfo.graduationThreshold,
            isGraduated: bondingCurveInfo.isGraduated,
            status: bondingCurveInfo.status,
            description: bondingCurveInfo.description
          }
        })
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting quote:', error);
    res.status(500).json({
      success: false,
      error: (error as Error).message || 'An error occurred while getting the quote'
    });
  }
}

/**
 * Execute a swap
 * @param req Express request
 * @param res Express response
 */
export async function executeSwap(req: Request, res: Response): Promise<void> {
  try {
    console.log('Executing swap with request:', JSON.stringify(req.body));

    const {
      tokenAddress,
      poolAddress,
      dexType,
      amount,
      direction,
      slippage,
      walletAddress,
      walletId,
      // New MEV protection parameters (optional)
      priorityFee,
      bribeAmount,
      mevProtection,
      priorityLevel
    } = req.body;

    // Validate request
    if (!tokenAddress || !poolAddress || !amount || amount <= 0 || !walletAddress) {
      res.status(400).json({
        success: false,
        error: 'Invalid request parameters. Required: tokenAddress, poolAddress, amount, walletAddress'
      });
      return;
    }

    // Validate dexType
    if (!Object.values(DexType).includes(dexType as DexType)) {
      res.status(400).json({
        success: false,
        error: `Invalid dexType. Must be one of: ${Object.values(DexType).join(', ')}`
      });
      return;
    }

    // Validate direction
    if (!Object.values(SwapDirection).includes(direction as SwapDirection)) {
      res.status(400).json({
        success: false,
        error: `Invalid direction. Must be one of: ${Object.values(SwapDirection).join(', ')}`
      });
      return;
    }

    // Enhanced parameter validation and conversion
    const tradeValueSol = parseFloat(amount);

    // Validate and convert slippage (supports both percentage and decimal formats)
    const inputSlippage = slippage ? parseFloat(slippage) : config.defaultSlippage * 100; // Convert default to percentage
    const slippageValidation = validateSlippage(inputSlippage);
    if (!slippageValidation.isValid) {
      res.status(400).json({
        success: false,
        error: `Invalid slippage: ${slippageValidation.error}`
      });
      return;
    }
    const slippageValue = convertSlippageToDecimal(inputSlippage);

    // Validate and convert priority fee (supports both percentage and microLamports)
    let finalPriorityFee = 200000; // Default 200,000 microLamports
    if (priorityFee !== undefined) {
      const priorityFeeValidation = validatePriorityFee(priorityFee, tradeValueSol);
      if (!priorityFeeValidation.isValid) {
        res.status(400).json({
          success: false,
          error: `Invalid priorityFee: ${priorityFeeValidation.error}`
        });
        return;
      }
      finalPriorityFee = convertPriorityFeeToMicroLamports(priorityFee, tradeValueSol);
    }

    // Validate and convert bribe amount (supports both percentage and lamports)
    let finalBribeAmountInput = bribeAmount;
    if (bribeAmount !== undefined) {
      const bribeValidation = validateBribeAmount(bribeAmount, tradeValueSol);
      if (!bribeValidation.isValid) {
        res.status(400).json({
          success: false,
          error: `Invalid bribeAmount: ${bribeValidation.error}`
        });
        return;
      }
    }

    if (priorityLevel && !['low', 'medium', 'high', 'veryHigh'].includes(priorityLevel)) {
      res.status(400).json({
        success: false,
        error: 'Invalid priorityLevel. Must be one of: low, medium, high, veryHigh'
      });
      return;
    }

    // Determine MEV protection based on priorityLevel (updated logic)
    const finalPriorityLevel = priorityLevel || 'medium';

    // FIXED: Allow MEV protection for all priority levels, not just high/veryHigh
    // This ensures low priority transactions can also benefit from MEV protection
    const shouldUseMEVFromPriority = jitoService.isMEVProtectionRecommended(tradeValueSol, slippageValue);

    // Calculate intelligent defaults for MEV parameters
    const shouldUseMEV = mevProtection !== undefined
      ? mevProtection === true || mevProtection === 'true'  // Explicit user choice takes precedence
      : shouldUseMEVFromPriority; // Use recommendation based on trade value and slippage, not priority level

    // Convert bribe amount using the new conversion logic
    let finalBribeAmount = 0;
    if (finalBribeAmountInput !== undefined) {
      finalBribeAmount = convertBribeAmountToLamports(finalBribeAmountInput, tradeValueSol);
    } else if (shouldUseMEV) {
      // Use intelligent default based on priority level
      const baseTipAmount = jitoService.calculateRecommendedTip(tradeValueSol);
      const priorityMultipliers = {
        low: 0.5,
        medium: 1.0,
        high: 1.5,
        veryHigh: 2.0
      };
      const multiplier = priorityMultipliers[finalPriorityLevel as keyof typeof priorityMultipliers] || 1.0;
      finalBribeAmount = Math.floor(baseTipAmount * multiplier);
    }

    console.log('Enhanced Parameter Conversion:');
    console.log(`- Input slippage: ${slippage} → Converted: ${slippageValue} (${(slippageValue * 100).toFixed(2)}%)`);
    console.log(`- Input priorityFee: ${priorityFee} → Converted: ${finalPriorityFee} microLamports`);
    console.log(`- Input bribeAmount: ${bribeAmount} → Converted: ${finalBribeAmount} lamports (${(finalBribeAmount / 1_000_000_000).toFixed(6)} SOL)`);

    console.log('MEV Protection Settings:');
    console.log(`- Priority level: ${finalPriorityLevel}`);
    console.log(`- MEV recommended for trade: ${shouldUseMEVFromPriority}`);
    console.log(`- Requested MEV protection: ${mevProtection}`);
    console.log(`- Trade value: ${tradeValueSol} SOL, Slippage: ${(slippageValue * 100).toFixed(2)}%`);
    console.log(`- Final MEV protection: ${shouldUseMEV}`);
    console.log(`- Final bribe amount: ${finalBribeAmount} lamports (${(finalBribeAmount / 1_000_000_000).toFixed(6)} SOL)`);

    const swapRequest: SwapRequest = {
      tokenAddress,
      poolAddress,
      dexType: dexType as DexType,
      amount: parseFloat(amount),
      direction: direction as SwapDirection,
      slippage: slippageValue,
      walletAddress,
      walletId, // Include walletId in the swap request

      // Include MEV protection parameters (converted from user-friendly formats)
      priorityFee: finalPriorityFee,
      bribeAmount: finalBribeAmount,
      mevProtection: shouldUseMEV,
      priorityLevel: finalPriorityLevel
    };

    console.log('Creating swap transaction with request:', JSON.stringify(swapRequest));

    try {
      // Choose execution method based on MEV protection settings
      let result: any;
      let executionMethod: string;

      // Force regular Privy execution when mevProtection is explicitly false
      if (mevProtection === false) {
        console.log('🚫 MEV Protection explicitly disabled by user - using regular Privy execution');
        result = await executeSwapWithPrivy(swapRequest, true, walletId);
        executionMethod = 'privy';
      } else if (shouldUseMEV && finalBribeAmount > 0) {
        // Use enhanced swap with MEV protection (available for all priority levels)
        console.log(`✅ MEV Protection enabled for ${finalPriorityLevel} priority - using Jito execution`);
        result = await executeEnhancedSwap(swapRequest);
        executionMethod = result.executionMethod || 'jito';
      } else {
        // Use regular Privy execution (MEV not recommended or no bribe amount)
        console.log(`ℹ️ MEV Protection not recommended for this trade - using regular Privy execution (${finalPriorityLevel} priority)`);
        result = await executeSwapWithPrivy(swapRequest, true, walletId);
        executionMethod = 'privy';
      }

      console.log('Swap executed successfully');

      // Format the values for the response
      const formattedOutAmount = direction === SwapDirection.Buy
        ? formatTokenAmount(result.quoteResult.outAmount)
        : formatSolAmount(result.quoteResult.outAmount);

      const formattedPrice = formatPrice(result.quoteResult.price);

      // Create enhanced response with MEV protection details
      let response: SwapResponse;

      if (result.signature || result.bundleId) {
        // Transaction was executed (either Privy or Jito)
        const solscanUrl = result.signature ? `https://solscan.io/tx/${result.signature}` : undefined;
        const jitoUrl = result.bundleId ? `https://explorer.jito.wtf/bundle/${result.bundleId}` : undefined;

        response = {
          success: true,
          data: {
            signature: result.signature,
            bundleId: result.bundleId,
            outAmount: formattedOutAmount,
            price: formattedPrice,
            solscanUrl,
            jitoUrl,

            // MEV protection details
            mevProtected: result.mevProtected || shouldUseMEV,
            tipAmount: result.tipAmount || finalBribeAmount,
            tipAmountSol: result.tipAmount ? result.tipAmount / 1_000_000_000 : finalBribeAmount / 1_000_000_000,
            executionMethod: executionMethod as 'regular' | 'jito' | 'privy' | 'client-side'
          }
        };

        if (solscanUrl) {
          console.log(`Transaction executed successfully. View on Solscan: ${solscanUrl}`);
        }
        if (jitoUrl) {
          console.log(`Bundle executed successfully. View on Jito: ${jitoUrl}`);
        }
      } else {
        // Return transaction for client-side signing (fallback)
        response = {
          success: true,
          data: {
            transaction: result.transaction!,
            outAmount: formattedOutAmount,
            price: formattedPrice,

            // MEV protection details
            mevProtected: false,
            tipAmount: 0,
            tipAmountSol: 0,
            executionMethod: 'client-side'
          }
        };
      }

      console.log('Sending successful response');
      res.status(200).json(response);
    } catch (innerError: any) {
      console.error('Error in createSwapTransaction:', innerError);
      res.status(500).json({
        success: false,
        error: innerError.message || 'An error occurred while creating the swap transaction'
      });
    }
  } catch (error: any) {
    console.error('Error executing swap:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'An error occurred while executing the swap'
    });
  }
}
