import { Request, Response } from 'express';
import { Connection } from '@solana/web3.js';
import { TokenBalanceRequest, TokenBalanceResponse } from '../../types/pump.types';
import { 
  getTokenBalance, 
  validateTokenBalanceRequest,
  formatTokenBalance 
} from '../../services/token/balance.service';

/**
 * Token balance controller
 */

/**
 * Get token balance for a wallet
 * @param req Express request
 * @param res Express response
 */
export async function getTokenBalanceEndpoint(req: Request, res: Response): Promise<void> {
  try {
    console.log('Token balance request received:', JSON.stringify(req.body));

    // Validate request
    const validation = validateTokenBalanceRequest(req.body);
    if (!validation.isValid) {
      res.status(400).json({
        success: false,
        error: validation.error
      } as TokenBalanceResponse);
      return;
    }

    const request: TokenBalanceRequest = {
      tokenAddress: req.body.tokenAddress,
      walletAddress: req.body.walletAddress
    };

    // Validate environment
    if (!process.env.SOLANA_RPC_URL) {
      res.status(500).json({
        success: false,
        error: 'SOLANA_RPC_URL environment variable is not set'
      } as TokenBalanceResponse);
      return;
    }

    // Create Solana connection
    const connection = new Connection(process.env.SOLANA_RPC_URL, 'confirmed');

    try {
      // Get token balance
      console.log('Fetching token balance...');
      const balanceData = await getTokenBalance(connection, request);

      // Format the response
      const response: TokenBalanceResponse = {
        success: true,
        data: {
          tokenBalance: formatTokenBalance(balanceData.tokenBalance, balanceData.decimals),
          rawBalance: balanceData.rawBalance,
          decimals: balanceData.decimals,
          solBalance: balanceData.solBalance,
          tokenAccountAddress: balanceData.tokenAccountAddress,
          hasTokenAccount: balanceData.hasTokenAccount
        }
      };

      console.log('Token balance response:', JSON.stringify(response));
      res.status(200).json(response);

    } catch (balanceError: any) {
      console.error('Error getting token balance:', balanceError);
      
      // Handle specific errors
      if (balanceError.message.includes('Invalid')) {
        res.status(400).json({
          success: false,
          error: balanceError.message
        } as TokenBalanceResponse);
      } else if (balanceError.message.includes('network') || balanceError.message.includes('fetch')) {
        res.status(503).json({
          success: false,
          error: 'Network error: Unable to connect to Solana RPC. Please try again later.'
        } as TokenBalanceResponse);
      } else {
        res.status(500).json({
          success: false,
          error: `Failed to get token balance: ${balanceError.message}`
        } as TokenBalanceResponse);
      }
    }

  } catch (error: any) {
    console.error('Token balance endpoint error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'An unexpected error occurred while getting token balance'
    } as TokenBalanceResponse);
  }
}

/**
 * Health check for token balance service
 * @param req Express request
 * @param res Express response
 */
export async function tokenBalanceHealthCheck(req: Request, res: Response): Promise<void> {
  try {
    // Check if RPC URL is configured
    if (!process.env.SOLANA_RPC_URL) {
      res.status(503).json({
        success: false,
        error: 'SOLANA_RPC_URL not configured'
      });
      return;
    }

    // Test connection to Solana RPC
    const connection = new Connection(process.env.SOLANA_RPC_URL, 'confirmed');
    
    try {
      // Simple test to check if RPC is responsive
      await connection.getSlot();
      
      res.status(200).json({
        success: true,
        data: {
          status: 'healthy',
          rpcUrl: process.env.SOLANA_RPC_URL,
          timestamp: new Date().toISOString()
        }
      });
    } catch (rpcError: any) {
      res.status(503).json({
        success: false,
        error: `RPC connection failed: ${rpcError.message}`
      });
    }

  } catch (error: any) {
    console.error('Token balance health check error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Health check failed'
    });
  }
}
