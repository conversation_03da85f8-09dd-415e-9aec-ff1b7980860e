import { Request, Response } from 'express';
import { Connection, PublicKey } from '@solana/web3.js';
import {
  getJupiterQuote as fetchJupiterQuote,
  createJupiterSwapTransaction,
  processJupiterQuote,
  executeJupiterSwapWithPrivy,
  getTokenDecimals
} from '../../services/jupiter/jupiter.service';
import { QuoteResponse, SwapResponse } from '../../types/pump.types';
import { config } from '../../config';

// Get the RPC URL from config
const RPC_URL = config.rpcUrl || 'https://api.mainnet-beta.solana.com';

/**
 * Get a quote for a potential Jupiter swap
 * @param req Express request
 * @param res Express response
 */
export async function getJupiterQuote(req: Request, res: Response): Promise<void> {
  try {
    console.log('Getting Jupiter quote with request:', JSON.stringify(req.body));

    const { inputToken, outputToken, amount } = req.body;

    // Validate request
    if (!inputToken || !outputToken || !amount || amount <= 0) {
      res.status(400).json({
        success: false,
        error: 'Invalid request parameters. Required: inputToken, outputToken, amount'
      });
      return;
    }

    // Connect to Solana
    console.log(`Connecting to Solana at ${RPC_URL}...`);
    const connection = new Connection(RPC_URL, 'confirmed');

    // Use the provided input and output tokens
    const inputMint = inputToken;
    const outputMint = outputToken;

    // Get token decimals for both input and output tokens
    const inputDecimals = await getTokenDecimals(connection, inputMint);
    const outputDecimals = await getTokenDecimals(connection, outputMint);

    // Convert amount to raw units based on input token decimals
    const rawAmount = Math.floor(amount * 10 ** inputDecimals);

    // Convert slippage to basis points (1% = 100 basis points)
    const slippageBps = Math.floor(config.defaultSlippage * 10000);

    // Convert platform fee to basis points
    const platformFeeBps = Math.floor(config.jupiterFeeRatio * 10000);

    console.log(`Getting Jupiter quote for ${rawAmount} ${inputMint} to ${outputMint}`);
    console.log(`Slippage: ${slippageBps} bps, Platform fee: ${platformFeeBps} bps`);

    // Get the quote from Jupiter
    const quoteResponse = await fetchJupiterQuote(
      inputMint,
      outputMint,
      rawAmount,
      slippageBps,
      platformFeeBps
    );

    // Process the quote
    const quoteResult = processJupiterQuote(
      quoteResponse,
      inputDecimals,
      outputDecimals
    );

    // Format the response
    const response: QuoteResponse = {
      success: true,
      data: {
        outAmount: quoteResult.outAmount,
        price: quoteResult.price,
        slippage: config.defaultSlippage,
        fee: config.jupiterFeeRatio,
        minOut: quoteResult.outAmount * (1 - config.defaultSlippage)
      }
    };

    res.status(200).json(response);
  } catch (error: any) {
    console.error('Error getting Jupiter quote:', error);

    res.status(500).json({
      success: false,
      error: `Failed to get Jupiter quote: ${error.message || error}`
    });
  }
}

/**
 * Execute a Jupiter swap
 * @param req Express request
 * @param res Express response
 */
export async function executeJupiterSwap(req: Request, res: Response): Promise<void> {
  try {
    console.log('Executing Jupiter swap with request:', JSON.stringify(req.body));

    const { inputToken, outputToken, amount, slippage, walletAddress, walletId } = req.body;

    // Validate request
    if (!inputToken || !outputToken || !amount || amount <= 0 || !walletAddress) {
      res.status(400).json({
        success: false,
        error: 'Invalid request parameters. Required: inputToken, outputToken, amount, walletAddress'
      });
      return;
    }

    // Validate walletId for Privy session signing
    if (!walletId) {
      res.status(400).json({
        success: false,
        error: 'walletId is required for Privy session signing'
      });
      return;
    }

    // Connect to Solana
    console.log(`Connecting to Solana at ${RPC_URL}...`);
    const connection = new Connection(RPC_URL, 'confirmed');

    // Use the provided input and output tokens
    const inputMint = inputToken;
    const outputMint = outputToken;

    // Get token decimals for both input and output tokens
    const inputDecimals = await getTokenDecimals(connection, inputMint);
    const outputDecimals = await getTokenDecimals(connection, outputMint);

    // Convert amount to raw units based on input token decimals
    const rawAmount = Math.floor(amount * 10 ** inputDecimals);

    // Validate slippage
    const slippageValue = slippage ? parseFloat(slippage.toString()) : config.defaultSlippage;
    if (isNaN(slippageValue) || slippageValue <= 0 || slippageValue > 0.5) {
      res.status(400).json({
        success: false,
        error: 'Invalid slippage. Must be a number between 0 and 0.5'
      });
      return;
    }

    // Convert slippage to basis points (1% = 100 basis points)
    const slippageBps = Math.floor(slippageValue * 10000);

    // Convert platform fee to basis points
    const platformFeeBps = Math.floor(config.jupiterFeeRatio * 10000);

    console.log(`Getting Jupiter quote for ${rawAmount} ${inputMint} to ${outputMint}`);
    console.log(`Slippage: ${slippageBps} bps, Platform fee: ${platformFeeBps} bps`);

    // Get the quote from Jupiter
    const quoteResponse = await fetchJupiterQuote(
      inputMint,
      outputMint,
      rawAmount,
      slippageBps,
      platformFeeBps
    );

    // Process the quote
    const quoteResult = processJupiterQuote(
      quoteResponse,
      inputDecimals,
      outputDecimals
    );

    // Create the swap transaction
    const swapResponse = await createJupiterSwapTransaction(
      quoteResponse,
      walletAddress
    );

    try {
      // Execute the swap with Privy session signing
      const signature = await executeJupiterSwapWithPrivy(
        swapResponse.swapTransaction,
        connection,
        walletAddress,
        walletId
      );

      // Format the response
      const response: SwapResponse = {
        success: true,
        data: {
          signature,
          outAmount: quoteResult.outAmount,
          price: quoteResult.price,
          solscanUrl: `https://solscan.io/tx/${signature}`
        }
      };

      res.status(200).json(response);
      return;
    } catch (error: any) {
      console.error('Error executing Jupiter swap with Privy:', error);

      // If we can't execute the transaction server-side, return it for client-side signing
      console.log('Returning transaction for client-side signing');

      // Return a custom response for client-side signing
      res.status(200).json({
        success: true,
        data: {
          transaction: swapResponse.swapTransaction,
          outAmount: quoteResult.outAmount,
          price: quoteResult.price
        },
        clientSideSign: true,
        message: 'Transaction requires client-side signing'
      });
      return;
    }
  } catch (error: any) {
    console.error('Error executing Jupiter swap:', error);

    res.status(500).json({
      success: false,
      error: `Failed to execute Jupiter swap: ${error.message || error}`
    });
  }
}
