import { PublicKey } from '@solana/web3.js';

/**
 * Common pool data interface
 */
export interface BasePoolData {
  exists: boolean;
  owner?: PublicKey;
}

/**
 * PumpFun pool data interface
 */
export interface PumpFunPoolData extends BasePoolData {
  isGraduated?: boolean;
  virtualTokenReserves?: bigint;
  virtualSolReserves?: bigint;
  realTokenReserves?: bigint;
  realSolReserves?: bigint;
  tokenTotalSupply?: bigint;
  feeRatio?: number;
}

/**
 * PumpSwap pool data interface
 */
export interface PumpSwapPoolData extends BasePoolData {
  reserves?: {
    token: bigint;
    sol: bigint;
  };
  baseVault?: PublicKey;
  quoteVault?: PublicKey;
  baseMint?: PublicKey; // Added to include the parsed base mint
  quoteMint?: PublicKey; // Added to include the parsed quote mint
  coinCreator?: PublicKey; // Added to support dynamic PDA derivation
}

/**
 * Quote result interface
 */
export interface QuoteResult {
  outAmount: number;
  price: number;
  outputAmountRaw: bigint;
}

/**
 * Swap direction
 */
export enum SwapDirection {
  Buy = 'buy',
  Sell = 'sell'
}

/**
 * DEX type
 */
export enum DexType {
  PumpFun = 'pumpfun',
  PumpSwap = 'pumpswap',
  Jupiter = 'jupiter'
}

/**
 * Quote request interface
 */
export interface QuoteRequest {
  tokenAddress: string;
  poolAddress: string;
  dexType: DexType;
  amount: number;
  direction: SwapDirection;
}

/**
 * MEV protection information interface
 */
export interface MEVProtectionInfo {
  recommended: boolean;
  tipAmount: number;
  tipAmountSol: number;
  reason: string;
  costPercentage: number;
  priorityLevels: {
    low: number;
    medium: number;
    high: number;
    veryHigh: number;
  };
}

/**
 * Parameter recommendations interface
 */
export interface ParameterRecommendations {
  slippage: number; // percentage (e.g., 8 for 8%)
  bribeAmount: number; // percentage of trade amount (e.g., 1.2 for 1.2%)
  priorityFee: number; // percentage of trade amount (e.g., 0.05 for 0.05%)
  reasoning: {
    slippage: string;
    bribeAmount: string;
    priorityFee: string;
  };
}

/**
 * Bonding curve information interface
 */
export interface BondingCurveInfo {
  progressPercentage: number;
  currentSolRaised: number;
  graduationThreshold: number;
  isGraduated: boolean;
  status: 'active' | 'graduated' | 'unknown';
  description: string;
}

/**
 * Quote response interface
 */
export interface QuoteResponse {
  success: boolean;
  data?: {
    outAmount: number;
    price: number;
    slippage: number;
    fee: number;
    minOut?: number;
    mevProtection?: MEVProtectionInfo; // Optional MEV protection info
    recommendations?: ParameterRecommendations; // Optional intelligent parameter recommendations
    bondingCurve?: BondingCurveInfo; // Optional bonding curve info (PumpFun only)
  };
  error?: string;
}

/**
 * Swap request interface
 */
export interface SwapRequest {
  tokenAddress: string;
  poolAddress: string;
  dexType: DexType;
  amount: number;
  direction: SwapDirection;
  slippage: number;
  walletAddress: string;
  walletId?: string; // Optional wallet ID for Privy session signing

  // New MEV protection parameters (optional for backward compatibility)
  priorityFee?: number; // Priority fee in microLamports
  bribeAmount?: number; // Jito tip amount in lamports
  mevProtection?: boolean; // Enable/disable MEV protection
  priorityLevel?: 'low' | 'medium' | 'high' | 'veryHigh'; // Priority level
}

/**
 * Swap response interface
 */
export interface SwapResponse {
  success: boolean;
  data?: {
    transaction?: string; // Base64 encoded transaction
    signature?: string;   // Transaction signature if executed with Privy
    bundleId?: string;    // Jito bundle ID if MEV protection was used
    outAmount: number;
    price: number;
    solscanUrl?: string;  // URL to view the transaction on Solscan
    jitoUrl?: string;     // URL to view the bundle on Jito explorer

    // MEV protection details (optional, doesn't break existing clients)
    mevProtected?: boolean;
    tipAmount?: number;
    tipAmountSol?: number;
    executionMethod?: 'regular' | 'jito' | 'privy' | 'client-side';
  };
  error?: string;
}

/**
 * Pool status
 */
export enum PoolStatus {
  Active = 'ACTIVE',
  Graduated = 'GRADUATED',
  Unknown = 'UNKNOWN'
}

/**
 * Token balance request interface
 */
export interface TokenBalanceRequest {
  tokenAddress: string;
  walletAddress: string;
}

/**
 * Token balance response interface
 */
export interface TokenBalanceResponse {
  success: boolean;
  data?: {
    tokenBalance: string;
    rawBalance: string;
    decimals: number;
    solBalance: string;
    tokenAccountAddress: string;
    hasTokenAccount: boolean;
  };
  error?: string;
}
