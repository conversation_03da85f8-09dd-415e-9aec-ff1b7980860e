

/**
 * LaunchLab Quote Request interface
 */
export interface LaunchLabQuoteRequest {
  poolId: string;
  amount: string;
  direction: 'buy' | 'sell';
  walletAddress?: string; // Optional wallet address for accurate fee calculation
}

/**
 * LaunchLab Quote Response interface
 */
export interface LaunchLabQuoteResponse {
  inputMint: string;
  outputMint: string;
  inputAmount: string; // Base units
  outputAmount: string; // Base units
  priceImpact: number;
  fee: string; // Base units
  platformFee: string; // Platform fee in base units
  netOutputAmount: string; // Output amount after platform fee
  newPrice: string; // Price after trade

  // Human-readable fields
  inputAmountHuman: string;
  outputAmountHuman: string;
  feeHuman: string;
  platformFeeHuman: string;
  netOutputAmountHuman: string;
  priceImpactPercent: string;
  newPriceHuman: string;
  platformFeePercent: string;
}

/**
 * LaunchLab Swap Request interface
 */
export interface LaunchLabSwapRequest {
  poolId: string;
  amount: string; // Amount to trade (SOL for buy, tokens for sell)
  walletAddress: string;
  walletId: string; // Privy wallet identifier (required)
  direction: 'buy' | 'sell'; // Trade direction
  slippage: number;
  priorityFee?: number;
  computeUnitLimit?: number;
}

/**
 * LaunchLab Trade Response interface
 */
export interface LaunchLabTradeResponse {
  txid: string;
  inputAmount: string; // Base units
  outputAmount: string; // Base units
  fee: string; // Base units
  priceImpact: number;
  newPrice: string; // New token price after trade

  // Human-readable fields
  inputAmountHuman: string;
  outputAmountHuman: string;
  feeHuman: string;
  priceImpactPercent: string;
  newPriceHuman: string;
}

/**
 * LaunchLab Pool Info interface
 */
export interface LaunchLabPoolInfo {
  poolId: string;
  tokenMint: string;
  quoteMint: string; // Usually SOL
  status: 'active' | 'migrated';
  supply: string;
  totalSellA: string;
  totalFundRaisingB: string;
  currentFundRaised: string;
  realA: string; // Current token reserves
  realB: string; // Current SOL reserves
  migrationProgress: number; // Percentage (0-100)
  currentPrice: string;
  marketCap: string;
  volume24h?: string;
  trades24h?: number;
  holders?: number;

  // Token metadata
  tokenName?: string;
  tokenSymbol?: string;
  tokenDescription?: string;
  tokenImage?: string;
  tokenWebsite?: string;
  tokenTelegram?: string;
  tokenTwitter?: string;

  // Human-readable fields
  supplyHuman: string;
  totalSellAHuman: string;
  totalFundRaisingBHuman: string;
  currentFundRaisedHuman: string;
  realAHuman: string;
  realBHuman: string;
  currentPriceHuman: string;
  marketCapHuman: string;
  volume24hHuman?: string;
}

/**
 * Platform Config interface
 */
export interface PlatformConfig {
  platformId: string;
  feeRate: number; // Platform fee in basis points * 100
  feeReceiver: string; // Platform fee receiver address
  migrateCpLockNftScale: {
    platformScale: number; // Platform share in basis points * 100
    creatorScale: number; // Creator share in basis points * 100
    burnScale: number; // Burn share in basis points * 100
  };
}

/**
 * API Response wrapper interface
 */
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
  };
  timestamp: number;
}

/**
 * LaunchLab API endpoints
 */
export const LAUNCHLAB_API_ENDPOINTS = {
  QUOTE: '/api/launchlab/quote',
  SWAP: '/api/launchlab/swap'
};

/**
 * LaunchLab configuration
 */
export interface LaunchLabConfig {
  rpcUrl: string;
  network: 'mainnet-beta';
  platformFeePercentage: number; // Platform fee as percentage (e.g., 0.1 = 0.1%)
}

/**
 * Error codes for LaunchLab operations
 */
export enum LaunchLabErrorCode {
  INITIALIZATION_FAILED = 'LAUNCHLAB_INITIALIZATION_FAILED',
  QUOTE_FAILED = 'LAUNCHLAB_QUOTE_FAILED',
  SWAP_FAILED = 'LAUNCHLAB_SWAP_FAILED',
  POOL_NOT_FOUND = 'LAUNCHLAB_POOL_NOT_FOUND',
  POOL_MIGRATED = 'LAUNCHLAB_POOL_MIGRATED',
  INVALID_AMOUNT = 'LAUNCHLAB_INVALID_AMOUNT',
  INVALID_DIRECTION = 'LAUNCHLAB_INVALID_DIRECTION',
  PRIVY_NOT_CONFIGURED = 'LAUNCHLAB_PRIVY_NOT_CONFIGURED',
  WALLET_NOT_CONFIGURED = 'LAUNCHLAB_WALLET_NOT_CONFIGURED'
}

/**
 * LaunchLab service interface
 */
export interface ILaunchLabService {
  getQuote(request: LaunchLabQuoteRequest): Promise<LaunchLabQuoteResponse>;
  executeSwap(request: LaunchLabSwapRequest): Promise<LaunchLabTradeResponse>;
  getPoolInfo(poolId: string): Promise<LaunchLabPoolInfo>;
  cleanup(): Promise<void>;
}
