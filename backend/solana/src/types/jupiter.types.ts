import { PublicKey } from '@solana/web3.js';

/**
 * Jupiter Quote Request interface
 */
export interface JupiterQuoteRequest {
  inputMint: string;
  outputMint: string;
  amount: number;
  slippageBps: number;
  onlyDirectRoutes?: boolean;
  asLegacyTransaction?: boolean;
  platformFeeBps?: number;
}

/**
 * Jupiter Quote Response interface
 */
export interface JupiterQuoteResponse {
  inputMint: string;
  outputMint: string;
  inAmount?: string;  // New field from API
  outAmount: string;
  otherAmountThreshold: string;
  swapMode: string;
  slippageBps: number;
  platformFee: {
    amount: string;
    feeBps: number;
  } | null;
  priceImpactPct: string | number;
  routePlan: Array<{
    swapInfo: {
      ammKey: string;
      label: string;
      inputMint: string;
      outputMint: string;
      inAmount: string;
      outAmount: string;
      feeAmount: string;
      feeMint: string;
    };
    percent: number;
  }>;
  contextSlot: number;
  timeTaken: number;
  swapUsdValue?: string;  // New field from API
}

/**
 * Jupiter Swap Request interface
 */
export interface JupiterSwapRequest {
  quoteResponse: JupiterQuoteResponse;
  userPublicKey: string;
  wrapAndUnwrapSol?: boolean;
  dynamicComputeUnitLimit?: boolean;
  prioritizationFeeLamports?: {
    priorityLevelWithMaxLamports: {
      maxLamports: number;
      priorityLevel: 'low' | 'medium' | 'high' | 'veryHigh';
    };
  };
}

/**
 * Jupiter Swap Response interface
 */
export interface JupiterSwapResponse {
  swapTransaction: string;
  lastValidBlockHeight?: number;
  prioritizationFee?: number;
}

/**
 * Jupiter Quote Result interface
 */
export interface JupiterQuoteResult {
  outAmount: number;
  price: number;
  outputAmountRaw: bigint;
  inputAmountRaw: bigint;
  priceImpactPct: number;
  quoteResponse: JupiterQuoteResponse;
}

/**
 * Jupiter API endpoints
 */
export const JUPITER_API_ENDPOINTS = {
  QUOTE: 'https://quote-api.jup.ag/v6/quote',
  SWAP: 'https://quote-api.jup.ag/v6/swap'
};

/**
 * Jupiter fee configuration
 */
export interface JupiterFeeConfig {
  feeBps: number;
  feeAccount: string;
}
