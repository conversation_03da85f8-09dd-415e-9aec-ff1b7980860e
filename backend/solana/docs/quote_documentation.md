# Pump.fun & PumpSwap Quote Logic: A Deep Dive

This document provides a complete, code-level walkthrough of the `getQuote` endpoint, explaining how it generates swap quotes for DEXs like Pump.fun and PumpSwap.

## 1. Overview

The endpoint's purpose is to provide a simulated result of a swap, including the output amount, price, and slippage-adjusted minimum output, without executing a transaction. For Pump.fun tokens, it also provides enhanced MEV (Maximal Extractable Value) protection analysis.

## 2. The Code Flow

The process is handled across three main files:
1.  `pump.controller.ts`: The entry point that handles the API request.
2.  `pump.service.ts`: The core logic for fetching quote data from the appropriate DEX.
3.  `utils.service.ts`: Helper functions for formatting the final output.

---

### Step 1: The Controller (`pump.controller.ts`)

The flow begins in the `getQuote` function. It validates the user's request, calls the service to get the raw data, performs calculations for slippage and MEV protection, and formats the final response.

**Code:**
```typescript
// /www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts

export async function getQuote(req: Request, res: Response): Promise<void> {
  try {
    const { tokenAddress, poolAddress, dexType, amount, direction } = req.body;

    // Validate request
    if (!tokenAddress || !poolAddress || !amount || amount <= 0) {
      res.status(400).json({
        success: false,
        error: 'Invalid request parameters. Required: tokenAddress, poolAddress, amount'
      });
      return;
    }

    // ... (validation for dexType and direction) ...

    const quoteRequest: QuoteRequest = {
      tokenAddress,
      poolAddress,
      dexType: dexType as DexType,
      amount: parseFloat(amount),
      direction: direction as SwapDirection
    };

    // Get quote and pool data
    const { quoteResult, poolStatus } = await getSwapQuote(quoteRequest);

    // Calculate slippage and fee
    const slippage = config.defaultSlippage;
    const minOut = quoteResult.outAmount * (1 - slippage);

    // ... (Enhanced analysis for PumpFun tokens) ...

    // Format the values for the response
    const formattedOutAmount = direction === SwapDirection.Buy
      ? formatTokenAmount(quoteResult.outAmount)
      : formatSolAmount(quoteResult.outAmount);

    const formattedPrice = formatPrice(quoteResult.price);

    const formattedMinOut = direction === SwapDirection.Buy
      ? formatTokenAmount(minOut)
      : formatSolAmount(minOut);

    const response: QuoteResponse = {
      success: true,
      data: {
        outAmount: formattedOutAmount,
        price: formattedPrice,
        minOut: formattedMinOut,
        // ... (optional analysis data)
      }
    };

    res.status(200).json(response);
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message });
  }
}
```

---

### Step 2: The Service (`pump.service.ts`)

The controller delegates the core quote logic to `getSwapQuote` in the service file. This function determines which DEX to use (`PumpFun` or `PumpSwap`) and calls the appropriate helper to calculate the raw output amount and price.

**Code:**
```typescript
// /www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/pump.service.ts

export async function getSwapQuote(
  quoteRequest: QuoteRequest
): Promise<{
  quoteResult: QuoteResult;
  tokenDecimals: number;
  poolStatus: PoolStatus;
}> {
  const { tokenAddress, poolAddress, dexType, amount, direction } = quoteRequest;
  const isBuy = direction === SwapDirection.Buy;

  const connection = new Connection(RPC_URL, 'confirmed');
  const tokenMint = new PublicKey(tokenAddress);
  const poolPublicKey = new PublicKey(poolAddress);

  // ... (fetches token decimals) ...

  let quoteResult: QuoteResult;
  let poolStatus: PoolStatus;

  // Get quote based on DEX type
  if (dexType === DexType.PumpFun) {
    const poolData = await checkPumpFunPool(connection, poolPublicKey);
    poolStatus = getPumpFunPoolStatus(poolData);
    quoteResult = calculateDirectQuote(amount, isBuy, poolData, tokenDecimals);
  } else if (dexType === DexType.PumpSwap) {
    const poolData = await checkPumpSwapPool(connection, poolPublicKey);
    poolStatus = getPumpSwapPoolStatus(poolData);
    quoteResult = await calculatePumpSwapQuote(connection, poolData, amount, isBuy, tokenDecimals);
  } else {
    throw new Error(`Unsupported DEX type: ${dexType}`);
  }

  return { quoteResult, tokenDecimals, poolStatus };
}
```

---

### Step 3: The Formatting Utilities (`utils.service.ts`)

Finally, before the response is sent to the user, the raw numerical values are formatted into clean, human-readable numbers using helper functions. The formatting is conditional based on whether the asset is a token or SOL and the magnitude of the price.

**Code:**
```typescript
// /www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/utils.service.ts

export function formatTokenAmount(amount: number): number {
  // For token amounts, preserve decimal places for small amounts
  return parseFloat(amount.toFixed(6));
}

export function formatSolAmount(amount: number): number {
  // For SOL amounts, we typically want to show 6 decimal places
  return parseFloat(amount.toFixed(6));
}

export function formatPrice(price: number): number {
  if (price < 0.000001) {
    // For very small prices, use scientific notation with 9 significant digits
    return parseFloat(price.toExponential(9));
  } else if (price < 0.001) {
    // For small prices, show 9 decimal places
    return parseFloat(price.toFixed(9));
  } else if (price < 1) {
    // For medium prices, show 6 decimal places
    return parseFloat(price.toFixed(6));
  } else {
    // For large prices, show 4 decimal places
    return parseFloat(price.toFixed(4));
  }
}
```

## 3. Final Response

A successful response returns a `QuoteResponse` object with the formatted data:

```json
{
  "success": true,
  "data": {
    "outAmount": 0.123456, // Formatted number
    "price": 1.2345e-7,  // Formatted number
    "minOut": 0.122221,   // Formatted number
    "analysis": { /* ... */ }
  }
}
```
