/**
 * Test script for priority fee and bribe amount conversion
 * Run with: node test-fee-conversion.js
 */

// Import the conversion functions
const {
  convertPriorityFeeToMicroLamports,
  convertBribeAmountToLamports,
  validatePriorityFee,
  validateBribeAmount
} = require('./dist/services/common/parameter-converter.service.js');

console.log('🧪 Testing Priority Fee and Bribe Amount Conversion Fix\n');

// Test data from presets
const testCases = [
  {
    name: 'Preset 1 Buy',
    priority: 0.0003,
    bribe: 0.0001,
    tradeAmount: 0.1
  },
  {
    name: 'Preset 1 Sell',
    priority: 0.0002,
    bribe: 0.00005,
    tradeAmount: 0.1
  },
  {
    name: 'Preset 3 Buy',
    priority: 0.001,
    bribe: 0.0002,
    tradeAmount: 1.0
  },
  {
    name: 'Large Trade',
    priority: 0.001,
    bribe: 0.0002,
    tradeAmount: 10.0
  }
];

// Test percentage inputs
const percentageTests = [
  {
    name: 'Percentage Priority (5%)',
    priority: 5,
    bribe: 2,
    tradeAmount: 1.0
  }
];

// Test backward compatibility
const backwardCompatTests = [
  {
    name: 'Backward Compat - MicroLamports',
    priority: 500000,
    bribe: 5000000,
    tradeAmount: 1.0
  }
];

function testConversions() {
  console.log('1️⃣ Testing SOL Amount Conversions (< 1)');
  console.log('=' .repeat(50));
  
  testCases.forEach(testCase => {
    console.log(`\n📋 ${testCase.name}:`);
    console.log(`   Trade Amount: ${testCase.tradeAmount} SOL`);
    
    // Test priority fee
    const priorityValidation = validatePriorityFee(testCase.priority, testCase.tradeAmount);
    if (priorityValidation.isValid) {
      const priorityMicroLamports = convertPriorityFeeToMicroLamports(testCase.priority, testCase.tradeAmount);
      const priorityLamports = priorityMicroLamports / 1_000_000 * 100_000; // Approximate conversion
      const prioritySol = priorityLamports / 1_000_000_000;
      
      console.log(`   Priority: ${testCase.priority} SOL → ${priorityMicroLamports} microLamports (~${prioritySol.toFixed(6)} SOL)`);
    } else {
      console.log(`   Priority: ${testCase.priority} SOL → ❌ ${priorityValidation.error}`);
    }
    
    // Test bribe amount
    const bribeValidation = validateBribeAmount(testCase.bribe, testCase.tradeAmount);
    if (bribeValidation.isValid) {
      const bribeLamports = convertBribeAmountToLamports(testCase.bribe, testCase.tradeAmount);
      const bribeSol = bribeLamports / 1_000_000_000;
      
      console.log(`   Bribe: ${testCase.bribe} SOL → ${bribeLamports} lamports (${bribeSol.toFixed(6)} SOL)`);
    } else {
      console.log(`   Bribe: ${testCase.bribe} SOL → ❌ ${bribeValidation.error}`);
    }
  });
  
  console.log('\n\n2️⃣ Testing Percentage Conversions (1-99 for priority, 1-999 for bribe)');
  console.log('=' .repeat(70));
  
  percentageTests.forEach(testCase => {
    console.log(`\n📋 ${testCase.name}:`);
    console.log(`   Trade Amount: ${testCase.tradeAmount} SOL`);
    
    // Test priority fee
    const priorityValidation = validatePriorityFee(testCase.priority, testCase.tradeAmount);
    if (priorityValidation.isValid) {
      const priorityMicroLamports = convertPriorityFeeToMicroLamports(testCase.priority, testCase.tradeAmount);
      const expectedSol = (testCase.priority / 100) * testCase.tradeAmount;
      
      console.log(`   Priority: ${testCase.priority}% → ${priorityMicroLamports} microLamports (${expectedSol.toFixed(6)} SOL equivalent)`);
    } else {
      console.log(`   Priority: ${testCase.priority}% → ❌ ${priorityValidation.error}`);
    }
    
    // Test bribe amount
    const bribeValidation = validateBribeAmount(testCase.bribe, testCase.tradeAmount);
    if (bribeValidation.isValid) {
      const bribeLamports = convertBribeAmountToLamports(testCase.bribe, testCase.tradeAmount);
      const expectedSol = (testCase.bribe / 100) * testCase.tradeAmount;
      const actualSol = bribeLamports / 1_000_000_000;
      
      console.log(`   Bribe: ${testCase.bribe}% → ${bribeLamports} lamports (${actualSol.toFixed(6)} SOL, expected ${expectedSol.toFixed(6)} SOL)`);
    } else {
      console.log(`   Bribe: ${testCase.bribe}% → ❌ ${bribeValidation.error}`);
    }
  });
  
  console.log('\n\n3️⃣ Testing Backward Compatibility (≥100 for priority, ≥1000 for bribe)');
  console.log('=' .repeat(75));
  
  backwardCompatTests.forEach(testCase => {
    console.log(`\n📋 ${testCase.name}:`);
    console.log(`   Trade Amount: ${testCase.tradeAmount} SOL`);
    
    // Test priority fee
    const priorityValidation = validatePriorityFee(testCase.priority, testCase.tradeAmount);
    if (priorityValidation.isValid) {
      const priorityMicroLamports = convertPriorityFeeToMicroLamports(testCase.priority, testCase.tradeAmount);
      
      console.log(`   Priority: ${testCase.priority} microLamports → ${priorityMicroLamports} microLamports (unchanged)`);
    } else {
      console.log(`   Priority: ${testCase.priority} microLamports → ❌ ${priorityValidation.error}`);
    }
    
    // Test bribe amount
    const bribeValidation = validateBribeAmount(testCase.bribe, testCase.tradeAmount);
    if (bribeValidation.isValid) {
      const bribeLamports = convertBribeAmountToLamports(testCase.bribe, testCase.tradeAmount);
      const bribeSol = bribeLamports / 1_000_000_000;
      
      console.log(`   Bribe: ${testCase.bribe} lamports → ${bribeLamports} lamports (${bribeSol.toFixed(6)} SOL)`);
    } else {
      console.log(`   Bribe: ${testCase.bribe} lamports → ❌ ${bribeValidation.error}`);
    }
  });
}

// Test edge cases
function testEdgeCases() {
  console.log('\n\n4️⃣ Testing Edge Cases');
  console.log('=' .repeat(30));
  
  const edgeCases = [
    { name: 'Zero values', priority: 0, bribe: 0, tradeAmount: 1.0 },
    { name: 'Very small SOL', priority: 0.000001, bribe: 0.000001, tradeAmount: 1.0 },
    { name: 'Boundary SOL (0.999)', priority: 0.999, bribe: 0.999, tradeAmount: 1.0 },
    { name: 'Boundary percentage (1)', priority: 1, bribe: 1, tradeAmount: 1.0 },
    { name: 'Boundary percentage (99)', priority: 99, bribe: 99, tradeAmount: 1.0 },
    { name: 'Boundary microLamports (100)', priority: 100, bribe: 1000, tradeAmount: 1.0 },
  ];
  
  edgeCases.forEach(testCase => {
    console.log(`\n📋 ${testCase.name}:`);
    
    const priorityValidation = validatePriorityFee(testCase.priority, testCase.tradeAmount);
    const bribeValidation = validateBribeAmount(testCase.bribe, testCase.tradeAmount);
    
    console.log(`   Priority ${testCase.priority}: ${priorityValidation.isValid ? '✅ Valid' : '❌ ' + priorityValidation.error}`);
    console.log(`   Bribe ${testCase.bribe}: ${bribeValidation.isValid ? '✅ Valid' : '❌ ' + bribeValidation.error}`);
  });
}

// Run tests
try {
  testConversions();
  testEdgeCases();
  
  console.log('\n\n🎉 Test completed!');
  console.log('\n📝 Summary:');
  console.log('✅ SOL amounts (< 1) are converted directly');
  console.log('✅ Percentages (1-99 for priority, 1-999 for bribe) are calculated correctly');
  console.log('✅ Backward compatibility maintained for large values');
  console.log('✅ Validation prevents excessive fees');
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.log('\n💡 Make sure to build the project first:');
  console.log('   cd backend/solana && npm run build');
}
