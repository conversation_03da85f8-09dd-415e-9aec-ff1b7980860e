# Solana Liquidity Pool

A Solana-based liquidity pool implementation with Privy wallet integration for secure transaction signing.

## Features

- Privy wallet integration for secure transaction signing
- PumpFun program interaction
- Token swapping functionality
- Comprehensive error handling

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Solana CLI (for local development)

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/solana-liquidity-pool.git
   cd solana-liquidity-pool
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn
   ```

3. Set up environment variables:
   Create a `.env` file in the root directory with the following variables:
   ```
   PRIVY_APP_ID=your_privy_app_id
   PRIVY_APP_SECRET=your_privy_app_secret
   PRIVY_SIGNING_KEY=your_privy_signing_key
   SOLANA_RPC_URL=your_solana_rpc_url
   ```

## Usage

### Development

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Run tests
npm test
```

## Project Structure

```
src/
  ├── services/           # Service layer
  │   ├── privy/          # Privy wallet integration
  │   └── pumpFun/        # PumpFun program integration
  ├── utils/              # Utility functions
  └── types/              # TypeScript type definitions
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
