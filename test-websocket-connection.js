#!/usr/bin/env node

/**
 * Test WebSocket connection to the backend server
 */

import { io } from 'socket.io-client';

console.log('🧪 Testing WebSocket connection to backend server...\n');

async function testWebSocketConnection() {
  const socketUrl = 'http://localhost:5001';
  
  console.log(`🔌 Attempting to connect to: ${socketUrl}`);
  
  const socket = io(socketUrl, {
    transports: ['websocket', 'polling'],
    timeout: 10000,
    reconnection: false,
    forceNew: true,
    upgrade: true,
    rememberUpgrade: false
  });

  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      socket.disconnect();
      reject(new Error('Connection timeout after 10 seconds'));
    }, 10000);

    socket.on('connect', () => {
      clearTimeout(timeout);
      console.log('✅ Successfully connected to WebSocket server');
      console.log(`   Socket ID: ${socket.id}`);
      console.log(`   Transport: ${socket.io.engine.transport.name}`);
      
      // Test registration
      console.log('📝 Testing client registration...');
      socket.emit('register', {
        userId: 'test-user-123',
        sessionId: 'test-session-456'
      });
      
      // Listen for registration confirmation
      socket.on('connection-status', (status) => {
        console.log('✅ Registration successful:', status);
        
        // Test pulse room join
        console.log('🎯 Testing pulse room join...');
        socket.emit('join-pulse', {
          userId: 'test-user-123',
          sessionId: 'test-session-456'
        });
        
        setTimeout(() => {
          console.log('✅ All tests completed successfully');
          socket.disconnect();
          resolve(true);
        }, 2000);
      });
    });

    socket.on('connect_error', (error) => {
      clearTimeout(timeout);
      console.error('❌ Connection error:', error.message);
      console.error('   Error type:', error.type);
      console.error('   Error description:', error.description);
      reject(error);
    });

    socket.on('disconnect', (reason) => {
      console.log(`🔌 Disconnected: ${reason}`);
    });

    socket.on('error', (error) => {
      console.error('❌ Socket error:', error);
    });

    // Log transport changes
    socket.io.on('upgrade', () => {
      console.log(`🔄 Upgraded to ${socket.io.engine.transport.name}`);
    });

    socket.io.on('upgradeError', (error) => {
      console.error('❌ Upgrade error:', error);
    });
  });
}

async function testHttpEndpoint() {
  console.log('🌐 Testing HTTP endpoint...');
  
  try {
    const response = await fetch('http://localhost:5001');
    const data = await response.json();
    console.log('✅ HTTP endpoint working:', data);
    return true;
  } catch (error) {
    console.error('❌ HTTP endpoint error:', error.message);
    return false;
  }
}

async function runTests() {
  try {
    // Test HTTP first
    const httpWorking = await testHttpEndpoint();
    if (!httpWorking) {
      console.log('❌ HTTP endpoint not working, skipping WebSocket test');
      process.exit(1);
    }
    
    console.log('');
    
    // Test WebSocket
    await testWebSocketConnection();
    
    console.log('\n🎉 All tests passed! WebSocket connection is working properly.');
    process.exit(0);
    
  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
    
    // Provide troubleshooting suggestions
    console.log('\n🔧 Troubleshooting suggestions:');
    console.log('1. Check if the backend server is running on port 5001');
    console.log('2. Verify the WebSocket service is properly initialized');
    console.log('3. Check for any firewall or network issues');
    console.log('4. Ensure Socket.IO versions are compatible');
    console.log('5. Check backend server logs for errors');
    
    process.exit(1);
  }
}

runTests();
