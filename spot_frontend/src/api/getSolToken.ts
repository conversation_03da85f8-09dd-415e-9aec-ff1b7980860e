import axios from 'axios';

// Get the backend API URL from environment variables with dynamic fallback
function getBackendApiUrl() {
  // First try environment variable
//   if (import.meta.env.VITE_API_URL) {
//     return import.meta.env.VITE_API_URL;
//   }

  // Dynamic fallback based on current hostname
  const hostname = window.location.hostname;

  // Production environments
  if (hostname.includes('crypfi.io')) {
    return 'https://redfyn.crypfi.io/api';
  }
  if (hostname.includes('lrbinfotech.com')) {
    return 'https://redfyn.lrbinfotech.com/api';
  }

  // Development environment
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    // For local development with direct API connection
    // if (import.meta.env.VITE_BACKEND_PORT) {
    //   return `http://localhost:${import.meta.env.VITE_BACKEND_PORT}/api`;
    // }
    // Default to port 4001 for local development if not specified in env
    return 'http://localhost:4001/api';
  }

  // Fallback
  return 'http://localhost:4001/api';
}

const BACKEND_API_URL = getBackendApiUrl();

// TypeScript interfaces for API requests and responses
export interface SolanaTokenBalanceRequest {
  walletAddress: string;
}

export interface TokenMetadata {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  balance: string;
  displayBalance: string;
  uiBalance: number;
  logo?: string;
  tags?: string[];
  extensions?: Record<string, any>;
}

export interface SolanaTokenBalanceResponse {
  success: boolean;
  data?: {
    walletAddress: string;
    solBalance: {
      balance: string;
      displayBalance: string;
      uiBalance: number;
    };
    tokens: TokenMetadata[];
    network: string;
  };
  message?: string;
  error?: string;
}

/**
 * Get the default Solana wallet address from localStorage
 * Returns null if not found
 */
export const getDefaultSolanaWalletAddress = (): string | null => {
  try {
    const defaultWalletsStr = localStorage.getItem('defaultWallets');
    console.log('Raw defaultWallets from localStorage:', defaultWalletsStr);
    if (!defaultWalletsStr) {
      console.warn('No defaultWallets found in localStorage');
      return null;
    }
    
    const defaultWallets = JSON.parse(defaultWalletsStr);
    console.log('Parsed defaultWallets:', defaultWallets);
    const solanaAddress = defaultWallets?.solana || null;
    console.log('Retrieved Solana address:', solanaAddress);
    return solanaAddress;
  } catch (error) {
    console.error('Error getting default Solana wallet address:', error);
    return null;
  }
};

/**
 * Get Solana token balances for the default wallet
 * Automatically retrieves the wallet address from localStorage
 */
export const getSolanaTokenBalances = async (): Promise<SolanaTokenBalanceResponse> => {
  try {
    const walletAddress = getDefaultSolanaWalletAddress();
    
    if (!walletAddress) {
      console.warn('No default Solana wallet address found in localStorage');
      return {
        success: false,
        error: 'No default Solana wallet address found'
      };
    }
    
    console.log('Getting Solana token balances for wallet:', walletAddress);
    
    const response = await axios.post<SolanaTokenBalanceResponse>(
      `${BACKEND_API_URL}/wallet/solana-token-balance`,
      { walletAddress },
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 15000, // 15 second timeout
      }
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching Solana token balances:', error);
    
    if (axios.isAxiosError(error)) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred'
      };
    }
    
    return {
      success: false,
      error: 'An unknown error occurred'
    };
  }
};

/**
 * Get Solana token balances for a specific wallet address
 * Use this when you need to specify the wallet manually
 */
export const getSolanaTokenBalancesForWallet = async (walletAddress: string): Promise<SolanaTokenBalanceResponse> => {
  try {
    if (!walletAddress) {
      return {
        success: false,
        error: 'Wallet address is required'
      };
    }
    
    console.log('Getting Solana token balances for specific wallet:', walletAddress);
    
    const response = await axios.post<SolanaTokenBalanceResponse>(
      `${BACKEND_API_URL}/wallet/solana-token-balance`,
      { walletAddress },
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 15000, // 15 second timeout
      }
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching Solana token balances for specific wallet:', error);
    
    if (axios.isAxiosError(error)) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred'
      };
    }
    
    return {
      success: false,
      error: 'An unknown error occurred'
    };
  }
};
