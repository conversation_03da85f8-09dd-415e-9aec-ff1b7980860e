import React, { useState } from 'react';
import { ChevronDown, Trash2, ChevronUpIcon } from 'lucide-react';
import { ChevronsRight, ArrowUp, ArrowDown } from 'lucide-react';

interface TradingOption {
  id: string;
  label: string;
  icon: any; // Lucide icons
  color?: 'emerald' | 'red' | 'orange' | 'purple';
}

interface InputValues {
  [key: string]: {
    value?: string;
    primary?: string;
    amount?: string;
  };
}

const AdvancedTradingStrategy: React.FC = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [selectedOptions, setSelectedOptions] = useState<TradingOption[]>([]);
  const [inputValues, setInputValues] = useState<InputValues>({});

  const allOptions: TradingOption[] = [
    { id: 'takeProfit', label: 'Take Profit', icon: ArrowUp, color: 'emerald' },
    { id: 'stopLoss', label: 'Stop Loss', icon: ArrowDown, color: 'red' },
    { id: 'devSell', label: 'Dev Sell', icon: ChevronUpIcon, color: 'orange' },
    { id: 'migration', label: 'Migration', icon: ChevronsRight, color: 'purple' }
  ];

  // Get available options (not yet selected)
  const availableOptions = allOptions.filter(option => 
    !selectedOptions.some(selected => selected.id === option.id)
  );

  const handleOptionSelect = (option: TradingOption) => {
    setSelectedOptions(prev => [...prev, option]);
    // Initialize input values for the new option
    if (option.id === 'takeProfit' || option.id === 'stopLoss') {
      setInputValues(prev => ({
        ...prev,
        [option.id]: { primary: '', amount: '' }
      }));
    } else {
      setInputValues(prev => ({
        ...prev,
        [option.id]: { value: '' }
      }));
    }
    setIsOpen(false);
  };

  const handleDelete = (optionId: string) => {
    setSelectedOptions(prev => prev.filter(option => option.id !== optionId));
    setInputValues(prev => {
      const newValues = { ...prev };
      delete newValues[optionId];
      return newValues;
    });
  };

  const updateInputValue = (optionId: string, field: string, value: string) => {
    setInputValues(prev => ({
      ...prev,
      [optionId]: {
        ...prev[optionId],
        [field]: value
      }
    }));
  };

  const getColorClasses = (color?: 'emerald' | 'red' | 'orange' | 'purple') => {
    const colorMap = {
      emerald: 'text-emerald-400',
      red: 'text-red-400',
      orange: 'text-orange-400',
      purple: 'text-purple-400'
    };
    return (color && colorMap[color]) || 'text-slate-400';
  };

  const renderInputField = (option: TradingOption) => {
    const values = inputValues[option.id] || {};

    switch (option.id) {
      case 'takeProfit':
        return (
          <div key={option.id} className="flex items-center gap-3 mb-3 w-full ">
            <div className="flex items-center bg-[#2A2D35] rounded-lg border border-slate-600/40 px-3 py-2 flex-1 w-1/3">
              <option.icon size={20} className={getColorClasses(option.color) + " mr-2"} />
              <span className="text-slate-300 text-sm font-medium mr-2">TP</span>
          
                <input
                  type="text"
                  placeholder="+0"
                  value={values.value || ''}
                  onChange={(e) => updateInputValue(option.id, 'value', e.target.value)}
                  className="bg-transparent text-white text-sm outline-none placeholder-slate-500 text-right w-full"
                />
                <span className="text-slate-400 text-sm ml-1">%</span>
         
            </div>
            
            <div className="flex items-center bg-[#2A2D35] rounded-lg border border-slate-600/40 px-3 py-2 flex-1 w-1/3">
              <span className="text-slate-300 text-sm font-medium mr-3">Amount</span>
           
                <input
                  type="text"
                  placeholder="0"
                  value={values.value || ''}
                  onChange={(e) => updateInputValue(option.id, 'value', e.target.value)}
                  className="bg-transparent text-white text-sm outline-none placeholder-slate-500 text-right w-full"
                />
                <span className="text-slate-400 text-sm ml-1">%</span>
        
            </div>
            
            <button
              onClick={() => handleDelete(option.id)}
              className="p-2 text-slate-400 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
            >
              <Trash2 size={16} />
            </button>
          </div>
        );

      case 'stopLoss':
        return (
          <div key={option.id} className="flex items-center gap-3 mb-3 w-full">
            <div className="flex items-center bg-[#2A2D35] rounded-lg border border-slate-600/40 px-3 py-2 flex-1 w-1/3">
              <option.icon size={20} className={getColorClasses(option.color) + " mr-2"} />
              <span className="text-slate-300 text-sm font-medium mr-2">SL</span>
           
                <input
                  type="text"
                  placeholder="-0"
                  value={values.value || ''}
                  onChange={(e) => updateInputValue(option.id, 'value', e.target.value)}
                  className="bg-transparent text-white text-sm outline-none placeholder-slate-500 text-right w-full"
                />
                <span className="text-slate-400 text-sm ml-1">%</span>
            
            </div>
            
            <div className="flex items-center bg-[#2A2D35] rounded-lg border border-slate-600/40 px-3 py-2 flex-1 w-1/3">
              <span className="text-slate-300 text-sm font-medium mr-3">Amount</span>
      
                <input
                  type="text"
                  placeholder="0"
                  value={values.value || ''}
                  onChange={(e) => updateInputValue(option.id, 'value', e.target.value)}
                  className="bg-transparent text-white text-sm outline-none placeholder-slate-500 text-right w-full"
                />
                <span className="text-slate-400 text-sm ml-1">%</span>
      
            </div>
            
            <button
              onClick={() => handleDelete(option.id)}
              className="p-2 text-slate-400 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
            >
              <Trash2 size={16} />
            </button>
          </div>
        );

      case 'devSell':
        return (
          <div key={option.id} className="flex items-center gap-3 mb-3">
            <div className="flex justify-between items-center bg-[#2A2D35] rounded-lg border border-slate-600/40 px-3 py-2 flex-1">
              {/* Left side: icon + label */}
              <div className="flex items-center whitespace-nowrap mr-3">
                <option.icon
                  size={16}
                  className={getColorClasses(option.color) + " mr-2"}
                />
                <span className="text-slate-300 text-sm font-medium">
                  Sell Amount on Dev Sell
                </span>
              </div>
        
              {/* Right side: input + % */}
              <div className="flex items-center min-w-[80px]">
                <input
                  type="text"
                  placeholder="0"
                  value={values.value || ''}
                  onChange={(e) => updateInputValue(option.id, 'value', e.target.value)}
                  className="bg-transparent text-white text-sm outline-none placeholder-slate-500 text-right w-full"
                />
                <span className="text-slate-400 text-sm ml-1">%</span>
              </div>
            </div>
        
            <button
              onClick={() => handleDelete(option.id)}
              className="p-2 text-slate-400 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
            >
              <Trash2 size={16} />
            </button>
          </div>
        );
        

      case 'migration':
        return (
          <div key={option.id} className="flex items-center gap-3 mb-3">
            <div className="flex justify-between items-center bg-[#2A2D35] rounded-lg border border-slate-600/40 px-3 py-2 flex-1">
              {/* Left side: icon + label */}
              <div className="flex items-center whitespace-nowrap mr-3">
                <option.icon
                  size={16}
                  className={getColorClasses(option.color) + " mr-2"}
                />
                <span className="text-slate-300 text-sm font-medium">
                  Sell Amount on Migration
                </span>
              </div>
        
              {/* Right side: input + % */}
              <div className="flex items-center min-w-[80px]">
                <input
                  type="text"
                  placeholder="0"
                  value={values.value || ''}
                  onChange={(e) => updateInputValue(option.id, 'value', e.target.value)}
                  className="bg-transparent text-white text-sm outline-none placeholder-slate-500 text-right w-full"
                />
                <span className="text-slate-400 text-sm ml-1">%</span>
              </div>
            </div>
        
            <button
              onClick={() => handleDelete(option.id)}
              className="p-2 text-slate-400 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
            >
              <Trash2 size={16} />
            </button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="w-full   rounded-xl">
      {/* Header */}
    

      {/* Custom Dropdown - Only show if there are available options */}
      {availableOptions.length > 0 && (
        <div className="relative mb-4">
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="w-full bg-[#2A2D35] hover:bg-[#2F3339] border border-slate-600/40 hover:border-slate-500/60 rounded-lg px-4 py-3 flex items-center justify-between text-white transition-all duration-200"
          >
            <span className="text-slate-400 text-base">Add Strategy</span>
            <ChevronDown 
              size={18} 
              className={`text-slate-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
            />
          </button>

          {/* Dropdown Menu */}
          {isOpen && (
            <div className="absolute top-full left-0 right-0 mt-2 bg-[#2A2D35] border border-slate-600/40 rounded-lg shadow-xl shadow-black/20 z-50 overflow-hidden">
              {availableOptions.map((option) => (
                <button
                  key={option.id}
                  onClick={() => handleOptionSelect(option)}
                  className="w-full px-4 py-3 text-left hover:bg-[#3A3D45] border-b border-slate-700/50 last:border-b-0 flex items-center transition-colors duration-150"
                >
                  <option.icon size={18} className={getColorClasses(option.color) + " mr-3"} />
                  <span className="text-white text-base font-medium">{option.label}</span>
                </button>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Dynamic Input Fields */}
      <div className="space-y-3">
        {selectedOptions.map((option) => renderInputField(option))}
      </div>

    </div>
  );
};

export default AdvancedTradingStrategy;