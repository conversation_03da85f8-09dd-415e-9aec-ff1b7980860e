import { TrendingUp, AlertTriangle, Fuel } from 'lucide-react';
import { usePreset } from '../Preset/PresetContext';

export default function Setting({ isBuy }: { isBuy: boolean }) {
  const { activePreset, activeTab, presetData } = usePreset();
  const current =
  activePreset !== null ? presetData[activePreset][isBuy ? 'buy' : 'sell'] : null;
  const Oval = ({ 
    size = 16, 
    color = "#FFEB3B", 
    strokeWidth = 2,
    rx = 10,
    ry = 6 
  }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      {/* Horizontal oval */}
      <ellipse 
        cx="12" 
        cy="12" 
        rx={rx} 
        ry={ry} 
        stroke={color} 
        strokeWidth={strokeWidth}
      />
      
      {/* Curved line through the middle - curved in opposite direction */}
      <path 
        d="M 4 12 Q 12 16 20 12" 
        stroke={color} 
        strokeWidth={strokeWidth}
        fill="none"
      />
    </svg>
  )
  if (!current) return null;

  const { slippage, bribe, priority, mevMode } = current;

  return (
    <div className="flex items-center space-x-4 mb-4 text-sm">
      {/* Slippage */}
      <div className="flex items-center space-x-2">
        <TrendingUp size={16} className="text-gray-400" />
        <span className="text-white">{slippage}%</span>
      </div>

      {/* Bribe */}


      {/* Priority */}
      <div className="flex items-center space-x-2">
      <Fuel size={14} className="text-yellow-500" />
        <span className="text-yellow-500">{priority}</span>
      </div>

      <div className="flex items-center space-x-2">
   <Oval/>
        <span className="text-yellow-500">{bribe}</span>
      </div>

      {/* MEV Mode */}
      <span className="text-gray-400">{mevMode}</span>
    </div>
  );
}
