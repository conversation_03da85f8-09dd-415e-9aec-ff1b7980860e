import { useState, useEffect } from 'react';
import { useBackendTradeData } from '@/hooks/useBackendTradeData';

interface StatsData {
  vol: string;
  buys: number;
  buy_amt: number;
  sells: number;
  sell_amt: number;
  net: string;
}

const emptyStats: StatsData = {
  vol: '$0',
  buys: 0,
  buy_amt: 0,
  sells: 0,
  sell_amt: 0,
  net: '$0'
};

const timeframes = ['5m', '1h', '6h', '24h'];

export const TradingStats = () => {
  const [hovered, setHovered] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState('24h');
  const [stats, setStats] = useState<StatsData>(emptyStats);

  // Get pool address from localStorage for WebSocket connection
  const getPoolAddressFromStorage = (): string | null => {
    try {
      const activePulseTokenStr = localStorage.getItem('activePulseToken');
      if (activePulseTokenStr) {
        const activePulseToken = JSON.parse(activePulseTokenStr);
        return activePulseToken.pool_address || null;
      }
    } catch (error) {
      console.error('Error parsing activePulseToken from localStorage:', error);
    }
    return null;
  };

  const poolAddress = getPoolAddressFromStorage();
  const { latestRawTrade, isConnected } = useBackendTradeData(poolAddress);

  // Format numbers with K/M/B suffixes
  const formatVolume = (value: number): string => {
    if (value === 0) return '$0';
    if (value >= 1_000_000_000) return `$${(value / 1_000_000_000).toFixed(2)}B`;
    if (value >= 1_000_000) return `$${(value / 1_000_000).toFixed(2)}M`;
    if (value >= 1_000) return `$${(value / 1_000).toFixed(2)}K`;
    return `$${value.toFixed(2)}`;
  };

  // Get volume data for all timeframes
  const getTimeframeVolume = (timeframe: string, pairData: any) => {
    switch (timeframe) {
      case '5m':
        return formatVolume(pairData?.volume_5min || 0);
      case '1h':
        return formatVolume(pairData?.volume_1h || 0);
      case '6h':
        return formatVolume(pairData?.volume_6h || 0);
      case '24h':
        return formatVolume(pairData?.volume24h || 0);
      default:
        return '$0';
    }
  };

  // Calculate stats from the latest trade data with volume metrics
  useEffect(() => {
    if (latestRawTrade && isConnected && latestRawTrade.pairData) {
      const pairData = latestRawTrade.pairData;

      // Format volume based on selected timeframe using Mobula data
      let volume = 0;
      let buyVolume = 0;
      let sellVolume = 0;
      let buyCount = 0;
      let sellCount = 0;

      switch (selectedTimeframe) {
        case '5m':
          volume = pairData.volume_5min || 0;
          buyVolume = pairData.buy_volume_5min || 0;
          sellVolume = pairData.sell_volume_5min || 0;
          buyCount = pairData.buyers_5min || 0;
          sellCount = pairData.sellers_5min || 0;
          break;
        case '1h':
          volume = pairData.volume_1h || 0;
          buyVolume = pairData.buy_volume_1h || 0;
          sellVolume = pairData.sell_volume_1h || 0;
          buyCount = pairData.buyers_1h || 0;
          sellCount = pairData.sellers_1h || 0;
          break;
        case '6h':
          volume = pairData.volume_6h || 0;
          buyVolume = pairData.buy_volume_6h || 0;
          sellVolume = pairData.sell_volume_6h || 0;
          buyCount = pairData.buyers_6h || 0;
          sellCount = pairData.sellers_6h || 0;
          break;
        case '24h':
        default:
          volume = pairData.volume24h || 0;
          buyVolume = pairData.buy_volume_24h || 0;
          sellVolume = pairData.sell_volume_24h || 0;
          buyCount = pairData.buyers_24h || 0;
          sellCount = pairData.sellers_24h || 0;
          break;
      }

      // Calculate net volume
      const netVolume = buyVolume - sellVolume;

      const newStats: StatsData = {
        vol: formatVolume(volume),
        buys: buyCount,
        buy_amt: Math.round(buyVolume),
        sells: sellCount,
        sell_amt: Math.round(sellVolume),
        net: formatVolume(Math.abs(netVolume))
      };

      setStats(newStats);

      console.log('📊 [TRADING STATS] Updated with Mobula data:', {
        selectedTimeframe,
        volume,
        buyVolume,
        sellVolume,
        netVolume,
        buyCount,
        sellCount,
        formattedStats: newStats,
        availableMobulaFields: Object.keys(pairData).filter(key => key.includes('volume') || key.includes('buy') || key.includes('sell'))
      });
    } else {
      // No data available, show empty stats
      setStats(emptyStats);
    }
  }, [latestRawTrade, isConnected, selectedTimeframe]);

  const totalAmount = stats.buy_amt + stats.sell_amt || 1; // avoid div by zero
  const buyPercent = (stats.buy_amt / totalAmount) * 100;
  const sellPercent = (stats.sell_amt / totalAmount) * 100;

  return (
    <div
      className="text-white border border-[#2a2a2e] h-full relative transition-all duration-200"
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      {/* Timeframe Selector */}
      <div
        className={`absolute inset-0  transition-opacity duration-200 ${
          hovered ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none px-5 py-4'
        }`}
      >
        <div className="grid grid-cols-4  text-center text-white text-sm h-full">
          {timeframes.map((tf) => (
            <button
              key={tf}
              onClick={() => setSelectedTimeframe(tf)}
              className={`p-2  transition-colors flex flex-col items-center justify-center ${
                selectedTimeframe === tf ? 'bg-[#1a1a1a]' : 'bg-transparent'
              } hover:bg-[#2a2a2e]`}
            >
              <div className="text-gray-300 text-xs">{tf}</div>
              <div className={`font-medium text-xs ${selectedTimeframe === tf ? 'text-blue-400' : 'text-white'}`}>
                {getTimeframeVolume(tf, latestRawTrade?.pairData)}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Stats Display */}
      <div
        className={`absolute inset-0 px-5 py-4 transition-opacity duration-200 flex flex-col justify-between ${
          hovered ? 'opacity-0 pointer-events-none' : 'opacity-100 pointer-events-auto'
        }`}
      >
        <div className="grid grid-cols-4 gap-4 text-sm">
          <div>
            <div className="text-gray-400 text-xs">{selectedTimeframe} Vol</div>
            <div className="text-white font-medium">{stats.vol}</div>
          </div>
          <div>
            <div className="text-gray-400 text-xs">Buys</div>
            <div className="text-green-400 font-medium">
              {stats.buys} / {formatVolume(stats.buy_amt)}
            </div>
          </div>
          <div>
            <div className="text-gray-400 text-xs">Sells</div>
            <div className="text-pink-400 font-medium">
              {stats.sells} / {formatVolume(stats.sell_amt)}
            </div>
          </div>
          <div>
            <div className="text-gray-400 text-xs">Net Vol.</div>
            <div className="text-pink-400 font-medium">{stats.net}</div>
          </div>
        </div>

        <div className="flex w-full mt-2 h-1 rounded overflow-hidden bg-[#333]">
          <div
            className="bg-green-500 h-full"
            style={{ width: `${buyPercent}%` }}
          />
          <div
            className="bg-pink-500 h-full"
            style={{ width: `${sellPercent}%` }}
          />
        </div>
      </div>
    </div>
  );
};
