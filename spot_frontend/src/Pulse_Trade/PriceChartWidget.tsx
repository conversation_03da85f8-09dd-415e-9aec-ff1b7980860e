import React, { useEffect, useRef, useState } from 'react';

interface PriceChartWidgetProps {
  tokenAddress?: string;
  pairAddress?: string;
  chainId?: string;
  theme?: 'light' | 'dark';
  defaultInterval?: string;
  showHoldersChart?: boolean;
  hideLeftToolbar?: boolean;
  hideTopToolbar?: boolean;
  hideBottomToolbar?: boolean;
}

export const PriceChartWidget: React.FC<PriceChartWidgetProps> = ({
  tokenAddress,
  pairAddress,
  chainId = 'solana',
  theme = 'dark',
  defaultInterval = '15',
  showHoldersChart = false,
  hideLeftToolbar = false,
  hideTopToolbar = false,
  hideBottomToolbar = false
}) => {
  const containerRef = useRef<HTMLIFrameElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Build the Birdeye iframe URL with parameters
  const buildBirdeyeUrl = () => {
    // Use only tokenAddress for the chart
    const address = tokenAddress;

    // console.log('🔍 ADDRESS SELECTION FOR CHART:');
    // console.log('  - tokenAddress:', tokenAddress);
    // console.log('  - selected address:', address);
    // console.log('  - address type: token');

    if (!address) {
      setError('No token address provided');
      return '';
    }

    const baseUrl = `https://birdeye.so/tv-widget/${address}`;

    // Build URL manually to handle special formatting for CSS properties and chart overrides
    const params = [
      `chain=${chainId === 'solana' ? 'solana' : 'ethereum'}`,
      'viewMode=pair',
      `chartInterval=${defaultInterval}`,
      'chartType=Candle',
      'chartTimezone=Asia%2FCalcutta',
      `chartLeftToolbar=${hideLeftToolbar ? 'hide' : 'show'}`,
      `theme=${theme}`,
      // Custom CSS properties for theme matching
      'cssCustomProperties=--tv-color-platform-background%3A%23141416',
      'cssCustomProperties=--tv-color-pane-background%3A%23141416',
      // Chart overrides for background styling
      'chartOverrides=paneProperties.backgroundType%3Asolid',
      'chartOverrides=paneProperties.background%3Argba%2820%2C+20%2C+22%2C+1%29'
    ];

    const finalUrl = `${baseUrl}?${params.join('&')}`;

    // Debug logging
    // console.log('🔍 Birdeye URL Debug Info:');
    // console.log('Selected Address:', address);
    // console.log('Address Type: token');
    // console.log('Chain ID:', chainId);
    // console.log('Final URL:', finalUrl);
    // console.log('URL Length:', finalUrl.length);

    return finalUrl;
  };

  useEffect(() => {
    // Reset states when token address changes
    setError(null);
    setIsLoading(true);

    // Simulate loading delay for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000); // Increased to 2 seconds for iframe loading

    return () => clearTimeout(timer);
  }, [tokenAddress]);

  // Handle iframe load events
  const handleIframeLoad = () => {
    console.log('✅ Birdeye iframe loaded successfully');
    setIsLoading(false);
    setError(null);
  };

  const handleIframeError = () => {
    console.error('❌ Birdeye iframe failed to load');
    setError('Failed to load Birdeye chart widget');
    setIsLoading(false);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center rounded-lg" style={{ backgroundColor: '#141416' }}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading chart...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center rounded-lg" style={{ backgroundColor: '#141416' }}>
        <div className="text-center">
          <div className="text-red-500 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-gray-400 mb-2">Failed to load chart</p>
          <p className="text-gray-500 text-sm">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const iframeUrl = buildBirdeyeUrl();



  if (!iframeUrl) {
    return (
      <div className="w-full h-full flex items-center justify-center rounded-lg" style={{ backgroundColor: '#141416' }}>
        <div className="text-center">
          <div className="text-red-500 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-gray-400 mb-2">No token address provided</p>
          <p className="text-gray-500 text-sm">Please provide a token address to display the chart</p>
          <p className="text-gray-600 text-xs mt-2">
            tokenAddress: {tokenAddress || 'undefined'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full rounded-lg overflow-hidden relative" style={{ backgroundColor: '#141416' }}>
      {/* Show loading overlay while iframe is loading */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-[#141416] z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading Birdeye chart...</p>
          </div>
        </div>
      )}

      <iframe
        ref={containerRef}
        src={iframeUrl}
        width="100%"
        height="600"
        allowFullScreen={true}
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        className="w-full h-full"
        style={{
          width: "100%",
          height: "100%",
          minHeight: "600px",
          backgroundColor: '#141416',
          border: 'none'
        }}
        title="Birdeye Trading Chart"
        // Additional iframe attributes for better compatibility
        allow="fullscreen"
      />
    </div>
  );
};

export default PriceChartWidget;
