import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Settings } from 'lucide-react';

interface TokenData {
  id: string;
  token: string;
  bought: number;
  sold: number;
  remaining: number;
  pnl: number;
}

const Positions: React.FC = () => {
  const [tokens, setTokens] = useState<TokenData[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // TODO: Replace with real API call to fetch positions data
  useEffect(() => {
    const fetchPositions = async () => {
      setIsLoading(true);
      try {
        // This would be replaced with actual API call
        // const response = await fetch('/api/positions');
        // const data = await response.json();
        // setTokens(data);

        // For now, show empty data instead of mock data
        setTokens([]);
      } catch (error) {
        console.error('Failed to fetch positions:', error);
        setTokens([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPositions();
  }, []);

const formatCurrency = (value: number) =>
  new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);

const getPnLColor = (pnl: number) =>
  pnl >= 0 ? 'text-green-400' : 'text-red-400';

const getPnLIcon = (pnl: number) =>
  pnl >= 0 ? <TrendingUp size={14} /> : <TrendingDown size={14} />;

  return (
    <div className=" rounded-lg overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="">
            <tr className="border-b border-neutral-700">
              <th className="text-left px-6 py-3 text-sm font-medium text-neutral-300">Token</th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">Bought</th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">Sold</th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">Remaining</th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">PnL</th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">
                <div className="flex items-center justify-end gap-1">
                 Actions

                </div>
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-neutral-800/50">
            {tokens.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-4 py-8 text-center text-neutral-400">
                  {isLoading ? 'Loading positions...' : 'No positions available'}
                </td>
              </tr>
            ) : (
              tokens.map((token) => (
              <tr key={token.id} className="hover:bg-neutral-800/30 transition-colors">
                <td className="px-6 py-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-sm text-white font-bold">
                      {token.token.slice(0, 2)}
                    </div>
                    <span className="font-medium text-white">{token.token}</span>
                  </div>
                </td>
                <td className="px-4 py-4 text-sm text-white font-mono text-right">
                  {formatCurrency(token.bought)}
                </td>
                <td className="px-4 py-4 text-sm text-white font-mono text-right">
                  {formatCurrency(token.sold)}
                </td>
                <td className="px-4 py-4 text-sm text-white font-mono text-right">
                  {formatCurrency(token.remaining)}
                </td>
                <td className={`px-4 py-4 text-sm font-mono text-right ${getPnLColor(token.pnl)}`}>
                  <div className="flex items-center justify-end gap-1">
                    {getPnLIcon(token.pnl)}
                    {formatCurrency(token.pnl)}
                  </div>
                </td>
                <td className="px-4 py-4 text-right">
                  <button className="text-neutral-500 hover:text-neutral-300 transition-colors">
                    
                  </button>
                </td>
              </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Positions;
