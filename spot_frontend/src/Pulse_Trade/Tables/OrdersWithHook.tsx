import React, { useState, useEffect } from 'react';
import { Settings, TrendingUp, TrendingDown, X, Clock, CheckCircle, XCircle, Trash2 } from 'lucide-react';
import { usePrivy } from '@privy-io/react-auth';
import { limitOrderService, LimitOrder } from '../../services/limitOrderService';
import { showSwapInfoToast, showSwapErrorToast } from '../../utils/swapToast';
import ConfirmationModal from '../../components/ConfirmationModal';
import useConfirmation from '../../hooks/useConfirmation';

// Type definitions for the combined data
interface BaseOrderItem {
  id: string;
  token: {
    name: string;
    logo: string;
    fullName: string;
  };
  type: string;
  amount: number;
  status: string;
}

interface LimitOrderItem extends BaseOrderItem {
  targetPrice: number;
  currentMarketCap?: number;
  targetMarketCap?: number;
  created_at: string;
  isLimitOrder: true;
  limitOrderId: string;
}

// Removed mock data - only showing real limit orders

export default function OrdersWithHook() {
  const { authenticated, user } = usePrivy();
  const [limitOrders, setLimitOrders] = useState<LimitOrder[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 🚀 USE CONFIRMATION HOOK
  const confirmation = useConfirmation();

  // Fetch limit orders
  const fetchLimitOrders = async () => {
    if (!authenticated || !user?.id) {
      setLimitOrders([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await limitOrderService.getLimitOrders(user.id, {
        order_by: 'created_at',
        order_direction: 'desc',
        limit: 50
      });

      if (response.success && response.data) {
        setLimitOrders(response.data);
      } else {
        setError(response.error || 'Failed to fetch limit orders');
        setLimitOrders([]);
      }
    } catch (err) {
      console.error('Error fetching limit orders:', err);
      setError('Failed to fetch limit orders');
      setLimitOrders([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Cancel limit order
  const cancelLimitOrder = async (orderId: string) => {
    if (!user?.id) return;

    try {
      const response = await limitOrderService.cancelLimitOrder(user.id, orderId);

      if (response.success) {
        showSwapInfoToast('Limit order cancelled successfully');
        fetchLimitOrders(); // Refresh the list
      } else {
        showSwapErrorToast(response.error || 'Failed to cancel limit order');
      }
    } catch (err) {
      console.error('Error cancelling limit order:', err);
      showSwapErrorToast('Failed to cancel limit order');
    }
  };

  // 🚀 DELETE WITH ELEGANT CONFIRMATION
  const handleDeleteOrder = (orderId: string) => {
    confirmation.showConfirmation(
      {
        title: 'Delete Limit Order',
        message: 'Are you sure you want to permanently delete this limit order? This action cannot be undone and the order will be removed from your history.',
        confirmText: 'Delete Order',
        cancelText: 'Cancel',
        type: 'danger'
      },
      async () => {
        if (!user?.id) return;

        const response = await limitOrderService.deleteLimitOrder(user.id, orderId);

        if (response.success) {
          showSwapInfoToast('Limit order deleted successfully');
          fetchLimitOrders(); // Refresh the list
        } else {
          showSwapErrorToast(response.error || 'Failed to delete limit order');
          throw new Error(response.error || 'Failed to delete limit order');
        }
      }
    );
  };

  // Fetch orders on component mount and when user changes
  useEffect(() => {
    fetchLimitOrders();
  }, [authenticated, user?.id]);

  // Listen for limit order creation events
  useEffect(() => {
    const handleLimitOrderCreated = () => {
      fetchLimitOrders();
    };

    window.addEventListener('limitOrderCreated', handleLimitOrderCreated);
    return () => {
      window.removeEventListener('limitOrderCreated', handleLimitOrderCreated);
    };
  }, []);

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(amount);
  };

  const formatPrice = (price: number) => {
    return price >= 0.01 ? `$${price.toFixed(4)}` : `$${price.toFixed(8)}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock size={14} className="text-yellow-400" />;
      case 'executed':
        return <CheckCircle size={14} className="text-green-400" />;
      case 'cancelled':
        return <XCircle size={14} className="text-red-400" />;
      case 'expired':
        return <XCircle size={14} className="text-gray-400" />;
      default:
        return <Clock size={14} className="text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-400';
      case 'executed':
        return 'text-green-400';
      case 'cancelled':
        return 'text-red-400';
      case 'expired':
        return 'text-gray-400';
      default:
        return 'text-gray-400';
    }
  };

  // Convert limit orders to display format
  const limitOrderItems: LimitOrderItem[] = limitOrders.map(order => ({
    id: `limit-${order.id}`,
    token: {
      name: order.token_symbol,
      logo: order.token_image || '', // Use actual token image URL
      fullName: order.token_name
    },
    type: order.direction.charAt(0).toUpperCase() + order.direction.slice(1),
    amount: order.amount,
    targetPrice: order.target_price,
    currentMarketCap: order.current_market_cap,
    targetMarketCap: order.target_market_cap,
    status: order.status,
    created_at: order.created_at,
    isLimitOrder: true,
    limitOrderId: order.id
  }));

  // Only show real limit orders (no mock data)
  const combinedData: LimitOrderItem[] = limitOrderItems;

  if (!authenticated) {
    return (
      <div className="rounded-2xl border border-neutral-800 overflow-hidden">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-neutral-400 text-sm mb-2">Connect your wallet to view orders</p>
            <p className="text-neutral-500 text-xs">Your limit orders will appear here</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-2xl border border-neutral-800 overflow-hidden">
      {/* Header with refresh button */}
      <div className="flex items-center justify-between p-4 border-b border-neutral-700 bg-neutral-900/40">
        <h3 className="text-white font-medium">Limit Orders</h3>
        <button
          onClick={fetchLimitOrders}
          disabled={isLoading}
          className="text-neutral-400 hover:text-white transition-colors disabled:opacity-50"
        >
          <Settings size={16} className={isLoading ? 'animate-spin' : ''} />
        </button>
      </div>

      {/* Error state */}
      {error && (
        <div className="p-4 bg-red-900/20 border-b border-red-800/30">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-neutral-900/60">
            <tr className="text-left">
              <th className="px-4 py-3 text-xs font-medium text-neutral-400 uppercase tracking-wider">Token</th>
              <th className="px-4 py-3 text-xs font-medium text-neutral-400 uppercase tracking-wider">Type</th>
              <th className="px-4 py-3 text-xs font-medium text-neutral-400 uppercase tracking-wider">Amount</th>
              <th className="px-4 py-3 text-xs font-medium text-neutral-400 uppercase tracking-wider">Target Price</th>
              <th className="px-4 py-3 text-xs font-medium text-neutral-400 uppercase tracking-wider">Status</th>
              <th className="px-4 py-3 text-xs font-medium text-neutral-400 uppercase tracking-wider">Created</th>
              <th className="px-4 py-3 text-xs font-medium text-neutral-400 uppercase tracking-wider text-right">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-neutral-800">
            {combinedData.length === 0 ? (
              <tr>
                <td colSpan={7} className="px-4 py-8 text-center">
                  <div className="text-neutral-400 text-sm">
                    {isLoading ? 'Loading orders...' : 'No limit orders found'}
                  </div>
                  <div className="text-neutral-500 text-xs mt-1">
                    {isLoading ? 'Please wait...' : 'Create your first limit order to get started'}
                  </div>
                </td>
              </tr>
            ) : (
              combinedData.map((position) => (
                <tr key={position.id} className="hover:bg-neutral-900/40 transition-colors">
                  {/* Token */}
                  <td className="px-4 py-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        {position.token.logo ? (
                          <img
                            src={position.token.logo}
                            alt={position.token.name}
                            className="w-8 h-8 rounded-full"
                            onError={(e) => {
                              (e.target as HTMLImageElement).style.display = 'none';
                            }}
                          />
                        ) : (
                          <div className="w-8 h-8 rounded-full bg-neutral-700 flex items-center justify-center">
                            <span className="text-xs font-medium text-neutral-300">
                              {position.token.name.charAt(0)}
                            </span>
                          </div>
                        )}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-white">{position.token.name}</div>
                        <div className="text-xs text-neutral-400">{position.token.fullName}</div>
                      </div>
                    </div>
                  </td>

                  {/* Type */}
                  <td className="px-4 py-4">
                    <div className="flex items-center space-x-1">
                      {position.type === 'Buy' ? (
                        <TrendingUp size={14} className="text-green-400" />
                      ) : (
                        <TrendingDown size={14} className="text-red-400" />
                      )}
                      <span className={`text-sm font-medium ${
                        position.type === 'Buy' ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {position.type}
                      </span>
                    </div>
                  </td>

                  {/* Amount */}
                  <td className="px-4 py-4 text-sm text-white">
                    {formatAmount(position.amount)}
                  </td>

                  {/* Target Price */}
                  <td className="px-4 py-4 text-sm text-white">
                    {formatPrice(position.targetPrice)}
                  </td>

                  {/* Status */}
                  <td className="px-4 py-4">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(position.status)}
                      <span className={`text-sm font-medium capitalize ${getStatusColor(position.status)}`}>
                        {position.status}
                      </span>
                    </div>
                  </td>

                  {/* Created */}
                  <td className="px-4 py-4 text-sm text-neutral-400 text-right">
                    {formatDate(position.created_at)}
                  </td>

                  {/* Actions */}
                  <td className="px-4 py-4 text-right">
                    <div className="flex items-center justify-end space-x-2">
                      {position.status === 'pending' ? (
                        <button
                          onClick={() => cancelLimitOrder(position.limitOrderId)}
                          className="text-red-400 hover:text-red-300 transition-colors"
                          title="Cancel Order"
                        >
                          <X size={16} />
                        </button>
                      ) : (position.status === 'cancelled' || position.status === 'expired') ? (
                        <button
                          onClick={() => handleDeleteOrder(position.limitOrderId)}
                          className="text-red-400 hover:text-red-300 transition-colors"
                          title="Delete Order"
                        >
                          <Trash2 size={16} />
                        </button>
                      ) : (
                        <div className="text-neutral-500">
                          <Settings size={16} />
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* 🚀 ELEGANT CONFIRMATION MODAL */}
      <ConfirmationModal
        isOpen={confirmation.isOpen}
        onClose={confirmation.hideConfirmation}
        onConfirm={confirmation.handleConfirm}
        title={confirmation.options?.title || ''}
        message={confirmation.options?.message || ''}
        confirmText={confirmation.options?.confirmText}
        cancelText={confirmation.options?.cancelText}
        type={confirmation.options?.type}
        isLoading={confirmation.isLoading}
      />
    </div>
  );
}
