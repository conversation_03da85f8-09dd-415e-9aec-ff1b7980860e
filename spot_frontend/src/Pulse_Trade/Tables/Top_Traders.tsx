import { useState, useEffect } from 'react';

interface HolderData {
  id: number;
  rank: number;
  address: string;
  amount: string;
  bought: { value: number; txns: number };
  sold: { value: number; txns: number };
  pnl: number;
  walletAddress: string;
  remaining: string;
  activity: string;
}

export default function Top_Traders() {
  const [sortConfig, setSortConfig] = useState({ key: 'rank', direction: 'asc' });
  const [holdersData, setHoldersData] = useState<HolderData[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // TODO: Replace with real API call to fetch top traders data
  useEffect(() => {
    const fetchTopTraders = async () => {
      setIsLoading(true);
      try {
        // This would be replaced with actual API call
        // const response = await fetch('/api/top-traders');
        // const data = await response.json();
        // setHoldersData(data);

        // For now, show empty data instead of mock data
        setHoldersData([]);
      } catch (error) {
        console.error('Failed to fetch top traders:', error);
        setHoldersData([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTopTraders();
  }, []);

  const formatCurrency = (value: number, decimals = 2) => {
    return `$${value.toFixed(decimals)}`;
  };

  const formatAmount = (amount: string) => {
    return amount;
  };

  const handleSort = (key: string) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };



  const getPnlColor = (pnl: number) => {
    return pnl >= 0 ? 'text-green-400' : 'text-red-400';
  };

  const sortedData = [...holdersData].sort((a, b) => {
    if (sortConfig.key === 'rank') {
      return sortConfig.direction === 'asc' ? a.rank - b.rank : b.rank - a.rank;
    }
    return 0;
  });

  return (
    <div className="h-full flex flex-col">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="sticky top-0 z-10">
            <tr className="border-b border-neutral-700">
              <th 
                className="text-left px-4 py-3 text-sm font-medium text-neutral-300 cursor-pointer hover:text-white transition-colors"
                onClick={() => handleSort('rank')}
              >
                <div className="flex items-center gap-1">
                  Rank
                </div>
              </th>
              <th className="text-left px-4 py-3 text-sm font-medium text-neutral-300">
                Address
              </th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">
                Amount
              </th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">
                Bought
              </th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">
                Sold
              </th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">
                PNL
              </th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">
                Remaining
              </th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">
                <div className="flex items-center justify-end gap-1">
                  Activity
                  
                </div>
              </th>
            </tr>
          </thead>
          </table>
          </div>
          <div className="flex-1 overflow-y-auto">
          <table className="w-full">
          <tbody className="divide-y divide-neutral-800/50 ">
            {sortedData.length === 0 ? (
              <tr>
                <td colSpan={8} className="px-4 py-8 text-center text-neutral-400">
                  {isLoading ? 'Loading top traders...' : 'No top traders data available'}
                </td>
              </tr>
            ) : (
              sortedData.map((holder) => (
              <tr key={holder.id} className="hover:bg-neutral-800/30 transition-colors">
                {/* Rank */}
                <td className="px-4 py-4 text-sm text-white font-medium">
                  {holder.rank}
                </td>

                {/* Address */}
                <td className="px-4 py-4">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1">
  
                      <span className="text-sm text-white font-mono">{holder.address}</span>

                    </div>
                  </div>
                </td>

                {/* Amount */}
                <td className="px-4 py-4 text-sm text-white font-mono text-right">
                  {formatAmount(holder.amount)}
                </td>

                {/* Bought */}
                <td className="px-4 py-4 text-right">
                  <div className="text-sm">
                    <div className="text-green-400 font-medium">
                      {formatCurrency(holder.bought.value)}
                    </div>
                    <div className="text-xs text-neutral-400">
                      {holder.bought.txns} txn{holder.bought.txns !== 1 ? 's' : ''}
                    </div>
                  </div>
                </td>

                {/* Sold */}
                <td className="px-4 py-4 text-right">
                  <div className="text-sm">
                    <div className="text-red-400 font-medium">
                      {formatCurrency(holder.sold.value)}
                    </div>
                    <div className="text-xs text-neutral-400">
                      {holder.sold.txns} txn{holder.sold.txns !== 1 ? 's' : ''}
                    </div>
                  </div>
                </td>

                {/* PNL */}
                <td className="px-4 py-4 text-right">
                  <div className="text-sm">
                    <div className={`font-medium ${getPnlColor(holder.pnl)}`}>
                      {holder.pnl >= 0 ? '+' : ''}{formatCurrency(holder.pnl)}
                    </div>
                    <div className="text-xs text-neutral-400 font-mono">
                      {holder.walletAddress}
                    </div>
                  </div>
                </td>

                {/* Remaining */}
                <td className="px-4 py-4 text-sm text-white font-medium text-right">
                  {holder.remaining}
                </td>

                {/* Activity */}
                <td className="px-4 py-4 text-sm text-neutral-400 text-right">
                  {holder.activity}
                </td>
              </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      
    </div>
  );
}