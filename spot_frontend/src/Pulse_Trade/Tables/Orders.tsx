import { useState, useEffect } from 'react';
import { Settings, TrendingUp, TrendingDown, X, Clock, CheckCircle, XCircle, Trash2 } from 'lucide-react';
import { usePrivy } from '@privy-io/react-auth';
import { limitOrderService, LimitOrder } from '../../services/limitOrderService';
import { showSwapInfoToast, showSwapErrorToast } from '../../utils/swapToast';
import ConfirmationModal from '../../components/ConfirmationModal';

// Type definitions for the combined data
interface BaseOrderItem {
  id: string;
  token: {
    name: string;
    logo: string;
    fullName: string;
  };
  type: string;
  amount: number;
  status: string;
}

interface LimitOrderItem extends BaseOrderItem {
  targetPrice: number;
  currentMarketCap?: number;
  targetMarketCap?: number;
  created_at: string;
  isLimitOrder: true;
  limitOrderId: string;
}

// Removed mock data - only showing real limit orders

export default function Orders() {
  const { authenticated, user } = usePrivy();
  const [limitOrders, setLimitOrders] = useState<LimitOrder[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 🚀 CONFIRMATION MODAL STATE
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [orderToDelete, setOrderToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  // Fetch limit orders
  const fetchLimitOrders = async () => {
    if (!authenticated || !user?.id) {
      setLimitOrders([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await limitOrderService.getLimitOrders(user.id, {
        order_by: 'created_at',
        order_direction: 'desc',
        limit: 50
      });

      if (response.success && response.data) {
        setLimitOrders(response.data);
      } else {
        setError(response.error || 'Failed to fetch limit orders');
        setLimitOrders([]);
      }
    } catch (err) {
      console.error('Error fetching limit orders:', err);
      setError('Failed to fetch limit orders');
      setLimitOrders([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Cancel limit order
  const cancelLimitOrder = async (orderId: string) => {
    if (!user?.id) return;

    try {
      const response = await limitOrderService.cancelLimitOrder(user.id, orderId);

      if (response.success) {
        showSwapInfoToast('Limit order cancelled successfully');
        fetchLimitOrders(); // Refresh the list
      } else {
        showSwapErrorToast(response.error || 'Failed to cancel limit order');
      }
    } catch (err) {
      console.error('Error cancelling limit order:', err);
      showSwapErrorToast('Failed to cancel limit order');
    }
  };

  // Show delete confirmation modal
  const showDeleteConfirmation = (orderId: string) => {
    setOrderToDelete(orderId);
    setShowDeleteModal(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!user?.id || !orderToDelete) return;

    setIsDeleting(true);

    try {
      const response = await limitOrderService.deleteLimitOrder(user.id, orderToDelete);

      if (response.success) {
        showSwapInfoToast('Limit order deleted successfully');
        fetchLimitOrders(); // Refresh the list
        setShowDeleteModal(false);
        setOrderToDelete(null);
      } else {
        showSwapErrorToast(response.error || 'Failed to delete limit order');
      }
    } catch (err) {
      console.error('Error deleting limit order:', err);
      showSwapErrorToast('Failed to delete limit order');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle delete cancellation
  const handleDeleteCancel = () => {
    if (!isDeleting) {
      setShowDeleteModal(false);
      setOrderToDelete(null);
    }
  };

  // Fetch orders on component mount and when user changes
  useEffect(() => {
    fetchLimitOrders();
  }, [authenticated, user?.id]);

  // Listen for limit order creation events
  useEffect(() => {
    const handleLimitOrderCreated = () => {
      fetchLimitOrders();
    };

    window.addEventListener('limitOrderCreated', handleLimitOrderCreated);
    return () => {
      window.removeEventListener('limitOrderCreated', handleLimitOrderCreated);
    };
  }, []);



  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(amount);
  };

  const formatPrice = (price: number) => {
    return price >= 0.01 ? `$${price.toFixed(4)}` : `$${price.toFixed(8)}`;
  };

  const formatMarketCap = (cap: number) => {
    if (cap >= 1_000_000_000) return `$${(cap / 1_000_000_000).toFixed(2)}B`;
    if (cap >= 1_000_000) return `$${(cap / 1_000_000).toFixed(2)}M`;
    if (cap >= 1_000) return `$${(cap / 1_000).toFixed(2)}K`;
    return `$${cap.toFixed(0)}`;
  };

  const getDirectionColor = (direction: string) => {
    return direction.toLowerCase() === 'buy'
      ? 'text-green-400 bg-green-400/10'
      : 'text-red-400 bg-red-400/10';
  };

  const getDirectionIcon = (direction: string) => {
    return direction.toLowerCase() === 'buy'
      ? <TrendingUp size={12} />
      : <TrendingDown size={12} />;
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return 'text-yellow-400';
      case 'executed': return 'text-green-400';
      case 'cancelled': return 'text-red-400';
      case 'expired': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return <Clock size={12} />;
      case 'executed': return <CheckCircle size={12} />;
      case 'cancelled': return <XCircle size={12} />;
      case 'expired': return <XCircle size={12} />;
      default: return <Clock size={12} />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Convert limit orders to display format
  const limitOrderItems: LimitOrderItem[] = limitOrders.map(order => ({
    id: `limit-${order.id}`,
    token: {
      name: order.token_symbol,
      logo: order.token_image || '', // Use actual token image URL
      fullName: order.token_name
    },
    type: order.direction.charAt(0).toUpperCase() + order.direction.slice(1),
    amount: order.amount,
    targetPrice: order.target_price,
    currentMarketCap: order.current_market_cap,
    targetMarketCap: order.target_market_cap,
    status: order.status,
    created_at: order.created_at,
    isLimitOrder: true,
    limitOrderId: order.id
  }));

  // Only show real limit orders (no mock data)
  const combinedData: LimitOrderItem[] = limitOrderItems;

  if (!authenticated) {
    return (
      <div className="rounded-2xl border border-neutral-800 overflow-hidden">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-neutral-400 text-sm mb-2">Connect your wallet to view orders</p>
            <p className="text-neutral-500 text-xs">Your limit orders will appear here</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-2xl border border-neutral-800 overflow-hidden">
      {/* Header with refresh button */}
      <div className="flex items-center justify-between p-4 border-b border-neutral-700 bg-neutral-900/40">
        <h3 className="text-white font-medium">Limit Orders</h3>
        <button
          onClick={fetchLimitOrders}
          disabled={isLoading}
          className="text-neutral-400 hover:text-white transition-colors disabled:opacity-50"
        >
          <Settings size={16} className={isLoading ? 'animate-spin' : ''} />
        </button>
      </div>

      {/* Error state */}
      {error && (
        <div className="p-4 bg-red-900/20 border-b border-red-800/30">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-neutral-700 bg-neutral-900/40 backdrop-blur-md">
              <th className="text-left px-6 py-3 text-xs font-semibold text-neutral-400 uppercase tracking-wider">
                Token
              </th>
              <th className="text-left px-4 py-3 text-xs font-semibold text-neutral-400 uppercase tracking-wider">
                Type
              </th>
              <th className="text-right px-4 py-3 text-xs font-semibold text-neutral-400 uppercase tracking-wider">
                Amount
              </th>
              <th className="text-right px-4 py-3 text-xs font-semibold text-neutral-400 uppercase tracking-wider">
                Target Price
              </th>
              <th className="text-right px-4 py-3 text-xs font-semibold text-neutral-400 uppercase tracking-wider">
                Market Cap
              </th>
              <th className="text-right px-4 py-3 text-xs font-semibold text-neutral-400 uppercase tracking-wider">
                Status
              </th>
              <th className="text-right px-4 py-3 text-xs font-semibold text-neutral-400 uppercase tracking-wider">
                Created
              </th>
              <th className="text-right px-4 py-3 text-xs font-semibold text-neutral-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-neutral-800/60">
            {isLoading ? (
              <tr>
                <td colSpan={8} className="px-6 py-8 text-center">
                  <div className="text-neutral-400">Loading orders...</div>
                </td>
              </tr>
            ) : combinedData.length === 0 ? (
              <tr>
                <td colSpan={8} className="px-6 py-8 text-center">
                  <div className="text-neutral-400">No orders found</div>
                  <div className="text-neutral-500 text-xs mt-1">Create a limit order to see it here</div>
                </td>
              </tr>
            ) : (
              combinedData.map((position) => (
                <tr key={position.id} className="hover:bg-neutral-800/40 transition-colors">
                  {/* Token */}
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-3">
                      {position.token.logo ? (
                        <img
                          src={position.token.logo}
                          alt={position.token.name}
                          className="w-8 h-8 rounded-full object-cover"
                          onError={(e) => {
                            // Fallback to gradient circle with initials if image fails to load
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const fallback = target.nextElementSibling as HTMLElement;
                            if (fallback) fallback.style.display = 'flex';
                          }}
                        />
                      ) : null}
                      <div
                        className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-sm text-white"
                        style={{ display: position.token.logo ? 'none' : 'flex' }}
                      >
                        {position.token.name.slice(0, 2).toUpperCase()}
                      </div>
                      <div>
                        <div className="font-semibold text-white">{position.token.name}</div>
                        <div className="text-xs text-neutral-400">{position.token.fullName}</div>
                        <div className="text-xs text-blue-400 mt-1">Limit Order</div>
                      </div>
                    </div>
                  </td>

                  {/* Type */}
                  <td className="px-4 py-4">
                    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getDirectionColor(position.type)}`}>
                      {getDirectionIcon(position.type)}
                      {position.type}
                    </div>
                  </td>

                  {/* Amount */}
                  <td className="px-4 py-4 text-sm text-white font-mono text-right">
                    {formatAmount(position.amount)}
                    <div className="text-xs text-neutral-400">
                      {position.type === 'Buy' ? 'SOL' : position.token.name}
                    </div>
                  </td>

                  {/* Target Price */}
                  <td className="px-4 py-4 text-sm text-white font-mono text-right">
                    {formatPrice(position.targetPrice)}
                  </td>

                  {/* Market Cap */}
                  <td className="px-4 py-4 text-sm text-white font-mono text-right">
                    {position.currentMarketCap && position.targetMarketCap ? (
                      <div>
                        <div className="text-neutral-400 text-xs">Current</div>
                        <div>{formatMarketCap(position.currentMarketCap)}</div>
                        <div className="text-neutral-400 text-xs mt-1">Target</div>
                        <div className="text-green-400">{formatMarketCap(position.targetMarketCap)}</div>
                      </div>
                    ) : (
                      <div className="text-neutral-500 text-xs">N/A</div>
                    )}
                  </td>

                  {/* Status */}
                  <td className="px-4 py-4 text-right">
                    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(position.status)}`}>
                      {getStatusIcon(position.status)}
                      {position.status}
                    </div>
                  </td>

                  {/* Created */}
                  <td className="px-4 py-4 text-sm text-neutral-400 text-right">
                    {formatDate(position.created_at)}
                  </td>

                  {/* Actions */}
                  <td className="px-4 py-4 text-right">
                    <div className="flex items-center justify-end space-x-2">
                      {position.status === 'pending' ? (
                        <button
                          onClick={() => cancelLimitOrder(position.limitOrderId)}
                          className="text-red-400 hover:text-red-300 transition-colors"
                          title="Cancel Order"
                        >
                          <X size={16} />
                        </button>
                      ) : (position.status === 'cancelled' || position.status === 'expired') ? (
                        <button
                          onClick={() => showDeleteConfirmation(position.limitOrderId)}
                          className="text-red-400 hover:text-red-300 transition-colors"
                          title="Delete Order"
                        >
                          <Trash2 size={16} />
                        </button>
                      ) : (
                        <div className="text-neutral-500">
                          <Settings size={16} />
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* 🚀 DELETE CONFIRMATION MODAL */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Limit Order"
        message="Are you sure you want to permanently delete this limit order? This action cannot be undone and the order will be removed from your history."
        confirmText="Delete Order"
        cancelText="Cancel"
        type="danger"
        isLoading={isDeleting}
      />
    </div>
  );
}