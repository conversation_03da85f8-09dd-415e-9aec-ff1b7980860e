import { useState } from 'react';
import { Info, FuelIcon, ShieldCheckIcon, Shield, ShieldOff } from 'lucide-react';
import { usePreset } from './PresetContext';

const Preset = () => {
  const {
    activePreset,
    setActivePreset,
    activeTab,
    setActiveTab,
    presetData,
    handleInputChange,
  } = usePreset();

  const mevOptions = ['Off', 'Red.', 'Sec.'];

  // New local state to track if content is open/closed
  const [contentOpen, setContentOpen] = useState(true);

  const current = activePreset ? presetData[activePreset][activeTab] : null;

  const onPresetClick = (preset: number) => {
    if (activePreset === preset) {
      // same preset clicked — toggle content open/close, keep preset active
      setContentOpen(!contentOpen);
    } else {
      // new preset clicked — set active preset and open content
      setActivePreset(preset);
      setContentOpen(true);
      // optionally reset activeTab if needed here
    }
  };

  

  const Oval = ({ 
    size = 16, 
    color = "currentColor", 
    strokeWidth = 2,
    rx = 10,
    ry = 6 
  }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      {/* Horizontal oval */}
      <ellipse 
        cx="12" 
        cy="12" 
        rx={rx} 
        ry={ry} 
        stroke={color} 
        strokeWidth={strokeWidth}
      />
      
      {/* Curved line through the middle - curved in opposite direction */}
      <path 
        d="M 4 12 Q 12 16 20 12" 
        stroke={color} 
        strokeWidth={strokeWidth}
        fill="none"
      />
    </svg>
  )

  return (
    <div className="text-white max-w-md mx-auto font-mono border border-gray-700">
      {/* Preset Tabs */}
      <div className="flex space-x-2 p-2 border-b border-gray-700">
        {[1, 2, 3].map(preset => (
          <button
            key={preset}
            onClick={() => onPresetClick(preset)}
            className={`flex-1 py-1.5 text-base rounded-md font-semibold transition-all ${
              activePreset === preset
                ? 'bg-[#232C45] text-[#6683ff]'
                : 'bg-transparent text-gray-400 hover:bg-gray-800'
            }`}
          >
            PRESET {preset}
          </button>
        ))}
      </div>

      {/* Content */}
      {activePreset && contentOpen && current && (
        <>
          {/* Buy/Sell Toggle */}
          <div className="flex text-sm p-2 border-b border-gray-700">
            <button
              className={`flex-1 py-2 rounded-md font-semibold ${
                activeTab === 'buy' ? 'bg-[#214638] text-[#14FFA2]' : 'bg-transparent text-gray-400'
              }`}
              onClick={() => setActiveTab('buy')}
            >
              Buy settings
            </button>
            <button
              className={`flex-1 py-2 rounded-md font-semibold ${
                activeTab === 'sell' ? 'bg-[#311D27] text-[#FF329B]' : 'bg-transparent text-gray-400'
              }`}
              onClick={() => setActiveTab('sell')}
            >
              Sell settings
            </button>
          </div>

          {/* Settings Grid */}
          <div className="grid grid-cols-3 gap-2 text-center text-sm p-4">
            {/* Slippage */}
            <div className="py-2 rounded-lg border border-gray-700 relative">
              <input
                type="number"
                value={current.slippage}
                onChange={e => handleInputChange('slippage', parseFloat(e.target.value))}
                className="text-center bg-transparent text-white text-base font-semibold w-full outline-none appearance-none"
              />
              <span className="absolute right-2 top-2 text-white text-base">%</span>
              <div className="text-gray-400 text-xs uppercase border-t border-gray-700 mt-2 flex items-center justify-center h-6">
                <span>⚡</span>&nbsp;SLIPPAGE
              </div>
            </div>

            {/* Priority */}
            <div className="py-2 rounded-lg border border-gray-700 relative">
              <input
                type="number"
                step="0.0001"
                value={current.priority}
                onChange={e => handleInputChange('priority', parseFloat(e.target.value))}
                className="text-center bg-transparent text-white text-base font-semibold w-full outline-none"
              />
              <div className="text-gray-400 text-xs uppercase border-t border-gray-700 mt-2 flex items-center justify-center h-6">
                <FuelIcon size={12} />&nbsp;PRIORITY
              </div>
            </div>

            {/* Bribe */}
            <div className="py-2 rounded-lg border border-gray-700 relative">
              <input
                type="number"
                step="0.00001"
                value={current.bribe}
                onChange={e => handleInputChange('bribe', parseFloat(e.target.value))}
                className="text-center bg-transparent text-white text-base font-semibold w-full outline-none"
              />
              <div className="text-gray-400 text-xs uppercase border-t border-gray-700 mt-2 flex items-center justify-center h-6">
                <Oval/>&nbsp;BRIBE
              </div>
            </div>
          </div>

          {/* Auto Fee Section */}
          <div className="flex items-center space-x-6 p-2 w-full">
            <label className="flex items-center space-x-2 cursor-pointer">
              <div
                className={`w-5 h-5 rounded border-2 ${
                  current.autoFee ? 'border-gray-300' : 'border-gray-600'
                } flex items-center justify-center transition`}
                onClick={() => handleInputChange('autoFee', !current.autoFee)}
              >
                {current.autoFee && <div className="w-2.5 h-2.5 bg-white rounded-sm" />}
              </div>
              <span className="text-white font-medium text-nowrap">Auto Fee</span>
            </label>

            <div
              className={`flex items-center px-4 py-2 rounded-full w-full transition border ${
                current.autoFee ? 'border-gray-400' : 'border-gray-700'
              }`}
            >
              <span className={`text-sm mr-2 text-nowrap ${current.autoFee ? 'text-gray-300' : 'text-gray-600'}`}>
                MAX FEE
              </span>
              <input
                type="number"
                disabled={!current.autoFee}
                value={current.maxFee}
                onChange={e => handleInputChange('maxFee', e.target.value)}
                className={`bg-transparent w-full outline-none text-sm ${
                  current.autoFee ? 'text-gray-300' : 'text-gray-600'
                }`}
              />
            </div>
          </div>

          {/* MEV Mode */}
          <div className="flex items-center justify-between px-4 py-2 w-full">
            {/* Label + Info */}
            <div className="flex items-center gap-2 text-sm font-medium text-white">
              <span>MEV Mode</span>
              <Info className="w-4 h-4 text-gray-400" />
            </div>

            {/* Button Group */}
            <div className="flex items-center gap-1 bg-[#111216] border border-[#2D2F3C] rounded-lg px-1 py-1">
              {mevOptions.map(option => {
                const isActive = current.mevMode === option;
                const iconClass = isActive ? 'text-[#4E6BFF]' : 'text-gray-400';
                const textClass = isActive ? 'text-[#4E6BFF]' : 'text-gray-400';

                return (
                  <button
                    key={option}
                    onClick={() => handleInputChange('mevMode', option)}
                    className={`flex items-center gap-1 px-3 py-1.5 text-sm rounded-md font-medium transition-all ${
                      isActive ? 'bg-[#1B1E2E]' : 'bg-transparent hover:bg-[#1a1b23]'
                    }`}
                  >
                    {option === 'Off' ? (
                      <ShieldOff size={14} className={iconClass} />
                    ) : option === 'Red.' ? (
                      <Shield size={14} className={iconClass} />
                    ) : (
                      <ShieldCheckIcon size={14} className={iconClass} />
                    )}
                    <span className={textClass}>{option}</span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* RPC Input */}
          <div className="p-4 pt-2">
            <div className="relative w-full">
              {/* Label inside the input */}
              <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-sm text-gray-400">
                RPC
              </span>

              {/* Input with left padding to avoid overlap */}
              <input
                type="text"
                value={current.rpc}
                onChange={e => handleInputChange('rpc', e.target.value)}
                placeholder="https://a...e.com"
                className="w-full text-sm bg-[#222222] border border-gray-700 text-gray-300 pl-14 pr-4 py-2 rounded-full outline-none focus:ring-2 focus:ring-gray-500"
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Preset;
