import React from 'react';
import {
  Users, ChefHat, Target, Ghost, Network,
  Flame, User, CandlestickChart, AlertTriangle, FileText
} from 'lucide-react';
import TokenInfoItem from './TokenInfoItem';
import { TokenData } from './types';

interface TokenInfoGridProps {
  data: TokenData;
}

const TokenInfoGrid: React.FC<TokenInfoGridProps> = ({ data }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        <TokenInfoItem icon={<Users size={18} />} value={data.topHolders} label="Top 10 H." />
        <TokenInfoItem icon={<ChefHat size={18} />} value={data.devHolders} label="Dev H." />
        <TokenInfoItem icon={<Target size={18} />} value={data.snipersHolders} label="Snipers H." />
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        <TokenInfoItem icon={<Ghost size={18} />} value={data.insiders} label="Insiders" />
        <TokenInfoItem icon={<Network size={18} />} value={data.bundlers} label="Bundlers" />
        <TokenInfoItem icon={<Flame size={18} />} value={data.lpBurned} label="LP Burned" />
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        <TokenInfoItem icon={<User size={18} />} value={data.holders} label="Holders" valueColor="text-white" />
        <TokenInfoItem icon={<CandlestickChart size={18} />} value={data.proTraders} label="Pro Traders" valueColor="text-white" />
        <TokenInfoItem icon={<AlertTriangle size={18} />} value={data.dexPaid} label="Dex Paid" valueColor="text-red-500" />
      </div>

      <div className="bg-[#1f1f22] px-3 py-2 rounded-lg text-xs flex items-center gap-2">
        <FileText size={16} className="text-gray-400 shrink-0" />
        <div className="text-gray-400 font-mono truncate">
          CA: {data.contractAddress}
        </div>
      </div>
    </div>
  );
};

export default TokenInfoGrid;
