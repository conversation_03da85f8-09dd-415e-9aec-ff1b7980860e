export interface TokenInfoItemProps {
    icon: React.ReactNode;
    value: string | number;
    label: string;
    valueColor?: string;
  }
  
  export interface TokenData {
    topHolders: string;
    devHolders: string;
    snipersHolders: string;
    insiders: string;
    bundlers: string;
    lpBurned: string;
    holders: number;
    proTraders: number;
    dexPaid: string;
    contractAddress: string;
  }