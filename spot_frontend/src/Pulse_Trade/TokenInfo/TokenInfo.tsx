import React, { useState } from 'react';
import { ChevronRight, ChevronDown } from 'lucide-react';
import TokenInfoGrid from './TokenInfoGrid';
import { TokenData } from './types';

const sampleData: TokenData = {
  topHolders: '0%',
  devHolders: '0%',
  snipersHolders: '0%',
  insiders: '0%',
  bundlers: '0%',
  lpBurned: '100%',
  holders: 1,
  proTraders: 0,
  dexPaid: 'Unpaid',
  contractAddress: '5dFSmLj5F8Vpuucm8FoKd...PU2N'
};

const TokenInfo: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="w-full   border border-zinc-700 text-sm text-white transition-all duration-300">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-3 py-2  hover:bg-[#2A2D30]  transition-colors"
      >
        <h2 className="text-lg font-medium tracking-wide">Token Info</h2>
        <div className="transition-transform duration-300">
          {isOpen ? <ChevronDown size={18} /> : <ChevronRight size={18} />}
        </div>
      </button>

      <div
        className={`overflow-hidden transition-all duration-500 ease-in-out ${
          isOpen ? 'max-h-[1500px] opacity-100 px-4 py-3' : 'max-h-0 opacity-0 px-4 py-0'
        }`}
      >
        {isOpen && <TokenInfoGrid data={sampleData} />}
      </div>
    </div>
  );
};

export default TokenInfo;
