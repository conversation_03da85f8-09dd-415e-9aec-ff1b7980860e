import React from 'react';
import { TokenInfoItemProps } from './types';

const TokenInfoItem: React.FC<TokenInfoItemProps> = ({
  icon,
  value,
  label,
  valueColor = 'text-green-400'
}) => {
  return (
    <div className="bg-[#1f1f22] p-3 rounded-md hover:bg-[#2a2a2d] transition-colors duration-200 text-center">
      <div className="flex justify-center items-center gap-1 mb-1">
        <div className="text-green-400">{icon}</div>
        <div className={`text-sm font-semibold ${valueColor}`}>{value}</div>
      </div>
      <div className="text-sm text-gray-400">{label}</div>
    </div>
  );
};

export default TokenInfoItem;
