import { useState, useEffect, useCallback } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { 
  limitOrderService, 
  LimitOrder, 
  CreateLimitOrderRequest, 
  LimitOrderFilters, 
  LimitOrderStats 
} from '../services/limitOrderService';

interface UseLimitOrdersReturn {
  // Data
  orders: LimitOrder[];
  stats: LimitOrderStats | null;
  
  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isCancelling: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  createOrder: (orderData: CreateLimitOrderRequest) => Promise<{ success: boolean; data?: LimitOrder; error?: string }>;
  cancelOrder: (orderId: string) => Promise<{ success: boolean; error?: string }>;
  deleteOrder: (orderId: string) => Promise<{ success: boolean; error?: string }>;
  refreshOrders: () => Promise<void>;
  refreshStats: () => Promise<void>;
  
  // Filtering
  filters: LimitOrderFilters;
  setFilters: (filters: LimitOrderFilters) => void;
  
  // Pagination
  hasMore: boolean;
  loadMore: () => Promise<void>;
}

export function useLimitOrders(initialFilters: LimitOrderFilters = {}): UseLimitOrdersReturn {
  const { authenticated, user } = usePrivy();
  
  // State
  const [orders, setOrders] = useState<LimitOrder[]>([]);
  const [stats, setStats] = useState<LimitOrderStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<LimitOrderFilters>({
    limit: 20,
    offset: 0,
    order_by: 'created_at',
    order_direction: 'desc',
    ...initialFilters
  });
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  // Get user ID
  const userId = user?.id;

  /**
   * Fetch orders from the API
   */
  const fetchOrders = useCallback(async (appendToExisting = false) => {
    if (!authenticated || !userId) {
      setOrders([]);
      setError('User not authenticated');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await limitOrderService.getLimitOrders(userId, filters);

      if (response.success && response.data) {
        if (appendToExisting) {
          setOrders(prev => [...prev, ...response.data!]);
        } else {
          setOrders(response.data);
        }
        
        setTotalCount(response.count || 0);
        
        // Check if there are more orders to load
        const currentCount = appendToExisting ? orders.length + response.data.length : response.data.length;
        setHasMore(currentCount < (response.count || 0));
      } else {
        setError(response.error || 'Failed to fetch orders');
        if (!appendToExisting) {
          setOrders([]);
        }
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      setError('Failed to fetch orders');
      if (!appendToExisting) {
        setOrders([]);
      }
    } finally {
      setIsLoading(false);
    }
  }, [authenticated, userId, filters, orders.length]);

  /**
   * Fetch order statistics
   */
  const fetchStats = useCallback(async () => {
    if (!authenticated || !userId) {
      setStats(null);
      return;
    }

    try {
      const response = await limitOrderService.getLimitOrderStats(userId);

      if (response.success && response.data) {
        setStats(response.data);
      } else {
        console.error('Failed to fetch stats:', response.error);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  }, [authenticated, userId]);

  /**
   * Create a new limit order
   */
  const createOrder = useCallback(async (orderData: CreateLimitOrderRequest) => {
    if (!authenticated || !userId) {
      return { success: false, error: 'User not authenticated' };
    }

    // Validate order data
    const validation = limitOrderService.validateOrderData(orderData);
    if (!validation.isValid) {
      return { success: false, error: validation.errors.join(', ') };
    }

    setIsCreating(true);
    setError(null);

    try {
      const response = await limitOrderService.createLimitOrder(orderData);

      if (response.success && response.data) {
        // Add the new order to the beginning of the list
        setOrders(prev => [response.data!, ...prev]);
        
        // Refresh stats
        await fetchStats();
        
        return { success: true, data: response.data };
      } else {
        const errorMessage = response.error || 'Failed to create order';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (error) {
      console.error('Error creating order:', error);
      const errorMessage = 'Failed to create order';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsCreating(false);
    }
  }, [authenticated, userId, fetchStats]);

  /**
   * Cancel a limit order
   */
  const cancelOrder = useCallback(async (orderId: string) => {
    if (!authenticated || !userId) {
      return { success: false, error: 'User not authenticated' };
    }

    setIsCancelling(true);
    setError(null);

    try {
      const response = await limitOrderService.cancelLimitOrder(userId, orderId);

      if (response.success && response.data) {
        // Update the order in the list
        setOrders(prev => 
          prev.map(order => 
            order.id === orderId 
              ? { ...order, status: 'cancelled' as const, updated_at: response.data!.updated_at }
              : order
          )
        );
        
        // Refresh stats
        await fetchStats();
        
        return { success: true };
      } else {
        const errorMessage = response.error || 'Failed to cancel order';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (error) {
      console.error('Error cancelling order:', error);
      const errorMessage = 'Failed to cancel order';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsCancelling(false);
    }
  }, [authenticated, userId, fetchStats]);

  /**
   * Delete a limit order (only cancelled/expired orders)
   */
  const deleteOrder = useCallback(async (orderId: string) => {
    if (!authenticated || !userId) {
      return { success: false, error: 'User not authenticated' };
    }

    setIsCancelling(true); // Reuse the same loading state
    setError(null);

    try {
      const response = await limitOrderService.deleteLimitOrder(userId, orderId);

      if (response.success) {
        // Remove the order from the list
        setOrders(prev => prev.filter(order => order.id !== orderId));

        // Refresh stats
        await fetchStats();

        return { success: true };
      } else {
        const errorMessage = response.error || 'Failed to delete order';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }
    } catch (error) {
      console.error('Error deleting order:', error);
      const errorMessage = 'Failed to delete order';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsCancelling(false);
    }
  }, [authenticated, userId, fetchStats]);

  /**
   * Refresh orders
   */
  const refreshOrders = useCallback(async () => {
    await fetchOrders(false);
  }, [fetchOrders]);

  /**
   * Refresh stats
   */
  const refreshStats = useCallback(async () => {
    await fetchStats();
  }, [fetchStats]);

  /**
   * Load more orders (pagination)
   */
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoading) return;

    const newFilters = {
      ...filters,
      offset: orders.length
    };

    setIsLoading(true);

    try {
      const response = await limitOrderService.getLimitOrders(userId!, newFilters);

      if (response.success && response.data) {
        setOrders(prev => [...prev, ...response.data!]);
        
        // Check if there are more orders to load
        const newCount = orders.length + response.data.length;
        setHasMore(newCount < (response.count || 0));
      }
    } catch (error) {
      console.error('Error loading more orders:', error);
    } finally {
      setIsLoading(false);
    }
  }, [hasMore, isLoading, filters, orders.length, userId]);

  /**
   * Update filters and reset pagination
   */
  const updateFilters = useCallback((newFilters: LimitOrderFilters) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      offset: 0 // Reset pagination when filters change
    }));
    setOrders([]); // Clear existing orders
    setHasMore(true); // Reset pagination state
  }, []);

  // Fetch orders when filters change or user authenticates
  useEffect(() => {
    if (authenticated && userId) {
      fetchOrders(false);
      fetchStats();
    } else {
      setOrders([]);
      setStats(null);
      setError(null);
    }
  }, [authenticated, userId, filters]);

  // Auto-refresh orders every 30 seconds for pending orders
  useEffect(() => {
    if (!authenticated || !userId) return;

    const interval = setInterval(() => {
      // Only refresh if we have pending orders
      const hasPendingOrders = orders.some(order => order.status === 'pending');
      if (hasPendingOrders) {
        fetchOrders(false);
        fetchStats();
      }
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [authenticated, userId, orders, fetchOrders, fetchStats]);

  return {
    // Data
    orders,
    stats,
    
    // Loading states
    isLoading,
    isCreating,
    isCancelling,
    
    // Error states
    error,
    
    // Actions
    createOrder,
    cancelOrder,
    deleteOrder,
    refreshOrders,
    refreshStats,
    
    // Filtering
    filters,
    setFilters: updateFilters,
    
    // Pagination
    hasMore,
    loadMore
  };
}
