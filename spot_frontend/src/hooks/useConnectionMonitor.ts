import { useEffect, useState } from 'react';
import { globalTradeWebSocketService } from '@/services/globalTradeWebSocketService';

interface ConnectionMonitorConfig {
  enabled?: boolean;
  logInterval?: number;
  alertOnLeaks?: boolean;
  autoCleanupOnLeaks?: boolean;
}

interface ConnectionMonitorData {
  stats: {
    isConnected: boolean;
    poolCount: number;
    subscriberCount: number;
    callbackCount: number;
    connectionStatusCallbackCount: number;
    reconnectAttempts: number;
  };
  leaks: {
    hasLeaks: boolean;
    issues: string[];
  };
  lastUpdate: number;
}

/**
 * Development hook for monitoring WebSocket connections
 * Provides real-time connection statistics and leak detection
 * Should only be used during development for debugging purposes
 */
export const useConnectionMonitor = (config: ConnectionMonitorConfig = {}) => {
  const {
    enabled = process.env.NODE_ENV === 'development',
    logInterval = 10000, // 10 seconds
    alertOnLeaks = true,
    autoCleanupOnLeaks = false
  } = config;

  const [monitorData, setMonitorData] = useState<ConnectionMonitorData>({
    stats: {
      isConnected: false,
      poolCount: 0,
      subscriberCount: 0,
      callbackCount: 0,
      connectionStatusCallbackCount: 0,
      reconnectAttempts: 0
    },
    leaks: {
      hasLeaks: false,
      issues: []
    },
    lastUpdate: Date.now()
  });

  useEffect(() => {
    if (!enabled) {
      return;
    }

    console.log('🔍 [CONNECTION MONITOR] Starting connection monitoring');

    const updateMonitorData = () => {
      const stats = globalTradeWebSocketService.getConnectionStats();
      const leaks = globalTradeWebSocketService.detectLeaks();

      setMonitorData({
        stats,
        leaks,
        lastUpdate: Date.now()
      });

      // Log periodic status
      console.log('📊 [CONNECTION MONITOR] Status:', {
        connected: stats.isConnected,
        pools: stats.poolCount,
        subscribers: stats.subscriberCount,
        callbacks: stats.callbackCount,
        hasLeaks: leaks.hasLeaks,
        issues: leaks.issues
      });

      // Alert on leaks if enabled
      if (alertOnLeaks && leaks.hasLeaks) {
        console.warn('⚠️ [CONNECTION MONITOR] Connection leaks detected:', leaks.issues);
        
        // Auto cleanup if enabled
        if (autoCleanupOnLeaks) {
          console.log('🧹 [CONNECTION MONITOR] Auto-cleaning up leaked connections');
          globalTradeWebSocketService.forceCleanup();
        }
      }

      // Detect multiple connections (should only be 1 with global pooling)
      if (stats.isConnected && stats.poolCount > 0) {
        // This is expected - one connection with multiple pool subscriptions
        console.log('✅ [CONNECTION MONITOR] Normal operation: 1 connection with multiple pools');
      } else if (stats.isConnected && stats.poolCount === 0) {
        console.warn('⚠️ [CONNECTION MONITOR] Connected but no pool subscriptions');
      } else if (!stats.isConnected && stats.poolCount > 0) {
        console.warn('⚠️ [CONNECTION MONITOR] Pool subscriptions exist but not connected');
      }
    };

    // Initial update
    updateMonitorData();

    // Set up periodic monitoring
    const interval = setInterval(updateMonitorData, logInterval);

    // Cleanup
    return () => {
      clearInterval(interval);
      console.log('🔍 [CONNECTION MONITOR] Stopping connection monitoring');
    };
  }, [enabled, logInterval, alertOnLeaks, autoCleanupOnLeaks]);

  // Utility functions for manual monitoring
  const forceRefresh = () => {
    const stats = globalTradeWebSocketService.getConnectionStats();
    const leaks = globalTradeWebSocketService.detectLeaks();
    
    setMonitorData({
      stats,
      leaks,
      lastUpdate: Date.now()
    });

    globalTradeWebSocketService.logConnectionState('Manual refresh from hook');
    return { stats, leaks };
  };

  const forceCleanup = () => {
    globalTradeWebSocketService.forceCleanup();
    return forceRefresh();
  };

  const forceReconnect = async () => {
    await globalTradeWebSocketService.reconnect();
    return forceRefresh();
  };

  return {
    ...monitorData,
    enabled,
    forceRefresh,
    forceCleanup,
    forceReconnect
  };
};

/**
 * Simple hook to log connection state changes
 * Useful for debugging connection lifecycle
 */
export const useConnectionLogger = (componentName: string) => {
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    console.log(`🔍 [${componentName}] Component mounted - checking connection state`);
    globalTradeWebSocketService.logConnectionState(`${componentName} mount`);

    return () => {
      console.log(`🔍 [${componentName}] Component unmounting - checking connection state`);
      globalTradeWebSocketService.logConnectionState(`${componentName} unmount`);
      
      // Check for leaks after component unmount
      setTimeout(() => {
        const leaks = globalTradeWebSocketService.detectLeaks();
        if (leaks.hasLeaks) {
          console.warn(`⚠️ [${componentName}] Potential leaks after unmount:`, leaks.issues);
        }
      }, 100);
    };
  }, [componentName]);
};

/**
 * Hook to monitor browser WebSocket connections
 * Checks for multiple WebSocket connections in the browser
 */
export const useBrowserWebSocketMonitor = () => {
  const [webSocketCount, setWebSocketCount] = useState(0);

  useEffect(() => {
    if (process.env.NODE_ENV !== 'development' || typeof window === 'undefined') {
      return;
    }

    // Override WebSocket constructor to count connections
    const originalWebSocket = window.WebSocket;
    let activeConnections = 0;

    window.WebSocket = class extends originalWebSocket {
      constructor(url: string | URL, protocols?: string | string[]) {
        super(url, protocols);
        activeConnections++;
        setWebSocketCount(activeConnections);
        
        console.log(`🔌 [BROWSER MONITOR] New WebSocket connection created: ${url}`);
        console.log(`🔌 [BROWSER MONITOR] Total active connections: ${activeConnections}`);

        this.addEventListener('close', () => {
          activeConnections--;
          setWebSocketCount(activeConnections);
          console.log(`🔌 [BROWSER MONITOR] WebSocket connection closed: ${url}`);
          console.log(`🔌 [BROWSER MONITOR] Total active connections: ${activeConnections}`);
        });
      }
    };

    return () => {
      // Restore original WebSocket
      window.WebSocket = originalWebSocket;
    };
  }, []);

  return {
    webSocketCount,
    isMultipleConnections: webSocketCount > 1
  };
};

export default useConnectionMonitor;
