import { useState, useEffect } from 'react';
import { homeAPI } from '@/utils/api';

export interface PulseTokenInfo {
  id: string;
  name: string;
  address: string;
  symbol: string;
  bonding_percent?: number;
  volume: number;
  exchange_name: string;
  pool_address: string;
  price_change_24h: any;
  bonding: number;
  liquidity: number;

  price: number;
  market_cap: number;
  supply: number;
  exchange_logo: string;
  imageUrl: string;
  network: string;
}

/**
 * Unified function to normalize token data to PulseTokenInfo format
 * Handles different property name variations from different sources
 */
const normalizeTokenData = (token: any): PulseTokenInfo => {
  // Determine the token address - prefer address field, fallback to id
  const tokenAddress = token.address || token.id || '';

  return {
    // ID and Address handling - ensure both fields contain the same token address value
    // This ensures consistent identification across the application
    id: tokenAddress,
    address: tokenAddress,

    // Basic token info
    name: token.name || '',
    symbol: token.symbol || '',
    network: token.network || 'solana',

    // Market data - handle different property name variations
    bonding_percent: token.bonding_percent || token.bonding || 0,
    volume: token.volume || token.total_volume || 0,
    price: token.price || token.current_price || 0,
    market_cap: token.market_cap || token.marketCap || 0,
    liquidity: token.liquidity || 0,
    supply: token.supply || 0,
bonding: token.bonding ||  token.bonding_percent || 0,
    // Exchange and pool info
    exchange_name: token.exchange_name || '',
    exchange_logo: token.exchange_logo || '',
    pool_address: token.pool_address || '',

    // Price change and image
    price_change_24h: token.price_change_24h || 0,
    imageUrl: token.imageUrl || token.image || '',
  };
};

/**
 * Migrates existing pulseTokens in localStorage to ensure they have complete data structure
 * This function normalizes any existing tokens that might be missing fields
 */
const migratePulseTokensData = (): PulseTokenInfo[] => {
  try {
    const stored = localStorage.getItem("pulseTokens");
    if (!stored) return [];

    const existingTokens = JSON.parse(stored);
    if (!Array.isArray(existingTokens)) return [];

    // Normalize all existing tokens to ensure complete data structure
    const migratedTokens = existingTokens.map((token: any) => normalizeTokenData(token));

    // Save the migrated data back to localStorage
    localStorage.setItem("pulseTokens", JSON.stringify(migratedTokens));

    console.log(`Migrated ${migratedTokens.length} pulse tokens to complete data structure`);
    return migratedTokens;
  } catch (error) {
    console.error("Error migrating pulse tokens data:", error);
    return [];
  }
};

/**
 * Migrates the activePulseToken in localStorage to ensure it has complete data structure
 * Also fixes empty address fields by ensuring both id and address contain the same value
 */
const migrateActivePulseToken = (): void => {
  try {
    const stored = localStorage.getItem("activePulseToken");
    if (!stored) return;

    const existingToken = JSON.parse(stored);
    if (!existingToken || typeof existingToken !== 'object') return;

    // Normalize the active token to ensure complete data structure and fix address field
    const migratedToken = normalizeTokenData(existingToken);

    // Check if address was fixed
    if (existingToken.address !== migratedToken.address) {
      console.log(`🔧 Fixed activePulseToken address: "${existingToken.address}" → "${migratedToken.address}"`);
    }

    // Save the migrated data back to localStorage
    localStorage.setItem("activePulseToken", JSON.stringify(migratedToken));

    console.log("Migrated activePulseToken to complete data structure");
  } catch (error) {
    console.error("Error migrating activePulseToken data:", error);
  }
};

/**
 * Custom hook for managing pulse token storage
 */
export const usePulseData = (onAddToken?: (token: PulseTokenInfo) => void) => {
  const [pulseTokens, setPulseTokens] = useState<PulseTokenInfo[]>(() => {
    // Migrate existing data to ensure complete structure
    migrateActivePulseToken(); // Also migrate activePulseToken
    const migratedTokens = migratePulseTokensData();

    // Fix any existing tokens with empty address fields
    // This ensures backward compatibility with existing data
    if (migratedTokens.length > 0) {
      const fixedTokens = migratedTokens.map(token => normalizeTokenData(token));
      localStorage.setItem("pulseTokens", JSON.stringify(fixedTokens));
      console.log("🔧 Auto-fixed existing token addresses during initialization");
      return fixedTokens;
    }

    return migratedTokens;
  });

  useEffect(() => {
    // Check if localStorage is already in sync to avoid unnecessary writes
    const currentStored = localStorage.getItem("pulseTokens");
    const currentStoredParsed = currentStored ? JSON.parse(currentStored) : [];
    const isInSync = JSON.stringify(currentStoredParsed) === JSON.stringify(pulseTokens);

    if (!isInSync) {
      // Only log when tokens are actually added/removed to reduce noise
      if (pulseTokens.length > 0) {
        console.log("usePulseData: Syncing React state to localStorage:", pulseTokens.length, "tokens");
        console.log("usePulseData: Token symbols:", pulseTokens.map(t => t.symbol).join(', '));
      }
      localStorage.setItem("pulseTokens", JSON.stringify(pulseTokens));
    }

    const event = new CustomEvent('pulseDataChanged', {
      detail: { pulseTokens }
    });
    window.dispatchEvent(event);
  }, [pulseTokens]);

  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'pulseTokens' && e.newValue !== null) {
        try {
          const newTokens = JSON.parse(e.newValue);

          setPulseTokens(newTokens);
        } catch (error) {
          console.error("Error parsing pulse tokens from storage event:", error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  /**
   * @deprecated Use setActiveToken instead. This function is kept for backward compatibility.
   * Sets a token as active and adds it to the wishlist (same behavior as setActiveToken)
   */
  const handlePulseTokenAdd = (e: React.MouseEvent | null, token: any) => {
    // Delegate to setActiveToken which now handles both operations
    setActiveToken(e, token);
  };
  /**
   * Adds a token to the wishlist only (does NOT set as active token)
   * Use this when you want to add a token to favorites without changing the current active token
   * For setting active token + adding to wishlist, use setActiveToken instead
   *
   * @param e - Mouse event (optional, used to stop propagation)
   * @param token - Token data to add to wishlist
   */

  /**
   * Sets a token as the active token and automatically adds it to the wishlist
   * This function handles both operations to ensure consistency when selecting tokens from the Pulse dashboard
   *
   * @param e - Mouse event (optional, used to stop propagation)
   * @param token - Token data to set as active and add to wishlist
   */
  const setActiveToken = (e: React.MouseEvent | null, token: any) => {
    if (e) e.stopPropagation();

    // Use unified normalization function for consistent data structure
    const normalizedToken = normalizeTokenData(token);

    // Set as active token
    localStorage.setItem("activePulseToken", JSON.stringify(normalizedToken));

    // Also add to wishlist if not already present (read from localStorage for accuracy)
    const currentStoredTokens = JSON.parse(localStorage.getItem("pulseTokens") || "[]");
    const isAlreadyPresent = currentStoredTokens.some((t: any) => t.id === normalizedToken.id);
    if (!isAlreadyPresent) {
      // Update localStorage immediately and then sync React state
      const updatedTokens = [...currentStoredTokens, normalizedToken];
      localStorage.setItem("pulseTokens", JSON.stringify(updatedTokens));
      setPulseTokens(updatedTokens);

      if (onAddToken) onAddToken(normalizedToken);
    }

    // Dispatch event for other components to react
    const event = new CustomEvent('pulseDataChanged', {
      detail: { activePulseToken: normalizedToken },
    });
    window.dispatchEvent(event);
  }

  /**
   * Enhanced function to set active token from wishlist selection
   * Ensures the selected token from wishlist becomes the active token with complete data
   */
  const setActiveTokenFromWishlist = (token: PulseTokenInfo) => {
    // Token from wishlist should already be normalized, but ensure consistency
    const normalizedToken = normalizeTokenData(token);
    localStorage.setItem("activePulseToken", JSON.stringify(normalizedToken));

    // Dispatch event for other components to react
    const event = new CustomEvent('pulseDataChanged', {
      detail: { activePulseToken: normalizedToken },
    });
    window.dispatchEvent(event);
  };

  const isPulseTokenPresent = (token: any) => {
    const tokenId = token.address || token.id;
    return pulseTokens.some(t => t.id === tokenId);
  };

  const removePulseToken = (id: string) => {
    setPulseTokens(prev => prev.filter(t => t.id !== id));
  };

  const clearPulseTokens = () => {
    setPulseTokens([]);
  };

  /**
   * Force refresh all tokens in pulseTokens to ensure they have complete data structure
   * Useful for fixing any remaining inconsistencies
   */
  const refreshAllTokensData = () => {
    const refreshedTokens = pulseTokens.map(token => normalizeTokenData(token));
    setPulseTokens(refreshedTokens);
    localStorage.setItem("pulseTokens", JSON.stringify(refreshedTokens));

    // Also refresh activePulseToken if it exists
    migrateActivePulseToken();

    console.log(`Refreshed ${refreshedTokens.length} tokens with complete data structure`);
    return refreshedTokens;
  };

  /**
   * Fix existing tokens in localStorage that have empty address fields
   * This function ensures both id and address fields contain the same token address value
   */
  const fixExistingTokenAddresses = () => {
    try {
      console.log("🔧 Starting fix for existing token addresses...");

      // Fix pulseTokens
      const storedTokens = localStorage.getItem("pulseTokens");
      if (storedTokens) {
        const tokens = JSON.parse(storedTokens);
        const fixedTokens = tokens.map((token: any) => {
          const originalToken = { ...token };
          const normalizedToken = normalizeTokenData(token);

          if (originalToken.address !== normalizedToken.address) {
            console.log(`🔧 Fixed token ${token.symbol}: address "${originalToken.address}" → "${normalizedToken.address}"`);
          }

          return normalizedToken;
        });

        localStorage.setItem("pulseTokens", JSON.stringify(fixedTokens));
        setPulseTokens(fixedTokens);
        console.log(`✅ Fixed ${fixedTokens.length} tokens in pulseTokens`);
      }

      // Fix activePulseToken
      const storedActiveToken = localStorage.getItem("activePulseToken");
      if (storedActiveToken) {
        const activeToken = JSON.parse(storedActiveToken);
        const originalAddress = activeToken.address;
        const normalizedToken = normalizeTokenData(activeToken);

        if (originalAddress !== normalizedToken.address) {
          console.log(`🔧 Fixed activePulseToken ${activeToken.symbol}: address "${originalAddress}" → "${normalizedToken.address}"`);
          // localStorage.setItem("activePulseToken", JSON.stringify(normalizedToken));
        }
      }

      console.log("✅ Finished fixing existing token addresses");
      return true;
    } catch (error) {
      console.error("❌ Error fixing existing token addresses:", error);
      return false;
    }
  };

  /**
   * Fetches token data by address and automatically adds it to both activePulseToken and pulseTokens
   * This function is used when navigating directly to a trade page URL
   *
   * @param address - Token address to fetch data for
   * @param network - Network (defaults to 'solana')
   * @returns Promise<boolean> - Returns true if successful, false otherwise
   */
  const fetchAndAddTokenByAddress = async (address: string, network: string = 'solana'): Promise<boolean> => {
    try {
      console.log(`🔄 FETCH AND ADD TOKEN - Starting process`);
      console.log(`🔄 Address: ${address}, Network: ${network}`);

      // Check if token is already in wishlist to avoid unnecessary API calls
      // Read from localStorage to get the most current state
      const storedTokensForCheck = JSON.parse(localStorage.getItem("pulseTokens") || "[]");
      console.log(`🔄 Current stored tokens count: ${storedTokensForCheck.length}`);

      const existingToken = storedTokensForCheck.find((t: any) =>
        t.address === address ||
        t.id === address ||
        (t.address && t.address.toLowerCase() === address.toLowerCase())
      );

      if (existingToken) {
        console.log('✅ Token already exists in wishlist, setting as active');
        console.log('✅ Existing token data:', existingToken);
        // localStorage.setItem("activePulseToken", JSON.stringify(existingToken));

        // Dispatch event for other components to react
        const event = new CustomEvent('pulseDataChanged', {
          detail: { activePulseToken: existingToken },
        });
        window.dispatchEvent(event);
        console.log('✅ Event dispatched for existing token');
        return true;
      }

      console.log('🔄 Token not found in wishlist, fetching from API...');

      // Fetch token data from API
      console.log('🔄 Calling homeAPI.getMarketData...');
      const response = await homeAPI.getMarketData(network, address);

      console.log('🔄 API Response received:', response);
      console.log('🔄 Response structure:', {
        hasResponse: !!response,
        hasData: !!(response?.data),
        hasDataData: !!(response?.data?.data),
        responseKeys: response ? Object.keys(response) : [],
        dataKeys: response?.data ? Object.keys(response.data) : []
      });

      if (!response || !response.data || !response.data.data) {
        console.error('❌ No token data received from API');
        console.error('❌ Response structure issue:', {
          response: !!response,
          responseData: !!response?.data,
          responseDataData: !!response?.data?.data
        });
        return false;
      }

      const tokenData = response.data.data;
      console.log('✅ Received token data from API:', tokenData);
      console.log('✅ Token data keys:', Object.keys(tokenData));

      // Transform API response to PulseTokenInfo format
      // Use address as primary ID for consistency, fallback to API id
      const normalizedToken = normalizeTokenData({
        id: address, // Use address as primary identifier for consistency
        name: tokenData.name || 'Unknown Token',
        address: address,
        symbol: tokenData.symbol || 'UNKNOWN',
        network: network,
        bonding_percent: tokenData.bonding_percent || tokenData.bonding || 0,
        volume: tokenData.volume || tokenData.total_volume || 0,
        price: tokenData.price || tokenData.current_price || 0,
        market_cap: tokenData.market_cap || tokenData.marketCap || 0,
        liquidity: tokenData.liquidity || 0,
        supply: tokenData.supply || tokenData.total_supply || 0,
        exchange_name: tokenData.exchange_name || 'Unknown',
        exchange_logo: tokenData.exchange_logo || '',
        pool_address: tokenData.pool_address || '',
        price_change_24h: tokenData.price_change_24h || tokenData.price_change_percentage_24h || 0,
        imageUrl: tokenData.imageUrl || tokenData.logo || '',
      });

      console.log('Normalized token for addition:', normalizedToken);

      // Set as active token
      // localStorage.setItem("activePulseToken", JSON.stringify(normalizedToken));

      // Add to wishlist - read current state from localStorage to ensure accuracy
      const currentStoredTokens = JSON.parse(localStorage.getItem("pulseTokens") || "[]");
      console.log(`Current stored tokens count: ${currentStoredTokens.length}`);

      const isAlreadyPresent = currentStoredTokens.some((t: any) => {
        const idMatch = t.id === normalizedToken.id;
        const addressMatch = t.address === normalizedToken.address;
        const caseInsensitiveMatch = t.address && normalizedToken.address &&
          t.address.toLowerCase() === normalizedToken.address.toLowerCase();

        console.log(`Checking existing token ${t.symbol}: ID match=${idMatch}, Address match=${addressMatch}, Case match=${caseInsensitiveMatch}`);
        return idMatch || addressMatch || caseInsensitiveMatch;
      });

      console.log(`Token already present check: ${isAlreadyPresent}`);

      if (!isAlreadyPresent) {
        // Update localStorage immediately to ensure data persistence
        const updatedTokens = [...currentStoredTokens, normalizedToken];
        localStorage.setItem("pulseTokens", JSON.stringify(updatedTokens));
        console.log(`✅ Token ${normalizedToken.symbol} (${normalizedToken.address}) added to localStorage`);
        console.log(`✅ Updated wishlist now contains ${updatedTokens.length} tokens`);

        // Update React state to keep UI in sync
        setPulseTokens(updatedTokens);
        console.log(`✅ React state updated to match localStorage`);

        // Trigger onAddToken callback if provided
        if (onAddToken) onAddToken(normalizedToken);
      } else {
        console.log(`⚠️ Token ${normalizedToken.symbol} already exists in wishlist, skipping addition`);
      }

      // Dispatch event for other components to react
      const event = new CustomEvent('pulseDataChanged', {
        detail: { activePulseToken: normalizedToken },
      });
      window.dispatchEvent(event);

      console.log(`Successfully fetched and processed token: ${normalizedToken.name} (${normalizedToken.symbol})`);
      return true;

    } catch (error) {
      console.error('Error fetching token data by address:', error);
      return false;
    }
  };

  /**
   * Utility function to validate data consistency between activePulseToken and pulseTokens
   * Useful for debugging and ensuring data integrity
   */
  const validateDataConsistency = () => {
    try {
      const activeToken = JSON.parse(localStorage.getItem("activePulseToken") || "{}");
      const wishlistTokens = JSON.parse(localStorage.getItem("pulseTokens") || "[]");

      const activeTokenKeys = Object.keys(activeToken).sort();
      const wishlistTokenKeys = wishlistTokens.length > 0 ? Object.keys(wishlistTokens[0]).sort() : [];

      const report = {
        activeTokenFields: activeTokenKeys,
        wishlistTokenFields: wishlistTokenKeys,
        fieldsMatch: JSON.stringify(activeTokenKeys) === JSON.stringify(wishlistTokenKeys),
        missingInWishlist: activeTokenKeys.filter(key => !wishlistTokenKeys.includes(key)),
        missingInActive: wishlistTokenKeys.filter(key => !activeTokenKeys.includes(key)),
        activeTokenHasNetwork: !!activeToken.network,
        wishlistTokensHaveNetwork: wishlistTokens.length > 0 ? !!wishlistTokens[0]?.network : false,
      };

      console.log("Data Consistency Report:", report);
      return report;
    } catch (error) {
      console.error("Error validating data consistency:", error);
      return null;
    }
  };

  return {
    pulseTokens,
    setPulseTokens,
    handlePulseTokenAdd,
    isPulseTokenPresent,
    removePulseToken,
    clearPulseTokens,
    setActiveToken,
    setActiveTokenFromWishlist,

    normalizeTokenData, // Export for use in other components
    validateDataConsistency, // Export for debugging
    refreshAllTokensData, // Export for manual data refresh
    fetchAndAddTokenByAddress, // Export for URL-based token loading
    fixExistingTokenAddresses, // Export for manual address fixing
  };
};

export default usePulseData;
