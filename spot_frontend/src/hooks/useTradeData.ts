import { useState, useEffect, useRef, useCallback } from 'react';

interface Trade {
  id?: string;
  timestamp?: number;
  date?: number; // Mobula API might use 'date' field
  type: 'buy' | 'sell';
  token_price?: number;
  token_amount?: number;
  token_amount_vs?: number; // SOL amount in trades
  token_amount_usd?: number;
  hash?: string;
  transaction_hash?: string;
  tx_hash?: string;
  sender?: string;
  pairData?: {
    price?: number;
    token0?: {
      address?: string;
      price?: number;
      marketCap?: number;
      totalSupply?: number;
      symbol?: string;
    };
    token1?: {
      address?: string;
      price?: number;
      marketCap?: number;
      totalSupply?: number;
      symbol?: string;
    };
    liquidity?: number;
  };
  // Additional fields from Mobula API
  marketCap?: number;
  amount?: number;
  usd_amount?: number;
  value_usd?: number;
  price?: number;
  symbol?: string;
  blockchain?: string;
  chain?: string;
}

interface FormattedTrade {
  id: string;
  timestamp: number;
  type: 'buy' | 'sell';
  amount: string;
  usdAmount: string;
  mc: string;
  price: string;
  trader: string;
  age: string;
  txHash: string;
  marketCap: number;
  tokenAmount: number; // SOL amount (token_amount_vs) - used for side panel
  tokenAmountUsd: number;
  actualTokenAmount: number; // Actual token quantity (token_amount) - used for bottom table
}

export const useTradeData = (poolAddress?: string | null) => {
  const [trades, setTrades] = useState<FormattedTrade[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<number | null>(null);
  const [latestRawTrade, setLatestRawTrade] = useState<Trade | null>(null);

  const wsRef = useRef<WebSocket | null>(null);
  const processedTradeIds = useRef(new Set<string>());
  const maxTrades = 50;

  // SOL address constant
  const SOL_ADDRESS = 'So11111111111111111111111111111111111111112';

  // Helper function to extract and normalize timestamp from trade data
  const getTradeTimestamp = useCallback((trade: Trade): number => {
    // Debug logging to see all timestamp-related fields
    console.log('🕐 Timestamp debug:', {
      timestamp: trade.timestamp,
      date: trade.date,
      type: typeof trade.timestamp,
      dateType: typeof trade.date,
      rawTrade: trade
    });

    let timestamp = 0;

    // Try different timestamp fields that Mobula API might use
    if (trade.date && typeof trade.date === 'number') {
      timestamp = trade.date;
    } else if (trade.timestamp && typeof trade.timestamp === 'number') {
      timestamp = trade.timestamp;
    } else {
      // Fallback to current time if no valid timestamp found
      console.warn('⚠️ No valid timestamp found in trade data, using current time');
      timestamp = Date.now();
    }

    // Ensure timestamp is in milliseconds (Mobula might send seconds)
    // If timestamp is less than year 2000 in milliseconds, it's probably in seconds
    if (timestamp < 946684800000) { // Jan 1, 2000 in milliseconds
      timestamp = timestamp * 1000;
    }

    console.log('✅ Final timestamp:', timestamp, 'Date:', new Date(timestamp).toISOString());
    return timestamp;
  }, []);

  // Helper function to determine which token is SOL and which is the actual token
  const getTokenPair = useCallback((trade: Trade) => {
    if (!trade.pairData?.token0 || !trade.pairData?.token1) {
      return { solToken: null, actualToken: null };
    }

    const token0 = trade.pairData.token0;
    const token1 = trade.pairData.token1;

    // Debug logging to see token addresses
    console.log('🔍 Token pair analysis:', {
      token0: { address: token0.address, symbol: token0.symbol },
      token1: { address: token1.address, symbol: token1.symbol },
      SOL_ADDRESS
    });

    // Check if token1 has SOL address
    if (token1.address === SOL_ADDRESS) {
      console.log('✅ Using token0 as actual token, token1 as SOL');
      return { solToken: token1, actualToken: token0 };
    }
    // Check if token0 has SOL address
    else if (token0.address === SOL_ADDRESS) {
      console.log('✅ Using token1 as actual token, token0 as SOL');
      return { solToken: token0, actualToken: token1 };
    }
    // Default fallback - assume token1 is the actual token if no SOL address found
    else {
      console.log('⚠️ No SOL address found, using token1 as actual token by default');
      return { solToken: token0, actualToken: token1 };
    }
  }, []);

  // Get effective pool address (same as trade_example.md pattern)
  const getEffectivePoolAddress = useCallback(() => {
    if (poolAddress) return poolAddress;

    try {
      const activePulseToken = localStorage.getItem('activePulseToken');
      if (activePulseToken) {
        const tokenData = JSON.parse(activePulseToken);
        return tokenData.pool_address || tokenData.pool_address;
      }
    } catch (error) {
      console.error('Error getting pool address from localStorage:', error);
    }
    return '9zG56JRfnJRru7k3kmY4gajvZQVwfWv2vMnbbccA4c6p'; // Default pool address
  }, [poolAddress]);

  // Generate unique trade ID (exact pattern from trade_example.md)
  const generateTradeId = useCallback((trade: Trade): string => {
    if (trade.id) return `id-${trade.id}`;
    if (trade.hash || trade.transaction_hash || trade.tx_hash) {
      const hash = trade.hash || trade.transaction_hash || trade.tx_hash;
      return `hash-${hash}`;
    }

    const timestamp = trade.timestamp || Date.now();
    const price = trade.price || trade.token_price || 0;
    const amount = trade.token_amount || trade.amount || 0;

    return `${timestamp}-${price}-${amount}`;
  }, []);

  // Format market cap (updated to use correct token based on SOL address)
  const formatMarketCap = useCallback((trade: Trade): string => {
    let marketCap = 0;

    // Use the helper function to get the correct token
    const { actualToken } = getTokenPair(trade);

    if (actualToken?.marketCap) {
      marketCap = actualToken.marketCap;
    } else if (trade.marketCap) {
      marketCap = trade.marketCap;
    }

    if (marketCap >= 1e12) {
      return (marketCap / 1e12).toFixed(2) + 'T';
    } else if (marketCap >= 1e9) {
      return (marketCap / 1e9).toFixed(2) + 'B';
    } else if (marketCap >= 1e6) {
      return (marketCap / 1e6).toFixed(2) + 'M';
    } else if (marketCap >= 1e3) {
      return (marketCap / 1e3).toFixed(2) + 'K';
    } else {
      return marketCap.toFixed(2);
    }
  }, [getTokenPair]);

  // Format token amount (using token_amount_vs for SOL amount with proper formatting, no SOL suffix)
  const formatTokenAmount = useCallback((trade: Trade): string => {
    // Use token_amount_vs which represents the SOL amount in trades
    let tokenAmount = 0;

    if (trade.token_amount_vs) {
      tokenAmount = trade.token_amount_vs;
    } else if (trade.token_amount) {
      tokenAmount = trade.token_amount;
    } else if (trade.amount) {
      tokenAmount = trade.amount;
    }

    let formattedAmount = '';
    if (tokenAmount >= 1e12) {
      formattedAmount = (tokenAmount / 1e12).toFixed(3) + 'T';
    } else if (tokenAmount >= 1e9) {
      formattedAmount = (tokenAmount / 1e9).toFixed(3) + 'B';
    } else if (tokenAmount >= 1e6) {
      formattedAmount = (tokenAmount / 1e6).toFixed(3) + 'M';
    } else if (tokenAmount >= 1e3) {
      formattedAmount = (tokenAmount / 1e3).toFixed(3) + 'K';
    } else {
      // For amounts less than 1000, show with 3 decimal places and add thousand separators
      formattedAmount = tokenAmount.toLocaleString('en-US', {
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
      });
    }

    // Return just the formatted amount without SOL suffix
    return formattedAmount;
  }, []);

  // Format trade value (exact pattern from trade_example.md)
  const formatTradeValue = useCallback((trade: Trade): string => {
    let usdValue = 0;

    if (trade.token_amount_usd) {
      usdValue = trade.token_amount_usd;
    } else if (trade.usd_amount) {
      usdValue = trade.usd_amount;
    } else if (trade.value_usd) {
      usdValue = trade.value_usd;
    } else {
      const price = trade.price || trade.token_price || 0;
      const amount = trade.token_amount || trade.amount || 0;
      usdValue = price * amount;
    }

    return usdValue.toFixed(2);
  }, []);

  // Format age with robust timestamp handling
  const formatAge = useCallback((trade: Trade): string => {
    const timestamp = getTradeTimestamp(trade);
    const now = Date.now();
    const diffSeconds = Math.floor((now - timestamp) / 1000);

    // Handle invalid timestamps - show "just now" instead of "unknown"
    if (isNaN(diffSeconds) || diffSeconds < 0) {
      console.warn('⚠️ Invalid timestamp difference:', { timestamp, now, diffSeconds });
      return 'just now';
    }

    if (diffSeconds < 5) {
      return 'just now';
    } else if (diffSeconds < 60) {
      return `${diffSeconds}s ago`;
    } else if (diffSeconds < 3600) {
      return `${Math.floor(diffSeconds / 60)}m ago`;
    } else if (diffSeconds < 86400) {
      return `${Math.floor(diffSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffSeconds / 86400)}d ago`;
    }
  }, [getTradeTimestamp]);

  // Format transaction hash for trader display (shortened format)
  const formatTraderHash = useCallback((trade: Trade): string => {
    let hash = '';

    if (trade.hash) {
      hash = trade.hash;
    } else if (trade.transaction_hash) {
      hash = trade.transaction_hash;
    } else if (trade.tx_hash) {
      hash = trade.tx_hash;
    }

    if (!hash) {
      return 'N/A';
    }

    return `${hash.substring(0, 4)}...${hash.substring(hash.length - 4)}`;
  }, []);

  // Get full transaction hash for Solscan link
  const getFullHash = useCallback((trade: Trade): string => {
    return trade.hash || trade.transaction_hash || trade.tx_hash || '';
  }, []);

  // Format trade data for UI (updated to use correct token based on SOL address)
  const formatTrade = useCallback((trade: Trade): FormattedTrade => {
    // Use the helper function to get the correct tokens
    const { actualToken } = getTokenPair(trade);

    const marketCap = actualToken?.marketCap || trade.marketCap || 0;
    // Use token_amount_vs (SOL amount) for tokenAmount (side panel)
    const tokenAmount = trade.token_amount_vs || trade.token_amount || trade.amount || 0;
    // Use token_amount (actual token quantity) for actualTokenAmount (bottom table)
    const actualTokenAmount = trade.token_amount || trade.amount || 0;
    const tokenAmountUsd = trade.token_amount_usd || trade.usd_amount || trade.value_usd || 0;

    // Get price from the actual token (not SOL)
    const price = actualToken?.price || trade.token_price || trade.price || 0;

    // Get normalized timestamp
    const normalizedTimestamp = getTradeTimestamp(trade);

    return {
      id: generateTradeId(trade),
      timestamp: normalizedTimestamp,
      type: trade.type,
      amount: formatTokenAmount(trade),
      usdAmount: `$${formatTradeValue(trade)}`,
      mc: `$${formatMarketCap(trade)}`,
      price: `$${price.toFixed(6)}`,
      trader: formatTraderHash(trade), // Use shortened hash instead of 'Other'
      age: formatAge(trade), // Pass the whole trade object
      txHash: getFullHash(trade), // Store full hash for Solscan link
      marketCap,
      tokenAmount, // SOL amount for side panel
      tokenAmountUsd,
      actualTokenAmount // Actual token quantity for bottom table
    };
  }, [generateTradeId, formatTokenAmount, formatTradeValue, formatMarketCap, formatAge, formatTraderHash, getFullHash, getTokenPair, getTradeTimestamp]);

  // Add trade to table (exact pattern from trade_example.md)
  const addTradeToTable = useCallback((trade: Trade) => {
    const tradeId = generateTradeId(trade);

    // Only add the trade if we haven't seen it before
    if (!processedTradeIds.current.has(tradeId)) {
      processedTradeIds.current.add(tradeId);
      const formattedTrade = formatTrade(trade);

      setTrades(prevTrades => {
        const newTrades = [formattedTrade, ...prevTrades];
        return newTrades.slice(0, maxTrades); // Keep only maxTrades
      });

      // Store the latest raw trade for live token data
      setLatestRawTrade(trade);
      setLastUpdate(Date.now());
    }
  }, [generateTradeId, formatTrade, maxTrades]);

  // Fetch historical trades (exact pattern from trade_example.md)
  const fetchHistoricalTrades = useCallback(async (blockchain: string, address: string) => {
    console.log(`📡 Fetching historical trades for ${blockchain}:${address}...`);
    setIsLoading(true);

    try {
      const apiUrl = `https://api.mobula.io/api/1/market/trades/pair?sortOrder=desc&mode=pair&blockchain=${blockchain}&address=${address}&limit=50`;

      const response = await fetch(apiUrl);
      const result = await response.json();

      if (result.data && Array.isArray(result.data)) {
        console.log(`📈 Received ${result.data.length} historical trades`);

        // Send trades in chronological order (oldest first)
        const trades = result.data.reverse();

        trades.forEach((trade: Trade) => {
          addTradeToTable(trade);
        });

        setIsConnected(true);
        setError(null);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error: any) {
      console.error('❌ Error fetching historical trades:', error);
      setError(error.message || 'Failed to fetch historical trades');
    } finally {
      setIsLoading(false);
    }
  }, [addTradeToTable]);

  // Connect to Mobula WebSocket (exact pattern from trade_example.md)
  const connectToMobula = useCallback((blockchain: string, address: string) => {
    // Close existing connection if any
    if (wsRef.current) {
      try {
        wsRef.current.close(1000, 'New connection');
      } catch (err) {
        console.error('Error closing existing connection:', err);
      }
      wsRef.current = null;
    }

    setIsLoading(true);
    setError(null);

    try {
      wsRef.current = new WebSocket('wss://api.mobula.io');

      wsRef.current.onopen = () => {
        console.log(`📡 Connected to Mobula API for ${blockchain}:${address}`);

        const subscriptionMessage = {
          type: 'pair',
          authorization: 'fffa68cd-6bde-4ac5-909d-eb627d8baca0', // Public API key
          payload: {
            blockchain: blockchain,
            address: address,
          }
        };

        try {
          wsRef.current!.send(JSON.stringify(subscriptionMessage));
          console.log(`✅ Subscribed to Mobula feed for ${blockchain}:${address}`);

          setIsConnected(true);
          setIsLoading(false);

          // Fetch historical trades
          fetchHistoricalTrades(blockchain, address);
        } catch (e: any) {
          console.error('Subscribe failed:', e.message);
          setError(`Failed to subscribe: ${e.message}`);
          setIsConnected(false);
          setIsLoading(false);
        }
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          const processTrade = (trade: Trade) => {
            console.log('📊 Received trade data structure:', {
              fullTrade: trade,
              timestamp: trade.timestamp,
              date: trade.date,
              timestampType: typeof trade.timestamp,
              dateType: typeof trade.date,
              keys: Object.keys(trade)
            });
            addTradeToTable(trade);
          };

          // Process single trade or array of trades
          Array.isArray(data) ? data.forEach(processTrade) : processTrade(data);
        } catch (e: any) {
          console.error('❌ WebSocket message error:', e.message);
        }
      };

      wsRef.current.onclose = (e) => {
        console.log(`🔌 Mobula connection closed. Code: ${e.code}`);
        setIsConnected(false);
        setIsLoading(false);
      };

      wsRef.current.onerror = (err: any) => {
        console.error('❌ Mobula WebSocket error:', err);
        setError('WebSocket connection failed');
        setIsConnected(false);
        setIsLoading(false);
      };
    } catch (err: any) {
      console.error('❌ Failed to create WebSocket connection:', err);
      setError(`Connection error: ${err.message}`);
      setIsLoading(false);
    }
  }, [fetchHistoricalTrades, addTradeToTable]);

  // Effect to connect when pool address changes
  useEffect(() => {
    const effectivePoolAddress = getEffectivePoolAddress();
    if (effectivePoolAddress) {
      console.log(`🔄 Pool address changed to: ${effectivePoolAddress}`);

      // Clear existing trades and processed IDs
      setTrades([]);
      processedTradeIds.current.clear();

      // Connect to Mobula with Solana blockchain
      connectToMobula('solana', effectivePoolAddress);
    }

    return () => {
      // Cleanup WebSocket connection
      if (wsRef.current) {
        try {
          if (wsRef.current.readyState === WebSocket.OPEN || wsRef.current.readyState === WebSocket.CONNECTING) {
            wsRef.current.close(1000, 'Component unmount');
          }
        } catch (err) {
          console.error('Error closing WebSocket on cleanup:', err);
        }
        wsRef.current = null;
      }
    };
  }, [getEffectivePoolAddress, connectToMobula]);

  return {
    trades,
    isLoading,
    isConnected,
    error,
    lastUpdate,
    latestRawTrade
  };
};
