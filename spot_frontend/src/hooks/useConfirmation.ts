import { useState, useCallback } from 'react';

interface ConfirmationOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'danger' | 'warning' | 'info';
}

interface ConfirmationState {
  isOpen: boolean;
  options: ConfirmationOptions | null;
  onConfirm: (() => void) | null;
  isLoading: boolean;
}

export const useConfirmation = () => {
  const [state, setState] = useState<ConfirmationState>({
    isOpen: false,
    options: null,
    onConfirm: null,
    isLoading: false
  });

  const showConfirmation = useCallback((
    options: ConfirmationOptions,
    onConfirm: () => void | Promise<void>
  ) => {
    setState({
      isOpen: true,
      options,
      onConfirm: async () => {
        setState(prev => ({ ...prev, isLoading: true }));
        try {
          await onConfirm();
          setState(prev => ({ ...prev, isOpen: false, isLoading: false }));
        } catch (error) {
          setState(prev => ({ ...prev, isLoading: false }));
          throw error;
        }
      },
      isLoading: false
    });
  }, []);

  const hideConfirmation = useCallback(() => {
    if (!state.isLoading) {
      setState({
        isOpen: false,
        options: null,
        onConfirm: null,
        isLoading: false
      });
    }
  }, [state.isLoading]);

  const handleConfirm = useCallback(() => {
    if (state.onConfirm && !state.isLoading) {
      state.onConfirm();
    }
  }, [state.onConfirm, state.isLoading]);

  return {
    isOpen: state.isOpen,
    options: state.options,
    isLoading: state.isLoading,
    showConfirmation,
    hideConfirmation,
    handleConfirm
  };
};

export default useConfirmation;
