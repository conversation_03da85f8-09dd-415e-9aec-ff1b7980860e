import { useState, useEffect, useRef, useCallback } from 'react';

// Types (matching backend)
interface FormattedTrade {
  id: string;
  timestamp: number;
  type: 'buy' | 'sell';
  amount: string;
  usdAmount: string;
  mc: string;
  price: string;
  trader: string;
  age: string;
  txHash: string;
  marketCap: number;
  tokenAmount: number;
  tokenAmountUsd: number;
  actualTokenAmount: number;
  // Additional fields for live data
  pairData?: {
    price?: number;
    token0?: {
      address?: string;
      price?: number;
      marketCap?: number;
      totalSupply?: number;
      symbol?: string;
    };
    token1?: {
      address?: string;
      price?: number;
      marketCap?: number;
      totalSupply?: number;
      symbol?: string;
    };
    liquidity?: number;
    // Mobula volume metrics (directly from API)
    volume24h?: number;
    volume_1min?: number;
    volume_5min?: number;
    volume_1h?: number;
    volume_6h?: number;
    volume_12h?: number;
    // Mobula buy/sell volumes
    buy_volume_1min?: number;
    buy_volume_5min?: number;
    buy_volume_1h?: number;
    buy_volume_6h?: number;
    buy_volume_12h?: number;
    buy_volume_24h?: number;
    sell_volume_1min?: number;
    sell_volume_5min?: number;
    sell_volume_1h?: number;
    sell_volume_6h?: number;
    sell_volume_12h?: number;
    sell_volume_24h?: number;
    // Mobula transaction counts
    buyers_1min?: number;
    buyers_5min?: number;
    buyers_1h?: number;
    buyers_6h?: number;
    buyers_12h?: number;
    buyers_24h?: number;
    sellers_1min?: number;
    sellers_5min?: number;
    sellers_1h?: number;
    sellers_6h?: number;
    sellers_12h?: number;
    sellers_24h?: number;
  };
  token_price?: number;
  // Allow any additional fields from original trade
  [key: string]: any;
}

interface WebSocketMessage {
  type: 'trade' | 'error' | 'connected' | 'disconnected';
  data?: any;
  error?: string;
}

interface SubscriptionRequest {
  action: 'subscribe' | 'unsubscribe';
  poolAddress: string;
}

// Get WebSocket URL following the same proxy pattern as other services
const getWebSocketUrl = (): string => {
  const hostname = window.location.hostname;
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';

  // Production environment
  if (hostname.includes('crypfi.io')) {
    return 'wss://redfyn.crypfi.io/trading-panel-ws';
  }

  // Production server IP
  if (hostname === '*************') {
    return `${protocol}//*************:5003`;
  }

  // Development environment - use proxy pattern like HTTP APIs
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    // Use the same host and port as the frontend, let Vite proxy handle the routing
    const frontendPort = window.location.port || '4001';
    return `${protocol}//localhost:${frontendPort}/trading-panel-ws`;
  }

  // Fallback for other environments
  return `${protocol}//localhost:5003`;
};

export const useBackendTradeData = (poolAddress?: string | null) => {
  const [trades, setTrades] = useState<FormattedTrade[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<number | null>(null);
  const [latestRawTrade, setLatestRawTrade] = useState<any>(null);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const reconnectDelay = 3000;
  const maxTrades = 50;

  // Get effective pool address
  const getEffectivePoolAddress = useCallback(() => {
    if (poolAddress) return poolAddress;

    try {
      const activePulseToken = localStorage.getItem('activePulseToken');
      if (activePulseToken) {
        const tokenData = JSON.parse(activePulseToken);
        return tokenData.pool_address || tokenData.pool_address;
      }
    } catch (error) {
      console.error('Error getting pool address from localStorage:', error);
    }
    return '9zG56JRfnJRru7k3kmY4gajvZQVwfWv2vMnbbccA4c6p'; // Default pool address
  }, [poolAddress]);

  // Connect to backend WebSocket
  const connectToBackend = useCallback((targetPoolAddress: string) => {
    // Close existing connection
    if (wsRef.current) {
      try {
        wsRef.current.close(1000, 'New connection');
      } catch (err) {
        console.error('Error closing existing connection:', err);
      }
      wsRef.current = null;
    }

    // Clear reconnect timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log(`📡 [FRONTEND] Connecting to backend WebSocket for pool: ${targetPoolAddress}`);

      // Connect to backend WebSocket server using proxy pattern
      const wsUrl = getWebSocketUrl();
      console.log('🔗 [FRONTEND] WebSocket URL:', wsUrl);
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('✅ [FRONTEND] Connected to backend WebSocket server');
        console.log('🔗 [FRONTEND] WebSocket connection details:', {
          url: wsUrl,
          readyState: wsRef.current?.readyState,
          protocol: wsRef.current?.protocol,
          extensions: wsRef.current?.extensions
        });
        setIsConnected(true);
        setIsLoading(false);
        reconnectAttempts.current = 0;

        // Subscribe to pool trades
        const subscribeMessage: SubscriptionRequest = {
          action: 'subscribe',
          poolAddress: targetPoolAddress
        };

        try {
          wsRef.current!.send(JSON.stringify(subscribeMessage));
          console.log(`✅ Subscribed to pool: ${targetPoolAddress}`);
        } catch (e: any) {
          console.error('Subscribe failed:', e.message);
          setError(`Failed to subscribe: ${e.message}`);
        }
      };

      wsRef.current.onmessage = (event) => {
        try {
          // Log raw data before parsing
          console.log('📥 [FRONTEND] Raw WebSocket data received:', {
            dataType: typeof event.data,
            dataLength: event.data?.length || 0,
            dataPreview: typeof event.data === 'string' ?
              event.data.substring(0, 200) + (event.data.length > 200 ? '...' : '') :
              event.data
          });

          const message: WebSocketMessage = JSON.parse(event.data);

          // Debug parsed message structure
          console.log('📥 [FRONTEND] Parsed WebSocket message:', {
            messageType: message.type,
            hasData: !!message.data,
            hasError: !!message.error,
            dataKeys: message.data ? Object.keys(message.data) : [],
            errorMessage: message.error
          });

          // Specific debugging for trade messages with liquidity data
          if (message.type === 'trade' && message.data) {
            console.log('📊 [FRONTEND] Received trade data structure:', {
              tradeId: message.data.id,
              hasPairData: !!message.data.pairData,
              pairDataKeys: message.data.pairData ? Object.keys(message.data.pairData) : [],
              pairDataLiquidity: message.data.pairData?.liquidity,
              liquidityType: typeof message.data.pairData?.liquidity,
              fullPairData: message.data.pairData,
              allTradeKeys: Object.keys(message.data)
            });
          }

          // Validate message format before processing
          if (!message || typeof message !== 'object') {
            console.error('❌ Invalid message format:', event.data);
            return;
          }

          // Check for valid message type
          if (!message.type || !['trade', 'error', 'connected', 'disconnected'].includes(message.type)) {
            console.error('❌ Unknown message type:', message);
            return;
          }

          handleBackendMessage(message);
        } catch (e: any) {
          console.error('❌ Error parsing backend message:', {
            error: e.message,
            errorType: e.name,
            rawDataType: typeof event.data,
            rawDataLength: event.data?.length || 0,
            rawDataSample: typeof event.data === 'string' ?
              event.data.substring(0, 100) :
              String(event.data).substring(0, 100),
            isValidJSON: (() => {
              try {
                JSON.parse(event.data);
                return true;
              } catch {
                return false;
              }
            })()
          });
          setError(`Message parsing error: ${e.message}`);
        }
      };

      wsRef.current.onclose = (e) => {
        console.log(`🔌 Backend connection closed. Code: ${e.code}`);
        setIsConnected(false);
        setIsLoading(false);

        // Attempt reconnection if not intentional
        if (e.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          scheduleReconnect(targetPoolAddress);
        }
      };

      wsRef.current.onerror = (err: any) => {
        console.error('❌ [FRONTEND] Backend WebSocket error:', {
          error: err,
          url: wsUrl,
          readyState: wsRef.current?.readyState,
          targetPoolAddress
        });
        setError('Backend connection failed');
        setIsConnected(false);
        setIsLoading(false);
      };

    } catch (err: any) {
      console.error('❌ Failed to create backend WebSocket connection:', err);
      setError(`Connection error: ${err.message}`);
      setIsLoading(false);
    }
  }, []);

  // Handle messages from backend
  const handleBackendMessage = useCallback((message: WebSocketMessage) => {
    console.log('🔄 [FRONTEND] Processing backend message:', {
      type: message.type,
      hasData: !!message.data,
      hasError: !!message.error
    });

    switch (message.type) {
      case 'trade':
        if (message.data) {
          console.log('📊 [FRONTEND] Processing trade data for state update:', {
            tradeId: message.data.id,
            hasPairData: !!message.data.pairData,
            liquidityValue: message.data.pairData?.liquidity,
            liquidityPreserved: !!message.data.pairData?.liquidity,
            willSetAsLatestRawTrade: true
          });

          // Add trade to the list
          setTrades(prevTrades => {
            const newTrades = [message.data, ...prevTrades];
            return newTrades.slice(0, maxTrades);
          });

          // Store as latest raw trade for live token data
          setLatestRawTrade(message.data);
          setLastUpdate(Date.now());

          console.log('✅ [FRONTEND] Trade data stored in state:', {
            tradeId: message.data.id,
            latestRawTradeSet: true,
            liquidityInLatestRawTrade: message.data.pairData?.liquidity
          });
        }
        break;

      case 'connected':
        console.log('✅ Backend confirmed connection:', message.data);
        setIsConnected(true);
        setError(null);
        break;

      case 'disconnected':
        console.log('🔌 Backend confirmed disconnection:', message.data);
        setIsConnected(false);
        break;

      case 'error':
        console.error('❌ Backend error:', message.error);
        setError(message.error || 'Unknown backend error');
        break;

      default:
        console.warn('⚠️ Unknown message type from backend:', message.type);
    }
  }, [maxTrades]);

  // Schedule reconnection
  const scheduleReconnect = useCallback((targetPoolAddress: string) => {
    reconnectAttempts.current++;
    console.log(`🔄 Scheduling reconnect attempt ${reconnectAttempts.current}/${maxReconnectAttempts} in ${reconnectDelay}ms`);
    
    reconnectTimeoutRef.current = setTimeout(() => {
      connectToBackend(targetPoolAddress);
    }, reconnectDelay);
  }, [connectToBackend]);

  // Effect to connect when pool address changes
  useEffect(() => {
    // Prioritize the passed poolAddress parameter over localStorage
    const effectivePoolAddress = poolAddress || getEffectivePoolAddress();
    if (effectivePoolAddress) {
      console.log(`🔄 [FRONTEND] Pool address changed to: ${effectivePoolAddress}`, {
        passedPoolAddress: poolAddress,
        fromLocalStorage: !poolAddress ? getEffectivePoolAddress() : null,
        usingParameter: !!poolAddress
      });

      // Clear existing trades
      setTrades([]);
      setLatestRawTrade(null);

      // Connect to backend
      connectToBackend(effectivePoolAddress);
    }

    return () => {
      // Cleanup on unmount
      if (wsRef.current) {
        try {
          // Send unsubscribe message
          const unsubscribeMessage: SubscriptionRequest = {
            action: 'unsubscribe',
            poolAddress: effectivePoolAddress || ''
          };
          
          if (wsRef.current.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify(unsubscribeMessage));
          }
          
          wsRef.current.close(1000, 'Component unmount');
        } catch (err) {
          console.error('Error during cleanup:', err);
        }
        wsRef.current = null;
      }

      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    };
  }, [poolAddress, getEffectivePoolAddress, connectToBackend]);

  return {
    trades,
    isLoading,
    isConnected,
    error,
    lastUpdate,
    latestRawTrade
  };
};
