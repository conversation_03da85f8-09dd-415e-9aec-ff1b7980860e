import { useState, useEffect, useRef, useCallback } from 'react';
import { globalTradeWebSocketService } from '@/services/globalTradeWebSocketService';

// Types (matching backend)
interface FormattedTrade {
  id: string;
  timestamp: number;
  type: 'buy' | 'sell';
  amount: string;
  usdAmount: string;
  mc: string;
  price: string;
  trader: string;
  age: string;
  txHash: string;
  marketCap: number;
  tokenAmount: number;
  tokenAmountUsd: number;
  actualTokenAmount: number;
  // Additional fields for live data
  pairData?: {
    price?: number;
    token0?: {
      address?: string;
      price?: number;
      marketCap?: number;
      totalSupply?: number;
      symbol?: string;
    };
    token1?: {
      address?: string;
      price?: number;
      marketCap?: number;
      totalSupply?: number;
      symbol?: string;
    };
    liquidity?: number;
    // Mobula volume metrics (directly from API)
    volume24h?: number;
    volume_1min?: number;
    volume_5min?: number;
    volume_1h?: number;
    volume_6h?: number;
    volume_12h?: number;
    // Mobula buy/sell volumes
    buy_volume_1min?: number;
    buy_volume_5min?: number;
    buy_volume_1h?: number;
    buy_volume_6h?: number;
    buy_volume_12h?: number;
    buy_volume_24h?: number;
    sell_volume_1min?: number;
    sell_volume_5min?: number;
    sell_volume_1h?: number;
    sell_volume_6h?: number;
    sell_volume_12h?: number;
    sell_volume_24h?: number;
    // Mobula transaction counts
    buyers_1min?: number;
    buyers_5min?: number;
    buyers_1h?: number;
    buyers_6h?: number;
    buyers_12h?: number;
    buyers_24h?: number;
    sellers_1min?: number;
    sellers_5min?: number;
    sellers_1h?: number;
    sellers_6h?: number;
    sellers_12h?: number;
    sellers_24h?: number;
  };
  token_price?: number;
  // Allow any additional fields from original trade
  [key: string]: any;
}

// Legacy hook - now uses global connection manager internally
// This maintains backward compatibility while eliminating multiple connections

export const useBackendTradeData = (poolAddress?: string | null) => {
  const [trades, setTrades] = useState<FormattedTrade[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<number | null>(null);
  const [latestRawTrade, setLatestRawTrade] = useState<any>(null);

  const subscriberIdRef = useRef<string>(`legacy_subscriber_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const currentPoolAddressRef = useRef<string | null>(null);

  // Get effective pool address (parameter takes priority over localStorage)
  const getEffectivePoolAddress = useCallback((): string | null => {
    if (poolAddress) return poolAddress;

    try {
      const activePulseToken = localStorage.getItem('activePulseToken');
      if (activePulseToken) {
        const tokenData = JSON.parse(activePulseToken);
        return tokenData.pool_address || null;
      }
    } catch (error) {
      console.error('Error getting pool address from localStorage:', error);
    }
    return null; // No default - let global service handle this
  }, [poolAddress]);

  // Handle trade data updates from global service
  const handleTradeDataUpdate = useCallback((update: any) => {
    console.log(`📊 [LEGACY HOOK] Received trade data update for pool: ${update.poolAddress}`);

    setTrades(update.trades || []);
    setLatestRawTrade(update.latestRawTrade);
    setLastUpdate(update.timestamp);
    setIsLoading(false);
    setError(null);
  }, []);

  // Handle connection status updates
  const handleConnectionStatus = useCallback((connected: boolean, errorMessage?: string) => {
    console.log(`🔌 [LEGACY HOOK] Connection status changed: ${connected}`);
    setIsConnected(connected);

    if (!connected && errorMessage) {
      setError(errorMessage);
    } else if (connected) {
      setError(null);
    }
  }, []);

  // Subscribe to connection status updates
  useEffect(() => {
    const unsubscribeFromStatus = globalTradeWebSocketService.onConnectionStatus(handleConnectionStatus);

    return () => {
      unsubscribeFromStatus();
    };
  }, [handleConnectionStatus]);

  // Main effect to manage pool subscription using global service
  useEffect(() => {
    const effectivePoolAddress = getEffectivePoolAddress();
    const subscriberId = subscriberIdRef.current;

    console.log(`🔄 [LEGACY HOOK] Pool address effect triggered:`, {
      effectivePoolAddress,
      previousPoolAddress: currentPoolAddressRef.current,
      subscriberId,
      hasPoolAddress: !!effectivePoolAddress
    });

    // Unsubscribe from previous pool if it exists and is different
    if (currentPoolAddressRef.current && currentPoolAddressRef.current !== effectivePoolAddress) {
      console.log(`🔄 [LEGACY HOOK] Unsubscribing from previous pool: ${currentPoolAddressRef.current}`);
      globalTradeWebSocketService.unsubscribeFromPool(currentPoolAddressRef.current, subscriberId);

      // Clear previous data
      setTrades([]);
      setLatestRawTrade(null);
      setLastUpdate(null);
    }

    // Subscribe to new pool if we have a valid address
    if (effectivePoolAddress) {
      console.log(`🔄 [LEGACY HOOK] Subscribing to new pool: ${effectivePoolAddress}`);
      setIsLoading(true);
      setError(null);

      // Update current pool address reference
      currentPoolAddressRef.current = effectivePoolAddress;

      // Subscribe to the pool using global service
      globalTradeWebSocketService.subscribeToPool(
        effectivePoolAddress,
        subscriberId,
        handleTradeDataUpdate
      ).catch((error) => {
        console.error('❌ [LEGACY HOOK] Failed to subscribe to pool:', error);
        setError(error.message || 'Failed to subscribe to pool');
        setIsLoading(false);
      });

      // Check if we already have data for this pool
      const existingData = globalTradeWebSocketService.getPoolData(effectivePoolAddress);
      if (existingData && existingData.trades.length > 0) {
        console.log(`📊 [LEGACY HOOK] Using existing data for pool: ${effectivePoolAddress}`);
        setTrades(existingData.trades);
        setLatestRawTrade(existingData.latestRawTrade);
        setLastUpdate(existingData.lastUpdate);
        setIsLoading(false);
      }
    } else {
      // No pool address, clear everything
      console.log('🔄 [LEGACY HOOK] No pool address, clearing data');
      currentPoolAddressRef.current = null;
      setTrades([]);
      setLatestRawTrade(null);
      setLastUpdate(null);
      setIsLoading(false);
    }

    // Cleanup function
    return () => {
      if (currentPoolAddressRef.current) {
        console.log(`🧹 [LEGACY HOOK] Cleanup: Unsubscribing from pool: ${currentPoolAddressRef.current}`);
        globalTradeWebSocketService.unsubscribeFromPool(currentPoolAddressRef.current, subscriberId);
      }
    };
  }, [poolAddress, getEffectivePoolAddress, handleTradeDataUpdate]);



  return {
    trades,
    isLoading,
    isConnected,
    error,
    lastUpdate,
    latestRawTrade
  };
};
