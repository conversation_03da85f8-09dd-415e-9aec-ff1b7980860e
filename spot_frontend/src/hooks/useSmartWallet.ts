/**
 * Hook to easily access the Privy Smart Wallet client
 * This hook integrates with the SmartWalletsProvider to provide access to the smart wallet functionality
 */

import { useEffect, useState } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { useSmartWallets } from '@privy-io/react-auth/smart-wallets';
import { isRealSmartWallet } from '../utils/smartWalletUtils';

// Define a type for the smart wallet client
export interface SmartWalletClient {
  // Smart wallet address
  address?: string;
  // Account object with address property
  account?: { address: string };
  // Chain ID
  chainId?: string | number;
  // Send a transaction
  sendTransaction: (tx: any) => Promise<string | { hash: string }>;
  // Send a user operation (ERC-4337)
  sendUserOperation?: (userOp: any) => Promise<any>;
  // Wait for user operation receipt
  waitForUserOperationReceipt?: (params: { hash: string }) => Promise<any>;
  // Optional sponsorUserOperation method for integrated paymaster support
  sponsorUserOperation?: (userOp: any) => Promise<any>;
  // Check if smart wallet is ready
  ready?: boolean;
  // Smart wallet type
  walletClientType?: string;
}

/**
 * Hook for accessing the Privy Smart Wallet
 * @returns An object containing the smart wallet client and loading state
 */
export const useSmartWallet = () => {
  // Get the smart wallet client from the SmartWalletsProvider
  const { client } = useSmartWallets();
  const { user, authenticated } = usePrivy();
  const [isLoading, setIsLoading] = useState(true);
  
  // Ensure the client is loaded properly
  useEffect(() => {
    if (client || !authenticated) {
      setIsLoading(false);
    }
  }, [client, authenticated]);

  // Validate that the client is a real smart wallet
  const isValid = client ? isRealSmartWallet(client) : false;

  return {
    smartWalletClient: client as SmartWalletClient,
    isLoading,
    isValid,
    smartWalletAddress: client?.account?.address || undefined
  };
}; 