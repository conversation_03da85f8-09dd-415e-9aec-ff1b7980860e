import { useState, useEffect } from 'react';

interface TokenInfo {
  id: string;
  symbol: string;
  network: string;
}
interface ActiveToken {
  id: string;
  symbol: string;
  name: string;
  image: string;
  current_price: number;
  market_cap: number;
  total_volume: number;
  price_change_percentage_24h: number;
  network: string;
  liquidity: number;
}

/**
 * Custom hook for managing token selection
 * @param {Function} onSelectToken - Optional callback when a token is selected
 * @returns {Object} - Selection state and handlers
 */
export const useTokenSelection = (onSelectToken?: (token: TokenInfo) => void) => {
  const [selectedTokens, setSelectedTokens] = useState<TokenInfo[]>(() => {
    try {
      return JSON.parse(localStorage.getItem("selectedTokens") || "[]");
    } catch (error) {
      console.error("Error parsing stored tokens:", error);
      return [];
    }
  });

  useEffect(() => {
    console.log("useTokenSelection: Saving tokens to localStorage:", selectedTokens);
    localStorage.setItem("selectedTokens", JSON.stringify(selectedTokens));

    const event = new CustomEvent('tokenSelectionChanged', {
      detail: { selectedTokens }
    });
    window.dispatchEvent(event);
  }, [selectedTokens]);

  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'selectedTokens' && e.newValue !== null) {
        try {
          const newTokens = JSON.parse(e.newValue);
          console.log("useTokenSelection: Detected localStorage change:", newTokens);
          setSelectedTokens(newTokens);
        } catch (error) {
          console.error("Error parsing tokens from storage event:", error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  const handleTokenSelect = (e: React.MouseEvent | null, token: any) => {
    if (e) e.stopPropagation();

    const tokenId = token.id || token.symbol;
    const tokenData: TokenInfo = {
      id: token.id,
      symbol: token.symbol,
      network: token.network
    };

    const isAlreadySelected = selectedTokens.some(t => t.id === tokenId);
    let updatedTokens;

    if (isAlreadySelected) {
      updatedTokens = selectedTokens.filter(t => t.id !== tokenId);
    } else {
      updatedTokens = [...selectedTokens, tokenData];
    }

    console.log("useTokenSelection: Updating selected tokens:", updatedTokens);
    setSelectedTokens(updatedTokens);

    if (onSelectToken) {
      onSelectToken(tokenData);
    }
  };

  const isTokenSelected = (token: any) => {
    const tokenId = token.id || token.symbol;
    return selectedTokens.some(t => t.id === tokenId);
  };

  const toggleSelection = (id: string) => {
    setSelectedTokens((prev) => {
      const isSelected = prev.some(t => t.id === id);
      return isSelected
        ? prev.filter(t => t.id !== id)
        : prev; // Can't add without network/symbol, so don't modify here
    });
  };
const getActiveToken = (): ActiveToken | null => {
    try {
      const stored = localStorage.getItem("activeToken");
      return stored ? JSON.parse(stored) as ActiveToken : null;
    } catch (error) {
      console.error("getActiveToken: Error parsing active token from localStorage", error);
      return null;
    }
  };
  
  return {
    selectedTokens,
    setSelectedTokens,
    handleTokenSelect,
    isTokenSelected,
    toggleSelection,
    getActiveToken 
  };
};

export default useTokenSelection;
