import { useState, useEffect, useRef, useCallback } from 'react';
import { globalTradeWebSocketService, FormattedTrade } from '@/services/globalTradeWebSocketService';

// Re-export types from the service for compatibility
export type { FormattedTrade };

interface UseGlobalTradeDataReturn {
  trades: FormattedTrade[];
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
  lastUpdate: number | null;
  latestRawTrade: any;
}

/**
 * Get effective pool address from localStorage or parameter
 */
const getEffectivePoolAddress = (): string | null => {
  try {
    const activePulseToken = localStorage.getItem('activePulseToken');
    if (activePulseToken) {
      const tokenData = JSON.parse(activePulseToken);
      return tokenData.pool_address || null;
    }
  } catch (error) {
    console.error('Error getting pool address from localStorage:', error);
  }
  return null;
};

/**
 * Global Trade Data Hook
 * Uses the global WebSocket connection manager to share connections across components
 * Supports multiple pool subscriptions with automatic cleanup
 */
export const useGlobalTradeData = (poolAddress?: string | null): UseGlobalTradeDataReturn => {
  const [trades, setTrades] = useState<FormattedTrade[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<number | null>(null);
  const [latestRawTrade, setLatestRawTrade] = useState<any>(null);

  const subscriberIdRef = useRef<string>(`subscriber_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const currentPoolAddressRef = useRef<string | null>(null);

  // Get effective pool address (parameter takes priority over localStorage)
  const getEffectivePoolAddressForHook = useCallback((): string | null => {
    return poolAddress || getEffectivePoolAddress();
  }, [poolAddress]);

  // Handle trade data updates from global service
  const handleTradeDataUpdate = useCallback((update: any) => {
    console.log(`📊 [HOOK] Received trade data update for pool: ${update.poolAddress}`);

    setTrades(update.trades || []);
    setLatestRawTrade(update.latestRawTrade);
    setLastUpdate(update.timestamp);
    setIsLoading(false);
    setError(null);
  }, []);

  // Handle connection status updates
  const handleConnectionStatus = useCallback((connected: boolean, errorMessage?: string) => {
    console.log(`🔌 [HOOK] Connection status changed: ${connected}`);
    setIsConnected(connected);

    if (!connected && errorMessage) {
      setError(errorMessage);
    } else if (connected) {
      setError(null);
    }
  }, []);

  // Subscribe to connection status updates
  useEffect(() => {
    const unsubscribeFromStatus = globalTradeWebSocketService.onConnectionStatus(handleConnectionStatus);

    return () => {
      unsubscribeFromStatus();
    };
  }, [handleConnectionStatus]);

  // Main effect to manage pool subscription
  useEffect(() => {
    const effectivePoolAddress = getEffectivePoolAddressForHook();
    const subscriberId = subscriberIdRef.current;

    console.log(`🔄 [HOOK] Pool address effect triggered:`, {
      effectivePoolAddress,
      previousPoolAddress: currentPoolAddressRef.current,
      subscriberId,
      hasPoolAddress: !!effectivePoolAddress
    });

    // Unsubscribe from previous pool if it exists and is different
    if (currentPoolAddressRef.current && currentPoolAddressRef.current !== effectivePoolAddress) {
      console.log(`🔄 [HOOK] Unsubscribing from previous pool: ${currentPoolAddressRef.current}`);
      globalTradeWebSocketService.unsubscribeFromPool(currentPoolAddressRef.current, subscriberId);

      // Clear previous data
      setTrades([]);
      setLatestRawTrade(null);
      setLastUpdate(null);
    }

    // Subscribe to new pool if we have a valid address
    if (effectivePoolAddress) {
      console.log(`🔄 [HOOK] Subscribing to new pool: ${effectivePoolAddress}`);
      setIsLoading(true);
      setError(null);

      // Update current pool address reference
      currentPoolAddressRef.current = effectivePoolAddress;

      // Subscribe to the pool
      globalTradeWebSocketService.subscribeToPool(
        effectivePoolAddress,
        subscriberId,
        handleTradeDataUpdate
      ).catch((error) => {
        console.error('❌ [HOOK] Failed to subscribe to pool:', error);
        setError(error.message || 'Failed to subscribe to pool');
        setIsLoading(false);
      });

      // Check if we already have data for this pool
      const existingData = globalTradeWebSocketService.getPoolData(effectivePoolAddress);
      if (existingData && existingData.trades.length > 0) {
        console.log(`📊 [HOOK] Using existing data for pool: ${effectivePoolAddress}`);
        setTrades(existingData.trades);
        setLatestRawTrade(existingData.latestRawTrade);
        setLastUpdate(existingData.lastUpdate);
        setIsLoading(false);
      }
    } else {
      // No pool address, clear everything
      console.log('🔄 [HOOK] No pool address, clearing data');
      currentPoolAddressRef.current = null;
      setTrades([]);
      setLatestRawTrade(null);
      setLastUpdate(null);
      setIsLoading(false);
    }

    // Cleanup function
    return () => {
      if (currentPoolAddressRef.current) {
        console.log(`🧹 [HOOK] Cleanup: Unsubscribing from pool: ${currentPoolAddressRef.current}`);
        globalTradeWebSocketService.unsubscribeFromPool(currentPoolAddressRef.current, subscriberId);
      }
    };
  }, [getEffectivePoolAddressForHook, handleTradeDataUpdate]);

  return {
    trades,
    isLoading,
    isConnected,
    error,
    lastUpdate,
    latestRawTrade
  };
};

// Export the hook as default for easier migration
export default useGlobalTradeData;

interface UseGlobalTradeDataReturn {
  trades: FormattedTrade[];
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
  lastUpdate: number | null;
  latestRawTrade: any;
}

/**
 * Get effective pool address from localStorage or parameter
 */
const getEffectivePoolAddress = (): string | null => {
  try {
    const activePulseToken = localStorage.getItem('activePulseToken');
    if (activePulseToken) {
      const tokenData = JSON.parse(activePulseToken);
      return tokenData.pool_address || null;
    }
  } catch (error) {
    console.error('Error getting pool address from localStorage:', error);
  }
  return null;
};

/**
 * Global Trade Data Hook
 * Uses the global WebSocket connection manager to share connections across components
 * Supports multiple pool subscriptions with automatic cleanup
 */
export const useGlobalTradeData = (poolAddress?: string | null): UseGlobalTradeDataReturn => {
  const [trades, setTrades] = useState<FormattedTrade[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<number | null>(null);
  const [latestRawTrade, setLatestRawTrade] = useState<any>(null);

  const subscriberIdRef = useRef<string>(`subscriber_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const currentPoolAddressRef = useRef<string | null>(null);

  // Get effective pool address (parameter takes priority over localStorage)
  const getEffectivePoolAddressForHook = useCallback((): string | null => {
    return poolAddress || getEffectivePoolAddress();
  }, [poolAddress]);

  // Handle trade data updates from global service
  const handleTradeDataUpdate = useCallback((update: any) => {
    console.log(`📊 [HOOK] Received trade data update for pool: ${update.poolAddress}`);
    
    setTrades(update.trades || []);
    setLatestRawTrade(update.latestRawTrade);
    setLastUpdate(update.timestamp);
    setIsLoading(false);
    setError(null);
  }, []);

  // Handle connection status updates
  const handleConnectionStatus = useCallback((connected: boolean, errorMessage?: string) => {
    console.log(`🔌 [HOOK] Connection status changed: ${connected}`);
    setIsConnected(connected);
    
    if (!connected && errorMessage) {
      setError(errorMessage);
    } else if (connected) {
      setError(null);
    }
  }, []);

  // Subscribe to connection status updates
  useEffect(() => {
    const unsubscribeFromStatus = globalTradeWebSocketService.onConnectionStatus(handleConnectionStatus);
    
    return () => {
      unsubscribeFromStatus();
    };
  }, [handleConnectionStatus]);

  // Main effect to manage pool subscription
  useEffect(() => {
    const effectivePoolAddress = getEffectivePoolAddressForHook();
    const subscriberId = subscriberIdRef.current;

    console.log(`🔄 [HOOK] Pool address effect triggered:`, {
      effectivePoolAddress,
      previousPoolAddress: currentPoolAddressRef.current,
      subscriberId,
      hasPoolAddress: !!effectivePoolAddress
    });

    // Unsubscribe from previous pool if it exists and is different
    if (currentPoolAddressRef.current && currentPoolAddressRef.current !== effectivePoolAddress) {
      console.log(`🔄 [HOOK] Unsubscribing from previous pool: ${currentPoolAddressRef.current}`);
      globalTradeWebSocketService.unsubscribeFromPool(currentPoolAddressRef.current, subscriberId);
      
      // Clear previous data
      setTrades([]);
      setLatestRawTrade(null);
      setLastUpdate(null);
    }

    // Subscribe to new pool if we have a valid address
    if (effectivePoolAddress) {
      console.log(`🔄 [HOOK] Subscribing to new pool: ${effectivePoolAddress}`);
      setIsLoading(true);
      setError(null);
      
      // Update current pool address reference
      currentPoolAddressRef.current = effectivePoolAddress;

      // Subscribe to the pool
      globalTradeWebSocketService.subscribeToPool(
        effectivePoolAddress,
        subscriberId,
        handleTradeDataUpdate
      ).catch((error) => {
        console.error('❌ [HOOK] Failed to subscribe to pool:', error);
        setError(error.message || 'Failed to subscribe to pool');
        setIsLoading(false);
      });

      // Check if we already have data for this pool
      const existingData = globalTradeWebSocketService.getPoolData(effectivePoolAddress);
      if (existingData && existingData.trades.length > 0) {
        console.log(`📊 [HOOK] Using existing data for pool: ${effectivePoolAddress}`);
        setTrades(existingData.trades);
        setLatestRawTrade(existingData.latestRawTrade);
        setLastUpdate(existingData.lastUpdate);
        setIsLoading(false);
      }
    } else {
      // No pool address, clear everything
      console.log('🔄 [HOOK] No pool address, clearing data');
      currentPoolAddressRef.current = null;
      setTrades([]);
      setLatestRawTrade(null);
      setLastUpdate(null);
      setIsLoading(false);
    }

    // Cleanup function
    return () => {
      if (currentPoolAddressRef.current) {
        console.log(`🧹 [HOOK] Cleanup: Unsubscribing from pool: ${currentPoolAddressRef.current}`);
        globalTradeWebSocketService.unsubscribeFromPool(currentPoolAddressRef.current, subscriberId);
      }
    };
  }, [getEffectivePoolAddressForHook, handleTradeDataUpdate]);

  // Force reconnection function
  const reconnect = useCallback(async () => {
    console.log('🔄 [HOOK] Force reconnecting...');
    await globalTradeWebSocketService.reconnect();
  }, []);

  return {
    trades,
    isLoading,
    isConnected,
    error,
    lastUpdate,
    latestRawTrade
  };
};

// Export the hook as default for easier migration
export default useGlobalTradeData;
