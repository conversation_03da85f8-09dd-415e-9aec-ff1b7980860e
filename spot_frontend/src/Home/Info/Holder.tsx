import React, { useEffect, useState } from "react";
import axios from "axios";
import { homeAPI } from "@/utils/api";

// Interface for the token prop
interface TokenProp {
  id?: string;
  network?: string;
  symbol?: string; // Added symbol if needed
}

// Interface for holder data from API (adjust based on actual API response)
interface ApiHolderData {
  address: string;
  totalSupplyShare: number;
  amount: number;
  // Add other potential properties
}

// Interface for formatted holder data used in state
interface FormattedHolder {
  address: string;
  rate: number; // Renamed from totalSupplyShare for consistency
  amount: string; // Stored as formatted string
}

const Holders: React.FC<{ token: TokenProp | null }> = ({ token }) => {
  const [holders, setHolders] = useState<FormattedHolder[]>([]);
  const [loading, setLoading] = useState(true);
  
  const [summary, setSummary] = useState({
    total: "0",
    top10: "0%",
    top10Value: "0",
    top100: "0%",
    top100Value: "0"
  });
  const formatAddress = (address: string | undefined | null) => {
    if (!address || typeof address !== 'string') return 'N/A';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatAmount = (value: number): string => {
    if (!value) return '$0.00'; // Handle null/undefined/zero
    if (value >= 1e12) return `$${(value / 1e12).toFixed(2)}T`;
    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    if (value >= 1e3) return `$${(value / 1e3).toFixed(2)}K`;
    return `$${value.toFixed(2)}`;
  };
  
  useEffect(() => {
    const fetchHoldersData = async () => {
      if (!token?.id || !token?.network) {
          setLoading(false);
          console.warn("Token ID or Network missing, cannot fetch holders.");
          setHolders([]); // Clear holders if token is invalid
          return;
      }
      setLoading(true);
      console.log("Fetching holders data for token:", token);
      try {
        let networkQuery = token.network;
        if(token.network === 'binance-smart-chain'){
          networkQuery='BNB Smart Chain (BEP20)';
          }
          const response = await homeAPI.fetchMarketInfo('holders', {
            address: token.id,
          network: networkQuery,
            limit:100,
            offset:0
          });

        const data = response.data?.data;
        console.log("Holders data received:", data);
     
        if (Array.isArray(data)) {
            const formattedHolders: FormattedHolder[] = data.map((holder: ApiHolderData) => ({
            address: holder.address,
              rate: holder.totalSupplyShare, // Use the correct property name from API
              amount: formatAmount(holder.amount), // Format the amount
          }));
          setHolders(formattedHolders);
          console.log("Formatted holders:", formattedHolders);
             // TODO: Calculate summary based on `data` if needed
             // Example (Needs adjustment based on API providing total supply etc.):
             /*
             const totalHolders = data.length; // Or from a summary field if API provides it
             const top10Share = data.slice(0, 10).reduce((sum, h) => sum + h.totalSupplyShare, 0);
             const top100Share = data.slice(0, 100).reduce((sum, h) => sum + h.totalSupplyShare, 0);
             setSummary({
                 total: totalHolders.toLocaleString(),
                 top10: `${(top10Share * 100).toFixed(2)}%`,
                 top10Value: "N/A", // Need valuation logic
                 top100: `${(top100Share * 100).toFixed(2)}%`,
                 top100Value: "N/A" // Need valuation logic
             });
             */
        } else {
            console.warn("Holders data is not an array:", data);
            setHolders([]);
        }

      } catch (error) {
        console.error("Error fetching holders data:", error);
        setHolders([]); // Clear holders on error
      }
      setLoading(false);
    };

    fetchHoldersData();
  }, [token]);

  return (
    <div className="w-full px-4 rounded-xl h-full flex flex-col overflow-hidden">
      {/* Summary Cards */}
      <div className="grid grid-cols-3 gap-3 mb-4">
        {[{ title: "Total", value: summary.total, subValue: "Holders" }, // Changed subValue
          { title: "Top 10", value: summary.top10, subValue: "Ownership" }, // Changed subValue
          { title: "Top 100", value: summary.top100, subValue: "Ownership" } // Changed subValue
        ].map((card, index) => (
          <div key={index} className="bg-[#1D2226] p-3 rounded-xl text-center">
            <p className="text-[#BBBBBB] pb-2 text-sm">{card.title}</p>
            {loading ? (
              <>
                <div className="h-4 bg-gray-700 rounded animate-pulse mx-auto w-2/3 mb-2" />
                <div className="h-3 bg-gray-700 rounded animate-pulse mx-auto w-1/2" />
              </>
            ) : (
              <>
                <p className="text-white text-md font-semibold">{card.value}</p>
                <p className="text-[#BBBBBB] text-sm">{card.subValue}</p>
              </>
            )}
          </div>
        ))}
      </div>

      {/* Table Header */}
      <div className="flex justify-between text-[#BBBBBB] text-sm font-semibold py-4 border-t border-gray-600">
        <span className="text-left">Address</span>
        <span>Rate (Amount)</span>
      </div>

      {/* Scrollable Holders List */}
      <div className="flex flex-col flex-grow min-h-[200px] max-h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent">
        {loading ? (
          Array.from({ length: 10 }).map((_, index) => (
            <div key={index} className="flex justify-between items-center py-2 animate-pulse">
              <div className="flex items-center space-x-2">
                {/* <div className="w-8 h-8 bg-gray-700 rounded-full" /> Removed placeholder icon */}
                <div className="w-32 h-4 bg-gray-700 rounded" />
              </div>
              <div className="w-28 h-4 bg-gray-700 rounded" />
            </div>
          ))
        ) : holders.length > 0 ? (
            holders.map((holder: FormattedHolder, index) => (
            <div key={index} className="flex text-sm justify-between items-center py-2 text-white">
              <div className="flex items-center space-x-2">
                <p className="text-gray-300 truncate max-w-[150px]">
                  {formatAddress(holder.address)}
                </p>
              </div>
              <p className="text-[#14FFA2] font-semibold">
                  {holder.rate ? `${(holder.rate * 100).toFixed(2)}%` : '0.00%'} ({holder.amount})
              </p>
            </div>
          ))
          ) : (
             <div className="flex items-center justify-center h-full text-gray-400">
                No holder data available.
             </div>
          )
        }
      </div>
    </div>
  );
};

export default Holders;