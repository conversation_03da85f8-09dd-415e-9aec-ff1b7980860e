import React, { useState, useEffect } from "react";
import axios from "axios"; // Assuming axios is used

// --- Placeholder Types/Interfaces (Adjust based on actual data structure) ---
interface TokenData {
  id: string;
  symbol: string;
  name: string;
  image?: string;
  market_cap_rank?: number;
  current_price?: number;
  price_change_percentage_24h?: number;
  // Add other properties as needed
}

interface ChartDataPoint {
  timestamp: number;
  price: number;
}

interface DexData { 
  // Define structure based on getDEXScreenerPairData response
  [key: string]: any; 
}

// --- Placeholder Functions/Variables (Implement or import these) ---
const searchTokensByName = async (term: string): Promise<any> => { 
  console.warn("searchTokensByName function not implemented"); 
  return { data: [] }; // Placeholder
};
const formatChartData = (prices: [number, number][]): ChartDataPoint[] => {
  console.warn("formatChartData function not implemented");
  return prices.map(p => ({ timestamp: p[0], price: p[1] })); // Placeholder
};
const getDEXScreenerPairData = async (token: TokenData): Promise<DexData | null> => { 
  console.warn("getDEXScreenerPairData function not implemented"); 
  return null; // Placeholder
};
const formatCurrency = (value: number | undefined): string => {
  console.warn("formatCurrency function not implemented");
  return value ? `$${value.toLocaleString()}` : '$--'; // Placeholder
};
const Spinner = () => <div>Loading...</div>; // Placeholder Spinner
const defaultTokenIcon = "/path/to/default/icon.png"; // Placeholder default icon path
const apiEndpoint = "/api"; // Placeholder API endpoint
const chartTimeframe = '1'; // Placeholder chart timeframe

// --- Component Props Interface ---
interface InfoPanelProps {
  selectedToken: TokenData | null;
}

// --- InfoPanel Component ---
const InfoPanel: React.FC<InfoPanelProps> = ({ selectedToken }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [basicTokenData, setBasicTokenData] = useState<TokenData | null>(null);
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [dexData, setDexData] = useState<DexData | null>(null);

  // --- Data Fetching Logic (Moved inside component) ---
  const fetchTokenData = async (token: TokenData | null) => {
    if (!token || (!token.id && !token.symbol)) { // Simplified check
      // Don't set error if no token selected, just clear data
      setBasicTokenData(null);
      setChartData([]);
      setDexData(null);
  setError(null);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);
    setBasicTokenData(null); // Clear previous data
    setChartData([]);
    setDexData(null);
    
    console.log("Fetching token data for:", token);
    const searchTerm = token.id || token.symbol; // Use ID or symbol
    
    try {
      const apiResponse = await searchTokensByName(searchTerm);
      
      if (!apiResponse?.data?.[0]) {
        throw new Error(`No data found for token ${searchTerm}`);
      }
      
      const extractedToken: TokenData = apiResponse.data[0];
      // Optional: Add more validation if needed
      
      setBasicTokenData(extractedToken);
      await fetchMarketData(extractedToken); // Fetch market data after basic data
      
    } catch (apiError: unknown) {
      console.error("Error fetching token data from API:", apiError);
      const message = apiError instanceof Error ? apiError.message : "Could not retrieve token data.";
      setError(`${message} The token may not be listed or the service might be unavailable.`);
      setLoading(false);
    }
    // Removed outer try-catch as it was redundant
};

  const fetchMarketData = async (token: TokenData) => {
    // No need for separate loading state here, handled by fetchTokenData
  try {
      if (!token?.id) {
      console.warn("Cannot fetch market data without token ID");
        return; // Exit silently if no ID
    }
    
    // Get token market data
    try {
      const marketData = await axios.get(`${apiEndpoint}/coins/${token.id}/market_chart`, {
        params: {
          vs_currency: 'usd',
          days: chartTimeframe
        }
      });
      
        if (marketData.data?.prices) {
        const formattedData = formatChartData(marketData.data.prices);
        setChartData(formattedData);
      } else {
        console.warn("Market data response is missing price data");
        setChartData([]);
      }
    } catch (marketError) {
      console.error("Failed to fetch market chart data:", marketError);
      setChartData([]);
        // Don't set global error for chart data failure
    }
    
    // Get token DEX data
    try {
        const fetchedDexData = await getDEXScreenerPairData(token);
        setDexData(fetchedDexData); // Set null if function returns null
      } catch (dexError) {
        console.error("Failed to fetch DEX data:", dexError);
        setDexData(null);
        // Don't set global error for DEX data failure
    }
    
  } catch (error) {
      // This catch might be redundant if inner catches handle specifics
      console.error("Error in fetchMarketData wrapper:", error);
    } finally {
        // Ensure loading is set to false only after all fetches attempt
    setLoading(false);
  }
};

  // Fetch data when selectedToken changes
  useEffect(() => {
    fetchTokenData(selectedToken);
  }, [selectedToken]);

  // --- Image Error Handler ---
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
      const target = e.target as HTMLImageElement;
      target.onerror = null; // Prevent infinite loop
      target.src = defaultTokenIcon;
  };

  // --- Rendering Logic ---
const InfoPanelContent = () => {
    if (loading && !basicTokenData) { // Show spinner only on initial load
    return <div className="flex justify-center items-center h-96"><Spinner /></div>;
  }
  
  if (error) {
    return (
      <div className="flex flex-col justify-center items-center h-96 text-center px-4">
        <div className="text-red-500 mb-4 text-2xl">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          Error
        </div>
        <p className="text-gray-300 mb-6">{error}</p>
        <button 
            onClick={() => fetchTokenData(selectedToken)} // Retry logic
          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
        >
          Try Again
        </button>
      </div>
    );
  }
  
  if (!basicTokenData) {
    return (
      <div className="flex flex-col justify-center items-center h-96 text-center px-4">
        <p className="text-gray-300">Select a token to view detailed information</p>
      </div>
    );
  }
  
    // Main content when data is available (even if loading more market/dex data)
  return (
    <div className="p-4 overflow-y-auto">
      {/* Token header */}
      <div className="flex items-center mb-6">
        <img 
            src={basicTokenData.image || defaultTokenIcon} // Use default icon if image missing
          alt={basicTokenData.name} 
          className="w-12 h-12 mr-4" 
            onError={handleImageError} // Use defined error handler
        />
        <div>
          <h2 className="text-2xl font-bold">{basicTokenData.name}</h2>
          <div className="flex items-center text-gray-400">
            <span className="mr-2">{basicTokenData.symbol.toUpperCase()}</span>
            {basicTokenData.market_cap_rank && (
              <span className="bg-gray-700 px-2 py-0.5 rounded text-xs">
                Rank #{basicTokenData.market_cap_rank}
              </span>
            )}
          </div>
        </div>
        <div className="ml-auto">
          <div className="text-2xl font-bold">
            {formatCurrency(basicTokenData.current_price)}
          </div>
            <div className={`flex items-center ${basicTokenData.price_change_percentage_24h && basicTokenData.price_change_percentage_24h >= 0 ? 'text-green-500' : 'text-red-500'}`}>
            <span>
                {basicTokenData.price_change_percentage_24h && basicTokenData.price_change_percentage_24h >= 0 ? '+' : ''}
              {basicTokenData.price_change_percentage_24h?.toFixed(2)}%
            </span>
            <span className="ml-1">
                {basicTokenData.price_change_percentage_24h && basicTokenData.price_change_percentage_24h >= 0 ? '↑' : '↓'}
            </span>
          </div>
        </div>
      </div>
      
        {/* Placeholder for rest of the content */}
        <div className="text-gray-400">Chart Data Placeholder: {chartData.length} points</div>
        <div className="text-gray-400 mt-4">Dex Data Placeholder: {dexData ? JSON.stringify(dexData) : 'None'}</div>
        {/* ... Add Chart, DEX info, etc. here using chartData and dexData ... */}
    </div>
  );
}; 

  return <InfoPanelContent />;
};

export default InfoPanel; 