import React, { useState, useEffect, useCallback } from "react";
import axios from "axios";
import { FiArrowUp, FiArrowDown, FiCopy, FiCheck } from "react-icons/fi";
import { homeAPI } from "@/utils/api";

// Interface for the token prop
interface TokenProp {
  id?: string;
  network?: string;
  symbol?: string; // Added symbol if needed
}

// Verify API keys are loaded from environment variables

interface Transaction {
  id: string;
  fullAddress: string;
  displayAddress: string;
  amount: number;
  volume: number;
  price: string;
  time: string;
  timestamp: number;
  representative?: boolean;
  type?:string;
  network?:string // Removed duplicate network
}

const Activity: React.FC<{ token: TokenProp | null }> = ({ token }) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);

 console.log("Token:", token);
 useEffect(() => {
  if (!token?.id || !token?.network) return;
  
  const fetchTransactionData = async () => {
    setLoading(true);
    setError(null);
    try {
      let networkQuery = token.network;
      if(token.network === 'binance-smart-chain'){
        networkQuery='BNB Smart Chain (BEP20)';
        }
        const res = await homeAPI.fetchMarketInfo('activities', {
          address: token.id,
        network: networkQuery
        });

      const { data } = res?.data;

      const txs: Transaction[] = data.map((txn: any) => ({
        id: txn.hash,
        fullAddress: txn.sender,
        displayAddress: formatAddressForDisplay(txn.sender),
        amount: txn.token_amount,
        volume: txn.token_amount_usd,
        price: formatAmount(txn.token_price),
        time: formatTimeAgo(txn.date),
        timestamp: txn.date,
        // representative: txn.token_amount_usd > 10000, // example logic
        type: txn.type || 'buy' ,
      }));

      setTransactions(txs);
    } catch (err: any) {
      console.error(err);
      setError("Unable to load transactions.");
    } finally {
      setLoading(false);
    }
  }
    fetchTransactionData();
  }, [token]);

  const copyToClipboard = (address: string) => {
    navigator.clipboard.writeText(address);
    setCopiedAddress(address);
    setTimeout(() => setCopiedAddress(null), 2000);
  };

  const getExplorerUrl = (address: string): string => {
    // Default to Ethereum network if not specified
    const tokenNetwork = token?.network || 'ethereum';
    console.log("Token Network:", tokenNetwork);
    // Determine network based on address format and token network
    if (address.startsWith('0x')) {
      // Ethereum or EVM compatible chains
      if (tokenNetwork === 'binance-smart-chain' || tokenNetwork === 'bsc' || tokenNetwork==='BNB Smart Chain (BEP20)') {
        return `https://bscscan.com/address/${address}`;
      } else if (tokenNetwork === 'polygon') {
        return `https://polygonscan.com/address/${address}`;
      } else if (tokenNetwork === 'avalanche') {
        return `https://snowtrace.io/address/${address}`;
      } else if (tokenNetwork === 'fantom') {
        return `https://ftmscan.com/address/${address}`;
      } else if (tokenNetwork === 'arbitrum') {
        return `https://arbiscan.io/address/${address}`;
      } else if (tokenNetwork === 'optimism') {
        return `https://optimistic.etherscan.io/address/${address}`;
      } else {
        // Default to Ethereum
        return `https://etherscan.io/address/${address}`;
      }
    } else if (address.length >= 32 && address.length <= 44) {
      // Likely Solana
      return `https://solscan.io/account/${address}`;
    } else if (address.startsWith('tz')) {
      // Tezos
      return `https://tzstats.com/${address}`;
    } else if (address.startsWith('addr')) {
      // Cardano
      return `https://cardanoscan.io/address/${address}`;
    } else {
      // Default to Ethereum for unknown formats
      return `https://etherscan.io/address/${address}`;
    }
  };
  const openInExplorer = (address: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const url = getExplorerUrl(address);
    window.open(url, '_blank');
  };

  // Formatting helpers here...
  const formatTimeAgo = (timestamp: number | string): string => {
    const now = new Date();
    const txTime = new Date(timestamp);
    const diffInSeconds = Math.floor((now.getTime() - txTime.getTime()) / 1000);
    
    if (diffInSeconds < 60) return `${diffInSeconds}s ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  const formatAmount = (value: number): string => {
    if (!value) return '$0.00'; // Handle null/undefined/zero
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    if (value >= 1e3) return `$${(value / 1e3).toFixed(2)}K`;
    return `$${value.toFixed(2)}`;
  };

  const formatAddressForDisplay = (fullAddress: string): string => {
    if (!fullAddress || fullAddress.length < 10) return fullAddress;
    return `${fullAddress.slice(0, 6)}...${fullAddress.slice(-4)}`;
  };
  const getColor = (type: string | undefined): string => {
    switch (type) {
      case 'buy':
        return 'text-[#14FFA2]';
      case 'sell':
        return 'text-[#FF329B]';
      case 'deposit':
        return 'text-blue-400';
      default:
        return 'text-gray-300';
    }
  };

  // Render section - already complete in your original code
  // Just paste the rendering block from your code here
  // Use `transactions`, `error`, `loading`, etc.

  return (
    <div className="h-full flex flex-col rounded-xl">
      {loading ? (
        <div className="flex-grow overflow-y-auto space-y-3 animate-pulse">
          {Array.from({ length: 5 }).map((_, index) => (
            <div
              key={index}
              className="p-3 bg-[#1A1E23] rounded-lg flex items-center justify-between border border-gray-800"
            >
              <div className="flex items-center">
                <div className="mr-3 p-2 rounded-lg bg-gray-700 h-8 w-8" />
                <div>
                  <div className="h-4 bg-gray-700 rounded w-32 mb-1" />
                  <div className="h-3 bg-gray-600 rounded w-20" />
                </div>
              </div>
              <div className="text-right">
                <div className="h-4 bg-gray-700 rounded w-16 mb-1" />
                <div className="h-3 bg-gray-600 rounded w-20" />
              </div>
            </div>
          ))}
        </div>
      ) : error ? (
        <div className="flex items-center justify-center h-full text-red-400">
          {error}
        </div>
       ) : transactions.length > 0 ? (
        <>
          {/* <div className="mb-4">
            <h3 className="text-lg font-semibold mb-1">Recent Transactions</h3>
            {transactions.some(tx => tx.representative) && (
              <p className="text-sm text-gray-400 italic">
                Showing representative activity based on trading statistics
              </p>
            )}
          </div> */}
          <div className="flex-grow overflow-y-auto space-y-3">
            {transactions.map((transaction) => (
              <div
                key={transaction.id}
                className={`p-3 bg-[#1A1E23] rounded-lg flex items-center justify-between ${
                  transaction.representative ? 'border border-gray-700 opacity-80' : ''
                }`}
              >
                <div className="flex items-center">
                  <div className={`mr-3 p-2 rounded-lg ${getColor(transaction.type)}`}>
                    {transaction.type === 'buy' ? <FiArrowUp size={16} /> : <FiArrowDown size={16} />}
                  </div>
                  <div>
                    <div className="flex items-center">
                      <span className="font-medium">{transaction.displayAddress}</span>
                      <div className="ml-2 flex space-x-1">
                        <button
                          onClick={() => copyToClipboard(transaction.fullAddress)}
                          className="text-gray-400 hover:text-white"
                          title="Copy address"
                        >
                          {copiedAddress === transaction.fullAddress ? <FiCheck size={12} /> : <FiCopy size={12} />}
                        </button>
                        <button
                          onClick={(e) => openInExplorer(transaction.fullAddress, e)}
                          className="text-gray-400 hover:text-white"
                          title="View in explorer"
                        >
                          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15 3 21 3 21 9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                          </svg>
                        </button>
                      </div>
                    </div>
                    <p className="text-xs text-gray-400">{transaction.time}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`font-semibold ${getColor(transaction.type)}`}>
                    {formatAmount(Math.abs(transaction.amount))}
                  </p>
                  <p className="text-xs text-gray-400">at {transaction.price}</p>
                </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="flex items-center justify-center h-full text-gray-400">
          No transaction data available.
        </div>
      )}
    </div>
  );
  
};

export default Activity;