import React, { useState, useEffect } from "react";
import axios from "axios";
import useTokenSelection from "@/hooks/useTokenSelection";
import { FiCopy, FiCheck } from 'react-icons/fi';
import { homeAPI } from "@/utils/api";

// --- Interfaces ---

interface TokenProp {
  id?: string;
  network?: string;
  symbol?: string;
}

interface PriceChanges {
  m5: number | string;
  h1: number | string;
  h6: number | string;
  h24: number | string;
}

interface TransactionDetail {
  buys: number;
  sells: number;
  buyVolume: number;
  sellVolume: number;
}

interface TransactionDetails {
  m5: TransactionDetail;
  h1: TransactionDetail;
  h6: TransactionDetail;
  h24: TransactionDetail;
}

interface Token {
  name: string;
  id?: string;
  symbol?: string;
}

interface Liquidity {
  usd: number;
}

interface PriceChange {
  m5?: string | number;
  h1?: string | number;
  h6?: string | number;
  h24?: string | number;
}

interface Transactions {
  buys: number;
  sells: number;
}

interface Volume {
  m5?: number;
  h1?: number;
  h6?: number;
  h24?: number;
}

interface TokenPair {
  liquidity?: Liquidity | number; // Allow number directly based on usage
  priceChange?: PriceChange;
  txns?: {
    m5?: Transactions;
    h1?: Transactions;
    h6?: Transactions;
    h24?: Transactions;
  };
  volume?: Volume;
  pairAddress?: string; // Made optional based on usage
  address?: string; // Added based on usage in liquidityData map
  baseToken?: {
    symbol: string;
  };
  quoteToken?: {
    symbol: string;
  };
  exchange?: {
    logo?: string;
    name?: string;
  };
  blockchain?: string; // Added based on usage
  volume24h?: number; // Added based on commented-out table
  price?: string | number; // Added based on commented-out table
  token0?: { logo?: string; symbol: string }; // Added based on commented-out table
  token1?: { logo?: string; symbol: string }; // Added based on commented-out table
}

interface StatsData {
  priceChange: number | string;
  transactions: number;
  buys: number;
  sells: number;
  volume: number;
  buyVolume: number;
  sellVolume: number;
  buyers: number;
  sellers: number;
  traders: number; // Added missing property
}

interface AllStats {
  [key: string]: StatsData;
}

// Define a basic ApiResponse type (adjust as needed)
interface ApiResponse {
  data: any; // Use a more specific type if known
  // Add other expected properties if any
}

const TIME_RANGES = ['5m', '1h', '6h', '24h'];

const API_KEY_MAPPING: Record<string, string> = {
  '5m': '5min',
  '1h': '1h',
  '4h': '4h', // Note: API seems to use 5min, 1h, 24h based on usage below
  '6h': '6h', // Added 6h mapping if needed by TIME_RANGES
  '24h': '24h'
};

const cryptoIcons = {
  ethereum: "https://mobulastorage.blob.core.windows.net/mobula-assets/assets/logos/d90e937d915ab0c865ff6e335361386c56524d4d33fb66f2f04defe1500082eb.png",
  bitcoin: "https://coin-images.coingecko.com/coins/images/825/large/bnb-icon2_2x.png?1696501970",
  solana: "https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png",
  "binance-smart-chain":"https://coin-images.coingecko.com/coins/images/825/large/bnb-icon2_2x.png?1696501970"
};

const About: React.FC<{ token: TokenProp | null }> = ({token}) => {
  console.log("About token", token);
  const getColor = (value: number | string): string => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return (numValue >= 0 ? "text-[#14FFA2]" : "text-[#FF329B]");
  };
  
  const [pairs, setPairs] = useState<TokenPair[]>([]);
  const [totalLiquidity, setTotalLiquidity] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState<string>("5m");
  const [allStats, setAllStats] = useState<AllStats | null>(null);
  const [tokenData, setTokenData] = useState<ApiResponse | null>(null); // Use defined type
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null); // Type state

  const copyToClipboard = (address: string) => { // Add type for address
    navigator.clipboard.writeText(address);
    setCopiedAddress(address);
    setTimeout(() => setCopiedAddress(null), 2000); // Reset after 2s
  };
  const formatAddress = (address: string | undefined | null) => {
    if (!address || typeof address !== 'string') return 'N/A';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatLiquidity = (value: number | undefined | null): string => {
    if (value === undefined || value === null) return '$0.00';
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    if (value >= 1e3) return `$${(value / 1e3).toFixed(2)}K`;
    return `$${value.toFixed(2)}`;
  };

  const [liquidityData, setLiquidityData] = useState<TokenPair[]>([]); // Type state

  useEffect(() => {
    if (!token?.id || !token?.network) return;
  
    const fetchData = async () => {
      setLoading(true);
      setError(null);
  
      try {
        let networkQuery = token.network;
        if(token.network === 'binance-smart-chain'){
          networkQuery='BNB Smart Chain (BEP20)';
          }
          const tokenDataResponse= await homeAPI.fetchMarketInfo('market-pair', {
            address: token.id,
          network: networkQuery,
          });
  
        const apiData = tokenDataResponse.data;
        console.log("API Response:", apiData);
  
        if (!apiData?.data) {
          setError("No data received from Token API");
          setLoading(false);
          return;
        }
  
        setTokenData(apiData);
  
        const formattedStats: AllStats = {};
        for (const timeRange of TIME_RANGES) {
          const apiKey = API_KEY_MAPPING[timeRange as keyof typeof API_KEY_MAPPING]; // Type assertion
          if (!apiKey) continue; // Skip if mapping doesn't exist
          formattedStats[timeRange] = {
            priceChange: apiData.data[`price_change_${apiKey}`] || 0,
            transactions: apiData.data[`trades_${apiKey}`] || 0,
            buys: apiData.data[`buys_${apiKey}`] || 0,
            sells: apiData.data[`sells_${apiKey}`] || 0,
            volume: apiData.data[`volume_${apiKey}`] || 0,
            buyVolume: apiData.data[`buy_volume_${apiKey}`] || 0,
            sellVolume: apiData.data[`sell_volume_${apiKey}`] || 0,
            buyers: apiData.data[`buyers_${apiKey}`] || 0,
            sellers: apiData.data[`sellers_${apiKey}`] || 0,
            traders: (apiData.data[`buyers_${apiKey}`] || 0) + (apiData.data[`sellers_${apiKey}`] || 0), // Calculate traders
          };
        }
        setAllStats(formattedStats);
        console.log("Formatted Stats:", formattedStats);
  
        const liquidityResponse = await homeAPI.fetchMarketInfo('market-pairs', {
          address: token.id,
          network: networkQuery,
        });
        
        const pairsData = liquidityResponse.data.data.pairs;
        // Ensure pairsData is an array before processing
        if (Array.isArray(pairsData)) {
          setLiquidityData(pairsData as TokenPair[]); // Cast to TokenPair[]
        console.log("Liquidity Data:", pairsData);
          const total = pairsData.reduce((acc: number, pair: TokenPair) => {
              // Handle both number and object format for liquidity
              const liqValue = typeof pair.liquidity === 'number' ? pair.liquidity :
                             (typeof pair.liquidity === 'object' && pair.liquidity !== null ? pair.liquidity.usd : 0);
              const liq = parseFloat(String(liqValue)) || 0;
          return acc + liq;
        }, 0);
        setTotalLiquidity(total);
        console.log("Total Liquidity:", total);
        } else {
          console.warn("Liquidity pairs data is not an array:", pairsData);
          setLiquidityData([]);
          setTotalLiquidity(0);
        }
  
      } catch (err) {
        console.error("Error fetching data:", err);
        setError("Failed to fetch data");
      } finally {
        setLoading(false);
      }
    };
  
    fetchData();
  }, [token]);

  console.log("All Stats:", allStats);
  return (
    <div className="rounded-xl  w-full flex flex-col h-full">
  
      {/* Tabs */}
      <div className="flex gap-2 mb-4 pb-2">
  {loading ? (
    // Skeleton placeholders
    Array(4).fill(0).map((_, i) => (
      <div
        key={i}
        className="px-4 py-2 rounded-lg w-[5rem] h-[3.75rem] bg-[#333333] animate-pulse flex flex-col items-center justify-center gap-1"
      >
        <div className="h-4 w-10 bg-gray-600 rounded"></div>
        <div className="h-4 w-8 bg-gray-600 rounded"></div>
      </div>
    ))
  ) : error ? (
    <div className="text-[#FF329B]">Error: {error}</div>
  ) : allStats ? (
    TIME_RANGES.map((tab) => {
            const statsForTab = allStats[tab];
            const value = statsForTab?.priceChange;
            const isPositive = value !== undefined && value !== null && value !== "N/A" && parseFloat(String(value)) >= 0;

      return (
        <div
          key={tab}
                className={`px-4 py-2 rounded-lg text-white w-[6rem] font-semibold flex flex-col items-center gap-1 cursor-pointer
            ${isPositive ? "bg-[#214638]" : "bg-[#47182A]"} 
                  ${selectedTab === tab ? (isPositive ? "border-2 border-[#14FFA2]" : "border-2 border-[#FF329B]") : "border-2 border-transparent"}`}
          onClick={() => setSelectedTab(tab)}
        >
          <span>{tab}</span>
          <span className={isPositive ? "text-[#14FFA2]" : "text-[#FF329B]"}>
            {value != null ? `${parseFloat(String(value)).toFixed(2)}%` : '—'}
          </span>
        </div>
      );
    })
  ) : (
    <div className="text-white">No data available</div>
  )}
</div>
  
      {/* Content Section */}
      <div className="h-full flex flex-col flex-grow  space-y-6 text-sm">
        {/* Show loading or error */}
        {loading ? (
  <div className="space-y-6 animate-pulse">
    {/* Skeleton Block for Transactions */}
    <div>
      <div className="h-4 w-32 bg-gray-600 rounded mb-2"></div>
      <div className="w-full h-1 bg-gray-700 rounded-full mb-2"></div>
      <div className="flex justify-between">
        <div className="h-4 w-24 bg-gray-600 rounded"></div>
        <div className="h-4 w-24 bg-gray-600 rounded"></div>
      </div>
    </div>
    {/* Skeleton Block for Volume */}
    <div>
      <div className="h-4 w-40 bg-gray-600 rounded mb-2"></div>
      <div className="w-full h-1 bg-gray-700 rounded-full mb-2"></div>
      <div className="flex justify-between">
        <div className="h-4 w-36 bg-gray-600 rounded"></div>
        <div className="h-4 w-36 bg-gray-600 rounded"></div>
      </div>
    </div>
    {/* Skeleton Block for Traders */}
    <div>
      <div className="h-4 w-28 bg-gray-600 rounded mb-2"></div>
      <div className="w-full h-1 bg-gray-700 rounded-full mb-2"></div>
      <div className="flex justify-between">
        <div className="h-4 w-20 bg-gray-600 rounded"></div>
        <div className="h-4 w-20 bg-gray-600 rounded"></div>
      </div>
    </div>
  </div>
        ) : error ? (
            <div className="text-center text-red-400 py-10">Error loading data: {error}</div>
        ) : allStats && allStats[selectedTab] ? (
  <>
    {/* Transactions Section */}
    <div>
      <p className="text-[#BBBBBB] font-semibold pb-2">
        Transactions
        <span className="float-right">
                  {allStats[selectedTab].transactions.toLocaleString()}
        </span>
      </p>
              <div className="w-full bg-[#FF329B] h-1 rounded-full overflow-hidden">
        <div
                  className="bg-[#14FFA2] h-1"
          style={{
                    width: `${allStats[selectedTab].transactions > 0 ? (allStats[selectedTab].buys / allStats[selectedTab].transactions) * 100 : 0}%`,
          }}
        ></div>
      </div>
      <p className="text-[#14FFA2] font-semibold pt-2">
                Buys: {allStats[selectedTab].buys.toLocaleString()}
        <span className="float-right text-[#FF329B]">
                  Sells: {allStats[selectedTab].sells.toLocaleString()}
        </span>
      </p>
    </div>

    {/* Volume Section */}
    <div>
      <p className="text-[#BBBBBB] font-semibold pb-2">
                Volume
        <span className="float-right">
                  ${allStats[selectedTab].volume.toLocaleString(undefined, { minimumFractionDigits: 2 })}
        </span>
      </p>
              <div className="w-full bg-[#FF329B] h-1 rounded-full overflow-hidden">
        <div
                  className="bg-[#14FFA2] h-1"
          style={{
                    width: `${allStats[selectedTab].volume > 0 ? (allStats[selectedTab].buyVolume / allStats[selectedTab].volume) * 100 : 0}%`,
          }}
        ></div>
      </div>
      <p className="text-[#14FFA2] font-semibold pt-2">
                Buy volume: $ {allStats[selectedTab].buyVolume.toLocaleString(undefined, { minimumFractionDigits: 2 })}
        <span className="float-right text-[#FF329B]">
                  Sell volume: $ {allStats[selectedTab].sellVolume.toLocaleString(undefined, { minimumFractionDigits: 2 })}
        </span>
      </p>
    </div>

    {/* Traders Section */}
    <div>
      <p className="text-[#BBBBBB] font-semibold pb-2">
        Traders
        <span className="float-right">
                  {allStats[selectedTab].traders.toLocaleString()}
        </span>
      </p>
              <div className="w-full bg-[#FF329B] h-1 rounded-full overflow-hidden">
        <div
                  className="bg-[#14FFA2] h-1"
          style={{
                    width: `${allStats[selectedTab].traders > 0 ? (allStats[selectedTab].buyers / allStats[selectedTab].traders) * 100 : 0}%`,
          }}
        ></div>
      </div>
      <p className="text-[#14FFA2] font-semibold pt-2">
                Buyers: {allStats[selectedTab].buyers.toLocaleString()}
        <span className="float-right text-[#FF329B]">
                  Sellers: {allStats[selectedTab].sellers.toLocaleString()}
        </span>
      </p>
    </div>
  </>
        ) : (
             <div className="text-center text-gray-400 py-10">No data available for this time range.</div>
)}

          {/* Liquidity Table */}
        <p className="text-[#BBBBBB] text-2xl font-semibold pt-4">Liquidity</p>
        <p className="text-[#BBBBBB] text-sm pb-2">
  Available liquidity 
  {loading ? (
    <span className="float-right bg-gray-600 h-4 w-24 rounded animate-pulse inline-block"></span>
  ) : (
    <span className="float-right text-white">{formatLiquidity(totalLiquidity)}</span>
  )}
</p>
<div className="h-full " >
          <div className="h-full overflow-y-auto border-t-2 pt-2 border-white/20 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent ">
    {loading ? (
      // Skeleton while loading
      <div className="space-y-4  h-full">
                {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 bg-gray-600 rounded-full" />
              <div className="space-y-1">
                <div className="w-24 h-3 bg-gray-600 rounded" />
                <div className="w-40 h-2 bg-gray-700 rounded" />
              </div>
            </div>
            <div className="w-16 h-4 bg-gray-600 rounded" />
          </div>
        ))}
      </div>
            ) : liquidityData.length > 0 ? (
                liquidityData.map((pair: TokenPair, index) => (
        <div key={index} className="flex items-center justify-between text-sm pb-3 ">
          <div className="flex items-center space-x-2">
            {pair.exchange?.logo ? (
               <div className="relative w-8 h-8">
               <img src={pair.exchange?.logo} alt={token.name} className="w-8 h-8 rounded-full" />
               {token.baseimage && (
                 <img
                   src={cryptoIcons[token.network.toLowerCase()]}
                   alt="base"
                   className="absolute bottom-[-4px] right-[-4px] w-4 h-4 rounded-full border-2 border-[#141416] shadow-md"
                 />
               )}
             </div>
            ) : (
                        <div className="w-5 h-5 bg-gray-300 rounded-full" /> // Placeholder
            )}
            <div>
                        <p className="text-white">{token?.symbol || 'TOKEN'}/{pair?.blockchain || 'UNKNOWN'}</p>
              <div className="flex items-center space-x-1">
                <strong className="text-[#BBBBBB]">Pair:</strong>
                          <span className="text-[#BBBBBB] truncate max-w-[100px]">
                            {pair?.address ? formatAddress(pair.address) : 'N/A'}
                          </span>
                {pair?.address && (
                  <button
                              onClick={() => copyToClipboard(pair.address!)} // Add non-null assertion
                    className="text-gray-400 hover:text-white"
                    title="Copy address"
                  >
                    {copiedAddress === pair.address ? <FiCheck size={12} /> : <FiCopy size={12} />}
                  </button>
                )}
              </div>
            </div>
          </div>
                    <span className="text-[#BBBBBB]">
                      {formatLiquidity(typeof pair.liquidity === 'number' ? pair.liquidity : pair.liquidity?.usd)}
                    </span>
        </div>
      ))
            ) : (
                <div className="text-center text-gray-400 py-5">No liquidity pairs found.</div>
    )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;