import React, { useState } from "react";
import About from "./About";
import Activity from "./Activity";
import Holders from "./Holder";

// Interface for the token prop (ensure consistency)
interface TokenProp {
  id?: string;
  network?: string;
  symbol?: string;
}

// Interface for component props
interface MenuProps {
  isSettingsOpen: boolean;
  setIsSettingsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  token: TokenProp | null; // Use the defined TokenProp type
}

const Menu: React.FC<MenuProps> = ({ isSettingsOpen, setIsSettingsOpen, token }) => {
  const [activeTab, setActiveTab] = useState<string>("About");
  
  return (
    <div
      className={`p-2 rounded-xl w-full h-full flex flex-col  transition-all duration-300 ${
        isSettingsOpen ? "h-[50px] flex items-center" : "h-auto"
      }`}
    >
      {/* Navigation Tabs (Only show when expanded) */}
      {!isSettingsOpen && (
        <div className="flex items-center justify-between w-full pb-2">
          <div className="flex space-x-4">
            {["About", "Activity", "Holders"].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-4 py-2  ${
                  activeTab === tab ? "font-bold text-white border-b border-white" : "font-semibold text-gray-400"
                }`}
              >
                {tab}
              </button>
            ))}
          </div>
          <button
        onClick={() => setIsSettingsOpen(!isSettingsOpen)}
        className="p-2 rounded-full transition hover:bg-gray-700 self-center"
      >
        {isSettingsOpen ? (
          // Collapsed state SVG
          <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6.295 11.5021C6.29462 11.4363 6.30723 11.371 6.33211 11.3101C6.357 11.2492 6.39366 11.1938 6.44 11.1471L11.585 6.00207L6.44 0.857065C6.35809 0.761354 6.31528 0.63841 6.32014 0.512504C6.32501 0.386597 6.37717 0.267257 6.46622 0.178208C6.55527 0.0891594 6.67464 0.0370118 6.80047 0.0321641C6.92631 0.0273164 7.04935 0.0701545 7.145 0.152065L13 6.00207L7.145 11.8571C7.07481 11.9259 6.98589 11.9725 6.88937 11.991C6.79285 12.0096 6.693 11.9993 6.6023 11.9614C6.51161 11.9235 6.43409 11.8597 6.37943 11.778C6.32477 11.6963 6.29541 11.6004 6.295 11.5021Z" fill="#BBBBBB"/>
          </svg>
        ) : (
          // Expanded state SVG
          <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6.705 0.497934C6.70538 0.563738 6.69277 0.628969 6.66789 0.689888C6.643 0.750807 6.60634 0.806213 6.56 0.852934L1.415 5.99793L6.56 11.1429C6.64191 11.2386 6.68472 11.3616 6.67986 11.4875C6.67499 11.6133 6.62283 11.7327 6.53378 11.8217C6.44473 11.9108 6.32536 11.9629 6.19953 11.9678C6.07369 11.9726 5.95065 11.9298 5.855 11.8479L-2.60967e-07 5.99793L5.855 0.142935C5.92519 0.0741308 6.01411 0.0275419 6.11063 0.00898907C6.20715 -0.00956372 6.307 0.000744544 6.3977 0.0386235C6.48839 0.0765025 6.56591 0.140274 6.62057 0.221966C6.67523 0.303657 6.70459 0.399646 6.705 0.497934Z" fill="#BBBBBB"/>
          </svg>
        )}
      </button>
        </div>
      )}

      {/* Toggle Button (Different SVG for Open/Closed State) */}
      {isSettingsOpen && (
      <button
        onClick={() => setIsSettingsOpen(!isSettingsOpen)}
        className="p-2 rounded-full transition hover:bg-gray-700 self-center"
      >
     
          
          <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6.295 11.5021C6.29462 11.4363 6.30723 11.371 6.33211 11.3101C6.357 11.2492 6.39366 11.1938 6.44 11.1471L11.585 6.00207L6.44 0.857065C6.35809 0.761354 6.31528 0.63841 6.32014 0.512504C6.32501 0.386597 6.37717 0.267257 6.46622 0.178208C6.55527 0.0891594 6.67464 0.0370118 6.80047 0.0321641C6.92631 0.0273164 7.04935 0.0701545 7.145 0.152065L13 6.00207L7.145 11.8571C7.07481 11.9259 6.98589 11.9725 6.88937 11.991C6.79285 12.0096 6.693 11.9993 6.6023 11.9614C6.51161 11.9235 6.43409 11.8597 6.37943 11.778C6.32477 11.6963 6.29541 11.6004 6.295 11.5021Z" fill="#BBBBBB"/>
          </svg>
     
      </button>
         ) }

      {/* Dynamic Section Content (Only show when expanded) */}
      {!isSettingsOpen && (
        <div className="pt-2 overflow-y-auto h-full">
          {activeTab === "About" && <About token={token} />}
          {activeTab === "Activity" && <Activity token={token}  />}
          {activeTab === "Holders" && <Holders token={token} />}
        </div>
      )}
    </div>
  );
};

export default Menu;
