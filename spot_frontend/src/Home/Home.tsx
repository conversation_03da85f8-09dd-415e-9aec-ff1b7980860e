import React, { useState } from "react";
import Connect from "./Connect/Connect";
import Favorites from "./Favorites/Favorites";
import Market from "./Market/Market";
import Menu from "./Menu/Menu";
import Navbar from "./Navbar/Navbar";
import Radar from "./Radar/Radar";

const Home = () => {
  const [activeTable, setActiveTable] = useState<string>("highlight");
  const [selectedNetwork, setSelectedNetwork] = useState<string>("Global");
  const [timeframe, setTimeframe] = useState<string>("24h");
  const [activeStatus, setActiveStatus] = useState<string>("Thriving");

  // Dummy function to satisfy Market prop type
  const handleSelectToken = (token: any) => {
    // This function is likely unused now that Market should use the hook.
    // Keeping it temporarily to resolve the linter error.
    console.log("Token selected (dummy handler):", token);
  };

  return (
    <div className="min-h-screen bg-[#141416]">
      <Navbar />
      <Connect selectedNetwork={selectedNetwork} />
      <Menu 
        setActiveTable={setActiveTable} 
        selectedNetwork={selectedNetwork}
        setSelectedNetwork={setSelectedNetwork}
        onTimeframeChange={setTimeframe}
        activeStatus={activeStatus}
        setActiveStatus={setActiveStatus}
      />
      {activeTable === "highlight" && (
        <Market 
          selectedNetwork={selectedNetwork}
          onSelectToken={handleSelectToken}
        />
      )}
      {activeTable === "radar" && (
        <Radar 
          selectedNetwork={selectedNetwork} 
          timeframe={timeframe}
          activeStatus={activeStatus}
        />
      )}
      {activeTable === "favorites" && <Favorites />}
    </div>
  );
};

export default Home;
