import React, { useState, useEffect } from 'react';
import { fetchCompletedTrades, fetchOrders, fetchTradeHistory } from '../../services/trade-history-service';
import { Trade, Order, TradeHistory } from '../../types/trade-history';
import { Tab } from '@headlessui/react';

interface TradeHistoryPanelProps {
  walletAddress: string;
}

const TradeHistoryPanel: React.FC<TradeHistoryPanelProps> = ({ walletAddress }) => {
  const [trades, setTrades] = useState<Trade[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [history, setHistory] = useState<TradeHistory[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [showByTicker, setShowByTicker] = useState<boolean>(false);
  const [selectedTab, setSelectedTab] = useState<number>(0);

  // Fetch trade data when wallet address changes
  useEffect(() => {
    if (walletAddress) {
      fetchData();
    }
  }, [walletAddress]);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      // Fetch all trade history data in parallel
      const [tradesData, ordersData, historyData] = await Promise.all([
        fetchCompletedTrades(walletAddress),
        fetchOrders(walletAddress),
        fetchTradeHistory(walletAddress)
      ]);

      setTrades(tradesData);
      setOrders(ordersData);
      setHistory(historyData);
    } catch (error) {
      console.error('Error fetching trade history:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return num.toLocaleString('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 2
    });
  };

  const formatNumber = (value: string | number, digits = 4) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return num.toLocaleString('en-US', {
      maximumFractionDigits: digits
    });
  };

  // Render completed trades table
  const renderTradesTable = () => (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-gray-800 text-gray-300">
        <thead>
          <tr className="border-b border-gray-700">
            <th className="px-4 py-3 text-left">Ticker</th>
            <th className="px-4 py-3 text-center">Qty</th>
            <th className="px-4 py-3 text-right">Entry Price</th>
            <th className="px-4 py-3 text-right">Exit Price</th>
            <th className="px-4 py-3 text-right">Entry Value</th>
            <th className="px-4 py-3 text-right">Exit Value</th>
            <th className="px-4 py-3 text-right">Closed P&L</th>
          </tr>
        </thead>
        <tbody>
          {trades.length === 0 ? (
            <tr>
              <td colSpan={7} className="px-4 py-5 text-center text-gray-500">
                No completed trades found
              </td>
            </tr>
          ) : (
            trades.map((trade) => (
              <tr key={trade.id} className="border-b border-gray-700 hover:bg-gray-700/50">
                <td className="px-4 py-3">{trade.ticker}</td>
                <td className="px-4 py-3 text-center">{formatNumber(trade.qty)}</td>
                <td className="px-4 py-3 text-right">{formatCurrency(trade.entry_price)}</td>
                <td className="px-4 py-3 text-right">{formatCurrency(trade.exit_price)}</td>
                <td className="px-4 py-3 text-right">{formatCurrency(trade.entry_value)}</td>
                <td className="px-4 py-3 text-right">{formatCurrency(trade.exit_value)}</td>
                <td className={`px-4 py-3 text-right ${parseFloat(trade.closed_pnl.toString()) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {parseFloat(trade.closed_pnl.toString()) >= 0 ? '+' : ''}{formatCurrency(trade.closed_pnl)}
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );

  // Render pending orders table
  const renderOrdersTable = () => (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-gray-800 text-gray-300">
        <thead>
          <tr className="border-b border-gray-700">
            <th className="px-4 py-3 text-left">Ticker</th>
            <th className="px-4 py-3 text-left">Type</th>
            <th className="px-4 py-3 text-center">Qty</th>
            <th className="px-4 py-3 text-center">Share</th>
            <th className="px-4 py-3 text-right">Value</th>
            <th className="px-4 py-3 text-right">Target Price</th>
            <th className="px-4 py-3 text-right">Target P&L</th>
            <th className="px-4 py-3 text-center">Status</th>
          </tr>
        </thead>
        <tbody>
          {orders.length === 0 ? (
            <tr>
              <td colSpan={8} className="px-4 py-5 text-center text-gray-500">
                No orders found
              </td>
            </tr>
          ) : (
            orders.map((order) => (
              <tr key={order.id} className="border-b border-gray-700 hover:bg-gray-700/50">
                <td className="px-4 py-3">{order.ticker}</td>
                <td className="px-4 py-3">{order.type}</td>
                <td className="px-4 py-3 text-center">{formatNumber(order.qty)}</td>
                <td className="px-4 py-3 text-center">{order.share}%</td>
                <td className="px-4 py-3 text-right">{formatCurrency(order.value)}</td>
                <td className="px-4 py-3 text-right">{formatCurrency(order.target_price)}</td>
                <td className={`px-4 py-3 text-right ${parseFloat(order.target_pnl.toString()) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {parseFloat(order.target_pnl.toString()) >= 0 ? '+' : ''}{formatCurrency(order.target_pnl)}
                </td>
                <td className="px-4 py-3 text-center">
                  <span className={`rounded-full px-2 py-1 text-xs ${
                    order.status === 'Open' ? 'bg-blue-900/50 text-blue-300' : 
                    order.status === 'Filled' ? 'bg-green-900/50 text-green-300' : 
                    'bg-red-900/50 text-red-300'
                  }`}>
                    {order.status}
                  </span>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );

  // Render trade history table
  const renderHistoryTable = () => (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-gray-800 text-gray-300">
        <thead>
          <tr className="border-b border-gray-700">
            <th className="px-4 py-3 text-left">Ticker</th>
            <th className="px-4 py-3 text-left">Type</th>
            <th className="px-4 py-3 text-center">Qty</th>
            <th className="px-4 py-3 text-right">Value</th>
            <th className="px-4 py-3 text-right">Filled Price</th>
            <th className="px-4 py-3 text-center">Date</th>
            <th className="px-4 py-3 text-center">DEX</th>
          </tr>
        </thead>
        <tbody>
          {history.length === 0 ? (
            <tr>
              <td colSpan={7} className="px-4 py-5 text-center text-gray-500">
                No trading history found
              </td>
            </tr>
          ) : (
            history.map((item) => (
              <tr key={item.id} className="border-b border-gray-700 hover:bg-gray-700/50">
                <td className="px-4 py-3">{item.ticker}</td>
                <td className={`px-4 py-3 ${item.trade_type === 'Buy' ? 'text-green-400' : 'text-red-400'}`}>
                  {item.trade_type}
                </td>
                <td className="px-4 py-3 text-center">{formatNumber(item.qty)}</td>
                <td className="px-4 py-3 text-right">{formatCurrency(item.value)}</td>
                <td className="px-4 py-3 text-right">{formatCurrency(item.filled_price)}</td>
                <td className="px-4 py-3 text-center">
                  {new Date(item.timestamp).toLocaleDateString()}
                </td>
                <td className="px-4 py-3 text-center capitalize">
                  {item.dex}
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
  
  return (
    <div className="bg-gray-900 rounded-lg shadow-xl overflow-hidden">
      <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
        <h3 className="text-lg leading-6 font-medium text-white">Trading History</h3>
        <div className="flex items-center space-x-4">
          <button 
            onClick={() => setShowByTicker(!showByTicker)}
            className="px-3 py-1 text-sm bg-gray-800 text-gray-300 rounded hover:bg-gray-700 transition-colors"
          >
            {showByTicker ? 'Show All' : 'Group by Ticker'}
          </button>
          <button 
            onClick={fetchData}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Refresh
          </button>
        </div>
      </div>
      
      <Tab.Group selectedIndex={selectedTab} onChange={setSelectedTab}>
        <Tab.List className="flex border-b border-gray-700">
          <Tab className={({ selected }) => 
            `px-6 py-3 text-sm font-medium focus:outline-none ${
              selected ? 'text-white border-b-2 border-blue-500' : 'text-gray-400 hover:text-gray-300'
            }`
          }>
            Trades
          </Tab>
          <Tab className={({ selected }) => 
            `px-6 py-3 text-sm font-medium focus:outline-none ${
              selected ? 'text-white border-b-2 border-blue-500' : 'text-gray-400 hover:text-gray-300'
            }`
          }>
            Orders
          </Tab>
          <Tab className={({ selected }) => 
            `px-6 py-3 text-sm font-medium focus:outline-none ${
              selected ? 'text-white border-b-2 border-blue-500' : 'text-gray-400 hover:text-gray-300'
            }`
          }>
            History
          </Tab>
        </Tab.List>
        
        <Tab.Panels>
          <Tab.Panel>
            {isLoading ? (
              <div className="flex justify-center items-center py-20">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              renderTradesTable()
            )}
          </Tab.Panel>
          <Tab.Panel>
            {isLoading ? (
              <div className="flex justify-center items-center py-20">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              renderOrdersTable()
            )}
          </Tab.Panel>
          <Tab.Panel>
            {isLoading ? (
              <div className="flex justify-center items-center py-20">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              renderHistoryTable()
            )}
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
    </div>
  );
};

export default TradeHistoryPanel;
