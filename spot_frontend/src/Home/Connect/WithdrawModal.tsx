import React, { useState, useEffect, useCallback, useRef } from "react";
import { IoClose } from "react-icons/io5";
import { FaChevronDown } from "react-icons/fa";
import { usePrivy, useWallets, ConnectedWallet, WalletWithMetadata } from "@privy-io/react-auth";
import CustomWalletSelector from "../TradingPanel/CustomWalletSelector";
import { walletAPI } from "../../utils/api";
import { ethers } from "ethers";
import bs58 from 'bs58';
import { 
    Connection, 
    PublicKey, 
    SystemProgram, 
    Transaction, 
    LAMPORTS_PER_SOL,
    clusterApiUrl 
} from '@solana/web3.js';
import { getAssociatedTokenAddress, createTransferInstruction, createAssociatedTokenAccountInstruction } from '@solana/spl-token'; // Added spl-token imports
import { useSolanaWallets } from '@privy-io/react-auth/solana';

const cryptoIcons: Record<string, JSX.Element> = {
  ETH: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2L3 12L12 17L21 12L12 2Z" fill="#627EEA" />
      <path d="M12 17L3 12L12 22L21 12L12 17Z" fill="#3C3C3B" />
    </svg>
  ),
  BTC: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="10" fill="#F7931A" />
      <path d="M9 8H15V16H9V8Z" fill="white" />
    </svg>
  ),
  SOL: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="24" height="24" rx="4" fill="#00FFA3" />
      <path d="M4 8H20L16 12H8L4 8Z" fill="#9945FF" />
      <path d="M4 12H20L16 16H8L4 12Z" fill="#19FB9B" />
    </svg>
  ),
  USDT: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="10" fill="#50AF95"/>
      <path d="M12 5V19M10 5H14M10 19H14M7 12H17" stroke="white" strokeWidth="1.5"/>
    </svg>
  ),
  USDC: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
       <circle cx="12" cy="12" r="10" fill="#2775CA"/>
       <path fillRule="evenodd" clipRule="evenodd" d="M12 5.5C8.41015 5.5 5.5 8.41015 5.5 12C5.5 15.5899 8.41015 18.5 12 18.5C15.5899 18.5 18.5 15.5899 18.5 12C18.5 8.41015 15.5899 5.5 12 5.5ZM12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16ZM14 12C14 13.1046 13.1046 14 12 14C10.8954 14 10 13.1046 10 12C10 10.8954 10.8954 10 12 10C13.1046 10 14 10.8954 14 12Z" fill="white"/>
    </svg>
  ),
  DEFAULT: (
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="10" fill="#4B5563" />
          <path d="M12 16.5C14.4853 16.5 16.5 14.4853 16.5 12C16.5 9.51472 14.4853 7.5 12 7.5C9.51472 7.5 7.5 9.51472 7.5 12C7.5 14.4853 9.51472 16.5 12 16.5Z" stroke="white" strokeWidth="1.5"/>
          <path d="M12 4.5V7.5M12 16.5V19.5M19.5 12H16.5M7.5 12H4.5" stroke="white" strokeWidth="1.5" strokeLinecap="round"/>
      </svg>
  )
};

// --- Minimal ERC20 ABI for transfer --- 
const erc20Abi = [
  "function transfer(address to, uint256 amount) returns (bool)"
];
// -------------------------------------

// --- Platform Fee Configuration --- 
const PLATFORM_FEE_PERCENTAGE = 0.001; // 0.1% fee
// ----------------------------------

interface WithdrawModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface TokenBalance {
    symbol: string;
    balance: string;
    decimals: number;
    chain: string;
    tokenAddress?: string;
}

interface SelectorWalletBase {
  id?: string;
  address?: string;
  walletClientType?: string;
  type?: string;
  chainName?: string;
  connectedAt?: Date | string | number;
  ethersProvider?: any;
  provider?: any;
  sendTransaction?: any;
  embedded?: boolean;
}

// Helper function to validate addresses based on chain type
const isValidAddress = (address: string, chainType: string | undefined): boolean => {
  if (!chainType) return false;
  
  if (chainType === 'Ethereum' || chainType === 'BSC') {
      // EVM validation
      return ethers.utils.isAddress(address);
  } else if (chainType === 'Solana') {
      // Solana validation (basic)
      if (!address || typeof address !== 'string' || address.length < 32 || address.length > 44) {
          return false;
      }
      // Try decoding base58 - throws error if invalid
      try {
          const decoded = bs58.decode(address);
          // Solana addresses are typically 32 bytes long
          return decoded.length === 32; 
      } catch (e) {
          return false;
      }
  }
  return false; // Unsupported chain
};

// Add a custom interface for the LinkedAccount objects
interface PrivyLinkedAccount {
  type: string;
  address?: string;
  connectorType?: string;
  verifiedAt?: string | Date;
  [key: string]: any;
}

const WithdrawModal: React.FC<WithdrawModalProps> = ({ isOpen, onClose }) => {
  const [selectedCrypto, setSelectedCrypto] = useState<string | null>("ETH");
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  const [amount, setAmount] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [balanceLoading, setBalanceLoading] = useState<boolean>(false);
  const [withdrawAddress, setWithdrawAddress] = useState<string>("");
  const [message, setMessage] = useState<string>("");
  const { wallets } = useWallets();
  const { wallets: solanaWallets } = useSolanaWallets();
  const { ready, authenticated, user, connectWallet } = usePrivy();

  const [selectedWallet, setSelectedWallet] = useState<ConnectedWallet | null>(null);
  const [walletBalances, setWalletBalances] = useState<Record<string, TokenBalance>>({});

  // Add state to display fee info (optional but recommended)
  const [calculatedFee, setCalculatedFee] = useState<string | null>(null);
  const [netAmountToSend, setNetAmountToSend] = useState<string | null>(null);

  // Add a state to store available wallets
  const [availableWallets, setAvailableWallets] = useState<any[]>([]);
  
  // Add refs to track component mount state and fetching status
  const isMountedRef = useRef(true);
  const fetchingBalancesRef = useRef(false);

  const rpcEndpoint = 'https://multi-alpha-general.solana-mainnet.quiknode.pro/5bdc85a008d8f5790e2b33cf11aac95de7ac399e/';

  // Track component mounted state
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    if (wallets && wallets.length > 0 && !selectedWallet) {
      console.log("WithdrawModal: Auto-selecting first wallet:", wallets[0].address);
      setSelectedWallet(wallets[0]);
    }
    if (selectedWallet && (!wallets || wallets.length === 0)) {
        console.log("WithdrawModal: Wallets empty, resetting selected wallet.");
        setSelectedWallet(null);
        setWalletBalances({});
    }
  }, [wallets, selectedWallet]);

  useEffect(() => {
    const fetchBalances = async () => {
      if (!selectedWallet || !selectedWallet.address) {
        setWalletBalances({});
        setSelectedCrypto(null);
        return;
      }
      
      // Don't run if we're already fetching balances
      if (fetchingBalancesRef.current) {
        console.log("WithdrawModal: Already fetching balances, skipping duplicate request");
        return;
      }

      console.log(`WithdrawModal: Fetching balances for ${selectedWallet.address}`);
      setBalanceLoading(true);
      fetchingBalancesRef.current = true;
      
      try {
        // Use local storage to implement a simple debounce mechanism
        const lastFetchTime = Number(localStorage.getItem('withdraw_balance_fetch_time') || '0');
        const now = Date.now();
        
        // If we've fetched within the last 2 seconds, don't fetch again
        if (now - lastFetchTime < 2000) {
          console.log("WithdrawModal: Skipping duplicate balance fetch (debounced)");
          if (isMountedRef.current) {
            setBalanceLoading(false);
          }
          fetchingBalancesRef.current = false;
          return;
        }
        
        // Mark this fetch time
        localStorage.setItem('withdraw_balance_fetch_time', now.toString());
        
        if (!isMountedRef.current) {
          console.log("WithdrawModal: Component unmounted, canceling balance fetch");
          fetchingBalancesRef.current = false;
          return;
        }
        
        setMessage("");
        setWalletBalances({});
        setSelectedCrypto(null);
        
        const response = await walletAPI.getAllBalances(selectedWallet.address);
        
        if (!isMountedRef.current) {
          console.log("WithdrawModal: Component unmounted after API call, skipping state updates");
          fetchingBalancesRef.current = false;
          return;
        }
        
        console.log("WithdrawModal API Response:", JSON.stringify(response, null, 2));

        if (response.success && response.data) {
          const balances: Record<string, TokenBalance> = {};
          
          const balanceData = response.data?.data?.balances;
          console.log("WithdrawModal Balance Data to process:", balanceData);

          const processChain = (chainName: string, tokens: any[]) => {
              console.log(`Processing chain: ${chainName}`, tokens);
              if (Array.isArray(tokens)) {
                  tokens.forEach(token => {
                      if (token.symbol) {
                           const upperSymbol = token.symbol.toUpperCase();
                           console.log(` -> For ${token.symbol}, found tokenAddress: ${token.tokenAddress}`); 
                           balances[upperSymbol] = {
                              symbol: token.symbol,
                              balance: token.balance || "0",
                              decimals: token.decimals || (chainName === 'Solana' ? 9 : 18),
                              chain: chainName,
                              tokenAddress: token.tokenAddress
                           };
                      } else {
                          console.warn("Token found without symbol:", token);
                      }
                  });
              } else {
                  console.log(`No tokens array found for chain: ${chainName}`);
              }
          };
          
          // More flexible processing for all response structures
          if (balanceData) {
              // Process each chain's data if present
              if (balanceData.ethereum && Array.isArray(balanceData.ethereum)) {
                  processChain('Ethereum', balanceData.ethereum);
              }
              if (balanceData.bsc && Array.isArray(balanceData.bsc)) {
                  processChain('BSC', balanceData.bsc);
              }
              if (balanceData.solana && Array.isArray(balanceData.solana)) {
                  processChain('Solana', balanceData.solana);
              }
              
              // Handle case where balanceData itself is an array
              if (Array.isArray(balanceData)) {
                  processChain('Unknown', balanceData);
              }
          } else if (response.data?.data && Array.isArray(response.data.data)) {
              // Handle alternate structure with tokens directly in data array
              processChain('Unknown', response.data.data);
          } else if (response.data && Array.isArray(response.data)) {
              // Handle case where tokens are directly in response.data
              processChain('Unknown', response.data);
          } else {
              console.warn("WithdrawModal: Could not find valid balances structure in response");
              console.log("WithdrawModal: Response structure:", response.data);
          }

          console.log("WithdrawModal: Processed balances object:", balances);
          
          if (isMountedRef.current) {
            setWalletBalances(balances);
          }
        } else {
          console.error("WithdrawModal: Failed to fetch balances", response.message);
          setMessage(`Error fetching balances: ${response.message || 'Unknown error'}`);
          setWalletBalances({});
        }
      } catch (error) {
        console.error("WithdrawModal: Exception fetching balances:", error);
        setMessage(`Error fetching balances: ${error instanceof Error ? error.message : 'Network error'}`);
        setWalletBalances({});
      } finally {
        setBalanceLoading(false);
        fetchingBalancesRef.current = false;
      }
    };

    // Use a small delay before running the fetch to avoid rapid changes
    const timeoutId = setTimeout(() => {
      fetchBalances();
    }, 500);
    
    // Clean up timeout on unmount or when deps change
    return () => {
      clearTimeout(timeoutId);
    };
  }, [selectedWallet?.address]); // Only depend on address change, not the entire wallet object

  useEffect(() => {
      if (!balanceLoading && Object.keys(walletBalances).length > 0 && !selectedCrypto) {
          const defaultCrypto = walletBalances["ETH"] ? "ETH" : Object.keys(walletBalances)[0];
          console.log("WithdrawModal: Auto-selecting crypto:", defaultCrypto);
          setSelectedCrypto(defaultCrypto);
      }
      else if (!balanceLoading && selectedCrypto && !walletBalances[selectedCrypto]) {
          console.log(`WithdrawModal: Selected crypto ${selectedCrypto} not found in balances, resetting.`);
          const defaultCrypto = walletBalances["ETH"] ? "ETH" : Object.keys(walletBalances)[0] ?? null;
          setSelectedCrypto(defaultCrypto);
      }
  }, [balanceLoading, walletBalances, selectedCrypto]);

  // Recalculate fee info when amount or selectedCrypto changes
  useEffect(() => {
    if (selectedCrypto && amount) {
      const numericAmount = Number(amount);
      if (!isNaN(numericAmount) && numericAmount > 0) {
        const fee = numericAmount * PLATFORM_FEE_PERCENTAGE;
        const net = numericAmount - fee;
        if (net > 0) {
          setCalculatedFee(fee.toLocaleString(undefined, { maximumFractionDigits: 8 }));
          setNetAmountToSend(net.toLocaleString(undefined, { maximumFractionDigits: 8 }));
          return; // Exit early if calculated
        }
      }
    }
    // Reset if amount/crypto is invalid or cleared
    setCalculatedFee(null);
    setNetAmountToSend(null);
  }, [amount, selectedCrypto]);

  const handleSelectWallet = (wallet: any) => {
    console.log("WithdrawModal: Selected wallet:", wallet);
    setSelectedWallet(wallet);
    setWalletBalances({});
    setSelectedCrypto(null);
    setAmount("");
  };

  useEffect(() => {
    // If we have Solana wallets but no selected wallet, use one of those
    if (solanaWallets && solanaWallets.length > 0 && !selectedWallet) {
      console.log("WithdrawModal: Found Solana wallets:", solanaWallets.length);
      setSelectedWallet(solanaWallets[0] as unknown as ConnectedWallet);
    }
  }, [solanaWallets, selectedWallet]);

  const getCurrentBalance = (): string => {
      if (balanceLoading) return "Loading...";
      if (!selectedCrypto) return "0";
      const balanceInfo = walletBalances[selectedCrypto.toUpperCase()];
      if (balanceInfo) {
          try {
              return parseFloat(balanceInfo.balance).toLocaleString(undefined, {maximumFractionDigits: 6});
          } catch {
              return balanceInfo.balance;
          }
      }
      return "0";
  };

  const handleWithdraw = async () => {
    setMessage(""); 

    // --- 1. Basic Checks --- 
    if (!selectedCrypto) {
        setMessage("Please select an asset to withdraw");
        return;
    }
    if (!amount || !selectedWallet || !withdrawAddress) {
      setMessage("Please fill in all fields");
      return;
    }

    // --- 2. Amount Validation --- 
    const numericAmount = Number(amount);
    if (isNaN(numericAmount) || numericAmount <= 0) {
        setMessage("Please enter a valid positive amount");
        return;
    }

    // --- Re-fetch balanceInfo inside handler for freshness --- 
    const upperCrypto = selectedCrypto.toUpperCase();
    const balanceInfo = walletBalances[upperCrypto]; // Get potentially stale info first for checks

    if (!balanceInfo) {
        setMessage(`Could not find balance info for ${selectedCrypto}. Please refresh.`);
        return;
    }
    // ----------------------------------------------------

    const currentBalance = parseFloat(balanceInfo.balance); // Use potentially stale balance for this check
    if (isNaN(currentBalance)) {
        setMessage(`Could not read balance for ${selectedCrypto}. Please refresh.`);
        return;
    }
    if (numericAmount > currentBalance) {
        setMessage(`Insufficient ${selectedCrypto} balance (${currentBalance.toLocaleString()})`);
         return;
     }
     
    // --- Calculate Fee and Net Amount --- 
    const feeAmount = numericAmount * PLATFORM_FEE_PERCENTAGE;
    const amountToSend = numericAmount - feeAmount;

    if (amountToSend <= 0) {
        setMessage("Amount too small after fee deduction.");
        return;
    }
    console.log(`WithdrawModal: Fee Calculation - Original: ${numericAmount}, Fee: ${feeAmount}, Net Amount: ${amountToSend}`);
    // -------------------------------------

    // --- 3. Address Validation --- 
    const selectedChainType = balanceInfo.chain; // Use potentially stale chain for validation
    if (!isValidAddress(withdrawAddress, selectedChainType)) {
        setMessage(`Invalid ${selectedChainType || 'destination'} address format.`);
        return;
    }

    // --- Re-add targetChainId definition needed for EVM path --- 
    const targetChainId = selectedChainType === 'BSC' ? 56 : 
                           selectedChainType === 'Ethereum' ? 1 : 
                           null;
    // --- Add back EVM capability check (using fullWallet from useWallets)
    if (targetChainId) {
        const evmWallet = wallets.find(w => w.address === selectedWallet.address);
        if (!evmWallet || typeof evmWallet.switchChain !== 'function' || typeof evmWallet.getEthereumProvider !== 'function') {
             throw new Error("Selected EVM wallet does not have switching/provider capabilities or wasn't found in useWallets.");
        }
    }
    // --------------------------------------------------------

    // --- If basic validations pass, proceed --- 
    console.log("Withdrawal validation passed. Proceeding...");
    setLoading(true);
    setMessage(""); // Clear previous messages
    
    try {
      let txHash: string | null = null; // Declare txHash here

      // --- Use the potentially refreshed balanceInfo from here --- 
      const currentBalanceInfo = walletBalances[selectedCrypto.toUpperCase()]; // Re-access state value
      if (!currentBalanceInfo) { // Double check it still exists
          throw new Error(`Balance information for ${selectedCrypto} disappeared.`);
      }
      
      // ================== SOLANA WITHDRAWAL LOGIC ==================
      if (selectedChainType === 'Solana') {
            setMessage("Preparing Solana transaction...");
            
            // --- Solana Specific Setup ---
            // Use QuickNode Solana RPC endpoint (HTTP only, no WebSocket)
            const connection = new Connection(
                rpcEndpoint,
                {
                    commitment: 'confirmed',
                    // Note: WebSocket subscriptions will not work with this endpoint  
                }
            ); 
            const senderPublicKey = new PublicKey(selectedWallet.address); 
            const recipientPublicKey = new PublicKey(withdrawAddress);
            const decimals = currentBalanceInfo.decimals || 9; // Default to 9 for Solana if undefined
            
            // ** Use amountToSend for Lamport conversion **
            const amountInLamports = BigInt(Math.floor(amountToSend * Math.pow(10, decimals)));
            
            if (amountInLamports <= 0) {
                throw new Error("Calculated amount to send is zero or negative.");
            }

            console.log(`WithdrawModal [SOL]: Prepared - Sender: ${senderPublicKey.toBase58()}, Recipient: ${recipientPublicKey.toBase58()}, Amount (lamports): ${amountInLamports}`);

            // --- Build Solana Transaction ---
            const transaction = new Transaction();
            const tokenAddr = currentBalanceInfo.tokenAddress;
            // Special case: Detect Wrapped SOL token address and treat it as native SOL
            const isWrappedSolToken = tokenAddr === 'So11111111111111111111111111111111111111112';
            
            if (tokenAddr === 'native' || !tokenAddr || isWrappedSolToken) { // Native SOL Transfer
                console.log("WithdrawModal [SOL]: Creating native SOL transfer instruction.");
                transaction.add(
                    SystemProgram.transfer({
                        fromPubkey: senderPublicKey,
                        toPubkey: recipientPublicKey,
                        lamports: amountInLamports,
                    })
                );
                setMessage("Sending native SOL, please approve...");

            } else { // SPL Token Transfer
                 console.log(`WithdrawModal [SOL]: Creating SPL token transfer instruction for ${tokenAddr}.`);
                 const mintPublicKey = new PublicKey(tokenAddr);

                 // Get Associated Token Accounts
                 const senderTokenAccount = await getAssociatedTokenAddress(
                     mintPublicKey,
                     senderPublicKey
                 );
                 const recipientTokenAccount = await getAssociatedTokenAddress(
                     mintPublicKey,
                     recipientPublicKey
                 );

                 console.log(`WithdrawModal [SOL]: Sender ATA: ${senderTokenAccount.toBase58()}, Recipient ATA: ${recipientTokenAccount.toBase58()}`);
                 
                 // Check if SENDER token account exists and create if needed
                 const senderAccountInfo = await connection.getAccountInfo(senderTokenAccount);
                 if (!senderAccountInfo) {
                     console.log("WithdrawModal [SOL]: Sender ATA doesn't exist, adding create instruction");
                     transaction.add(
                         createAssociatedTokenAccountInstruction(
                             senderPublicKey, // Payer
                             senderTokenAccount,
                             senderPublicKey,
                             mintPublicKey
                         )
                     );
                     console.log("WithdrawModal [SOL]: Added instruction to create sender ATA.");
                     
                     // If we're creating the sender's token account in this transaction, 
                     // we can't also transfer tokens in the same transaction because the account won't have any tokens yet
                     setMessage("Creating your token account. Please approve this transaction first, then try your withdrawal again.");
                     
                     // Early return - just create the token account for now
                     // Skip adding the transfer instruction
                     return;
                 }
                 
                 // Check if RECIPIENT token account exists and create if needed
                 const recipientAccountInfo = await connection.getAccountInfo(recipientTokenAccount);
                 if (!recipientAccountInfo) {
                     console.log("WithdrawModal [SOL]: Recipient ATA doesn't exist, adding create instruction");
                     transaction.add(
                         createAssociatedTokenAccountInstruction(
                             senderPublicKey, // Payer
                             recipientTokenAccount,
                             recipientPublicKey,
                             mintPublicKey
                         )
                     );
                     console.log("WithdrawModal [SOL]: Added instruction to create recipient ATA.");
                 }

                 // Important: For SPL tokens, we need to convert BigInt to Number 
                 // Use Number() and handle potential precision issues
                 const transferAmount = Number(amountInLamports.toString());
                 console.log(`WithdrawModal [SOL]: Creating transfer instruction with amount: ${transferAmount}`);
                 
                 // Ensure the sender token account has sufficient balance
                 try {
                     const senderTokenInfo = await connection.getTokenAccountBalance(senderTokenAccount);
                     console.log(`WithdrawModal [SOL]: Sender token balance: ${senderTokenInfo.value.uiAmount} ${senderTokenInfo.value.uiAmountString}`);
                     
                     // Check if the balance is sufficient
                     if (!senderTokenInfo.value || !senderTokenInfo.value.uiAmount || senderTokenInfo.value.uiAmount < (amountToSend)) {
                         const currentBalance = senderTokenInfo.value?.uiAmountString || '0';
                         throw new Error(`Insufficient balance. You have ${currentBalance} tokens but are trying to send ${amountToSend}.`);
                     }
                 } catch (balanceError) {
                     if (balanceError instanceof Error) {
                         throw new Error(`Failed to check token balance: ${balanceError.message}`);
                     } else {
                         throw new Error(`Source token account ${senderTokenAccount.toBase58()} not found or has no balance.`);
                     }
                 }
                 
                 // Create the transfer instruction with explicit empty multiSigners array
                 transaction.add(
                    createTransferInstruction(
                        senderTokenAccount,         // source
                        recipientTokenAccount,      // destination
                        senderPublicKey,            // owner of the source account
                        transferAmount,             // amount as a Number (not BigInt)
                        []                          // multiSigners (empty array, not undefined)
                    )
                );
                setMessage("Sending SPL token, please approve...");
            }
            
            // --- Sign and Send Transaction ---
            try {
                 // Get recent blockhash
                 setMessage("Fetching recent blockhash...");
                 const { blockhash } = await connection.getLatestBlockhash('confirmed');
                 transaction.recentBlockhash = blockhash;
                 transaction.feePayer = senderPublicKey;
                 
                 console.log("WithdrawModal [SOL]: Transaction built:", transaction);
                 
                 setMessage("Requesting transaction approval from Privy...");
                 
                 // --- Find the specific Solana wallet object from the hook ---
                 const activeSolanaWallet = solanaWallets.find(w => w.address === selectedWallet.address);
                 if (!activeSolanaWallet) {
                    throw new Error(`Could not find active Solana wallet object for address ${selectedWallet.address} in useSolanaWallets list.`);
                 }
                 
                 // --- Check for signTransaction method (as per Privy docs) ---
                 if (typeof activeSolanaWallet.sendTransaction !== 'function') {
                    throw new Error("Privy Solana wallet object does not have expected 'sendTransaction' method.");
                 }

                 console.log("WithdrawModal [SOL]: Transaction built:", transaction);
                 
                 // --- Use sendTransaction which should handle signing and sending + prompt --- 
                 const signature = await activeSolanaWallet.sendTransaction(transaction, connection); 
                 // Note: Including connection might still be needed or might cause issues, test carefully.
                 // If errors occur, try await activeSolanaWallet.sendTransaction(transaction);
                  
                 console.log(`WithdrawModal [SOL]: Transaction submitted with signature: ${signature}`);
                 // --- HTTP only, no WebSocket so can't wait for confirmation
                 txHash = signature; 
                 setMessage(`Transaction sent. Signature: ${signature}`);
 
                 // --- Check transaction status with getSignatureStatus
                 let confirmationAttempts = 0;
                 const checkConfirmationStatus = async () => {
                     confirmationAttempts++;
                     console.log(`Checking transaction status, attempt ${confirmationAttempts}...`);
                     const { value: status } = await connection.getSignatureStatus(signature);
                     
                     if (status?.confirmationStatus === 'confirmed' || status?.confirmationStatus === 'finalized') {
                         setMessage(`Transaction confirmed. Signature: ${signature}`);
                         setLoading(false);
                         setTimeout(() => onClose(), 1000); 
                     } 
                     else if (confirmationAttempts >= 5) {
                         setMessage(`Transaction sent but not confirmed after 5 checks. Signature: ${signature}`);
                         setLoading(false);
                     }
                     else {
                         setTimeout(checkConfirmationStatus, 1000);
                     }
                 };
                 checkConfirmationStatus();
                 // ----------------------------------------------

            } catch (solError) {
                 console.error("WithdrawModal [SOL]: Error signing/sending:", solError);
                 let solErrorMessage = "Failed to send Solana transaction";
                 if (solError instanceof Error) solErrorMessage = solError.message;
                 // Check for common user rejection patterns in Solana errors
                 if (solErrorMessage.toLowerCase().includes('user rejected') || solErrorMessage.toLowerCase().includes('cancelled')) {
                     setMessage('Error: The user rejected the request');
                 } else {
                     setMessage(`Error: ${solErrorMessage}`);
                 }
                 // --- Stop execution after Solana error --- 
                 setLoading(false);
                 return; 
                 // -----------------------------------------
            }
            
      // ================== EVM WITHDRAWAL LOGIC ==================
      } else if (targetChainId) { 
          setMessage(`Requesting network switch to ${currentBalanceInfo.chain}...`);
          try {
              if (typeof selectedWallet.switchChain !== 'function') {
                  throw new Error("Wallet does not support switching networks.");
              }
              await selectedWallet.switchChain(targetChainId);
              console.log(`WithdrawModal: Switched to chain ${targetChainId}`);
          } catch (switchError) {
              console.error("WithdrawModal: Failed to switch network:", switchError);
              setMessage(`Failed to switch network: ${switchError instanceof Error ? switchError.message : 'User rejected'}`);
              setLoading(false);
              return;
          }
      }

      // --- Provider Check & Wrapping --- 
      setMessage("Getting wallet provider...");
      let ethersProvider; // Use a different name for the ethers provider
      try {
          if (typeof selectedWallet.getEthereumProvider !== 'function') {
              throw new Error("Selected wallet does not support Ethereum provider.");
          }
          const rawProvider = await selectedWallet.getEthereumProvider(); 
          if (!rawProvider) throw new Error("Ethereum provider not available after switching chain.");
          
          // Wrap the raw provider with ethers
          ethersProvider = new ethers.providers.Web3Provider(rawProvider);

      } catch (e) {
          console.error("Error getting/wrapping provider:", e)
          setMessage(`Failed to get provider: ${e instanceof Error ? e.message : 'Unknown wallet issue'}`);
          setLoading(false);
          return;
      }

      // --- Transaction Logic (using currentBalanceInfo) --- 
      try {
        const decimals = currentBalanceInfo.decimals || 18;
        // ** Use amountToSend for Wei conversion **
        const valueInWeiToSend = ethers.utils.parseUnits(amountToSend.toString(), decimals);

        setMessage("Preparing transaction...");

        // Log the object right before accessing tokenAddress
        console.log("WithdrawModal: Checking tokenAddr. currentBalanceInfo:", JSON.stringify(currentBalanceInfo, null, 2));

        const tokenAddr = currentBalanceInfo.tokenAddress; 
        if (tokenAddr === 'native') {
            // --- Native Currency Transfer --- 
          const txParams = {
            to: withdrawAddress,
              from: selectedWallet.address,
              value: valueInWeiToSend.toHexString(), // Use net amount
            };
            setMessage("Sending native token, please approve...");
            // Use ethersProvider.send directly
            txHash = await ethersProvider.send('eth_sendTransaction', [txParams]);
        } else if (tokenAddr && typeof tokenAddr === 'string') {
            // --- ERC20 Token Transfer --- 
            const signer = ethersProvider.getSigner(); 
            const tokenContract = new ethers.Contract(
                tokenAddr, 
                erc20Abi, 
                signer 
            );
          
            // --- Log values right before transfer --- 
            const currentDecimals = currentBalanceInfo.decimals || 18;
            const valueToParse = amount;
            const parsedValue = ethers.utils.parseUnits(valueToParse, currentDecimals);
            console.log(`WithdrawModal: Attempting ERC20 Transfer`, { 
                 amountOriginal: amount, // Log original requested amount
                 amountToSend: amountToSend.toString(), // Log net amount
                 feeAmount: feeAmount.toString(), // Log fee amount
                 decimals: currentDecimals, 
                 valueInWeiToSend: valueInWeiToSend.toString(), // Log net value in wei
                 recipient: withdrawAddress, 
                 tokenAddress: tokenAddr 
             });
            // ---------------------------------------
            
            setMessage("Sending ERC20 token, please approve...");
            // Add a gasLimit option to the transfer call
            const txOptions = { gasLimit: 100000 }; // Example: 100,000 gas limit, adjust if needed
            const tx = await tokenContract.transfer(withdrawAddress, valueInWeiToSend, txOptions); 
            setMessage("Waiting for ERC20 transaction confirmation...");
            const receipt = await tx.wait(); 
            txHash = receipt.transactionHash;
        } else {
            console.error("BalanceInfo causing error (logged above):", currentBalanceInfo);
            throw new Error(`Invalid or missing token address for ${selectedCrypto}`);
        }

        // --- Post-Transaction --- 
        if (txHash) {
          // --- Updated Success Message --- 
          const explorerUrl = selectedChainType === 'Solana' 
            ? `https://explorer.solana.com/tx/${txHash}?cluster=mainnet`
            : targetChainId === 1 
              ? `https://etherscan.io/tx/${txHash}`
              : targetChainId === 56
                ? `https://bscscan.com/tx/${txHash}`
                : null;
          
          const shortHash = txHash.length > 60 ? txHash.substring(0, 10) + '...' + txHash.substring(txHash.length - 10) : txHash;
          setMessage(`Tx Submitted: ${shortHash}` + (explorerUrl ? `` : '')); // Keep message concise, maybe add link separately
          console.log(`WithdrawModal: Transaction hash/signature: ${txHash}`);
          if (explorerUrl) console.log(`View on explorer: ${explorerUrl}`);
          // -------------------------

          // TODO: Maybe refresh balance after success?
          // Consider fetching balances again here
          setTimeout(() => onClose(), 4000); // Keep timeout to close modal
        } else {
            // Check if still loading, if not, means error message was set
            if (!loading) {
                console.warn("Withdrawal process ended without a txHash and without error being caught in inner try block.");
                // setMessage might already be set by Solana error handler
            } else {
                // This case might happen if solError handling fails or logic error
                setMessage("Transaction sent but no hash received.");
                console.warn("Transaction potentially sent but txHash is null/undefined");
            }
        }
        // -----------------------

        } catch (error) {
        console.error(`WithdrawModal: Error sending ${selectedCrypto}:`, error);
        const errorMessage = error instanceof Error ? error.message : "Failed to send transaction";
        let detailedError = errorMessage;
        if (typeof error === 'object' && error !== null && 'message' in error) {
            detailedError = String(error.message);
      }
        // Check for common user rejection error code
        if (detailedError.includes('rejected') || (error as any)?.code === 4001) {
            setMessage('Error: The user rejected the request');
        } else {
           setMessage(`Error: ${detailedError}`);
      }
      }
      // ---------------------------------------------

    } catch (error) {
      console.error('WithdrawModal: Error during withdrawal process:', error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      setMessage(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Update the useEffect to populate available wallets
  useEffect(() => {
    if (authenticated && ready && user?.linkedAccounts) {
      console.log("WithdrawModal: Processing user linkedAccounts");
      
      // Type assertion for the entire array
      const typedAccounts = user.linkedAccounts as unknown as PrivyLinkedAccount[];
      const walletsList: any[] = [];
      
      // Extract all smart wallets
      const smartWallets = typedAccounts.filter(account => 
        account.type === 'smart_wallet' && account.address
      );
      
      // Extract all Solana wallets (addresses not starting with 0x)
      const solanaWallets = typedAccounts.filter(account => 
        account.type === 'wallet' && 
        account.address && 
        !account.address.startsWith('0x')
      );
      
      // Add smart wallets to the list
      smartWallets.forEach(wallet => {
        console.log("WithdrawModal: Found smart wallet:", wallet.address);
        walletsList.push({
          address: wallet.address,
          walletClientType: 'smart',
          type: 'smart_wallet',
          connectedAt: new Date().toISOString()
        });
      });
      
      // Add Solana wallets to the list
      solanaWallets.forEach(wallet => {
        console.log("WithdrawModal: Found Solana wallet:", wallet.address);
        walletsList.push({
          address: wallet.address,
          walletClientType: wallet.connectorType || 'external',
          type: 'solana',
          connectedAt: new Date().toISOString()
        });
      });
      
      // Update available wallets list
      console.log("WithdrawModal: Setting available wallets:", walletsList);
      setAvailableWallets(walletsList);
      
      // Select a wallet if one is available and none is currently selected
      if (!selectedWallet && walletsList.length > 0) {
        setSelectedWallet(walletsList[0] as unknown as ConnectedWallet);
      }
    }
  }, [authenticated, ready, user?.linkedAccounts, selectedWallet]);

  if (!isOpen) return null;

  const availableTokens = Object.values(walletBalances);

  return (
    <div className="fixed inset-0 flex items-center justify-center p-4 z-50">
      <div 
        className="fixed inset-0 bg-black bg-opacity-70" 
        onClick={onClose}
        style={{ backdropFilter: 'blur(3px)' }}
      />
      
      <div className="bg-[#141416] rounded-2xl p-6 w-full max-w-md relative z-50">
        <button
          onClick={onClose}
          className="absolute right-6 top-6 text-white hover:text-gray-300"
        >
          <IoClose size={24} />
        </button>

        <h2 className="text-white text-2xl font-bold mb-6">Withdraw</h2>

        <div className="mb-6">
          <p className="text-[#BBBBBB] pb-2 text-lg">From Wallet</p>
          {authenticated ? (
            <CustomWalletSelector 
              selectedWallet={selectedWallet as any}
              onSelectWallet={(wallet) => handleSelectWallet(wallet)}
              onConnectWallet={connectWallet}
              wallets={availableWallets}
            />
          ) : (
            <button
              onClick={connectWallet}
              className="w-full bg-[#14FFA2] text-black font-semibold py-3 rounded-lg hover:opacity-90 transition-opacity">
              Connect Wallet
            </button>
          )}
        </div>

        <p className="text-[#BBBBBB] pb-3 text-xl mt-6">Select Asset</p>
        <div
          className="mt-2 bg-[#1D2226] p-4 rounded-lg flex justify-between items-center cursor-pointer relative w-full"
          onClick={() => setDropdownOpen(!dropdownOpen)}
        >
          {selectedCrypto ? (
          <div className="flex items-center gap-3">
              {cryptoIcons[selectedCrypto.toUpperCase() as keyof typeof cryptoIcons] || cryptoIcons.DEFAULT}
            <span className="text-white text-lg">{selectedCrypto}</span>
          </div>
           ) : (
             <span className="text-gray-400 text-lg">{balanceLoading ? 'Loading Tokens...' : 'Select Token'}</span>
           )}
          <FaChevronDown className={`text-white transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} size={18} />
          
          {dropdownOpen && (
            <div className="absolute top-[110%] left-0 w-full bg-[#1D2226] rounded-lg shadow-lg z-50 p-2 max-h-48 overflow-y-auto border border-gray-600">
              {balanceLoading ? (
                 <div className="text-center p-4 text-gray-400">Loading...</div>
              ) : availableTokens.length > 0 ? (
                availableTokens.map((token) => (
                <div
                    key={token.symbol}
                    className={`flex items-center justify-between gap-4 px-4 py-3 rounded-lg cursor-pointer ${selectedCrypto === token.symbol ? "bg-gray-700" : "hover:bg-gray-800/60"}`}
                  onClick={(e) => {
                    e.stopPropagation();
                      setSelectedCrypto(token.symbol);
                    setDropdownOpen(false);
                      setMessage(""); 
                      setAmount("");
                  }}
                >
                    <div className="flex items-center gap-3">
                       {cryptoIcons[token.symbol.toUpperCase() as keyof typeof cryptoIcons] || cryptoIcons.DEFAULT}
                       <span className="text-white text-lg">{token.symbol}</span>
                       <span className="text-xs text-gray-500">({token.chain})</span>
                    </div>
                    <span className="text-sm text-gray-300">
                        Bal: {parseFloat(token.balance).toLocaleString(undefined, {maximumFractionDigits: 4})}
                    </span>
                </div>
                ))
              ) : (
                 <div className="text-center p-4 text-gray-400">No tokens found</div>
              )}
            </div>
          )}
        </div>

        <div className="mt-6 bg-[#1D2226] p-4 rounded-lg flex justify-between items-center w-full">
          <div className="flex items-center gap-3">
            {selectedCrypto && (cryptoIcons[selectedCrypto.toUpperCase() as keyof typeof cryptoIcons] || cryptoIcons.DEFAULT)}
            <div className="text-white">
              <p className="text-sm text-[#BBBBBB]">
                Amount
                <span className="ml-2 text-xs">(Balance: {getCurrentBalance()})</span>
                {balanceLoading && <span className="ml-1 text-xs text-blue-400">(loading...)</span>}
              </p>
              <input
                type="number"
                value={amount}
                onChange={(e) => {
                  setAmount(e.target.value);
                  setMessage("");
                }}
                placeholder="0.00"
                className="bg-transparent text-2xl outline-none w-full mt-1"
                disabled={!selectedWallet || !selectedCrypto}
              />
            </div>
          </div>
          <button
            onClick={() => {
              if (selectedCrypto) { 
                const balanceStr = walletBalances[selectedCrypto.toUpperCase()]?.balance || "0";
                setAmount(balanceStr);
              }
            }}
            className={`text-[#14FFA2] text-sm font-medium ${!selectedWallet || !selectedCrypto || balanceLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={!selectedWallet || !selectedCrypto || balanceLoading}
          >
            Max
          </button>
        </div>

        {/* Display Fee Info (Recommended UI Addition) */}
        {calculatedFee && netAmountToSend && selectedCrypto && (
            <div className="mt-2 text-xs text-gray-400 px-1 flex justify-between">
                <span>Fee ({(PLATFORM_FEE_PERCENTAGE * 100).toFixed(1)}%):</span>
                <span>{calculatedFee} {selectedCrypto}</span>
            </div>
        )}
        {netAmountToSend && selectedCrypto && (
             <div className="mt-1 text-xs text-gray-300 px-1 flex justify-between">
                <span>You will receive:</span>
                <span className="font-medium">{netAmountToSend} {selectedCrypto}</span>
            </div>
        )}

        <div className="mt-6">
          <p className="text-[#BBBBBB] pb-2 text-lg">To Address</p>
          <input
            type="text"
            value={withdrawAddress}
            onChange={(e) => {
                setWithdrawAddress(e.target.value);
                setMessage("");
            }}
            placeholder={`Enter destination ${selectedCrypto || 'token'} address`}
            className="w-full bg-[#1D2226] p-4 rounded-lg text-white outline-none"
            disabled={!selectedWallet || !selectedCrypto}
          />
        </div>

        {message && (
          <div className={`mt-4 text-center text-sm px-2 py-1 rounded bg-opacity-20 ${message.toLowerCase().includes("error") ? "text-red-400 bg-red-900" : "text-green-400 bg-green-900"}`}>
            <p>
              {message}
            </p>
          </div>
        )}

        <button
          onClick={handleWithdraw}
          disabled={loading || !amount || !selectedWallet || !withdrawAddress || Number(amount) <= 0 || !selectedCrypto}
          className={`w-full mt-6 py-4 rounded-lg text-lg font-semibold transition-colors duration-200
            ${loading || !amount || !selectedWallet || !withdrawAddress || Number(amount) <= 0 || !selectedCrypto
              ? "bg-gray-600 text-gray-400 cursor-not-allowed"
              : "bg-[#14FFA2] text-black hover:bg-[#0ee889]"
            }`}
        >
          {loading ? "Processing..." : `Withdraw ${selectedCrypto || 'Asset'}`}
        </button>
      </div>
    </div>
  );
};

export { WithdrawModal };
export default WithdrawModal;
