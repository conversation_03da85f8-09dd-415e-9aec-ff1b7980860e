import React from "react";
import { useState, useEffect, useCallback } from "react";
import { usePrivy, useWallets } from "@privy-io/react-auth";
import { WithdrawModal } from "./WithdrawModal";
import DepositModal from "./DepositModal";
import { homeAPI, walletAPI } from "../../utils/api";

// Define token and balance interfaces
interface TokenBalance {
  symbol: string;
  balance: string;
  [key: string]: any;
}

interface WalletBalances {
  [symbol: string]: number;
}

interface CoinData {
  symbol?: string;
  current_price?: string | number;
  market_cap_change_percentage_24h?: string | number;
  [key: string]: any;
}

interface WalletObject {
  address?: string;
  walletClientType?: string;
  embedded?: boolean;
  connector?: {
    name?: string;
  };
  [key: string]: any;
}

const cryptoIcons = {
  ETH: "https://mobulastorage.blob.core.windows.net/mobula-assets/assets/logos/d90e937d915ab0c865ff6e335361386c56524d4d33fb66f2f04defe1500082eb.png",
  BTC: "https://coin-images.coingecko.com/coins/images/825/large/bnb-icon2_2x.png?1696501970",
  SOL: "https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png",
  
};

const Connect = ({ selectedNetwork }: { selectedNetwork?: string }) => {
  const [isWithdrawOpen, setIsWithdrawOpen] = useState<boolean>(false);
  const [isDepositOpen, setIsDepositOpen] = useState<boolean>(false);
  const [connectCoins, setConnectCoins] = useState<CoinData[]>([]);
  const [totalBalance, setTotalBalance] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");
  const [walletBalances, setWalletBalances] = useState<WalletBalances>({});
  const [walletError, setWalletError] = useState<string>("");
  
  const { user, authenticated, login } = usePrivy();
  const { wallets } = useWallets();
  const connectedWallet = wallets?.[0];
  const truncatedAddress = connectedWallet?.address 
    ? `${connectedWallet.address.slice(0, 6)}...${connectedWallet.address.slice(-4)}`
    : null;
  
  const isWalletConnected = authenticated && connectedWallet;
  
  // Get accurate wallet type identification function
  const getActualWalletType = (wallet: WalletObject) => {
    if (!wallet) return "unknown";
    
    // First, check if this is a Solana wallet by examining the address format
    const isSolanaAddress = wallet.address && !wallet.address.startsWith('0x');
    
    // Check for Phantom wallet specifically
    const isPhantomWallet = 
      wallet.walletClientType === "phantom" ||
      (isSolanaAddress && window.phantom && window.phantom.solana) ||
      (wallet.connector && wallet.connector.name === "phantom");
    
    if (isPhantomWallet) {
      return "phantom";
    }
    
    // Check for MetaMask wallet
    const isMetaMask = 
      (wallet.walletClientType === "injected" && window.ethereum?.isMetaMask) ||
      (wallet.walletClientType === "metamask") ||
      (wallet.connector && wallet.connector.name === "metamask");
    
    if (isMetaMask) {
      return "metamask";
    }
    
    // For Privy embedded wallets
    if (wallet.embedded || wallet.walletClientType === "embedded") {
      return "embedded";
    }
    
    // For other wallets, use their client type
    return wallet.walletClientType || "external";
  };

  // Add detection for Phantom wallets that aren't properly detected by Privy
  useEffect(() => {
    if (authenticated && window.phantom?.solana) {
      console.log("Detected Phantom wallet presence");
      // Check if we already have a Solana address in our wallets array
      const hasSolanaWallet = wallets?.some(w => !w.address.startsWith('0x'));
      
      if (!hasSolanaWallet) {
        console.log("No Solana wallet found in Privy wallets, attempting direct connection");
        // Try to connect directly to Phantom
        window.phantom.solana.connect({ onlyIfTrusted: true })
          .then(({ publicKey }) => {
            if (publicKey) {
              console.log("Got Phantom wallet address from direct connection:", publicKey.toString());
              // Try to reconstruct a wallet object for the component
              const phantomWallet = {
                address: publicKey.toString(),
                walletClientType: "phantom",
                chainId: "solana",
                connected: true
              };
              
              // If we're not showing a wallet yet, use this phantom wallet in the UI
              if (!connectedWallet) {
                console.log("Using directly connected Phantom wallet for display");
                // Update wallet display through a global event
                const event = new CustomEvent('wallet-update', { 
                  detail: { phantomWallet } 
                });
                window.dispatchEvent(event);
              }
            }
          })
          .catch(err => {
            console.log("Could not auto-connect to Phantom:", err.message);
          });
      }
    }
  }, [authenticated, wallets, connectedWallet]);

  // Get wallet balances using our new API
  const getWalletBalances = useCallback(async () => {
    if (!isWalletConnected || !connectedWallet?.address) {
      console.log("No wallet connected for getting balances");
      return {} as WalletBalances;
    }

    const walletType = getActualWalletType(connectedWallet);
    console.log(`Getting balances for ${walletType} wallet: ${connectedWallet.address}`);
    
    try {
      // Use our new API endpoint to get all balances across chains
      const response = await walletAPI.getAllBalances(connectedWallet.address);
      
      if (response.success && response.data) {
        console.log(`Wallet balances received for ${connectedWallet.address}:`, response.data);
        
        // Process the balances into a format usable by this component
        const balances: WalletBalances = {};
        
        // Check if balances object exists
        if (response.data.balances) {
          // Process Ethereum balances (with null check)
          if (response.data.balances.ethereum) {
            response.data.balances.ethereum.forEach((token: TokenBalance) => {
              if (parseFloat(token.balance) > 0) {
                balances[token.symbol.toUpperCase()] = parseFloat(token.balance);
              }
            });
          }
          
          // Process BSC balances (with null check)
          if (response.data.balances.bsc) {
            response.data.balances.bsc.forEach((token: TokenBalance) => {
              if (parseFloat(token.balance) > 0) {
                // If we already have this token from another chain, sum them
                const symbol = token.symbol.toUpperCase();
                balances[symbol] = (balances[symbol] || 0) + parseFloat(token.balance);
              }
            });
          }
          
          // Process Solana balances (with null check)
          if (response.data.balances.solana) {
            response.data.balances.solana.forEach((token: TokenBalance) => {
              if (parseFloat(token.balance) > 0) {
                // If we already have this token from another chain, sum them
                const symbol = token.symbol.toUpperCase();
                balances[symbol] = (balances[symbol] || 0) + parseFloat(token.balance);
              }
            });
          }
        } else {
          console.warn("No balances object in API response:", response.data);
        }
        
        console.log("Processed balances:", balances);
        return balances;
      } else {
        console.warn("Failed to get balances:", response);
        return {} as WalletBalances;
      }
    } catch (error) {
      console.error("Error fetching wallet balances:", error);
      return {} as WalletBalances;
    }
  }, [connectedWallet, isWalletConnected]);
  
  useEffect(() => {
    console.log("Connect component useEffect triggered");
    console.log("Wallet connected:", isWalletConnected);
    console.log("Connected wallet address:", connectedWallet?.address);
    
    // Track if component is mounted to prevent state updates after unmount
    let isMounted = true;
    
    // Add a variable to track if fetch is in progress to prevent multiple concurrent requests
    let fetchInProgress = false;
    
    const fetchData = async () => {
      // Prevent duplicate fetches while one is in progress
      if (fetchInProgress) {
        console.log("Fetch already in progress, skipping duplicate request");
        return;
      }
      
      fetchInProgress = true;
      console.log("fetchData function executing");
      
      try {
        if (isMounted) setLoading(true);
        if (isMounted) setError("");
        if (isMounted) setWalletError("");
        
        // Check if homeAPI is properly imported
        if (!homeAPI || typeof homeAPI.getConnectCoins !== 'function') {
          console.error("homeAPI not properly imported or getConnectCoins is not a function", homeAPI);
          if (isMounted) setError("API utility not available");
          fetchInProgress = false;
          return;
        }
        
        console.log("Making API call to getConnectCoins");
        const response = await homeAPI.getConnectCoins();
        
        console.log("API response:", response);
        
        // Only proceed if component is still mounted
        if (!isMounted) {
          console.log("Component unmounted during fetch, stopping processing");
          fetchInProgress = false;
          return;
        }
        
        if (!response || !Array.isArray(response) || response.length === 0) {
          console.error("Invalid response format:", response);
          setError("Invalid data format received");
          fetchInProgress = false;
          return;
        }
        
        const coinData = response;
        console.log("Coin data extracted:", coinData);
        setConnectCoins(coinData);

        // Get balances for connected wallet
        if (isWalletConnected && connectedWallet?.address) {
          try {
            // Using our new API to get balances
            const balances = await getWalletBalances();
            console.log("API wallet balances:", balances);
            
            if (isMounted) setWalletBalances(balances);
            
            // Calculate total balance
            const total = coinData.reduce((acc, coin) => {
              const symbol = coin.symbol?.toUpperCase() || '';
              const balance = balances[symbol] || 0;
              const price = typeof coin.current_price === 'number' 
                ? coin.current_price 
                : parseFloat(String(coin.current_price || '0'));
              return acc + (balance * price);
            }, 0);
            
            console.log("Total balance calculated:", total);
            if (isMounted) setTotalBalance(total);
            if (isMounted) setWalletError("");
          } catch (balanceError) {
            console.error("Error fetching wallet balances:", balanceError);
            if (isMounted) setWalletError("Failed to load wallet balances (showing zero)");
            
            // Reset balances to zero instead of using placeholders
            const zeroBalances: WalletBalances = {};
            coinData.forEach(coin => {
              const symbol = coin.symbol?.toUpperCase() || '';
              zeroBalances[symbol] = 0;
            });
            
            if (isMounted) setWalletBalances(zeroBalances);
            
            // Calculate total (will be zero)
            if (isMounted) setTotalBalance(0);
          }
        } else {
          // Reset if no wallet connected
          if (isMounted) setWalletBalances({});
          if (isMounted) setTotalBalance(0);
        }
      } catch (error: unknown) {
        console.error('Error in fetchData:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        if (isMounted) setError(`Failed to load data: ${errorMessage}`);
      } finally {
        if (isMounted) setLoading(false);
        fetchInProgress = false;
        console.log("fetchData completed");
      }
    };
    
    // Initial fetch when component mounts or dependencies change
    fetchData();
    
    // Set up refresh interval with a reasonable delay (5 minutes)
    const intervalId = setInterval(() => {
      console.log('Interval triggered, fetching fresh data...');
      fetchData();
    }, 300000); // 5 minutes
    
    return () => {
      console.log("Component unmounting, cleaning up");
      isMounted = false;
      clearInterval(intervalId);
    };
  }, [
    // Only trigger this effect when these dependencies change
    connectedWallet?.address, // Address rather than whole object
    isWalletConnected,
    getWalletBalances
  ]);
  
  // Calculate 24h change percentage
  const calculateChangePercentage = () => {
    if (!connectCoins || !connectCoins.length || !isWalletConnected) return 0;
    
    let totalChange = 0;
    let totalValue = 0;

    connectCoins.forEach((coin: CoinData) => {
      const symbol = coin.symbol?.toUpperCase() || '';
      const balance = symbol ? walletBalances[symbol] || 0 : 0;
      
      // Safely parse numeric values
      const currentPrice = typeof coin.current_price === 'number' 
        ? coin.current_price 
        : parseFloat(String(coin.current_price || '0'));
        
      const change24h = typeof coin.market_cap_change_percentage_24h === 'number'
        ? coin.market_cap_change_percentage_24h
        : parseFloat(String(coin.market_cap_change_percentage_24h || '0'));
      
      const value = balance * currentPrice;
      totalValue += value;
      totalChange += value * (change24h / 100);
    });

    return totalValue > 0 ? (totalChange / totalValue) * 100 : 0;
  };

  const changePercentage = calculateChangePercentage();
  const isPositiveChange = changePercentage >= 0;
  const changeAmount = (totalBalance * changePercentage) / 100;

  // Handle wallet connection
  const handleConnectWallet = async () => {
    if (!authenticated) {
      console.log("Not authenticated, initiating login");
      
      // Check if token exists before login
      console.log("Token exists before login:", !!localStorage.getItem('privy:token'));
      
      await login();
      
      // Check if token exists after login
      setTimeout(() => {
        console.log("Token exists after login:", !!localStorage.getItem('privy:token'));
        
        // If Phantom is available, try connecting it explicitly
        if (window.phantom?.solana) {
          console.log("Phantom detected after login, connecting explicitly");
          window.phantom.solana.connect()
            .then(({ publicKey }) => {
              console.log("Connected to Phantom after login:", publicKey.toString());
            })
            .catch(err => {
              console.error("Error connecting to Phantom after login:", err);
            });
        }
      }, 2000);
    }
  };

  // Show authentication error if wallet is connected but there's an error
  const renderAuthError = () => {
    if (walletError && walletError.includes("authentication") && isWalletConnected) {
      return (
        <div className="bg-red-600/10 border border-red-600/30 rounded-xl p-3 mt-4 text-red-500 text-sm">
          <p className="font-medium">Authentication Error</p>
          <p>Your wallet session has expired. Please reconnect your wallet.</p>
          <button 
            onClick={login}
            className="mt-2 bg-red-600/20 hover:bg-red-600/30 text-red-500 px-3 py-1 rounded-md text-sm font-medium"
          >
            Reconnect Wallet
          </button>
        </div>
      );
    }
    return null;
  };

  // Get coin icon dynamically
  const getCoinIcon = (symbol: string | undefined): string => {
  
    if (!symbol) return '';
    const upperSymbol = symbol.toUpperCase();
    return cryptoIcons[upperSymbol as keyof typeof cryptoIcons] ;
  };

  return (
    <div className="flex flex-col md:flex-row items-stretch justify-between gap-4 md:gap-8 bg-[#141416] text-white px-4 md:px-6 py-4 h-[17.5rem]">
      {/* Left: Balance Card */}
      <WithdrawModal isOpen={isWithdrawOpen} onClose={() => setIsWithdrawOpen(false)} />
      <DepositModal isOpen={isDepositOpen} onClose={() => setIsDepositOpen(false)} />
      <div className="bg-[#181C20] p-5 rounded-xl w-full md:w-1/4 flex flex-col h-full">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-md text-gray-400">Balance</h3>
          {isWalletConnected ? (
            <button 
              className="text-sm bg-[#242731] px-2 py-1 rounded-md text-white font-medium hover:bg-[#2c303a]"
            >
              {truncatedAddress}
            </button>
          ) : (
            <button 
              onClick={handleConnectWallet}
              className="text-sm bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded-md text-white"
            >
              Connect Wallet
            </button>
          )}
        </div>
        
        {loading ? (
          <p className="text-3xl font-bold my-3">Loading...</p>
        ) : error ? (
          <p className="text-xl font-bold my-3 text-red-500">{error}</p>
        ) : !isWalletConnected ? (
          <div className="my-4 text-center py-6">
            <p className="text-xl text-gray-400 mb-4">Connect your wallet to view your balance</p>
            <button 
              onClick={handleConnectWallet}
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-lg text-lg"
            >
              Connect Wallet
            </button>
          </div>
        ) : (
          <>
            <p className="text-3xl font-bold my-3">${totalBalance.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</p>
            {walletError ? (
              <div className="flex items-center gap-1 bg-[#242731] rounded px-2 py-1 mb-3">
                <span className="text-xs text-red-500">
                  {walletError} 
                  <span className="text-gray-400"> (showing estimates)</span>
                </span>
              </div>
            ) : (
              <div className="flex gap-2 my-3">
                {(Math.abs(changePercentage) > 0.01 || Math.abs(changeAmount) > 0.01) ? (
                  <span className={`text-[12px] ${isPositiveChange ? 'text-[#16A34A]' : 'text-[#ED2626]'} flex items-center`}>
                    {isPositiveChange ? '+' : '-'}${Math.abs(changeAmount).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} ({changePercentage > 0 ? '+' : ''}{changePercentage.toFixed(2)}%)
                  </span>
                ) : null}
                {(Math.abs(changePercentage) > 0.01 || Math.abs(changeAmount) > 0.01) ? (
                  <p className="text-[12px] text-white opacity-50 flex items-center">
                    (24H)
                  </p>
                ) : null}
              </div>
            )}
          </>
        )}
        
        {isWalletConnected && (
          <div className="flex justify-between mt-auto pt-4">
            {/* Send Button */}
            <button className="flex flex-col items-center text-lg">
              <div onClick={() => setIsDepositOpen(true)} className="bg-[#242731] p-3 rounded-full hover:bg-[#2c303a] transition-colors mb-1">
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M16.1591 18H1.84091C1.35267 18 0.884427 17.806 0.53919 17.4608C0.193953 17.1156 0 16.6473 0 16.1591V13.9091C0 13.6921 0.0862012 13.484 0.23964 13.3305C0.393079 13.1771 0.601187 13.0909 0.818182 13.0909C1.03518 13.0909 1.24328 13.1771 1.39672 13.3305C1.55016 13.484 1.63636 13.6921 1.63636 13.9091V16.1591C1.63636 16.2133 1.65791 16.2654 1.69627 16.3037C1.73463 16.3421 1.78666 16.3636 1.84091 16.3636H16.1591C16.2133 16.3636 16.2654 16.3421 16.3037 16.3037C16.3421 16.2654 16.3636 16.2133 16.3636 16.1591V13.9091C16.3636 13.6921 16.4498 13.484 16.6033 13.3305C16.7567 13.1771 16.9648 13.0909 17.1818 13.0909C17.3988 13.0909 17.6069 13.1771 17.7604 13.3305C17.9138 13.484 18 13.6921 18 13.9091V16.1591C18 16.6473 17.806 17.1156 17.4608 17.4608C17.1156 17.806 16.6473 18 16.1591 18ZM14.7273 1.63636H3.27273C3.05573 1.63636 2.84762 1.55016 2.69419 1.39672C2.54075 1.24328 2.45455 1.03518 2.45455 0.818182C2.45455 0.601187 2.54075 0.393079 2.69419 0.23964C2.84762 0.0862012 3.05573 0 3.27273 0H14.7273C14.9443 0 15.1524 0.0862012 15.3058 0.23964C15.4593 0.393079 15.5455 0.601187 15.5455 0.818182C15.5455 1.03518 15.4593 1.24328 15.3058 1.39672C15.1524 1.55016 14.9443 1.63636 14.7273 1.63636Z" fill="white"/>
<path d="M13.025 8.49271C12.9637 8.64212 12.8594 8.77003 12.7255 8.8603C12.5915 8.95057 12.4338 8.99918 12.2723 8.99998H9.81776V12.2727C9.81776 12.4897 9.73156 12.6978 9.57812 12.8513C9.42468 13.0047 9.21658 13.0909 8.99958 13.0909C8.78259 13.0909 8.57448 13.0047 8.42104 12.8513C8.2676 12.6978 8.1814 12.4897 8.1814 12.2727V8.99998H5.72685C5.56532 8.99918 5.40765 8.95057 5.27369 8.8603C5.13974 8.77003 5.03551 8.64212 4.97413 8.49271C4.91147 8.34371 4.89435 8.17949 4.92493 8.02077C4.95551 7.86205 5.03242 7.71595 5.14595 7.60089L8.41867 4.32816C8.49648 4.25368 8.58824 4.19529 8.68867 4.15635C8.88787 4.07451 9.1113 4.07451 9.31049 4.15635C9.41092 4.19529 9.50268 4.25368 9.58049 4.32816L12.8532 7.60089C12.9667 7.71595 13.0437 7.86205 13.0742 8.02077C13.1048 8.17949 13.0877 8.34371 13.025 8.49271Z" fill="white"/>
</svg>

              </div>
              <span className="text-sm">Deposit</span>
            </button>
            {/* Withdraw Button */}
            <button className="flex flex-col items-center text-lg">
              <div onClick={() => setIsWithdrawOpen(true)} className="bg-[#242731] p-3 rounded-full hover:bg-[#2c303a] transition-colors mb-1">
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M16.1591 18H1.84091C1.35267 18 0.884427 17.806 0.53919 17.4608C0.193953 17.1156 0 16.6473 0 16.1591V13.9091C0 13.6921 0.0862012 13.484 0.23964 13.3305C0.393079 13.1771 0.601187 13.0909 0.818182 13.0909C1.03518 13.0909 1.24328 13.1771 1.39672 13.3305C1.55016 13.484 1.63636 13.6921 1.63636 13.9091V16.1591C1.63636 16.2133 1.65791 16.2654 1.69627 16.3037C1.73463 16.3421 1.78666 16.3636 1.84091 16.3636H16.1591C16.2133 16.3636 16.2654 16.3421 16.3037 16.3037C16.3421 16.2654 16.3636 16.2133 16.3636 16.1591V13.9091C16.3636 13.6921 16.4498 13.484 16.6033 13.3305C16.7567 13.1771 16.9648 13.0909 17.1818 13.0909C17.3988 13.0909 17.6069 13.1771 17.7604 13.3305C17.9138 13.484 18 13.6921 18 13.9091V16.1591C18 16.6473 17.806 17.1156 17.4608 17.4608C17.1156 17.806 16.6473 18 16.1591 18ZM14.7273 1.63636H3.27273C3.05573 1.63636 2.84762 1.55016 2.69419 1.39672C2.54075 1.24328 2.45455 1.03518 2.45455 0.818182C2.45455 0.601187 2.54075 0.393079 2.69419 0.23964C2.84762 0.0862012 3.05573 0 3.27273 0H14.7273C14.9443 0 15.1524 0.0862012 15.3058 0.23964C15.4593 0.393079 15.5455 0.601187 15.5455 0.818182C15.5455 1.03518 15.4593 1.24328 15.3058 1.39672C15.1524 1.55016 14.9443 1.63636 14.7273 1.63636Z" fill="white"/>
<path d="M4.97496 8.6932C5.03634 8.54379 5.14058 8.41588 5.27453 8.32561C5.40849 8.23534 5.56616 8.18674 5.72769 8.18593L8.18224 8.18593L8.18224 4.9132C8.18224 4.69621 8.26844 4.4881 8.42188 4.33466C8.57532 4.18122 8.78342 4.09502 9.00042 4.09502C9.21741 4.09502 9.42552 4.18122 9.57896 4.33466C9.7324 4.4881 9.8186 4.69621 9.8186 4.9132L9.8186 8.18593L12.2731 8.18593C12.4347 8.18674 12.5924 8.23534 12.7263 8.32561C12.8603 8.41588 12.9645 8.54379 13.0259 8.6932C13.0885 8.8422 13.1056 9.00642 13.0751 9.16514C13.0445 9.32386 12.9676 9.46997 12.8541 9.58502L9.58133 12.8577C9.50352 12.9322 9.41176 12.9906 9.31133 13.0296C9.11213 13.1114 8.8887 13.1114 8.68951 13.0296C8.58908 12.9906 8.49732 12.9322 8.41951 12.8577L5.14678 9.58502C5.03325 9.46997 4.95635 9.32386 4.92577 9.16514C4.89519 9.00642 4.91231 8.8422 4.97496 8.6932Z" fill="white"/>
</svg>

              </div>
              <span className="text-sm">Withdraw</span>
            </button>
            {/* Buy Crypto Button */}
            <button className="flex flex-col items-center text-lg">
              <div className="bg-[#242731] p-3 rounded-full hover:bg-[#2c303a] transition-colors mb-1">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M16.1591 18H1.84091C1.35267 18 0.884427 17.806 0.53919 17.4608C0.193953 17.1156 0 16.6473 0 16.1591V13.9091C0 13.6921 0.0862012 13.484 0.23964 13.3305C0.393079 13.1771 0.601187 13.0909 0.818182 13.0909C1.03518 13.0909 1.24328 13.1771 1.39672 13.3305C1.55016 13.484 1.63636 13.6921 1.63636 13.9091V16.1591C1.63636 16.2133 1.65791 16.2654 1.69627 16.3037C1.73463 16.3421 1.78666 16.3636 1.84091 16.3636H16.1591C16.2133 16.3636 16.2654 16.3421 16.3037 16.3037C16.3421 16.2654 16.3636 16.2133 16.3636 16.1591V13.9091C16.3636 13.6921 16.4498 13.484 16.6033 13.3305C16.7567 13.1771 16.9648 13.0909 17.1818 13.0909C17.3988 13.0909 17.6069 13.1771 17.7604 13.3305C17.9138 13.484 18 13.6921 18 13.9091V16.1591C18 16.6473 17.806 17.1156 17.4608 17.4608C17.1156 17.806 16.6473 18 16.1591 18Z" fill="white"/>
                  <path d="M9 5V13M5 9H13" stroke="white" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
              </div>
              <span className="text-sm">Buy Crypto</span>
            </button>
          </div>
        )}
      </div>
      
      {/* Center: Image Display */}
      <div className="bg-green-900 flex-1 rounded-xl flex items-center justify-center min-h-[150px]">
        <p className="text-gray-400">Image Display Area</p>
      </div>
      
      {/* Right: Market Overview */}
      <div className="bg-[#181C20] p-5 rounded-xl w-full md:w-1/5 h-full">
  <div className="flex flex-col gap-3">
    <h3 className="text-md text-gray-400 mb-2">Top Coins</h3>
    {loading ? (
      // Skeleton loader for 5 items
      Array.from({ length: 3 }).map((_, index) => (
        <div key={index} className="flex justify-between items-center ">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gray-700 rounded-full" />
            <div className="w-24 h-4 bg-gray-700 rounded" />
          </div>
          <div className="text-right space-y-1">
            <div className="w-16 h-4 bg-gray-700 rounded" />
            <div className="w-12 h-3 bg-gray-700 rounded" />
          </div>
        </div>
      ))
    ) : error ? (
      <p className="text-red-500">{error}</p>
    ) : connectCoins && connectCoins.length > 0 ? (
      connectCoins.map((coin) => (
        <div key={coin.id || coin.symbol} className="flex justify-between items-center">
          <div className="flex items-center gap-3">
            <img src={getCoinIcon(coin.symbol)} alt={coin.name} className="w-8 h-8" />
            <p className="text-lg">{coin.name}</p>
          </div>
          <div className="text-right">
            <p className="text-lg">${(coin.current_price || 0).toLocaleString()}</p>
            <p
              className={`${
                (parseFloat(String(coin.market_cap_change_percentage_24h || 0)) || 0) < 0
                  ? 'text-[#ED2626]'
                  : 'text-[#16A34A]'
              } text-xs`}
            >
              {(parseFloat(String(coin.market_cap_change_percentage_24h || 0)) || 0) < 0 ? '▼' : '▲'}{' '}
              {Math.abs(parseFloat(String(coin.market_cap_change_percentage_24h || 0))).toFixed(2)}%
            </p>
          </div>
        </div>
      ))
    ) : (
      <p>No coins available</p>
    )}
  </div>
</div>

      
      {/* Authentication Error Notification */}
      {isWalletConnected && renderAuthError()}
    </div>
  );
};

export default Connect;
