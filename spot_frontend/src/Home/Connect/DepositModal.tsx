import React, { useState, useEffect } from "react";
import { IoClose } from "react-icons/io5";
import { FaChevronDown } from "react-icons/fa";
import { FiCopy } from "react-icons/fi";
import QRCode from "react-qr-code";
import { usePrivy, useWallets } from "@privy-io/react-auth";

// --- Copied Types and Interfaces ---
interface TokenAddress {
  address: string;
  type: string;
  chainName: string;
  embedded?: boolean;
  connectorType?: string;
}

interface AddressesByChain {
  ethereum: TokenAddress[];
  solana: TokenAddress[];
}

// Updated Wallet type to include properties used by helper functions
type Wallet = {
  address?: string;
  walletClientType?: string; // Used in getWalletAddresses
  connector?: {
    name: string;
  };
  embedded?: boolean;
  chains?: string[];
  chainId?: string | number;
  connected?: boolean;
  connectedAt?: Date | string; // Used in getWalletAddresses
  chainName?: string;
  type?: string;
  connectorType?: string; // Add connectorType for Solana wallet identification
};

// --- Copied Helper Functions from Navbar.tsx ---

// Helper function to validate if a string looks like a valid blockchain address
const isValidBlockchainAddress = (address: string): boolean => {
  if (!address || typeof address !== 'string') return false;
  if (address.startsWith('0x')) {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  }
  if (address.includes('@') || address.includes('.com') || address.includes('.io') || address.includes('.org')) {
    return false;
  }
  return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address);
};

// Helper function to determine blockchain type from address format
const getBlockchainTypeFromAddress = (address: string): { type: string; chainName: string } | null => {
  if (!address || typeof address !== 'string') return null;
  if (/^0x[a-fA-F0-9]{40}$/.test(address)) {
    return { type: 'ethereum', chainName: 'Ethereum' };
  }
  if (address.startsWith('HzHsCX')) { // Example specific check
    return { type: 'solana', chainName: 'Solana' };
  }
  if (!address.startsWith('0x')) {
    return { type: 'solana', chainName: 'Solana' };
  }
  return null;
};

// Function to get all available wallet addresses (adapted for DepositModal context)
const getAllWalletAddresses = (user: any, wallets: ReadonlyArray<Wallet>): TokenAddress[] => {
  const addresses: TokenAddress[] = [];
  const seenAddresses = new Set();

  const addUniqueAddress = (address: string, type: string, chainName: string, connectorType?: string): boolean => {
    if (!isValidBlockchainAddress(address)) return false;
    const normalizedAddress = address.toLowerCase();
    if (seenAddresses.has(normalizedAddress)) return false;
    seenAddresses.add(normalizedAddress);
    addresses.push({ 
      address, 
      type, 
      chainName,
      // Store additional properties to help with embedded identification
      embedded: connectorType === 'embedded',
      connectorType: connectorType
    });
    return true;
  };

  // Check for smart wallet in user object
  if (user?.smartWallet?.address) {
    addUniqueAddress(
      user.smartWallet.address, 
      'smart', 
      'Ethereum', 
      'smart'
    );
  }

  if (user?.wallet?.address) {
    addUniqueAddress(user.wallet.address, 'embedded', 'Ethereum', 'embedded');
  }

  if (user?.linkedAccounts && user.linkedAccounts.length > 0) {
    for (const account of user.linkedAccounts) {
      const accountAddress = (account as any).address;
      if (accountAddress && (account as any).type === 'wallet') {
        const blockchainInfo = getBlockchainTypeFromAddress(accountAddress);
        if (blockchainInfo) {
          // Pass connectorType from linkedAccounts to identify embedded Solana wallets
          addUniqueAddress(
            accountAddress, 
            blockchainInfo.type, 
            blockchainInfo.chainName,
            (account as any).connectorType
          );
        }
      }
    }
  }

  if (wallets && wallets.length > 0) {
    const connectedWallets = wallets.filter(w => w.connectedAt);
    connectedWallets.forEach(wallet => {
      if (wallet.address) {
        let blockchainInfo = null;
        // Special handling for smart wallets - include them explicitly
        if (wallet.walletClientType === 'smart') {
          addUniqueAddress(
            wallet.address,
            'smart',
            'Ethereum',
            'smart'
          );
          return;
        }
        
        if (wallet.walletClientType === 'phantom') {
          blockchainInfo = { type: 'solana', chainName: 'Solana' };
        } else {
          blockchainInfo = getBlockchainTypeFromAddress(wallet.address);
        }
        if (blockchainInfo) {
           let chainName = blockchainInfo.chainName;
            // Refine chain name based on chainId if possible (Example)
           if (blockchainInfo.type === 'ethereum' && wallet.chainId) {
             const chainIdStr = String(wallet.chainId).replace('eip155:', '');
             if (chainIdStr === '8453') chainName = 'Base'; // Example
             else if (chainIdStr === '137') chainName = 'Polygon'; // Example
             // Keep 'Ethereum' for mainnet or unknown EVM chains
             else if (chainIdStr !== '1') chainName = `EVM (${chainIdStr})`;
             else chainName = 'Ethereum';
           }
           addUniqueAddress(
             wallet.address, 
             blockchainInfo.type, 
             chainName,
             wallet.connectorType || wallet.walletClientType
           );
        }
      }
    });
  }
  return addresses;
};

// Function to group addresses by chain (adapted for DepositModal context)
const groupAddressesByChain = (allAddresses: TokenAddress[]): AddressesByChain => {
  const result: AddressesByChain = { ethereum: [], solana: [] };
  allAddresses.forEach(address => {
    if (address.address && address.address.startsWith('HzHsCX')) { // Example specific check
      result.solana.push({ ...address, type: 'solana', chainName: 'Solana' });
      return;
    }
    if (address.address && !address.address.startsWith('0x')) {
      result.solana.push({ ...address, type: 'solana', chainName: 'Solana' });
      return;
    }
    const chainKey = address.chainName.toLowerCase();
    if (chainKey === 'ethereum' || chainKey.startsWith('evm') || chainKey === 'base' || chainKey === 'polygon') {
       if (address.address && address.address.startsWith('0x')) {
         result.ethereum.push(address);
       } else { // Non-0x address wrongly categorized, move to solana
         result.solana.push({ ...address, type: 'solana', chainName: 'Solana' });
       }
    } else if (chainKey === 'solana') {
      result.solana.push(address);
    } else { // Default categorization
      if (address.address && address.address.startsWith('0x')) {
        result.ethereum.push(address);
      } else {
        result.solana.push({ ...address, type: 'solana', chainName: 'Solana' });
      }
    }
  });
  // Simple deduplication within groups
  result.ethereum = result.ethereum.filter((addr, index, self) =>
      index === self.findIndex((a) => a.address === addr.address));
  result.solana = result.solana.filter((addr, index, self) =>
      index === self.findIndex((a) => a.address === addr.address));
  return result;
};

// --- Original DepositModal Constants ---
const cryptoIcons: Record<string, JSX.Element> = {
  Ethereum: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2L3 12L12 17L21 12L12 2Z" fill="#627EEA" />
      <path d="M12 17L3 12L12 22L21 12L12 17Z" fill="#3C3C3B" />
    </svg>
  ),
  Solana: (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="24" height="24" rx="4" fill="#00FFA3" />
      <path d="M4 8H20L16 12H8L4 8Z" fill="#9945FF" />
      <path d="M4 12H20L16 16H8L4 12Z" fill="#19FB9B" />
    </svg>
  ),
};

const cryptoNetworks: Record<string, string> = {
  Ethereum: "Ethereum Mainnet",
  Solana: "Solana Mainnet",
};

// --- DepositModal Component ---
const DepositModal = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {
  const [selectedCrypto, setSelectedCrypto] = useState<string>("Ethereum");
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  
  const { ready, authenticated, user } = usePrivy();
  // Cast wallets to our more detailed Wallet type
  const { wallets } = useWallets();
  
  useEffect(() => {
    if (ready && authenticated) {
        setLoading(false);
    }
  }, [ready, authenticated]);

  const handleCopyAddress = (address: string) => {
    if (address) {
      navigator.clipboard.writeText(address);
    }
  };
      
  // --- Updated getWalletAddress using shared logic ---
  const getWalletAddress = (crypto: string): string => {
    console.log(`DepositModal - Getting address for: ${crypto}`);
    if (!ready || !authenticated || !user) {
        console.log("DepositModal - User not ready or authenticated");
        return "";
    }

    // Use the comprehensive address gathering and grouping logic
    const allAddresses = getAllWalletAddresses(user, wallets as unknown as ReadonlyArray<Wallet>);
    const addressesByChain = groupAddressesByChain(allAddresses);

    console.log("DepositModal - Addresses by chain:", addressesByChain);

    // For Solana, prioritize smart wallet > embedded > external
    if (crypto === "Solana") {
      // First check if we have a smart wallet for Solana
      const smartSolanaWallet = addressesByChain.solana.find(addr => addr.type === 'smart');
      if (smartSolanaWallet) {
        console.log("DepositModal - Found smart Solana wallet:", smartSolanaWallet.address);
        return smartSolanaWallet.address;
      }
      
      // Then check for embedded Solana wallet 
      const embeddedSolanaWallet = addressesByChain.solana.find(addr => 
        addr.embedded || addr.connectorType === 'embedded');
      if (embeddedSolanaWallet) {
        console.log("DepositModal - Found embedded Solana wallet:", embeddedSolanaWallet.address);
        return embeddedSolanaWallet.address;
      }
      
      // Fallback to any Solana wallet
      const solanaAddress = addressesByChain.solana[0]?.address;
      console.log("DepositModal - Solana address found:", solanaAddress);
      return solanaAddress || "";
    } else { // For Ethereum and other chains
      // First check for smart wallet
      const smartEthWallet = addressesByChain.ethereum.find(addr => addr.type === 'smart');
      if (smartEthWallet) {
        console.log("DepositModal - Found smart Ethereum wallet:", smartEthWallet.address);
        return smartEthWallet.address;
      }
      
      // Then look for any ethereum wallet
      const ethereumAddress = addressesByChain.ethereum[0]?.address;
      console.log("DepositModal - Ethereum address found:", ethereumAddress);
      // Fallback to embedded wallet if needed, though grouped logic should find it
      return ethereumAddress || user?.wallet?.address || "";
    }
  };
  
  const walletAddress = getWalletAddress(selectedCrypto);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center p-4 z-50">
      {/* Overlay */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-70" 
        onClick={onClose}
        style={{ backdropFilter: 'blur(3px)' }}
      />
      
      {/* Modal Content */}
      <div className="bg-[#141416] rounded-2xl p-6 w-full max-w-md relative z-50">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute right-6 top-6 text-white hover:text-gray-300"
        >
          <IoClose size={24} />
        </button>

        {/* Title */}
        <h2 className="text-white text-2xl font-bold mb-6">Deposit</h2>

        {/* Crypto Network Selector */}
        <p className="text-[#BBBBBB] pb-3 text-xl mt-6">Select network</p>
        <div
          className="mt-2 bg-[#1D2226] p-4 rounded-lg flex justify-between items-center cursor-pointer relative w-full"
          onClick={() => setDropdownOpen(!dropdownOpen)}
        >
          <div className="flex items-center gap-3">
            {cryptoIcons[selectedCrypto]}
            <span className="text-white text-lg">{selectedCrypto}</span>
          </div>
          <FaChevronDown className="text-white" size={18} />
          
          {dropdownOpen && (
            <div className="absolute top-[110%] left-0 w-full bg-[#1D2226] rounded-lg shadow-lg z-50 p-2">
              {["Ethereum", "Solana"].map((crypto) => (
                <div
                  key={crypto}
                  className={`flex items-center gap-4 px-6 py-4 rounded-lg cursor-pointer ${
                    selectedCrypto === crypto ? "bg-gray-700" : "hover:bg-gray-800"
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedCrypto(crypto);
                    setDropdownOpen(false);
                  }}
                >
                  {cryptoIcons[crypto]}
                  <span className="text-white text-lg">{crypto}</span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* QR Code and Address Section */}
        <div className="mt-6 flex flex-col items-center">
          {loading ? (
            <div className="py-8 text-white">Loading wallet address...</div>
          ) : walletAddress ? (
            <>
              <div className="bg-white p-4 rounded-lg mb-4">
                <QRCode
                  value={walletAddress}
                  size={180}
                />
              </div>

              <div className="w-full bg-[#1D2226] p-4 rounded-lg">
                <p className="text-[#BBBBBB] text-sm mb-2">Deposit Address ({cryptoNetworks[selectedCrypto]})</p>
                <div className="flex justify-between items-center">
                  <p className="text-white text-sm font-mono break-all">
                    {walletAddress}
                  </p>
                  <button
                    onClick={() => handleCopyAddress(walletAddress)}
                    className="ml-2 p-2 hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    <FiCopy className="text-white" size={20} />
                  </button>
                </div>
              </div>
            </>
          ) : (
            <div className="py-8 text-yellow-400 text-center">
              No wallet address available for {selectedCrypto}
            </div>
          )}
        </div>

        {/* Warning */}
        <p className="mt-6 text-[#BBBBBB] text-sm text-center">
          Only send {selectedCrypto} to this deposit address.
          <br />Sending any other asset may result in permanent loss.
        </p>
      </div>
    </div>
  );
};

export default DepositModal;

