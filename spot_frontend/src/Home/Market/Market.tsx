import { useState, useEffect, useRef, MouseEvent } from "react";
import { FaArrowDown, FaChartBar } from "react-icons/fa";
import { homeAPI } from "../../utils/api";
import { useNavigate } from "react-router-dom";
import useTokenSelection from "../../hooks/useTokenSelection";

// Define the Token interface based on expected data
interface Token {
  id: string;
  name: string;
  symbol: string;
  image: string;
  network: string;
  current_price: number | null;
  price_change_percentage_24h: number | null;
  market_cap: number | null;
  total_volume: number | null;
  baseimage:string | null;
  createdAt?: string | null; // Optional if not always present
  // Add any other relevant fields
}

// Define the structure for MarketData state
interface MarketData {
  trending: Token[];
  gainers: Token[];
  losers: Token[];
  new: Token[];
  [key: string]: Token[]; // Index signature for dynamic access
}

const categories = [
  { title: "🔥 Trending", key: "trending" },
  { title: "🚀 Gainers", key: "gainers" },
  { title: "🚨 Losers", key: "losers" },
  { title: "✨ New", key: "new" },
];
const cryptoIcons = {
  ETH: "https://mobulastorage.blob.core.windows.net/mobula-assets/assets/logos/d90e937d915ab0c865ff6e335361386c56524d4d33fb66f2f04defe1500082eb.png",
  BTC: "https://coin-images.coingecko.com/coins/images/825/large/bnb-icon2_2x.png?1696501970",
  solana: "https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png",
  "binance-smart-chain":"https://coin-images.coingecko.com/coins/images/825/large/bnb-icon2_2x.png?1696501970"
};
// Define props type
interface MarketProps {
  selectedNetwork: string;
  onSelectToken: (token: Token) => void;
}

/**
 * Formats a price, using subscript notation for very small values.
 * e.g., 0.00000123 -> $0.0<subscript>5</subscript>123
 */
const formatPriceWithSubscript = (price: number | null): React.ReactNode => {
  if (price === null || price === undefined || isNaN(price)) {
    return "$0.00";
  }

  if (price > 0 && price < 0.01) {
    const priceStr = price.toExponential(3); // Use exponential to handle very small numbers reliably
    const [coefficient, exponent] = priceStr.split('e');
    const expValue = parseInt(exponent, 10);

    if (expValue <= -3) { // Typically where we want subscript
        const numZeros = Math.abs(expValue) - 1;
        const significantDigits = parseFloat(coefficient).toFixed(3).replace('.',''); // Adjust precision as needed
        
        // Ensure we don't show negative zeros or handle exponent differently if needed
        if (numZeros > 0) {
             return (
                <span className="whitespace-nowrap">
                    $0.0<sub>{numZeros}</sub>{significantDigits}
                </span>
             );
        }
    }
    // Fallback for slightly larger small numbers or if exponential logic fails
    return `$${price.toFixed(6)}`; 
  } else {
    // Standard formatting for numbers >= 0.01
    return `$${price.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  }
};

const Market: React.FC<MarketProps> = ({ selectedNetwork, onSelectToken }) => {
  const navigate = useNavigate();
  const [itemsPerCategory, setItemsPerCategory] = useState(10);
  // Use the specific MarketData interface for state type
  const [marketData, setMarketData] = useState<MarketData>({
    trending: [],
    gainers: [],
    losers: [],
    new: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('all');
  
  // Define type for sortConfig state
  interface SortConfig {
    key: keyof Token | null; // Use keys from Token interface
    direction: 'asc' | 'desc';
    category: string | null;
  }
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: "market_cap",
    direction: "desc",
    category: null
  });

  // Use the custom token selection hook
  const { 
    selectedTokens, 
    setSelectedTokens, 
    handleTokenSelect, 
    isTokenSelected 
  } = useTokenSelection(onSelectToken as any);

  const DownArrow = () => (
    <svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3.86961 5.46501C3.48635 6.14002 2.51365 6.14002 2.13039 5.46501L0.384009 2.38918C0.00550654 1.72253 0.487018 0.895432 1.25362 0.895432L4.74638 0.895432C5.51298 0.895432 5.99449 1.72253 5.61599 2.38917L3.86961 5.46501Z" fill="#ED2626"/>
    </svg>
  );
  
  const UpArrow = () => (
    <svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M2.13039 0.535C2.51365 -0.14 3.48635 -0.14 3.86961 0.535L5.61599 3.61083C5.99449 4.27747 5.51298 5.10457 4.74638 5.10457H1.25362C0.487018 5.10457 0.00550654 4.27747 0.384009 3.61082L2.13039 0.535Z" fill="#16A34A"/>
    </svg>
  );

  const SortArrows = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="white"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="white"/>
    </svg>
  );

  const ActiveUpArrow = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="#444"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="#16A34A"/>
    </svg>
  );

  const ActiveDownArrow = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="#ED2626"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="#444"/>
    </svg>
  );

  // Sort indicator helper - Added types
  const getSortArrow = (fieldName: keyof Token, category: string) => {
    if (sortConfig.category !== category) return <SortArrows />;
    if (sortConfig.key === fieldName) {
      return sortConfig.direction === 'asc' ? <ActiveUpArrow /> : <ActiveDownArrow />;
    }
    return <SortArrows />;
  };

  // Handle sorting - Added types
  const requestSort = (key: keyof Token, category: string) => {
    let direction: 'asc' | 'desc' = 'desc';
    if (sortConfig.key === key && sortConfig.direction === 'desc' && sortConfig.category === category) {
      direction = 'asc';
    }
    setSortConfig({ key, direction, category });

    if (marketData[category]) {
      const sortedData = [...marketData[category]].sort((a: Token, b: Token) => {
        const valA = a[key] ?? 0; // Handle null/undefined with ?? 0
        const valB = b[key] ?? 0; // Handle null/undefined with ?? 0
        
        if (typeof valA === 'number' && typeof valB === 'number') {
            return direction === 'asc' ? valA - valB : valB - valA;
        } 
        // Add string comparison if needed for other keys
        // if (typeof valA === 'string' && typeof valB === 'string') {
        //     return direction === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
        // }
        return 0;
      });

      setMarketData((prevData) => ({
        ...prevData,
        [category]: sortedData
      }));
    }
  };

  // Fetch Market Data
  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        setLoading(true);
        const network = selectedNetwork === "Global" ? "universal" : selectedNetwork.toLowerCase();
        const response = await homeAPI.getNetworkHighlights(network);
        const highlights = response?.data ? response.data : response;

        console.log("Processed data:", highlights);
        
        const network_icon = await homeAPI.getNetworkIcon(network);
        console.log("Network Icon",network_icon)
        if (highlights && typeof highlights === 'object' && !Array.isArray(highlights)) {
            // Type assertion to ensure highlights match MarketData structure
            const typedHighlights = highlights as MarketData;
            // Ensure all category arrays exist, even if empty
            const sanitizedData: MarketData = {
              trending: typedHighlights.trending || [],
              gainers: typedHighlights.gainers || [],
              losers: typedHighlights.losers || [],
              new: typedHighlights.new || [],
            };
            setMarketData(sanitizedData);
            setError(null);
        } else {
          console.error("Invalid data format:", response);
          setError("Invalid data format received from API");
          setMarketData({ trending: [], gainers: [], losers: [], new: [] }); // Reset data on error
        }
      } catch (err: unknown) { // Catch as unknown
        console.error("Error fetching market data:", err);
        // Type guard for error message
        let message = "Failed to load market data";
        if (err instanceof Error) {
           message = err.message;
        }
        setError(message);
        setMarketData({ trending: [], gainers: [], losers: [], new: [] }); // Reset data on error
      } finally {
        setLoading(false);
      }
    };

    fetchMarketData();
  }, [selectedNetwork]);

  // Format large numbers (Trillion, Billion, Million) - Added type
  const formatNumber = (num: number | null): string => {
    if (num === null || num === undefined || isNaN(num)) {
      return '0';
    }
    if (num >= 1e12) return (num / 1e12).toFixed(2) + "T";
    if (num >= 1e9) return (num / 1e9).toFixed(2) + "B";
    if (num >= 1e6) return (num / 1e6).toFixed(2) + "M";
    // For numbers less than 1 Million, use standard locale string with commas
    return num.toLocaleString('en-US', { maximumFractionDigits: 0 }); 
  };

  // Format volume (Billion, Million, K) - Added type
  const formatVolume = (value: number | null): string => {
    if (value === null || value === undefined || isNaN(value)) {
      return '0';
    }
    if (value >= 1e9) return (value / 1e9).toFixed(2) + 'B';
    if (value >= 1e6) return (value / 1e6).toFixed(2) + 'M';
    if (value >= 1e3) return (value / 1e3).toFixed(2) + 'K';
    return value.toFixed(2);
  };

  // Change items per category handler - Added type
  const handleItemsPerCategoryChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(event.target.value, 10);
    setItemsPerCategory(value);
  };

  // Handle token click - Added types
  const handleRowClick = async (e: MouseEvent<HTMLDivElement>, token: Token) => {
    console.log("Row clicked for token:", token);
    await handleTokenSelect(e, token);
    localStorage.setItem("activeToken", JSON.stringify(token));
    navigate('/trade');
  };

  // Active token select icon
  const ActiveTokenSelectIcon = () => (
    <svg width="11" height="12" viewBox="0 0 11 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.42857 0.770645H1.57143C0.704956 0.770645 0 1.48749 0 2.36857V8.05007C0 8.93116 0.704956 9.648 1.57143 9.648H5.10714V11.1572H3.28903C3.07207 11.1572 2.89617 11.336 2.89617 11.5567C2.89617 11.7773 3.07207 11.9561 3.28903 11.9561H7.71097C7.92812 11.9561 8.10383 11.7773 8.10383 11.5567C8.10383 11.336 7.92812 11.1572 7.71097 11.1572H5.89286V9.648H9.42857C10.2952 9.648 11 8.93116 11 8.05007V2.36857C11 1.48749 10.2952 0.770645 9.42857 0.770645ZM10.2143 8.05007C10.2143 8.49071 9.86171 8.84904 9.42857 8.84904H1.57143C1.1381 8.84904 0.785714 8.49071 0.785714 8.05007V2.36857C0.785714 1.92793 1.1381 1.56961 1.57143 1.56961H9.42857C9.86171 1.56961 10.2143 1.92793 10.2143 2.36857V8.05007Z" fill="#0A7AFF"/>
      <path d="M7.85714 6.79506H3.14286C2.9259 6.79506 2.75 6.97393 2.75 7.19454C2.75 7.41515 2.9259 7.59402 3.14286 7.59402H7.85714C8.07429 7.59402 8.25 7.41515 8.25 7.19454C8.25 6.97393 8.07429 6.79506 7.85714 6.79506ZM4.28843 6.0669C4.36305 6.15916 4.47431 6.21261 4.59171 6.21261C4.70911 6.21261 4.82036 6.15916 4.89498 6.0669L5.59073 5.20747L5.81689 5.48718C5.89017 5.57769 5.99874 5.63094 6.11384 5.63289C6.22433 5.62528 6.33943 5.5851 6.41539 5.49713L8.15217 3.48782C8.29527 3.32202 8.27954 3.06961 8.11649 2.9241C7.95382 2.77839 7.70522 2.79419 7.56212 2.96038L6.13034 4.61683L5.89401 4.32502C5.81958 4.23275 5.70813 4.17931 5.59073 4.17931C5.47334 4.17931 5.36208 4.23275 5.28746 4.32502L4.59171 5.18445L4.11445 4.59498C3.97672 4.4245 3.72907 4.39992 3.56161 4.54036C3.39396 4.68042 3.36998 4.93224 3.5079 5.10253L4.28843 6.0669Z" fill="#0A7AFF"/>
    </svg>
  );

  // Inactive token select icon
  const InactiveTokenSelectIcon = () => (
    <svg width="11" height="12" viewBox="0 0 11 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.42857 0.770645H1.57143C0.704956 0.770645 0 1.48749 0 2.36857V8.05007C0 8.93116 0.704956 9.648 1.57143 9.648H5.10714V11.1572H3.28903C3.07207 11.1572 2.89617 11.336 2.89617 11.5567C2.89617 11.7773 3.07207 11.9561 3.28903 11.9561H7.71097C7.92812 11.9561 8.10383 11.7773 8.10383 11.5567C8.10383 11.336 7.92812 11.1572 7.71097 11.1572H5.89286V9.648H9.42857C10.2952 9.648 11 8.93116 11 8.05007V2.36857C11 1.48749 10.2952 0.770645 9.42857 0.770645ZM10.2143 8.05007C10.2143 8.49071 9.86171 8.84904 9.42857 8.84904H1.57143C1.1381 8.84904 0.785714 8.49071 0.785714 8.05007V2.36857C0.785714 1.92793 1.1381 1.56961 1.57143 1.56961H9.42857C9.86171 1.56961 10.2143 1.92793 10.2143 2.36857V8.05007Z" fill="white"/>
      <path d="M7.85714 6.79506H3.14286C2.9259 6.79506 2.75 6.97393 2.75 7.19454C2.75 7.41515 2.9259 7.59402 3.14286 7.59402H7.85714C8.07429 7.59402 8.25 7.41515 8.25 7.19454C8.25 6.97393 8.07429 6.79506 7.85714 6.79506ZM4.28843 6.0669C4.36305 6.15916 4.47431 6.21261 4.59171 6.21261C4.70911 6.21261 4.82036 6.15916 4.89498 6.0669L5.59073 5.20747L5.81689 5.48718C5.89017 5.57769 5.99874 5.63094 6.11384 5.63289C6.22433 5.62528 6.33943 5.5851 6.41539 5.49713L8.15217 3.48782C8.29527 3.32202 8.27954 3.06961 8.11649 2.9241C7.95382 2.77839 7.70522 2.79419 7.56212 2.96038L6.13034 4.61683L5.89401 4.32502C5.81958 4.23275 5.70813 4.17931 5.59073 4.17931C5.47334 4.17931 5.36208 4.23275 5.28746 4.32502L4.59171 5.18445L4.11445 4.59498C3.97672 4.4245 3.72907 4.39992 3.56161 4.54036C3.39396 4.68042 3.36998 4.93224 3.5079 5.10253L4.28843 6.0669Z" fill="white"/>
    </svg>
  );


  return (
    <div className="flex flex-col p-6 bg-[#141416]">
      {/* Removed Items per category selector */}
      
      {/* Grid layout for categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {categories.map(({ title, key }, index) => (
          <div key={index} className="bg-[#181C20] p-5 rounded-xl w-full overflow-hidden">
            {/* Header */}
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-2xl text-white font-semibold">{title}</h3>
              <div className="flex items-center">
                {/* Use correct type for marketData access */}
                <span className="text-white text-sm mr-2">
                  {marketData[key]?.length || 0} items 
                </span>
                 {/* Icon */}
                 <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12.8571 0.455307H2.14286C0.961304 0.455307 0 1.43282 0 2.6343V10.3818C0 11.5833 0.961304 12.5608 2.14286 12.5608H6.96429V14.6188H4.48504C4.18919 14.6188 3.94932 14.8627 3.94932 15.1635C3.94932 15.4643 4.18919 15.7083 4.48504 15.7083H10.515C10.8111 15.7083 11.0507 15.4643 11.0507 15.1635C11.0507 14.8627 10.8111 14.6188 10.515 14.6188H8.03571V12.5608H12.8571C14.039 12.5608 15 11.5833 15 10.3818V2.6343C15 1.43282 14.039 0.455307 12.8571 0.455307ZM13.9286 10.3818C13.9286 10.9827 13.4478 11.4713 12.8571 11.4713H2.14286C1.55195 11.4713 1.07143 10.9827 1.07143 10.3818V2.6343C1.07143 2.03343 1.55195 1.5448 2.14286 1.5448H12.8571C13.4478 1.5448 13.9286 2.03343 13.9286 2.6343V10.3818Z" fill="white"/>
                  <path d="M10.7143 8.67041H4.28571C3.98987 8.67041 3.75 8.91433 3.75 9.21516C3.75 9.516 3.98987 9.75991 4.28571 9.75991H10.7143C11.0104 9.75991 11.25 9.516 11.25 9.21516C11.25 8.91433 11.0104 8.67041 10.7143 8.67041ZM5.84787 7.67747C5.94962 7.80328 6.10134 7.87617 6.26142 7.87617C6.42151 7.87617 6.57322 7.80328 6.67498 7.67747L7.62373 6.50552L7.93213 6.88695C8.03205 7.01037 8.18011 7.08298 8.33705 7.08564C8.48772 7.07527 8.64467 7.02047 8.74826 6.90051L11.1166 4.16055C11.3117 3.93445 11.2903 3.59026 11.0679 3.39183C10.8461 3.19314 10.5071 3.21468 10.312 3.44131L8.35955 5.7001L8.03728 5.30218C7.93579 5.17636 7.78381 5.10348 7.62373 5.10348C7.46364 5.10348 7.31192 5.17636 7.21017 5.30218L6.26142 6.47413L5.61061 5.67031C5.4228 5.43783 5.0851 5.40432 4.85674 5.59583C4.62812 5.78681 4.59542 6.1302 4.7835 6.36241L5.84787 7.67747Z" fill="white"/>
                </svg>
              </div>
            </div>

            {/* Column Headers */}
            <div className="flex justify-between text-white text-sm pb-2">
              {/* Use keyof Token for sort keys */}
              <div className="flex items-center">
                <span className="cursor-pointer flex items-center" onClick={() => requestSort('market_cap', key)}>
                  Token / MCap 
                  <span className="ml-1">{getSortArrow('market_cap', key)}</span>
                </span>
                <span className="mx-1">/ </span>
                <span className="cursor-pointer flex items-center" onClick={() => requestSort('total_volume', key)}>
                  24h Vol
                  <span className="ml-1">{getSortArrow('total_volume', key)}</span>
                </span>
              </div>
             
              <div className="flex items-center">
                <span className="cursor-pointer flex items-center" onClick={() => requestSort('current_price', key)}>
                  Price 
                  <span className="ml-1">{getSortArrow('current_price', key)}</span>
                </span>
                <span className="mx-1">/ </span>
                <span className="cursor-pointer flex items-center" onClick={() => requestSort('price_change_percentage_24h', key)}>
                  24h%
                  <span className="ml-1">{getSortArrow('price_change_percentage_24h', key)}</span>
                </span>
              </div>
            </div>

            {/* Data Loading & Error Handling */}
            {loading ? (
  <div className="flex flex-col gap-3 mt-4">
    {Array.from({ length: 8}).map((_, i) => (
      <div key={i} className="flex justify-between items-center py-3 border-b border-gray-700 px-2 rounded-lg animate-pulse bg-[#181C20]">
        
        {/* Left Skeleton (Image + Info) */}
        <div className="flex items-center gap-3">
          <div className="relative w-8 h-8">
            <div className="w-8 h-8 rounded-full bg-gray-700" />
            <div className="absolute bottom-[-4px] right-[-4px] w-4 h-4 rounded-full border-2 border-[#141416] bg-gray-700" />
          </div>

          <div>
            <div className="w-14 h-4 bg-gray-700 rounded mb-1" />
            <div className="w-24 h-3 bg-gray-700 rounded" />
          </div>
        </div>

        {/* Right Skeleton (Price + Change + Icon) */}
        <div className="flex items-center gap-2">
          <div className="flex flex-col items-end">
            <div className="w-16 h-5 bg-gray-700 rounded mb-1" />
            <div className="w-12 h-3 bg-gray-700 rounded" />
          </div>
          <div className="w-6 h-6 bg-gray-700 rounded-full" />
        </div>
      </div>
    ))}
  </div>
) : error ? (

              <p className="text-red-500 mt-4 text-center">{error}</p>
            ) : (
              <div className="overflow-y-auto max-h-[420px] mt-3 scrollbar-thin scrollbar-thumb-[#BBBBBB]">
                {marketData[key]?.map((token: Token, i: number) => ( // Added types here
                  <div 
                    key={token.id || i} // Use token.id if available, fallback to index
                    className="flex justify-between items-center py-3 border-b border-gray-700 hover:bg-[#1E2328] cursor-pointer transition-all duration-200 px-2 rounded-lg"
                    onClick={(e) => handleRowClick(e,token)}
                  >
                    {/* Left: Token Info */}
                    <div className="flex items-center gap-3">
                    <div className="relative w-8 h-8">
  <img src={token.image} alt={token.name} className="w-8 h-8 rounded-full" />
  {token.baseimage && (
    <img
      src={cryptoIcons[token.network]}
      alt="base"
      className="absolute bottom-[-4px] right-[-4px] w-4 h-4 rounded-full border-2 border-[#141416] shadow-md"
    />
  )}
</div>

                      <div>
                        <p className="text-white text-lg">{token.symbol ? token.symbol.toUpperCase() : ''}</p>
                        <p className="text-md text-gray-400">
                          ${formatNumber(token.market_cap)} • ${formatNumber(token.total_volume)}
                        </p>
                      </div>
                    </div>

                    {/* Right: Price & Change */}
                    <div className="flex items-center gap-2">
                      <div className="flex flex-col items-end">
                        {/* Use the new formatPriceWithSubscript function */}
                        <p className="text-white text-xl font-semibold">
                          {formatPriceWithSubscript(token.current_price)}
                        </p>
                        <p
                          className={`text-md flex items-center gap-1 ${ (token.price_change_percentage_24h ?? 0) < 0 ? "text-red-500" : "text-green-500" }`}
                        >
                          {(token.price_change_percentage_24h ?? 0) < 0 ? <DownArrow /> : <UpArrow />}
                          {Math.abs(token.price_change_percentage_24h ?? 0).toFixed(2)}%
                        </p>
                      </div>
                      <button 
                        className="p-2 hover:bg-gray-700 rounded-lg transition-colors duration-200"
                        onClick={(e: MouseEvent<HTMLButtonElement>) => { // Added type
                          e.stopPropagation(); 
                          handleTokenSelect(e, token);
                        }}
                      >
                        {isTokenSelected(token) ? 
                          <ActiveTokenSelectIcon /> : 
                          <InactiveTokenSelectIcon />
                        }
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Market;