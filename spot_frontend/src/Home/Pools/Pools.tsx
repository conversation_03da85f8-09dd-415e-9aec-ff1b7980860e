import React from "react";
const fetchPoolsData = async () => {
  setLoading(true);
  setError(null);
  
  try {
    let allPools = [];
    const poolsMap = new Map(); // Use a map to avoid duplicates
    
    // For each supported network, fetch pools
    for (const network of supportedNetworks) {
      try {
        console.log(`Fetching pools for network: ${network}`);
        
        // API call to get pools for this network
        const response = await axios.get(`${API_BASE_URL}/pools/${network.toLowerCase()}`);
        
        if (response && response.data && response.data.success) {
          const networkPools = response.data.data || [];
          
          if (networkPools.length > 0) {
            console.log(`Found ${networkPools.length} pools for ${network}`);
            
            // Add network info to each pool and normalize
            const processedPools = networkPools.map(pool => ({
              ...pool,
              network: network,
              id: `${pool.protocol}-${pool.pairAddress || pool.address}`, // Create unique ID
              normalizedApy: parseFloat(pool.apy || "0"),
              normalizedTvl: parseFloat(pool.tvl || "0")
            }));
            
            // Add to map using unique ID
            processedPools.forEach(pool => {
              poolsMap.set(pool.id, pool);
            });
          } else {
            console.warn(`No pools returned for network ${network}`);
          }
        } else {
          console.warn(`Invalid response for ${network}:`, response?.data);
        }
      } catch (networkError) {
        console.error(`Error fetching pools for ${network}:`, networkError);
        // Continue with other networks instead of failing completely
      }
    }
    
    // Convert map to array
    allPools = Array.from(poolsMap.values());
    
    if (allPools.length === 0) {
      // No pools from any network
      setError('Could not retrieve liquidity pools from any supported network. Please try again later.');
      setPools([]);
    } else {
      console.log(`Total unique pools: ${allPools.length}`);
      setPools(allPools);
      
      // Apply initial filtering and sorting
      filterAndSortPools(allPools);
    }
  } catch (error) {
    console.error('Error fetching pools:', error);
    setError(error.message || 'Failed to fetch liquidity pools. Please try again later.');
    setPools([]);
  } finally {
    setLoading(false);
  }
};

// Function to filter and sort pools based on current state
const filterAndSortPools = useCallback((allPools = null, refresh = false) => {
  const poolsToProcess = allPools || pools;
  
  if (!poolsToProcess || poolsToProcess.length === 0) {
    console.log('No pools to process');
    setFilteredPools([]);
    return;
  }
  
  try {
    // Set loading status if this is a refresh
    if (refresh) {
      setUpdating(true);
    }
    
    console.log(`Processing ${poolsToProcess.length} pools for filtering and sorting`);
    
    // Start with all pools
    let result = [...poolsToProcess];
    
    // Apply network filter
    if (networkFilter !== 'all') {
      result = result.filter(pool => pool.network.toLowerCase() === networkFilter.toLowerCase());
    }
    
    // Apply protocol filter
    if (protocolFilter !== 'all') {
      result = result.filter(pool => pool.protocol.toLowerCase() === protocolFilter.toLowerCase());
    }
    
    // Apply token filter if any is selected
    if (tokenFilter) {
      result = result.filter(pool => {
        const tokenSymbol = tokenFilter.toUpperCase();
        const token0 = pool.token0?.symbol?.toUpperCase() || '';
        const token1 = pool.token1?.symbol?.toUpperCase() || '';
        
        return token0 === tokenSymbol || token1 === tokenSymbol;
      });
    }
    
    // Apply search filter
    if (searchTerm.trim() !== '') {
      const term = searchTerm.trim().toLowerCase();
      result = result.filter(pool => {
        const token0 = pool.token0?.symbol?.toLowerCase() || '';
        const token1 = pool.token1?.symbol?.toLowerCase() || '';
        const pairName = `${token0}/${token1}`;
        const reversePairName = `${token1}/${token0}`;
        
        return token0.includes(term) || 
               token1.includes(term) || 
               pairName.includes(term) || 
               reversePairName.includes(term) ||
               pool.protocol.toLowerCase().includes(term);
      });
    }
    
    // Sort the results
    result.sort((a, b) => {
      if (sortBy === 'tvl') {
        return sortDirection === 'desc' ? 
          (b.normalizedTvl || 0) - (a.normalizedTvl || 0) : 
          (a.normalizedTvl || 0) - (b.normalizedTvl || 0);
      } else if (sortBy === 'apy') {
        return sortDirection === 'desc' ? 
          (b.normalizedApy || 0) - (a.normalizedApy || 0) : 
          (a.normalizedApy || 0) - (b.normalizedApy || 0);
      }
      // Default sorting by TVL
      return (b.normalizedTvl || 0) - (a.normalizedTvl || 0);
    });
    
    // Paginate results
    const startIndex = (currentPage - 1) * pageSize;
    const paginatedResults = result.slice(startIndex, startIndex + pageSize);
    
    // Update state
    setFilteredPools(paginatedResults);
    setTotalPools(result.length);
    setTotalPages(Math.ceil(result.length / pageSize));
    
    if (refresh) {
      setUpdating(false);
    }
    
    console.log(`Filtered to ${result.length} pools, showing ${paginatedResults.length} on page ${currentPage}`);
    
  } catch (error) {
    console.error('Error filtering and sorting pools:', error);
    setFilteredPools([]);
    setTotalPools(0);
    setTotalPages(1);
    
    if (refresh) {
      setUpdating(false);
    }
  }
}, [pools, networkFilter, protocolFilter, tokenFilter, searchTerm, sortBy, sortDirection, currentPage, pageSize]);

// Update the ErrorState component to be more informative
const ErrorState = ({ message }) => (
  <div className="flex flex-col items-center justify-center p-10 bg-[#1a1d22] rounded-lg mt-4">
    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-red-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
    </svg>
    <h3 className="text-xl font-semibold text-white mb-2">Unable to Load Pools</h3>
    <p className="text-gray-400 text-center mb-6">{message}</p>
    <button 
      onClick={() => fetchPoolsData()}
      className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-white"
    >
      Try Again
    </button>
  </div>
);

// Update the EmptyState component for better feedback
const EmptyState = () => (
  <div className="flex flex-col items-center justify-center p-10 bg-[#1a1d22] rounded-lg mt-4">
    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
    </svg>
    <h3 className="text-xl font-semibold text-white mb-2">No Pools Found</h3>
    <p className="text-gray-400 text-center">
      No pools match your current filters. Try adjusting your search criteria.
    </p>
  </div>
); 