import React, { useState, useEffect } from 'react';
import { marketAPI } from '../../utils/api';

const AllCoinsTable = () => {
  const [coins, setCoins] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<null>(null);

  useEffect(() => {
    fetchCoins();
  }, []);

  const fetchCoins = async () => {
    try {
      setIsLoading(true);
      const data = await marketAPI.getData();
      setCoins(data);
    } catch (err) {
      setError('Failed to fetch coin data');
      console.error('Error fetching coins:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>AllCoinsTable</div>
  )
}

export default AllCoinsTable