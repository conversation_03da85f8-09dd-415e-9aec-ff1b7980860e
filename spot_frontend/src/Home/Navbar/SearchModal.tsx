import React, { useEffect, useState } from "react";
import { FaSearch } from "react-icons/fa";
import { AiOutlineFile, AiOutlineGlobal } from "react-icons/ai";
import RadarTable from "./RadarTable";
import AllCoinsTable from "./AllCoinsTable";
import FavoritesTable from "./FavoritesTable";
import HighlightsTable from "./HighlightsTable";
import { useNavigate } from "react-router-dom";
import { useTokenSelection } from "../../hooks/useTokenSelection";
import { usePulseData } from "../../hooks/usePulseData";
import axios from "axios";
import { homeAPI } from "@/utils/api";
import { useRef } from "react";
// API key removed for security - use backend API instead
const SearchModal = ({ onClose }: { onClose: () => void }) => {
  const [activeTab, setActiveTab] = useState<string>("Highlights");
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [loading,setLoading] = useState(false);
  const { isTokenSelected, handleTokenSelect,setSelectedTokens } = useTokenSelection();
  const { setActiveToken } = usePulseData();
  const navigate = useNavigate();
  const [sortConfig, setSortConfig] = useState({
    key: "market_cap",
    direction: "desc"
  });
  const modalRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);
  // Close modal on Escape key press
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [onClose]);

  const handleRowClick = async (e: any, token: any) => {
    console.log("🚀 SEARCH RESULT CLICKED - Starting navigation process");
    console.log("🚀 CLICKED TOKEN OBJECT:", JSON.stringify(token, null, 2));

    // Extract token address from the token object
    const tokenAddress = token.id || token.address || token.contract;

    console.log("🚀 ADDRESS EXTRACTION:");
    console.log("  - token.id:", token.id);
    console.log("  - token.address:", token.address);
    console.log("  - token.contract:", token.contract);
    console.log("  - Selected address:", tokenAddress);

    if (!tokenAddress) {
      console.error("❌ NO TOKEN ADDRESS FOUND - Falling back to original behavior");
      console.error("❌ Token object keys:", Object.keys(token));
      // Fallback to original behavior if no address found
      await handleTokenSelect(e, token);
      localStorage.setItem("activeToken", JSON.stringify(token));
      if (window.location.pathname === "/trade") {
        onClose();
      } else {
        navigate("/trade", { state: { selectedToken: token } });
      }
      return;
    }

    // Basic validation for token address format
    const isValidAddress = tokenAddress && typeof tokenAddress === 'string' && tokenAddress.length > 10;

    console.log("🚀 ADDRESS VALIDATION:");
    console.log("  - Address:", tokenAddress);
    console.log("  - Type:", typeof tokenAddress);
    console.log("  - Length:", tokenAddress?.length);
    console.log("  - Is valid:", isValidAddress);

    if (!isValidAddress) {
      console.warn("⚠️ INVALID TOKEN ADDRESS FORMAT - Falling back to original behavior");
      // Fallback to original behavior for invalid addresses
      await handleTokenSelect(e, token);
      localStorage.setItem("activeToken", JSON.stringify(token));
      if (window.location.pathname === "/trade") {
        onClose();
      } else {
        navigate("/trade", { state: { selectedToken: token } });
      }
      return;
    }

    console.log("✅ NAVIGATION APPROVED - Proceeding to Pulse Trade page");
    console.log("✅ Target URL: /pulse-trade/" + tokenAddress);
    console.log("✅ Token summary:", {
      name: token.name,
      symbol: token.symbol,
      network: token.network,
      address: tokenAddress,
      hasRawData: !!(token.raw_pair_data || token.raw_token_data)
    });

    // Store additional debugging info BEFORE any operations
    const navigationData = {
      timestamp: new Date().toISOString(),
      sourceToken: token,
      extractedAddress: tokenAddress,
      targetUrl: `/pulse-trade/${tokenAddress}`,
      currentPath: window.location.pathname
    };

    localStorage.setItem("lastSearchNavigation", JSON.stringify(navigationData));
    console.log("🚀 Navigation data stored for debugging:", navigationData);

    // Check if we're already on a pulse-trade page
    const currentPath = window.location.pathname;
    const isPulseTradeRoute = currentPath.startsWith('/pulse-trade/');

    console.log("🔍 DEBUGGING ROUTE DETECTION:");
    console.log("  - Current path:", currentPath);
    console.log("  - Is pulse-trade route:", isPulseTradeRoute);
    console.log("  - Target address:", tokenAddress);

    // 🎯 IMPLEMENT PULSE TOKEN MANAGEMENT FLOW
    // 1. Add token to pulseTokens array (if not already present)
    // 2. Set token as activePulseToken
    // This automatically handles both operations using the usePulseData hook
    console.log("🎯 Adding token to pulse data and setting as active...");
    setActiveToken(e, token);
    console.log("✅ Token added to pulseTokens and set as activePulseToken");

    // Wait a moment for localStorage to be updated
    await new Promise(resolve => setTimeout(resolve, 50));

    if (isPulseTradeRoute) {
      console.log("🔄 ALREADY ON PULSE-TRADE PAGE - Applying comprehensive fix");

      // Method 1: Force navigation to trigger useParams change
      console.log("🔄 Method 1: Forcing navigation with new address");
      navigate(`/pulse-trade/${tokenAddress}`, { replace: false });

      // Method 2: Immediate event dispatch (no timeout)
      console.log("🔄 Method 2: Dispatching immediate events");
      const immediateEvent = new CustomEvent('pulseTradeTokenChange', {
        detail: {
          tokenAddress,
          token: token,
          timestamp: Date.now(),
          immediate: true
        }
      });
      window.dispatchEvent(immediateEvent);

      // Method 3: Force localStorage-based update
      console.log("🔄 Method 3: Forcing localStorage update event");
      const storageEvent = new CustomEvent('pulseDataChanged', {
        detail: {
          activePulseToken: JSON.parse(localStorage.getItem('activePulseToken') || '{}'),
          forceUpdate: true,
          source: 'searchModal'
        }
      });
      window.dispatchEvent(storageEvent);

      // Method 4: Delayed events as backup (in case immediate events are missed)
      setTimeout(() => {
        console.log("🔄 Method 4: Dispatching delayed backup events");
        const delayedEvent = new CustomEvent('pulseTradeTokenChange', {
          detail: {
            tokenAddress,
            token: token,
            timestamp: Date.now(),
            delayed: true
          }
        });
        window.dispatchEvent(delayedEvent);

        const delayedStorageEvent = new CustomEvent('pulseDataChanged', {
          detail: {
            activePulseToken: JSON.parse(localStorage.getItem('activePulseToken') || '{}'),
            forceUpdate: true,
            source: 'searchModalDelayed'
          }
        });
        window.dispatchEvent(delayedStorageEvent);
      }, 300);

    } else {
      console.log("🚀 Navigating to pulse-trade page from different route");
      navigate(`/pulse-trade/${tokenAddress}`);
    }

    onClose(); // Close the search modal
    console.log("🚀 Navigation command executed - should be redirecting now");
  };
    useEffect(() => {
      const fetchCoins = async () => {
        const trimmedQuery = searchQuery.trim();
    
        if (!trimmedQuery) {
          setSearchResults([]);
          return;
        }
        setLoading(true);
        try {
          const response = await homeAPI.fetchSearchResults(trimmedQuery);

          console.log("🔍 RAW SEARCH API RESPONSE:", response);
          console.log("🔍 RESPONSE DATA:", response?.data);

          const results = response?.data?.data || [];
          console.log("🔍 SEARCH RESULTS ARRAY:", results);
          console.log("🔍 SEARCH RESULTS LENGTH:", results.length);

          // Log the complete structure of the first result for analysis
          if (results.length > 0) {
            console.log("🔍 FIRST SEARCH RESULT STRUCTURE:", JSON.stringify(results[0], null, 2));
          }

          // Flatten the data from all token pairs
          const baseTokens = results.flatMap((item: any, itemIndex: number) => {
            console.log(`🔍 PROCESSING ITEM ${itemIndex}:`, item);

            if (!item?.pairs || !Array.isArray(item.pairs)) {
              console.log(`⚠️ Item ${itemIndex} has no pairs or pairs is not an array`);
              return [];
            }

            // Filter pairs by Solana blockchain and preferred types (pumpswap, pumpfun)
            const filteredPairs = item.pairs.filter((pairItem: any) => {
              const isValidBlockchain = pairItem?.blockchain === 'Solana';
              const isPreferredType = ['pumpswap', 'pumpfun'].includes(pairItem?.type);

              console.log(`🔍 PAIR FILTER CHECK:`, {
                blockchain: pairItem?.blockchain,
                type: pairItem?.type,
                isValidBlockchain,
                isPreferredType,
                pairAddress: pairItem?.address
              });

              return isValidBlockchain && isPreferredType;
            });

            console.log(`🔍 FILTERED PAIRS (${filteredPairs.length}/${item.pairs.length}):`, filteredPairs);

            // If no preferred pairs found, fall back to any Solana pair
            const pairsToProcess = filteredPairs.length > 0 ? filteredPairs :
              item.pairs.filter((p: any) => p?.blockchain === 'Solana');

            // Take only the first pair to avoid duplicates
            const selectedPair = pairsToProcess[0];

            if (!selectedPair) {
              console.log(`⚠️ No valid Solana pairs found for item ${itemIndex}`);
              return [];
            }

            console.log(`🔍 SELECTED PAIR:`, selectedPair);

            const baseTokenKey = selectedPair?.baseToken;
            const token = selectedPair?.[baseTokenKey];

            console.log(`🔍 BASE TOKEN KEY: ${baseTokenKey}`);
            console.log(`🔍 TOKEN OBJECT:`, token);

            if (!token) {
              console.log(`⚠️ No token found for baseTokenKey: ${baseTokenKey}`);
              return [];
            }

            const processedToken = {
              id: token.address,
              name: token.name,
              symbol: token.symbol,
              image: token.logo || '',
              network: selectedPair?.blockchain || '',
              current_price: selectedPair?.price || 0,
              price_change_percentage_24h: selectedPair?.price_change_24h || 0,
              market_cap: token?.circulatingSupply
                ? token.price * token.circulatingSupply
                : null,
              total_volume: selectedPair.volume24h || 0,
              createdAt: selectedPair.createdAt || null,
              // Add pool/pair address information
              pool_address: selectedPair.address, // This is the key addition!
              pair_type: selectedPair.type,
              exchange_name: selectedPair.exchange?.name || 'Unknown',
              exchange_logo: selectedPair.exchange?.logo || '',
              liquidity: selectedPair.liquidity || 0,
              // Add additional fields for debugging
              raw_pair_data: selectedPair,
              raw_token_data: token,
              base_token_key: baseTokenKey
            };

            console.log(`✅ PROCESSED TOKEN WITH POOL ADDRESS:`, {
              symbol: processedToken.symbol,
              tokenAddress: processedToken.id,
              poolAddress: processedToken.pool_address,
              pairType: processedToken.pair_type,
              exchange: processedToken.exchange_name
            });

            return [processedToken]; // Return as array since we're only processing one pair
          });
    
          // Flatten the results since we're now returning arrays
          const flattenedTokens = baseTokens.flat();

          const uniqueTokensMap = new Map();
          flattenedTokens.forEach((token: any) => {
            if (token && token.id && !uniqueTokensMap.has(token.id)) {
              uniqueTokensMap.set(token.id, token);
            }
          });

          const uniqueTokens = Array.from(uniqueTokensMap.values());
          console.log("✅ FINAL UNIQUE TOKENS WITH POOL DATA:", uniqueTokens);
          console.log("✅ TOKENS WITH POOL ADDRESSES:", uniqueTokens.filter(t => t.pool_address).length);
          setSearchResults(uniqueTokens);
          setLoading(false);
        } catch (err) {
          console.error('Failed to fetch data from Mobula API:', err);
        }
      };
    
      const debounceTimeout = setTimeout(fetchCoins, 300); // debounce
    
      return () => clearTimeout(debounceTimeout); // cleanup
    }, [searchQuery]);
    
  
  
  // Define tabs with SVGs
  const DownArrow = () => (
    <svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3.86961 5.46501C3.48635 6.14002 2.51365 6.14002 2.13039 5.46501L0.384009 2.38918C0.00550654 1.72253 0.487018 0.895432 1.25362 0.895432L4.74638 0.895432C5.51298 0.895432 5.99449 1.72253 5.61599 2.38917L3.86961 5.46501Z" fill="#ED2626"/>
    </svg>
  );
  
  const UpArrow = () => (
    <svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M2.13039 0.535C2.51365 -0.14 3.48635 -0.14 3.86961 0.535L5.61599 3.61083C5.99449 4.27747 5.51298 5.10457 4.74638 5.10457H1.25362C0.487018 5.10457 0.00550654 4.27747 0.384009 3.61082L2.13039 0.535Z" fill="#16A34A"/>
    </svg>
  );

  const SortArrows = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="white"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="white"/>
    </svg>
  );

  const ActiveUpArrow = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="#444"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="#16A34A"/>
    </svg>
  );

  const ActiveDownArrow = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="#ED2626"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="#444"/>
    </svg>
  );
  const ActiveTokenSelectIcon = () => (
    <svg width="11" height="12" viewBox="0 0 11 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.42857 0.770645H1.57143C0.704956 0.770645 0 1.48749 0 2.36857V8.05007C0 8.93116 0.704956 9.648 1.57143 9.648H5.10714V11.1572H3.28903C3.07207 11.1572 2.89617 11.336 2.89617 11.5567C2.89617 11.7773 3.07207 11.9561 3.28903 11.9561H7.71097C7.92812 11.9561 8.10383 11.7773 8.10383 11.5567C8.10383 11.336 7.92812 11.1572 7.71097 11.1572H5.89286V9.648H9.42857C10.2952 9.648 11 8.93116 11 8.05007V2.36857C11 1.48749 10.2952 0.770645 9.42857 0.770645ZM10.2143 8.05007C10.2143 8.49071 9.86171 8.84904 9.42857 8.84904H1.57143C1.1381 8.84904 0.785714 8.49071 0.785714 8.05007V2.36857C0.785714 1.92793 1.1381 1.56961 1.57143 1.56961H9.42857C9.86171 1.56961 10.2143 1.92793 10.2143 2.36857V8.05007Z" fill="#0A7AFF"/>
      <path d="M7.85714 6.79506H3.14286C2.9259 6.79506 2.75 6.97393 2.75 7.19454C2.75 7.41515 2.9259 7.59402 3.14286 7.59402H7.85714C8.07429 7.59402 8.25 7.41515 8.25 7.19454C8.25 6.97393 8.07429 6.79506 7.85714 6.79506ZM4.28843 6.0669C4.36305 6.15916 4.47431 6.21261 4.59171 6.21261C4.70911 6.21261 4.82036 6.15916 4.89498 6.0669L5.59073 5.20747L5.81689 5.48718C5.89017 5.57769 5.99874 5.63094 6.11384 5.63289C6.22433 5.62528 6.33943 5.5851 6.41539 5.49713L8.15217 3.48782C8.29527 3.32202 8.27954 3.06961 8.11649 2.9241C7.95382 2.77839 7.70522 2.79419 7.56212 2.96038L6.13034 4.61683L5.89401 4.32502C5.81958 4.23275 5.70813 4.17931 5.59073 4.17931C5.47334 4.17931 5.36208 4.23275 5.28746 4.32502L4.59171 5.18445L4.11445 4.59498C3.97672 4.4245 3.72907 4.39992 3.56161 4.54036C3.39396 4.68042 3.36998 4.93224 3.5079 5.10253L4.28843 6.0669Z" fill="#0A7AFF"/>
    </svg>
  );

  // Inactive token select icon
  const InactiveTokenSelectIcon = () => (
    <svg width="11" height="12" viewBox="0 0 11 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.42857 0.770645H1.57143C0.704956 0.770645 0 1.48749 0 2.36857V8.05007C0 8.93116 0.704956 9.648 1.57143 9.648H5.10714V11.1572H3.28903C3.07207 11.1572 2.89617 11.336 2.89617 11.5567C2.89617 11.7773 3.07207 11.9561 3.28903 11.9561H7.71097C7.92812 11.9561 8.10383 11.7773 8.10383 11.5567C8.10383 11.336 7.92812 11.1572 7.71097 11.1572H5.89286V9.648H9.42857C10.2952 9.648 11 8.93116 11 8.05007V2.36857C11 1.48749 10.2952 0.770645 9.42857 0.770645ZM10.2143 8.05007C10.2143 8.49071 9.86171 8.84904 9.42857 8.84904H1.57143C1.1381 8.84904 0.785714 8.49071 0.785714 8.05007V2.36857C0.785714 1.92793 1.1381 1.56961 1.57143 1.56961H9.42857C9.86171 1.56961 10.2143 1.92793 10.2143 2.36857V8.05007Z" fill="white"/>
      <path d="M7.85714 6.79506H3.14286C2.9259 6.79506 2.75 6.97393 2.75 7.19454C2.75 7.41515 2.9259 7.59402 3.14286 7.59402H7.85714C8.07429 7.59402 8.25 7.41515 8.25 7.19454C8.25 6.97393 8.07429 6.79506 7.85714 6.79506ZM4.28843 6.0669C4.36305 6.15916 4.47431 6.21261 4.59171 6.21261C4.70911 6.21261 4.82036 6.15916 4.89498 6.0669L5.59073 5.20747L5.81689 5.48718C5.89017 5.57769 5.99874 5.63094 6.11384 5.63289C6.22433 5.62528 6.33943 5.5851 6.41539 5.49713L8.15217 3.48782C8.29527 3.32202 8.27954 3.06961 8.11649 2.9241C7.95382 2.77839 7.70522 2.79419 7.56212 2.96038L6.13034 4.61683L5.89401 4.32502C5.81958 4.23275 5.70813 4.17931 5.59073 4.17931C5.47334 4.17931 5.36208 4.23275 5.28746 4.32502L4.59171 5.18445L4.11445 4.59498C3.97672 4.4245 3.72907 4.39992 3.56161 4.54036C3.39396 4.68042 3.36998 4.93224 3.5079 5.10253L4.28843 6.0669Z" fill="white"/>
    </svg>
  );
  const tabs = [
    // {
    //   name: "All Coins",
    //   svg: (isActive) => (
    //     <svg width="18" height="18" viewBox="0 0 18 18" fill={isActive ? "#181C20" : "white"} xmlns="http://www.w3.org/2000/svg">
    //     <path d="M14.6178 0H2.4363C1.09295 0 0 1.11138 0 2.47739V11.2858C0 12.6519 1.09295 13.7632 2.4363 13.7632H7.91799V16.103H5.09923C4.76287 16.103 4.49015 16.3803 4.49015 16.7224C4.49015 17.0644 4.76287 17.3417 5.09923 17.3417H11.9549C12.2916 17.3417 12.564 17.0644 12.564 16.7224C12.564 16.3803 12.2916 16.103 11.9549 16.103H9.13614V13.7632H14.6178C15.9615 13.7632 17.0541 12.6519 17.0541 11.2858V2.47739C17.0541 1.11138 15.9615 0 14.6178 0ZM15.836 11.2858C15.836 11.969 15.2894 12.5245 14.6178 12.5245H2.4363C1.76448 12.5245 1.21815 11.969 1.21815 11.2858V2.47739C1.21815 1.79423 1.76448 1.23869 2.4363 1.23869H14.6178C15.2894 1.23869 15.836 1.79423 15.836 2.47739V11.2858Z" fill={isActive ? "#181C20" : "white"}/>
    //     <path d="M12.1797 9.34024H4.87079C4.53443 9.34024 4.26172 9.61755 4.26172 9.95958C4.26172 10.3016 4.53443 10.5789 4.87079 10.5789H12.1797C12.5164 10.5789 12.7888 10.3016 12.7888 9.95958C12.7888 9.61755 12.5164 9.34024 12.1797 9.34024ZM6.64687 8.21132C6.76256 8.35436 6.93505 8.43722 7.11706 8.43722C7.29907 8.43722 7.47156 8.35436 7.58725 8.21132L8.66592 6.87888L9.01655 7.31254C9.13016 7.45286 9.29849 7.53542 9.47693 7.53844C9.64823 7.52665 9.82667 7.46435 9.94444 7.32796L12.6371 4.21278C12.859 3.95573 12.8346 3.5644 12.5818 3.3388C12.3296 3.1129 11.9442 3.13739 11.7223 3.39505L9.50251 5.96316L9.13611 5.51075C9.02072 5.36771 8.84793 5.28484 8.66592 5.28484C8.48391 5.28484 8.31142 5.36771 8.19573 5.51075L7.11706 6.84319L6.37713 5.92929C6.16359 5.66498 5.77965 5.62688 5.52002 5.84461C5.26009 6.06175 5.22292 6.45217 5.43675 6.71618L6.64687 8.21132Z" fill={isActive ? "#181C20" : "white"}/>
    //     </svg>
        
    //   ),
    // },
    {
      name: "Favorites",
      svg: (isActive: boolean) => (
        <svg width="18" height="19" viewBox="0 0 18 19" fill={isActive ? "#181C20" : "white"} xmlns="http://www.w3.org/2000/svg">
<path d="M10.382 2.22719L11.79 5.24383C11.9821 5.66373 12.4941 6.06663 12.9261 6.14313L15.4773 6.59788C17.1094 6.88943 17.4934 8.15762 16.3174 9.40882L14.3333 11.5338C13.9973 11.8934 13.8133 12.5878 13.9173 13.0851L14.4853 15.7158C14.9333 17.7983 13.9013 18.6033 12.1813 17.5153L9.7892 15.998C9.35719 15.7243 8.64517 15.7243 8.20516 15.998L5.8147 17.5153C4.10265 18.6033 3.06263 17.789 3.51064 15.7158L4.07865 13.0851C4.18265 12.5878 3.99865 11.8934 3.66264 11.5338L1.67859 9.40882C0.511361 8.15677 0.887371 6.88943 2.51861 6.59788L5.07068 6.14313C5.49469 6.06663 6.0067 5.66373 6.1987 5.24383L7.60674 2.22719C8.37476 0.590938 9.62279 0.590938 10.3828 2.22719" fill={isActive ? "#181C20" : "white"} strokeLinecap="round" strokeLinejoin="round"/>
</svg>

        
      ),
    },
    {
      name: "Radar",
      svg: (isActive: boolean) => (
        <svg width="18" height="18" viewBox="0 0 16 15" fill={isActive ? "#181C20" : "white"} xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M9.78121 1.26787C7.96926 0.812702 6.13348 1.07927 4.61878 1.88043C4.35037 2.02239 4.01003 1.93349 3.8586 1.68185C3.70717 1.43022 3.802 1.11115 4.0704 0.969175C5.82923 0.0388955 7.96465 -0.271626 10.07 0.257252C14.3367 1.32907 16.8687 5.44064 15.7255 9.44072C14.6955 13.0444 11.0347 15.3274 7.19944 14.9617C6.777 14.9214 6.35268 14.849 5.92994 14.7428C5.63227 14.668 5.45561 14.3811 5.53538 14.1021C5.61513 13.823 5.92111 13.6574 6.21879 13.7322C6.58363 13.8238 6.94908 13.8861 7.31246 13.9208C10.6117 14.2354 13.7615 12.2698 14.6475 9.16994C15.6312 5.72799 13.4525 2.19013 9.78121 1.26787ZM7.99999 3.83808C7.56735 3.83808 7.15227 3.90381 6.7648 4.0248C6.47239 4.11609 6.15643 3.96788 6.05906 3.69376C5.96168 3.41965 6.11977 3.12343 6.41215 3.03214C6.91196 2.87609 7.446 2.79181 7.99999 2.79181C8.91377 2.79181 9.77213 3.02109 10.5114 3.42198C10.7781 3.56665 10.8693 3.88667 10.715 4.13676C10.5607 4.38685 10.2193 4.47231 9.95255 4.32764C9.37863 4.01638 8.71237 3.83808 7.99999 3.83808ZM1.31046 3.38658C1.73752 2.77594 2.59891 2.72865 3.14445 3.14261L4.72177 4.33948C4.78851 4.3591 4.8524 4.39088 4.90968 4.43518C4.96413 4.47728 5.00788 4.52705 5.04062 4.58142L8.35101 7.09338C8.59058 7.27515 8.62763 7.60459 8.43374 7.82912C8.23985 8.05372 7.88846 8.08838 7.64889 7.90661L4.64337 5.62602C4.29433 6.17413 4.09397 6.81473 4.09397 7.50003C4.09397 9.52247 5.84275 11.162 7.99999 11.162C10.1572 11.162 11.906 9.52247 11.906 7.50003C11.906 7.18322 11.8632 6.87652 11.783 6.58441C11.7062 6.30463 11.8859 6.01942 12.1844 5.94742C12.4828 5.87543 12.7871 6.0439 12.8638 6.32368C12.9672 6.70026 13.022 7.0945 13.022 7.50003C13.022 10.1003 10.7736 12.2082 7.99999 12.2082C5.2264 12.2082 2.97796 10.1003 2.97796 7.50003C2.97796 6.56613 3.26847 5.69491 3.76906 4.96259L2.44235 3.95587C2.40079 3.92435 2.35405 3.91487 2.31693 3.91908C2.28314 3.92291 2.26024 3.93667 2.24374 3.96027C1.8524 4.51983 1.54771 5.14695 1.35246 5.83012C0.686759 8.15925 1.4688 10.533 3.18419 12.1109C3.40417 12.3132 3.40753 12.6445 3.19171 12.8506C2.97588 13.0569 2.62259 13.06 2.40261 12.8577C0.411605 11.0264 -0.499572 8.26757 0.274478 5.55932C0.500848 4.76728 0.854822 4.03808 1.31046 3.38658Z" fill={isActive ? "#181C20" : "white"}/>
        </svg>
        
      ),
    },
    {
        name: "Highlights",
        svg: (isActive: boolean) => (
            <svg width="18" height="18" viewBox="0 0 18 18" fill={isActive ? "#181C20" : "white"} xmlns="http://www.w3.org/2000/svg">
            <path d="M1 9H6.24954C6.29802 9 6.33951 9.03477 6.348 9.0825L7.81508 17.3348C7.83522 17.4481 7.99931 17.4434 8.01285 17.3291L9.98716 0.670884C10.0007 0.556633 10.1648 0.551877 10.1849 0.665151L11.652 8.9175C11.6605 8.96523 11.702 9 11.7505 9H17" stroke={isActive ? "#181C20" : "white"} strokeLinecap="round"/>
            </svg>
            
            
          
        ),
      },
  ];
  
  const formatNumber = (num: number) => {
    if (!num && num !== 0) return "--";
    if (num >= 1e12) return (num / 1e12).toFixed(2) + "T"; // Trillion
    if (num >= 1e9) return (num / 1e9).toFixed(2) + "B";   // Billion
    if (num >= 1e6) return (num / 1e6).toFixed(2) + "M";   // Million
    return num.toLocaleString(); // Default
  };
  const getSortArrow = (column: string) => {
    if (sortConfig.key === column) {
      return sortConfig.direction === "asc" ? <ActiveUpArrow /> : <ActiveDownArrow />;
    }
    return <SortArrows />;
  };
  const requestSort = (key: string) => {
    let direction = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });

    setSearchResults((prevData) => {
      const sortedData = [...prevData].sort((a, b) => {
        // Handle different data types appropriately
        const aValue = a[key] || 0;
        const bValue = b[key] || 0;
        if (direction === "asc") {
          return aValue - bValue;
        } else {
          return bValue - aValue;
        }
      });
      return sortedData;
    });
  };

  return (
    <div className="fixed inset-0  bg-opacity-60  backdrop-blur-lg flex pt-[5vh] justify-center z-50">
      <div ref={modalRef} 
        className="bg-[#181C20] rounded-3xl w-[50rem] max-w-full h-[75vh] pt-10 shadow-lg "
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header with Icons */}
        <div className="flex items-center justify-between mb-4 px-8 ">
          {/* Back Button */}
          <button className="text-gray-400 hover:text-white" onClick={onClose}>
          <svg width="7" height="10" viewBox="0 0 7 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M6.72929 8.71655C6.90272 8.85799 7 9.04899 7 9.24803C7 9.44708 6.90272 9.63808 6.72929 9.77951C6.64413 9.84935 6.54262 9.9048 6.43067 9.94265C6.31872 9.98051 6.19857 10 6.0772 10C5.95583 10 5.83568 9.98051 5.72373 9.94265C5.61178 9.9048 5.51027 9.84935 5.42511 9.77951L0.270281 5.53202C0.0970978 5.39026 2.49576e-07 5.19913 2.5828e-07 5C2.66985e-07 4.80086 0.0970978 4.60974 0.270281 4.46798L5.42511 0.220486C5.51027 0.150654 5.61178 0.0951986 5.72373 0.057346C5.83568 0.0194933 5.95583 -4.5642e-08 6.0772 -4.03369e-08C6.19857 -3.50317e-08 6.31872 0.0194933 6.43067 0.057346C6.54262 0.0951987 6.64413 0.150654 6.72929 0.220486C6.90272 0.361925 7 0.552923 7 0.751966C7 0.951008 6.90272 1.14201 6.72929 1.28345L2.5018 5.00163L6.72929 8.71655Z" fill="white"/>
</svg>

          </button>

          {/* Search Bar */}
          <div className="relative flex-1 mx-4">
            <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500" />
            <input
  type="text"
  placeholder="Search..."
  value={searchQuery}
  onChange={(e) => setSearchQuery(e.target.value)}
  className="bg-[#23272E] text-white px-12 py-3 pr-14 rounded-full w-full outline-none"
/>
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white cursor-pointer" >
            <svg width="12" height="15" viewBox="0 0 12 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1 1.65C1 1.47761 1.06848 1.31228 1.19038 1.19038C1.31228 1.06848 1.47761 1 1.65 1H8.15L11.4 4.25V13.35C11.4 13.5224 11.3315 13.6877 11.2096 13.8096C11.0877 13.9315 10.9224 14 10.75 14H1.65C1.47761 14 1.31228 13.9315 1.19038 13.8096C1.06848 13.6877 1 13.5224 1 13.35V1.65Z" stroke="white" strokeLinejoin="round"/>
<path d="M3.59961 6.19995H8.79961M3.59961 8.79995H8.79961" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
</svg>

             </div>   
          </div>

          {/* Icons */}
          <div className="flex items-center space-x-4 py-3" >
            <div className="bg-[#23272E] p-3 rounded-full">
              <div className="text-gray-400 hover:text-white cursor-pointer" >
              <svg width="20" height="20" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14 7.5C14 9.22391 13.3152 10.8772 12.0962 12.0962C10.8772 13.3152 9.22391 14 7.5 14C5.77609 14 4.12279 13.3152 2.90381 12.0962C1.68482 10.8772 1 9.22391 1 7.5M14 7.5C14 5.77609 13.3152 4.12279 12.0962 2.90381C10.8772 1.68482 9.22391 1 7.5 1C5.77609 1 4.12279 1.68482 2.90381 2.90381C1.68482 4.12279 1 5.77609 1 7.5M14 7.5H1" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
<path d="M10 7.5C9.8772 9.87699 9.00168 12.1533 7.5 14C5.99832 12.1533 5.1228 9.87699 5 7.5C5.1228 5.12301 5.99832 2.84665 7.5 1C9.00168 2.84665 9.8772 5.12301 10 7.5Z" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
</svg>

                </div>
            </div>
          </div>
        </div>

        {/* Tabs: Controls Different Tables */}
        <div className="p-0 overflow-y-auto">
  <div className="mt-6">
    {searchQuery === "" ? (
      <>
        <div>
          <div className="flex space-x-4 mb-4 justify-start px-4  ">
            {tabs.map(({ name, svg }) => (
              <button
                key={name}
                onClick={() => setActiveTab(name)}
                className={`flex items-center justify-center w-14 h-14 rounded-full transition-all ${
                  activeTab === name ? "bg-white" : "bg-[#23272E]"
                }`}
              >
                {svg(activeTab === name)}
              </button>
            ))}
          </div>

          {activeTab === "Highlights" && (
            <HighlightsTable searchQuery={searchQuery} onRowClick={handleRowClick} />
          )}
          {activeTab === "Favorites" && (
            <FavoritesTable searchQuery={searchQuery} onRowClick={handleRowClick} />
          )}
          {activeTab === "Radar" && (
            <RadarTable searchQuery={searchQuery} onRowClick={handleRowClick} />
          )}
        </div>
      </>
    ) : (
      <div>
     
        {searchResults.length > 0 ? (
          <div className="w-full text-left table-auto border-collapse h-full overflow-y-auto">
        <div className="flex justify-between text-white text-lg font-medium px-3 py-4 ">
          <div className="flex items-center">
            <span className="cursor-pointer flex items-center" onClick={() => requestSort('market_cap')}>
              Token / MCap 
              <span className="ml-1">{getSortArrow('market_cap')}</span>
            </span>
            <span className="mx-1">/ </span>
            <span className="cursor-pointer flex items-center" onClick={() => requestSort('total_volume')}>
              24h Vol
              <span className="ml-1">{getSortArrow('total_volume')}</span>
            </span>
          </div>
          
          <div className="flex items-center">
            <span className="cursor-pointer flex items-center" onClick={() => requestSort('current_price')}>
              Price 
              <span className="ml-1">{getSortArrow('current_price')}</span>
            </span>
            <span className="mx-1">/ </span>
            <span className="cursor-pointer flex items-center" onClick={() => requestSort('price_change_percentage_24h')}>
              24h%
              <span className="ml-1">{getSortArrow('price_change_percentage_24h')}</span>
            </span>
          </div>
        </div>
        <div className="max-h-[500px] overflow-y-auto bg-[#1D2226] ">
              {searchResults.map((token,i) => (
              <div
              onClick={(e) => handleRowClick(e,token)}
              key={i}
              className="flex justify-between items-center py-3 px-3 hover:bg-[#2D3236] cursor-pointer transition-all duration-200"
            >
                {/* Token Info */}
                <div className="flex items-center gap-3">
                  <img 
                    src={token.image || `https://coinicons-api.vercel.app/api/icon/${token.symbol?.toLowerCase()}`} 
                    alt={token.name} 
                    className="w-6 h-6 rounded-full" 
              
                  />
                  <div>
                    <p className="text-white text-lg">{token.symbol?.toUpperCase()}</p>
                    <p className="text-sm opacity-50">
                      ${formatNumber(token.market_cap)} • ${formatNumber(token.total_volume)}
                    </p>
                  </div>
                </div>

                {/* Price & Change */}
                <div className="flex items-center gap-2">
                  <div className="text-right">
                    <p className="text-white font-semibold">
                      ${token.current_price?.toFixed(2) || token.price?.toFixed(2) || "0.00"}
                    </p>
                    <p className={`text-sm flex items-center justify-end ${
                      (token.price_change_percentage_24h || 0) < 0 ? "text-red-500" : "text-green-500"
                    }`}>
                      {(token.price_change_percentage_24h || 0) < 0 ? <DownArrow /> : <UpArrow />}
                      <span className="ml-1">
                        {Math.abs(token.price_change_percentage_24h || 0).toFixed(2)}%
                      </span>
                    </p>
                  </div>
                  <span className="py-3" onClick={(e) => e.stopPropagation()}>
                  <button
                onClick={(e) => handleTokenSelect(null, token)}
                className={`w-6 h-6 flex items-center justify-center ${
                  isTokenSelected(token) ? "text-purple-500" : "text-white"
                }`}>
                      <svg width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9.07143 0.691528H1.92857C1.14087 0.691528 0.5 1.3324 0.5 2.1201V7.19945C0.5 7.98715 1.14087 8.62802 1.92857 8.62802H5.14286V9.97724H3.49003C3.29279 9.97724 3.13288 10.1372 3.13288 10.3344C3.13288 10.5316 3.29279 10.6915 3.49003 10.6915H7.50997C7.70738 10.6915 7.86712 10.5316 7.86712 10.3344C7.86712 10.1372 7.70738 9.97724 7.50997 9.97724H5.85714V8.62802H9.07143C9.85931 8.62802 10.5 7.98715 10.5 7.19945V2.1201C10.5 1.3324 9.85931 0.691528 9.07143 0.691528ZM9.78571 7.19945C9.78571 7.59338 9.46519 7.91373 9.07143 7.91373H1.92857C1.53463 7.91373 1.21429 7.59338 1.21429 7.19945V2.1201C1.21429 1.72616 1.53463 1.40581 1.92857 1.40581H9.07143C9.46519 1.40581 9.78571 1.72616 9.78571 2.1201V7.19945Z" fill="currentColor"/>
                        <path d="M7.64286 6.07748H3.35714C3.15991 6.07748 3 6.23739 3 6.43462C3 6.63185 3.15991 6.79177 3.35714 6.79177H7.64286C7.84026 6.79177 8 6.63185 8 6.43462C8 6.23739 7.84026 6.07748 7.64286 6.07748ZM4.39858 5.4265C4.46641 5.50898 4.56756 5.55676 4.67428 5.55676C4.78101 5.55676 4.88215 5.50898 4.94999 5.4265L5.58248 4.65815L5.78809 4.90822C5.8547 4.98914 5.9534 5.03674 6.05803 5.03849C6.15848 5.03169 6.26311 4.99576 6.33217 4.91711L7.91106 3.12076C8.04116 2.97253 8.02686 2.74688 7.87863 2.61679C7.73075 2.48652 7.50474 2.50065 7.37465 2.64922L6.07303 4.13011L5.85819 3.86923C5.79053 3.78674 5.68921 3.73896 5.58248 3.73896C5.47576 3.73896 5.37462 3.78674 5.30678 3.86923L4.67428 4.63757L4.24041 4.11058C4.1152 3.95816 3.89007 3.93619 3.73783 4.06175C3.58541 4.18696 3.56362 4.41209 3.689 4.56433L4.39858 5.4265Z" fill="currentColor"/>
                      </svg>
                    </button>
                  </span>
                </div>
              </div>
              ))
              
              }
            
          </div>
          </div>
        ) : (
          <p className="text-gray-400">No results found.</p>
        )}

      </div>
    )}
  </div>
</div>

    </div>
    </div>

  );
            }




export default SearchModal;


