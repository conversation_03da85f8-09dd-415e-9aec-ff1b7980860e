import React from "react";
import { useState, useEffect } from "react";
import { FaArrowDown, FaChartBar } from "react-icons/fa";
import { homeAPI } from "../../utils/api";
import { useTokenSelection } from "../../hooks/useTokenSelection";
import { useNavigate } from "react-router-dom";

// Define interfaces for props and state
interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
  category: string | null;
}

interface TokenData {
  id?: string; // Assuming an ID field, make optional if not always present
  name: string;
  contract?: string; // Make optional if not always present
  symbol: string;
  image: string;
  market_cap?: number; // Make optional if not always present
  total_volume?: number; // Make optional if not always present
  current_price?: number; // Make optional if not always present
  price_change_percentage_24h?: number; // Make optional if not always present
  // Add other relevant fields from your API response if needed
}

interface HighlightsTableProps {
  onRowClick: (e: React.MouseEvent, token: TokenData) => void;
  searchQuery: string;
  selectedNetwork?: string;
}

const categories = [
  { title: "🔥 Trending", key: "trending" },
  { title: "🚀 Gainers", key: "gainers" },
  { title: "🚨 Losers", key: "losers" },
  { title: "✨ New", key: "new" },
];

const HighlightsTable: React.FC<HighlightsTableProps> = ({onRowClick,searchQuery, selectedNetwork = "Global" }) => {
  const [marketData, setMarketData] = useState<Record<string, TokenData[]>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");
  const [activeCategory, setActiveCategory] = useState<string>("trending");
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: "market_cap",
    direction: "desc",
    category: "trending"
  });

  const navigate = useNavigate();
  const { isTokenSelected, handleTokenSelect,setSelectedTokens } = useTokenSelection();

  const DownArrow = () => (
    <svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3.86961 5.46501C3.48635 6.14002 2.51365 6.14002 2.13039 5.46501L0.384009 2.38918C0.00550654 1.72253 0.487018 0.895432 1.25362 0.895432L4.74638 0.895432C5.51298 0.895432 5.99449 1.72253 5.61599 2.38917L3.86961 5.46501Z" fill="#ED2626"/>
    </svg>
  );
  
  const UpArrow = () => (
    <svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M2.13039 0.535C2.51365 -0.14 3.48635 -0.14 3.86961 0.535L5.61599 3.61083C5.99449 4.27747 5.51298 5.10457 4.74638 5.10457H1.25362C0.487018 5.10457 0.00550654 4.27747 0.384009 3.61082L2.13039 0.535Z" fill="#16A34A"/>
    </svg>
  );

  const SortArrows = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="white"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="white"/>
    </svg>
  );

  const ActiveUpArrow = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="#444"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="#16A34A"/>
    </svg>
  );

  const ActiveDownArrow = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="#ED2626"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="#444"/>
    </svg>
  );

  // Sort indicator helper
  const getSortArrow = (fieldName: string) => {
    if (sortConfig.category !== activeCategory) return <SortArrows />;
    if (sortConfig.key === fieldName) {
      return sortConfig.direction === "asc" ? <ActiveUpArrow /> : <ActiveDownArrow />;
    }
    return <SortArrows />;
  };
  

  // Handle sorting
  const requestSort = (key: keyof TokenData, category: string) => {
    let direction: 'asc' | 'desc' = "desc";
    
    // Toggle direction if already sorting by this key
    if (sortConfig.key === key && sortConfig.category === category) {
      direction = sortConfig.direction === "desc" ? "asc" : "desc";
    }
  
    const newSortConfig = { key, direction, category };
    setSortConfig(newSortConfig);
  
    // Sort only the active category
    setMarketData((prevData) => {
      if (!prevData[category]) return prevData;
  
      const sortedData = [...prevData[category]].sort((a, b) => {
        // Use bracket notation for dynamic keys and provide defaults
        const rawA = a[key];
        const rawB = b[key];

        // Provide default values ONLY if raw value is null or undefined
        const aValue = rawA ?? 0;
        const bValue = rawB ?? 0;

        // Check if both are numbers *after* default assignment
        if (typeof aValue === 'number' && typeof bValue === 'number') {
           return direction === 'asc' ? aValue - bValue : bValue - aValue;
        }
        // Add comparison logic for other types if necessary (e.g., strings)
        // if (typeof aValue === 'string' && typeof bValue === 'string') {
        //    return direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
        // }

        // Fallback for mixed types or unhandled types - maintain original order
        return 0;
      });
  
      return { ...prevData, [category]: sortedData };
    });
  };
  

  useEffect(() => {
    // Load selected tokens from localStorage - No longer needed as it's handled by useTokenSelection
    
    const fetchMarketData = async () => {
      try {
        setLoading(true);
        setError("");
        
        // Convert network name for API calls - API expects lowercase or 'universal'
        const network = selectedNetwork === "Global" ? "universal" : selectedNetwork.toLowerCase();
        
        console.log("Fetching highlights data for network:", network);
        
        // Use homeAPI utility instead of direct fetch
        const response = await homeAPI.getNetworkHighlights(network);
        
        // Handle nested response structure - API returns {data: {...}, status: 'success'}
        const highlights = response?.data ? response.data : response;
        
        console.log("Processed highlights data:", highlights);
        
        if (highlights && typeof highlights === 'object') {
          setMarketData(highlights);
        } else {
          console.error("Invalid data format:", response);
          setError("Invalid data format received from API");
        }
      } catch (err) {
        console.error("Error fetching highlights data:", err);
        setError("Failed to load market data.");
      } finally {
        setLoading(false);
      }
    };
    
    fetchMarketData();
    
    // Set up refresh interval (every 5 minutes = 300000 ms)
    const intervalId = setInterval(() => {
      console.log('Fetching fresh highlights data...');
      fetchMarketData();
    }, 300000);
    
    // Clean up on unmount
    return () => clearInterval(intervalId);
  }, [selectedNetwork]);

  // Handle token click to navigate to trade page

  const handleRowClick = async (e: React.MouseEvent, token: TokenData) => {
onRowClick(e,token);

  };

  return (
<div className="">
  {/* Tabs Section */}
  <div className="flex space-x-4 border-b">
    {categories.map(({ title, key }) => (
      <button
        key={key}
        onClick={() => setActiveCategory(key)}
        className={`px-3 py-3 text-white text-sm font-medium  ${
          activeCategory === key
            ? "bg-[#1D2226] border-t-2 border-l-2 border-r-2 border-white rounded-t-xl"
            : "text-gray-400"
        }`}
      >
        {title}
      </button>
    ))}
  </div>

  {/* Column Headers */}
  <div className="flex justify-between text-white text-sm font-medium px-3 py-4  ">
  <div className="flex items-center">
              <span className="cursor-pointer flex items-center" onClick={() => requestSort('market_cap', activeCategory)}>
                Token / MCap 
                <span className="ml-1">{getSortArrow('market_cap')}</span>
              </span>
              <span className="mx-1">/ </span>
              <span className="cursor-pointer flex items-center" onClick={() => requestSort('total_volume', activeCategory)}>
                24h Vol
                <span className="ml-1">{getSortArrow('total_volume')}</span>
              </span>
            </div>
           
            <div className="flex items-center">
              <span className="cursor-pointer flex items-center" onClick={() => requestSort('current_price', activeCategory)}>
                Price 
                <span className="ml-1">{getSortArrow('current_price')}</span>
              </span>
              <span className="mx-1">/ </span>
              <span className="cursor-pointer flex items-center" onClick={() => requestSort('price_change_percentage_24h',activeCategory)}>
                24h%
                <span className="ml-1">{getSortArrow('price_change_percentage_24h')}</span>
              </span>
            </div>
  </div>

  {/* Data Handling */}
  {loading ? (
    <p className="text-center text-white mt-4">Loading...</p>
  ) : error ? (
    <p className="text-center text-red-500 mt-4">{error}</p>
  ) : (
    <div className="overflow-y-auto max-h-[400px] bg-[#1D2226]">
        {marketData[activeCategory]
  ?.filter((token: TokenData) =>
    token.name?.toLowerCase().startsWith(searchQuery.toLowerCase()) ||
    token.contract?.toLowerCase().startsWith(searchQuery.toLowerCase()) ||
    token.symbol?.toLowerCase().startsWith(searchQuery.toLowerCase())
  ).map((token: TokenData, i: number) => (
        <div 
          className="flex justify-between items-center py-3 px-3 hover:bg-[#292d32] cursor-pointer"
          onClick={(e) => handleRowClick(e,token)}
          key={token.id || token.contract || i}
        >
          {/* Token Info */}
          <div className="flex items-center gap-3">
            <img src={token.image} alt={token.name} className="w-6 h-6 rounded-full" />
            <div>
              <p className="text-white text-sm">{token.symbol?.toUpperCase()}</p>
              <p className="text-sm opacity-50">${token.market_cap?.toLocaleString() ?? 'N/A'} • ${token.total_volume?.toLocaleString() ?? 'N/A'}</p>
            </div>
          </div>

          {/* Price & Change */}
          <div className="flex items-center gap-2">
            <div className="text-right">
              <p className="text-white font-semibold">${token.current_price?.toFixed(2) ?? 'N/A'}</p>
              <p className={`text-sm ${token.price_change_percentage_24h != null && token.price_change_percentage_24h < 0 ? "text-red-500" : "text-green-500"}`}>
                {token.price_change_percentage_24h?.toFixed(2) ?? 'N/A'}%
              </p>
            </div>
            <span className="py-3" onClick={(e) => e.stopPropagation()}>
              <button
                onClick={(e) => handleTokenSelect(e, token)}
                className={`w-6 h-6 flex items-center justify-center ${
                  isTokenSelected(token) ? "text-purple-500" : "text-white"
                }`}
              >
                <svg width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9.07143 0.691528H1.92857C1.14087 0.691528 0.5 1.3324 0.5 2.1201V7.19945C0.5 7.98715 1.14087 8.62802 1.92857 8.62802H5.14286V9.97724H3.49003C3.29279 9.97724 3.13288 10.1372 3.13288 10.3344C3.13288 10.5316 3.29279 10.6915 3.49003 10.6915H7.50997C7.70738 10.6915 7.86712 10.5316 7.86712 10.3344C7.86712 10.1372 7.70738 9.97724 7.50997 9.97724H5.85714V8.62802H9.07143C9.85931 8.62802 10.5 7.98715 10.5 7.19945V2.1201C10.5 1.3324 9.85931 0.691528 9.07143 0.691528ZM9.78571 7.19945C9.78571 7.59338 9.46519 7.91373 9.07143 7.91373H1.92857C1.53463 7.91373 1.21429 7.59338 1.21429 7.19945V2.1201C1.21429 1.72616 1.53463 1.40581 1.92857 1.40581H9.07143C9.46519 1.40581 9.78571 1.72616 9.78571 2.1201V7.19945Z" fill="currentColor"/>
                  <path d="M7.64286 6.07748H3.35714C3.15991 6.07748 3 6.23739 3 6.43462C3 6.63185 3.15991 6.79177 3.35714 6.79177H7.64286C7.84026 6.79177 8 6.63185 8 6.43462C8 6.23739 7.84026 6.07748 7.64286 6.07748ZM4.39858 5.4265C4.46641 5.50898 4.56756 5.55676 4.67428 5.55676C4.78101 5.55676 4.88215 5.50898 4.94999 5.4265L5.58248 4.65815L5.78809 4.90822C5.8547 4.98914 5.9534 5.03674 6.05803 5.03849C6.15848 5.03169 6.26311 4.99576 6.33217 4.91711L7.91106 3.12076C8.04116 2.97253 8.02686 2.74688 7.87863 2.61679C7.73075 2.48652 7.50474 2.50065 7.37465 2.64922L6.07303 4.13011L5.85819 3.86923C5.79053 3.78674 5.68921 3.73896 5.58248 3.73896C5.47576 3.73896 5.37462 3.78674 5.30678 3.86923L4.67428 4.63757L4.24041 4.11058C4.1152 3.95816 3.89007 3.93619 3.73783 4.06175C3.58541 4.18696 3.56362 4.41209 3.689 4.56433L4.39858 5.4265Z" fill="currentColor"/>
                </svg>
              </button>
            </span>
          </div>
        </div>
      ))}
    </div>
  )}
</div>

  );
};

export default HighlightsTable;