import React, { useEffect, useState, useCallback } from "react";
import { useLocation } from "react-router-dom";
import Navbar from "./Navbar/Navbar";
import SelectedTokensBar from "./SelectedTokensBar/SelectedTokensBar";
import TokenStats from "./TokenStats/TokenStats";
import TradingPanel from "./TradingPanel/TradingPanel";
import InfoPanel from "./Info/Menu";
import ActiveCoin from "./ActiveCoin/ActiveCoin";
import TradesTable from "./Table/Trades";
import SupabaseTest from "../components/SupabaseTest";
import MyAccount from "./Account/Account";
import ScrollingTicker from "./InfiniteCarousel/InfiniteCarousel";
import TokenPriceChart from '../components/PriceChart/TokenPriceChart';

// Define interface if not already present
interface ActiveTokenType {
  id?: string;
  symbol?: string;
  name?: string;
  address?: string;
  tokenAddress?: string;
  network?: string;
  [key: string]: any;
}

const Trade = () => {
  const location = useLocation();
  const storedTokensString = localStorage.getItem("selectedTokens");
  const storedTokens = storedTokensString ? JSON.parse(storedTokensString) : [];
  const tokensFromNavigation = location.state?.selectedTokens || [];

  const [selectedTokens, setSelectedTokens] = useState([]);
  const [activeToken, setActiveToken] = useState<ActiveTokenType | null>(null);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  // Wrap the function definition with useCallback
  const handleActiveTokenChange = useCallback((token: ActiveTokenType | null) => {
    // Logic from previous step (logging, checking address, setActiveToken)
    console.log("Trade.tsx: handleActiveTokenChange received token:", token);
    if (token && (token.address || token.tokenAddress)) {
      console.log("Trade.tsx: Setting active token with address:", token.address || token.tokenAddress);
      setActiveToken(token);
    } else if (token) {
      console.warn("Trade.tsx: Received token is missing address:", token);
    setActiveToken(token);
    } else {
      setActiveToken(null);
    }
  }, []); // Empty dependency array ensures the function reference is stable

  return (
    <div className={` relative bg-[#141416] min-h-screen ${isSearchOpen ? "overflow-hidden" : ""}`}>
      {/* Background Blur & Overlay */}
      {isSearchOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-lg transition-opacity duration-300 z-40"
          onClick={() => setIsSearchOpen(false)}
        />
      )}
  
      {/* Navbar */}
      <Navbar />
  <div className="px-4">
  
      {/* Token Selector */}
      <div className="px-2 md:px-4 mb-4">
        <SelectedTokensBar onActiveTokenChange={handleActiveTokenChange} />
      </div>
  
      {/* Responsive Layout */}
      <div className="text-white flex flex-col md:grid md:grid-cols-12 md:grid-rows-12 gap-4 md:gap-2 h-auto md:h-[1200px]">
        
        {/* Active Coin */}
        <div className="bg-[#181C20] p-3 rounded-xl w-full md:col-span-3 md:row-span-1">
     
            <ActiveCoin token={activeToken as any} /> 
            
        </div>
  
        {/* Token Stats */}
        <div className="bg-[#181C20] p-3 rounded-xl w-full md:col-span-6 md:row-span-1">
         
             <TokenStats token={{ id: activeToken?.id, network: activeToken?.network }} />
        
        </div>
  
        {/* Trading Panel */}
        <div className="bg-[#181C20] p-3 rounded-xl w-full md:col-span-3 md:row-span-7">
          <TradingPanel activeToken={activeToken} />
        </div>
  
        {/* Info Panel */}
        <div className={`
          bg-[#181C20] p-3 rounded-xl w-full 
          overflow-y-auto 
          h-[250px] md:h-auto 
          md:row-span-11 
          ${isSettingsOpen ? "md:col-span-1" : "md:col-span-3"}
        `}>
          <InfoPanel 
            isSettingsOpen={isSettingsOpen} 
            setIsSettingsOpen={setIsSettingsOpen} 
            token={activeToken}
          />
        </div>
  
        {/* Token Price Chart */}
        <div className={`
          bg-[#181C20] p-3 rounded-xl w-full 
          overflow-y-auto 
          h-[300px] md:h-auto 
          md:row-span-6 
          ${isSettingsOpen ? "md:col-span-8" : "md:col-span-6"}
        `}>
          {activeToken ? 
            <TokenPriceChart token={activeToken as any} />
            : <div className="animate-pulse bg-gray-700 w-30 h-10"></div>
          }
        </div>
  
        {/* Trades Table */}
        <div className={`
          bg-[#181C20] px-3 rounded-xl w-full 
          overflow-y-auto 
          h-[300px] md:h-auto 
          md:row-span-5 
          ${isSettingsOpen ? "md:col-span-8" : "md:col-span-6"}
        `}>
          <TradesTable />
        </div>
  
        {/* My Account */}
        <div className="bg-[#181C20] p-3 rounded-xl w-full md:col-span-3 md:row-span-5">
          <MyAccount />
        </div>
      </div>
  
      {/* Scrolling Ticker */}
      <div className="py-2">
        <ScrollingTicker />
      </div>

      {/* Temporary Supabase Test */}
      </div>
    </div>
  );
  
};

export default Trade;
