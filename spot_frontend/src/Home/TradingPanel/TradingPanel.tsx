import React, { useState, useEffect, useCallback, useRef, useMemo, memo } from "react";
import { Switch } from "@headlessui/react";
import { ChevronUpIcon, ChevronDownIcon, CheckIcon } from "@heroicons/react/24/outline";
import { IoMdSettings } from "react-icons/io"; // Import settings icon
import { IoWarningOutline } from "react-icons/io5"; // Import warning icon
import { HiOutlineCheckCircle } from "react-icons/hi"; // Import check circle icon for success
import { FaExternalLinkAlt } from "react-icons/fa"; // Import external link icon
import BalanceDropdown from "./BalanceDropdown"; // Ensure this component is imported
import SettingsModal from "./SettingsModal";
import { FaExchangeAlt, FaChevronDown, FaChevronUp, FaCheck, FaPlus } from "react-icons/fa";
import Tpslmodal from "./Tpslmodal";
import { usePrivy, useWallets, useSendTransaction } from "@privy-io/react-auth"; // Import Privy hook, useSendTransaction
import { ethers } from "ethers";
// Import from our API files
import { walletAPI } from "../../utils/api";
import { getQuoteDataForToken, getBestDEXQuote, getPairList } from "../../utils/trading-api";
import { quotePancakeswap, quoteRaydium, quoteMeteora, quoteFourMeme } from "../../utils/quote";
import { completeTradePancakeswap, execSolanaSwap } from "../../utils/swap";
import { storeTradeHistory } from "../../services/trade-history-service";
import { TRADE_COMPLETED_EVENT } from "../Table/Trades";
import { completeTradeRaydium } from "../../utils/raydium";
import { completeTradeMeteora } from "../../utils/meteora";
import { completeTradeFourMeme } from "../../utils/fourmeme";
import DexSelector from './DexSelector'; // Import the DEX selector component
import "./TradingPanelStyles.css"; // Add this import for the slider styles
import CustomWalletSelector from './CustomWalletSelector'; // Use our custom wallet selector
import { fetchQuote } from "../../utils/fetchQuote";
import { useSmartWallet } from "../../hooks/useSmartWallet"; // Import smart wallet hook

// Import our type definitions
import { 
  TokenBase, 
  QuoteData, 
  WalletBase as ExternalWalletBase, // Rename imported type
  TradingPanelProps, 
  WalletSelectorProps,
  UseWalletsInterface,
  PancakeswapQuoteData,
  QuoteErrorResponse
} from "../../types/trading";

// Define WalletBase locally to include chainId if not present in imported type
interface WalletBase extends ExternalWalletBase {
  chainId?: string | number; // Add optional chainId
}

// ERC20 ABI for token balance checks
const ERC20_ABI = [
  // balanceOf function
  {
    "constant": true,
    "inputs": [{"name": "_owner", "type": "address"}],
    "name": "balanceOf",
    "outputs": [{"name": "balance", "type": "uint256"}],
    "type": "function"
  },
  // decimals function
  {
    "constant": true,
    "inputs": [],
    "name": "decimals",
    "outputs": [{"name": "", "type": "uint8"}],
    "type": "function"
  }
];

// Add these styles at the top where other styles are defined
const styles = {
  // ... existing styles ...
  
  tokenPairDisplay: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '8px 12px',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: '8px',
    marginBottom: '12px',
    fontSize: '14px',
  },
  
  priceInfo: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    padding: '12px',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: '8px',
    marginBottom: '16px',
  },
  
  priceRow: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    fontSize: '14px',
  },
  
  priceLabel: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  
  priceValue: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500',
  }
};

// Create a memoized version of the WalletSelector to prevent unnecessary re-renders
const MemoizedWalletSelector = memo(({ onSelectWallet, selectedWallet }: WalletSelectorProps) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const { wallets: privyWalletsHook } = useWallets();
  const { user: privyUserHook } = usePrivy();
  const dropdownRef = useRef<HTMLDivElement | null>(null);
  const prevSelectedWalletRef = useRef(selectedWallet);
  
  // Add more detailed logging about user object to debug the wallet issue
  useEffect(() => {
    if (privyUserHook) {
      console.log("WalletSelector: Full Privy user object:", JSON.parse(JSON.stringify(privyUserHook)));
      
      // Check specifically for linkedAccounts
      if (privyUserHook.linkedAccounts && Array.isArray(privyUserHook.linkedAccounts)) {
        console.log(`WalletSelector: Found ${privyUserHook.linkedAccounts.length} linked accounts in user object`);
        
        // Log wallet/smart wallet accounts specifically
        const walletAccounts = privyUserHook.linkedAccounts.filter((acc: any) => 
          acc.type === 'wallet' || acc.type === 'smart_wallet'
        );
        
        console.log(`WalletSelector: Found ${walletAccounts.length} wallet/smart wallet accounts:`, 
          walletAccounts.map((acc: any) => ({
            type: acc.type,
            address: acc.address ? `${acc.address.slice(0,6)}...${acc.address.slice(-4)}` : 'No address',
            chainType: acc.chainType || 'unknown',
            walletClientType: acc.walletClientType || 'unknown',
            smartWalletType: acc.smartWalletType || 'none'
          }))
        );
      } else {
        console.log("WalletSelector: No linkedAccounts found in user object");
      }
      
      // Check for smart wallet specifically
      if (privyUserHook.smartWallet) {
        console.log("WalletSelector: Smart wallet found in user object:", 
          JSON.parse(JSON.stringify(privyUserHook.smartWallet)));
      }
    }
  }, [privyUserHook]);
  
  // Also log all available wallets from useWallets hook
  useEffect(() => {
    if (privyWalletsHook && privyWalletsHook.length > 0) {
      console.log("WalletSelector: Raw available wallets from useWallets():", 
        JSON.parse(JSON.stringify(privyWalletsHook)));
    } else {
      console.log("WalletSelector: No wallets available from useWallets()");
    }
  }, [privyWalletsHook]);
  
  useEffect(() => {
    prevSelectedWalletRef.current = selectedWallet;
  }, [selectedWallet]);

  // Improved wallet detection that prioritizes smart wallets and properly handles all wallet types
  const connectedWallets = useMemo(() => {
    const uniqueWallets: WalletBase[] = [];
    const seenAddresses = new Set<string>();
    
    // STEP 1: First check for user's smart wallet - this is the highest priority
    if (privyUserHook?.smartWallet?.address) {
      console.log(`WalletSelector: Found smart wallet in user.smartWallet: ${privyUserHook.smartWallet.address}`);
      
      uniqueWallets.push({
        id: privyUserHook.smartWallet.address,
        address: privyUserHook.smartWallet.address,
        walletClientType: 'smart-wallet',
        connectedAt: new Date().toISOString(),
        connected: true,
        chain: 'BSC', // Smart wallets are typically on BSC
        chainId: 56,
        embedded: true,
        smartWalletType: privyUserHook.smartWallet.smartWalletType || 'light_account'
      });
      
      seenAddresses.add(privyUserHook.smartWallet.address);
    }
    
    // STEP 2: Process linked accounts from user object
    if (privyUserHook?.linkedAccounts && Array.isArray(privyUserHook.linkedAccounts)) {
      console.log(`WalletSelector: Processing ${privyUserHook.linkedAccounts.length} linked accounts`);
      
      privyUserHook.linkedAccounts.forEach((account: any) => {
        // Skip if not a wallet type or missing key data
        if (!account || 
            !['wallet', 'smart_wallet', 'eoa_wallet'].includes(account.type) || 
            !account.address || 
            typeof account.address !== 'string' || 
            account.address.includes('@')) {
          return;
        }
        
        // Skip if we've already added this address
        if (seenAddresses.has(account.address)) {
          return;
        }
        
        console.log(`WalletSelector: Processing linked account: ${account.address.slice(0,6)}...${account.address.slice(-4)} (${account.type})`);
        
        seenAddresses.add(account.address);
        uniqueWallets.push({
          id: account.address,
          address: account.address,
          walletClientType: account.walletClientType || 
                           (account.type === 'smart_wallet' ? 'smart-wallet' : 
                            account.chainType === 'solana' ? 'phantom' : 'privy'),
          connectedAt: account.latestVerifiedAt || account.verifiedAt || new Date().toISOString(),
          connected: true,
          chain: account.chainType || (account.address.startsWith('0x') ? 'BSC' : 'Solana'),
          chainId: account.chainType === 'solana' ? 'solana' : 56,
          embedded: account.connectorType === 'embedded' || account.walletClientType === 'privy',
          smartWalletType: account.smartWalletType,
          chainType: account.chainType || (account.address.startsWith('0x') ? 'ethereum' : 'solana'),
          connectorType: account.connectorType || 'unknown'
        });
      });
    }
    
    // STEP 3: Process wallets from useWallets hook as fallback
    if (privyWalletsHook && privyWalletsHook.length > 0) {
      console.log(`WalletSelector: Processing ${privyWalletsHook.length} wallets from useWallets`);
      
      (privyWalletsHook as any[]).forEach(wallet => {
        if (!wallet || !wallet.address || typeof wallet.address !== 'string' || wallet.address.includes('@')) {
          return;
        }
        
        // Skip if we've already added this address
        if (seenAddresses.has(wallet.address)) {
          return;
        }
        
        console.log(`WalletSelector: Adding wallet from useWallets: ${wallet.address.slice(0,6)}...${wallet.address.slice(-4)}`);
        
        seenAddresses.add(wallet.address);
        uniqueWallets.push({
          id: wallet.id || wallet.address,
          address: wallet.address,
          walletClientType: wallet.walletClientType || 
                           (wallet.chainType === 'solana' ? 'phantom' : 'privy'),
          connectedAt: wallet.connectedAt || new Date().toISOString(),
          connected: true,
          chain: wallet.chain || wallet.chainType || (wallet.address.startsWith('0x') ? 'BSC' : 'Solana'),
          chainId: wallet.chainType === 'solana' ? 'solana' : 56,
          embedded: wallet.embedded || wallet.walletClientType === 'embedded' || wallet.connectorType === 'embedded',
          chainType: wallet.chainType || (wallet.address.startsWith('0x') ? 'ethereum' : 'solana'),
          connectorType: wallet.connectorType || 'unknown'
        });
      });
    }
    
    // Log final results
    console.log(`WalletSelector: Found ${uniqueWallets.length} unique wallets after processing all sources`);
    uniqueWallets.forEach((wallet, index) => {
      console.log(`  ${index+1}. ${wallet.address ? `${wallet.address.slice(0,6)}...${wallet.address.slice(-4)}` : 'No address'} - ${wallet.walletClientType || 'unknown'} - ${wallet.chain || 'unknown'}`);
    });
    
    return uniqueWallets;
  }, [privyUserHook, privyWalletsHook]);

  const formatAddress = (address?: string) => {
    if (!address || typeof address !== 'string') return 'No Address';
    return address.length > 10 ? `${address.slice(0, 6)}...${address.slice(-4)}` : address;
  };

  const isSolanaAddress = (address?: string) => {
    return address && typeof address === 'string' && !address.startsWith('0x') && address.length > 30;
  };

  const getWalletId = useCallback((wallet: WalletBase) => {
    if (!wallet) return null;
    let walletType = "unknown";
    const isSolana = isSolanaAddress(wallet.address);
    
    if (wallet.walletClientType === "phantom" || 
        (isSolana && (window as any).phantom?.solana) || 
        (wallet as any)?.connector?.name === "phantom") {
      walletType = "phantom";
    } else if ((wallet.walletClientType === "injected" && (window as any).ethereum?.isMetaMask) ||
              (wallet.walletClientType === "metamask") ||
              (wallet as any)?.connector?.name === "metamask") {
      walletType = "metamask";
    } else if (wallet.walletClientType === "smart-wallet" || (wallet as any)?.smartWalletType) {
      walletType = "smart-wallet";
    } else if ((wallet as any)?.embedded || wallet.walletClientType === "embedded") {
      walletType = "embedded";
    } else {
      walletType = wallet.walletClientType || "external";
    }
    const walletId = wallet.address || wallet.id || "";
    return `${walletType}:${walletId}`;
  }, []);

  // Improved auto-selection logic
  const hasAutoSelectedRef = useRef(false);
  
  useEffect(() => {
    if (hasAutoSelectedRef.current || selectedWallet || !connectedWallets) {
      return;
    }
    
    if (connectedWallets.length === 0) {
      console.log("WalletSelector: No connected wallets available for auto-selection");
      return;
    }
    
    // Find smart wallet first (preferred)
    const smartWallet = connectedWallets.find(w => 
      w.walletClientType === 'smart-wallet' || (w as any).smartWalletType
    );
    
    // If smart wallet found, select it
    if (smartWallet) {
      console.log("WalletSelector: Auto-selecting smart wallet:", 
        smartWallet.address ? `${smartWallet.address.slice(0,6)}...${smartWallet.address.slice(-4)}` : 'No address');
      
      hasAutoSelectedRef.current = true;
      setTimeout(() => onSelectWallet(smartWallet), 0);
      return;
    }
    
    // Otherwise, select first connected wallet
    console.log("WalletSelector: No smart wallet found, auto-selecting first wallet:", 
      connectedWallets[0].address ? `${connectedWallets[0].address.slice(0,6)}...${connectedWallets[0].address.slice(-4)}` : 'No address');
    
    hasAutoSelectedRef.current = true;
    setTimeout(() => onSelectWallet(connectedWallets[0]), 0);
  }, [connectedWallets, selectedWallet, onSelectWallet]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative mb-3" ref={dropdownRef}>
      <div 
        className="bg-[#1D2226] p-3 rounded-lg cursor-pointer border border-gray-700 hover:border-gray-500 transition flex justify-between items-center"
        onClick={(e) => {
          e.stopPropagation();
          setIsOpen(!isOpen);
        }}
      >
        {selectedWallet && selectedWallet.address ? (
          <div className="flex items-center space-x-2">
            <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${
              isSolanaAddress(selectedWallet.address) ? "bg-[#9945FF]" : 
              selectedWallet.walletClientType === "smart-wallet" ? "bg-[#14FFA2]" :
              "bg-[#627EEA]"
            }`}>
              {isSolanaAddress(selectedWallet.address) ? "SOL" : 
               selectedWallet.walletClientType === "smart-wallet" ? "SW" : "ETH"}
            </div>
            <div>
              <span className="text-white">Wallet</span>
              <div className="text-sm text-gray-400">{formatAddress(selectedWallet.address)}</div>
            </div>
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center">
              <span className="text-white text-xs">?</span>
            </div>
            <span className="text-white">Select Wallet</span>
          </div>
        )}
        <ChevronDownIcon className="w-5 h-5 text-white" />
      </div>

      {isOpen && (
        <div className="absolute mt-1 w-full bg-[#181C20] rounded-lg shadow-lg z-20 p-2 max-h-60 overflow-y-auto border border-gray-700">
          <div className="flex justify-between items-center bg-[#14FFA2]/10 p-2 rounded-t-md mb-2">
            <div className="text-white text-sm font-medium">Select Wallet</div>
            <div className="text-[#14FFA2] text-xs">{Array.isArray(connectedWallets) ? connectedWallets.length : 0} Connected</div>
          </div>
          
          {Array.isArray(connectedWallets) && connectedWallets.length > 0 ? (
            <div className="grid grid-cols-1 gap-1">
              {connectedWallets.map((wallet: WalletBase, index: number) => {
                const walletId = getWalletId(wallet);
                const walletType = walletId ? walletId.split(':')[0] : "unknown";
                const isSolana = isSolanaAddress(wallet.address);
                const isSmartWallet = walletType === "smart-wallet" || wallet.walletClientType === "smart-wallet";
                const isSelected = selectedWallet && selectedWallet.address === wallet.address;
                
                return (
                  <div 
                    key={index} 
                    className={`flex items-center justify-between p-2 rounded-lg cursor-pointer 
                      ${isSelected ? 'bg-[#2A3038] border border-[#14FFA2]' : 'bg-[#1D2226] hover:bg-[#2A3038] border border-gray-800'}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log(`Selecting wallet: ${formatAddress(wallet.address)}`);
                      setTimeout(() => {
                        onSelectWallet(wallet);
                        setIsOpen(false);
                      }, 0);
                    }}
                  >
                    <div className="flex items-center space-x-2">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${
                        isSolana ? "bg-[#9945FF]" : 
                        isSmartWallet ? "bg-[#14FFA2]" :
                        walletType === "embedded" ? "bg-[#6C5DD3]" :
                        "bg-[#627EEA]"
                      }`}>
                        {isSolana ? "SOL" : 
                         isSmartWallet ? "SW" : "ETH"}
                      </div>
                      <div>
                        <span className="text-white text-sm">{formatAddress(wallet.address)}</span>
                        <div className="flex items-center space-x-1">
                          <span className={`text-xs px-1.5 py-0.5 rounded-full ${
                            isSolana ? "bg-purple-900/30 text-purple-400" : 
                            isSmartWallet ? "bg-green-900/30 text-green-400" :
                            walletType === "embedded" ? "bg-indigo-900/30 text-indigo-400" :
                            "bg-blue-900/30 text-blue-400"
                          }`}>
                            {isSolana ? "Solana" : 
                             isSmartWallet ? "Smart Wallet" :
                             "Ethereum"}
                          </span>
                          <span className="text-xs text-gray-500">
                            {isSmartWallet ? "ERC-4337" :
                             walletType === "embedded" ? "Privy" : 
                             walletType === "phantom" ? "Phantom" : 
                             walletType}
                          </span>
                        </div>
                      </div>
                    </div>
                    {isSelected && (
                      <div className="bg-[#14FFA2] text-black text-xs px-1.5 py-0.5 rounded-full">
                        Active
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-3 text-gray-400">
              No wallets connected
            </div>
          )}
        </div>
      )}
    </div>
  );
});

// Add missing utility functions for formatting quote data
const formatPrice = (price: string | number | undefined): string => {
  if (!price || price === '--') return 'N/A';
  
  try {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    if (isNaN(numPrice)) return 'N/A';
    
    if (numPrice < 0.01) {
      return numPrice.toFixed(8);
    } else if (numPrice < 1) {
      return numPrice.toFixed(6);
    } else if (numPrice < 1000) {
      return numPrice.toFixed(4);
    } else {
      return numPrice.toFixed(2);
    }
  } catch (e) {
    console.error("Error formatting price:", e);
    return 'N/A';
  }
};

const formatTokenPair = (tokenA: TokenBase | null, tokenB: TokenBase | null): string => {
  if (!tokenA || !tokenB) return '';
  
  // Extract symbols safely
  const symbolA = tokenA.symbol || 'Unknown';
  const symbolB = tokenB.symbol || 'Unknown';
  
    return `${symbolA}/${symbolB}`;
};

// Get chain name from chain ID (complement to getChainIdFromName)
const getChainName = (chainId: number | string | undefined): string | null => {
  if (chainId === null || chainId === undefined) return null;
  
  const chainIdMap: Record<string | number, string> = {
    1: 'Ethereum',
    137: 'Polygon',
    56: 'BSC',
    43114: 'Avalanche',
    10: 'Optimism',
    42161: 'Arbitrum',
    8453: 'Base',
    'solana': 'Solana'
  };
  
  return chainIdMap[chainId] || `Chain #${chainId}`;
};

// Add a proper type interface for the TradeSuccessPopup props
interface TradeSuccessPopupProps {
  isOpen: boolean;
  onClose: () => void;
  txHash: string;
  tokenIn: TokenBase | null | undefined;
  tokenOut: TokenBase | null | undefined;
  amountIn: string;
  amountOut: string;
}

// Add a success popup component with proper TypeScript types
const TradeSuccessPopup: React.FC<TradeSuccessPopupProps> = ({ isOpen, onClose, txHash, tokenIn, tokenOut, amountIn, amountOut }) => {
  // Add a useEffect to log when the popup opens or closes
  useEffect(() => {
    if (isOpen) {
      console.log("POPUP DEBUG: Trade Success Popup opened with data:", {
        txHash, 
        tokenIn: tokenIn?.symbol, 
        tokenOut: tokenOut?.symbol,
        amountIn,
        amountOut
      });
      
      // Force popup visibility with CSS animation
      document.documentElement.style.setProperty('--popup-visibility', 'visible');
      document.documentElement.style.setProperty('--popup-opacity', '1');
      
      // Make sure popup is actually rendered by logging its presence
      setTimeout(() => {
        const popupElement = document.querySelector('.popup-success-container');
        console.log("POPUP DEBUG: Popup element present in DOM:", !!popupElement);
      }, 100);
    }
  }, [isOpen, txHash, tokenIn, tokenOut, amountIn, amountOut]);

  if (!isOpen) return null;
  
  // Get explorer URL based on the network
  const getExplorerUrl = () => {
    // Default to BSCScan
    const bscExplorerUrl = "https://bscscan.com/tx/";
    return `${bscExplorerUrl}${txHash}`;
  };
  
  return (
    <div className="fixed inset-0 flex items-center justify-center z-[9999] bg-black bg-opacity-80 popup-success-container">
      <div className="bg-[#101114] border-2 border-[#14FFA2] rounded-lg p-6 max-w-md w-full mx-4 shadow-2xl animate-fadeIn" style={{animationDuration: '0.5s', animationFillMode: 'both'}}>
        <div className="flex flex-col items-center">
          <div className="w-20 h-20 flex items-center justify-center rounded-full bg-[#214638] mb-4 animate-enhanced-pulse">
            <HiOutlineCheckCircle className="text-[#14FFA2] w-12 h-12" />
          </div>
          
          <h3 className="text-xl font-bold text-white mb-2">Trade Successful!</h3>
          
          <div className="w-full p-4 bg-[#1B1D21] rounded-lg mb-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-400">You paid</span>
              <span className="text-white font-medium">{amountIn} {tokenIn?.symbol}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">You received</span>
              <span className="text-white font-medium">{amountOut} {tokenOut?.symbol}</span>
            </div>
          </div>
          
          {txHash && (
            <a 
              href={getExplorerUrl()} 
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center text-[#14FFA2] hover:underline mb-4"
            >
              View transaction <FaExternalLinkAlt className="ml-1 text-sm" />
            </a>
          )}
          
          <button
            onClick={onClose}
            className="py-3 px-6 bg-[#214638] text-[#14FFA2] rounded-lg font-medium hover:bg-[#2a5b48] transition-colors duration-200 w-full"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

// Add this helper function near the top of the file, after imports
const wrapTransactionFunction = (fn: any) => async (tx: any) => {
  const result = await fn(tx);
  return typeof result === 'string' ? { hash: result } : result;
};

// Add this interface near the top with other interfaces
interface PlatformFee {
  recipient: string;
  amount: string;
  token: string;
  percentage: number;
}

// ** Main Trading Panel **
const TradingPanel = ({ activeToken }: TradingPanelProps) => {
  // console.log("activeToken", activeToken.address);
  const [amount, setAmount] = useState<string>(''); // Changed from number to string to match NewTradingPanel
  // Consolidated TP/SL state variables
  const [isTakeProfit, setIsTakeProfit] = useState<boolean>(false);
  const [isStopLoss, setIsStopLoss] = useState<boolean>(false);
  const [takeProfit, setTakeProfit] = useState<string>(''); // Changed from string|number to string
  const [stopLoss, setStopLoss] = useState<string>(''); // Changed from string|number to string
  const [range, setRange] = useState<string>("0"); // Add missing range state for slider
  
  // Privy hooks and state
  const { user, authenticated, ready } = usePrivy();
  // Correctly call useWallets ONCE
  const { wallets = [], connectWallet }: { 
    wallets: WalletBase[];
    connectWallet: (options?: any) => Promise<any> 
  } = useWallets() as any;
  // Call useSendTransaction ONCE
  const { sendTransaction } = useSendTransaction(); 
  
  // Add smart wallet and bundled transactions hooks
  const { smartWalletClient, isLoading: isSmartWalletLoading } = useSmartWallet();

  const [coins, setCoins] = useState<TokenBase[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  
  // State for selected coin and trading data
  const [selectedCoin, setSelectedCoin] = useState<TokenBase | null>(null);
  const [quoteData, setQuoteData] = useState<QuoteData | null>(null);
  const [isLoadingQuote, setIsLoadingQuote] = useState<boolean>(false);
  const [dexQuotes, setDexQuotes] = useState<QuoteData[] | null>(null);
  const [bestDex, setBestDex] = useState<string | null>(null);
  const [selectedDex, setSelectedDex] = useState<string | null>(null);
  const [expectedPrice, setExpectedPrice] = useState<string>("N/A");
  const [priceImpact, setPriceImpact] = useState<string | null>("N/A");
  const [showAllQuotes, setShowAllQuotes] = useState<boolean>(false);
  const [tokenPair, setTokenPair] = useState<string>('');
  const [outputAmount, setOutputAmount] = useState<string>('N/A');
  const [outputTokenSymbol, setOutputTokenSymbol] = useState<string>('');
  const [platformFee, setPlatformFee] = useState<PlatformFee | null>(null);
  const [activeChainId, setActiveChainId] = useState<null>(null);
  const [statusMessage, setStatusMessage] = useState<string>("");
  const [realTimeBalances, setRealTimeBalances] = useState<Record<string, string>>({});
  const [inputValue, setInputValue] = useState<string>('');
  const [outputValue, setOutputValue] = useState<string>('');
  
  // Wallet selection state
  const [selectedWallet, setSelectedWallet] = useState<WalletBase | null>(null);
  
  // Track initialization state with a ref instead of triggering re-renders
  const isInitializedRef = useRef(false);
  const walletUpdateTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const hasCheckedCoinsRef = useRef<boolean>(false);
  const skipFirstRenderRef = useRef(true);
  const previousWalletsRef = useRef<string>('');
  const isFirstRenderRef = useRef(true);
  const isMountedRef = useRef(true);
  const isFetchingCoinsRef = useRef(false);
  const fetchTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const currentValueRef = useRef(''); // Add this ref for tracking input value
  
  // Add missing state variables for quote result
  const [quoteResult, setQuoteResult] = useState<null>(null);
  const [lastCalculatedAmount, setLastCalculatedAmount] = useState<string>("");
  const [isTokenTradeable, setIsTokenTradeable] = useState<boolean>(true);
  
  // Add a state to store the original pancakeswap quote data
  const [pancakeswapQuoteData, setPancakeswapQuoteData] = useState<PancakeswapQuoteData | null>(null);
  const [isPancakeswapPair, setIsPancakeswapPair] = useState<boolean>(false);
  
  // Add state for showing all available DEXes
  const [showDexSelector, setShowDexSelector] = useState<boolean>(false);
  
  // Add debounce functionality for input changes
  const debounceTimeoutRef = useRef<number | null>(null);
  
  // Add state for transaction status
  const [isTransacting, setIsTransacting] = useState<boolean>(false);
  const [transactionStatus, setTransactionStatus] = useState<string>("");

  // Add missing state variables 
  const [isSettingsOpen, setIsSettingsOpen] = useState<boolean>(false);
  const [slippageType, setSlippageType] = useState<string>("Auto");
  const [slippageValue, setSlippageValue] = useState<string>("0.5");
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  
  // Add missing state variables for Limit Tab
  const [price, setPrice] = useState<string>("");
  const [marketCap, setMarketCap] = useState<string>("");
  const [isPriceMode, setIsPriceMode] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<string>("Market");
  
  // Add a state to track if we're in sell mode
  const [tradeMode, setTradeMode] = useState<'buy' | 'sell'>('buy');
  
  // Add state for quote timeout
  const [quoteTimeout, setQuoteTimeout] = useState<NodeJS.Timeout | null>(null);
  
  // Add function to toggle between buy/sell mode
  const toggleTradeMode = (mode: 'buy' | 'sell') => {
    setTradeMode(mode);
    
    // Only reset output amount but keep the input value
    setOutputAmount('');
    
    // Reset quote data
    setQuoteData(null);
    setDexQuotes([]);
    
    // If there's already an input value, trigger a quote fetch for the new mode
    if (inputValue && parseFloat(inputValue) > 0 && selectedCoin && activeToken && authenticated) {
      // The quote will be fetched by the useEffect that watches for changes to tradeMode
    }
  };
  
  // Function to toggle between price and market cap mode
  const toggleMode = () => setIsPriceMode(!isPriceMode);
  
  // Toggle take profit handler
  const handleToggleTakeProfit = () => {
    setIsTakeProfit(!isTakeProfit);
    if (!isTakeProfit) {
      // Open the TP/SL modal when enabling
      setIsModalOpen(true);
    } else {
      // Reset values when turning off
      setTakeProfit('');
      setStopLoss('');
    }
  };

  // This is the only handleWalletSelection function we should have
  const handleWalletSelection = useCallback((wallet: WalletBase) => {
    if (!wallet || !wallet.address) {
      console.log("Invalid wallet passed to handleWalletSelection");
      return;
    }
    
    // Helper function to format addresses safely
    const formatWalletAddress = (address?: string) => {
      if (!address || typeof address !== 'string') return 'No Address';
      return address.length > 10 ? `${address.slice(0, 6)}...${address.slice(-4)}` : address;
    };
    
    // Log the selected wallet for debugging
    console.log("TradingPanel - Received wallet selection:", {
      address: formatWalletAddress(wallet.address),
      type: wallet.walletClientType || 'unknown',
      connected: !!wallet.connectedAt
    });
    
    // Skip unnecessary updates
    if (selectedWallet && selectedWallet.address && selectedWallet.address === wallet.address) {
      console.log("Selected wallet unchanged, skipping update");
      return;
    }
    
    console.log(`Setting selected wallet to: ${formatWalletAddress(wallet.address)} (${wallet.walletClientType || 'unknown type'})`);
    
    // Make sure we clear any existing data before changing wallets
    setCoins([]);
    setSelectedCoin(null);
    setStatusMessage("");
    hasCheckedCoinsRef.current = false;
    
    // Create a clean wallet object to avoid reference issues
    const cleanWallet: WalletBase = {
      id: wallet.id,
      address: wallet.address,
      walletClientType: wallet.walletClientType,
      connectedAt: wallet.connectedAt || new Date().toISOString(),
      connected: true,
      // Copy any other needed properties
      ethersProvider: wallet.ethersProvider,
      provider: wallet.provider,
      sendTransaction: wallet.sendTransaction,
      embedded: wallet.embedded
    };
    
    // Set the selected wallet - do this BEFORE fetching coin data
    setSelectedWallet(cleanWallet);
    
    try {
      // Clear any API caches
      if (walletAPI && typeof (walletAPI as any)._balancesCache?.clear === 'function') {
        (walletAPI as any)._balancesCache.clear();
        console.log("Cleared wallet API balance cache on wallet change");
      }
      
      // Also clear pending requests
      if (walletAPI && typeof (walletAPI as any)._pendingRequests?.clear === 'function') {
        (walletAPI as any)._pendingRequests.clear();
        console.log("Cleared wallet API pending requests on wallet change");
      }
    } catch (e) {
      console.warn("Error clearing API caches:", e);
    }
  }, [selectedWallet]); // Added selectedWallet to dependency array
  
  // Add a new handler for coins update from BalanceDropdown
  const handleCoinsUpdate = useCallback((updatedCoins: any[]) => {
    console.log(`Received ${updatedCoins.length} coins from BalanceDropdown:`, 
      updatedCoins.slice(0, 3).map(c => `${c.symbol}: ${c.balance}`));
    
    // Update the coins state in TradingPanel
    setCoins(updatedCoins.map(coin => {
      // Get chainId with proper null handling
      const chainIdValue = coin.chain ? getChainIdFromName(coin.chain) : undefined;
      // Ensure null is converted to undefined to match TokenBase type
      const safeChainId = chainIdValue === null ? undefined : chainIdValue;
      
      return {
        name: coin.name || coin.symbol || "Unknown",
        symbol: coin.symbol || "UNKNOWN",
        chain: coin.chain || "Unknown",
        address: coin.tokenAddress || "",
        tokenAddress: coin.tokenAddress || "",
        balance: coin.balance || "0",
        decimals: coin.decimals || 18,
        chainId: safeChainId
      };
    }));
    
    // Also update realTimeBalances for immediate use
    const balanceUpdates: Record<string, string> = {};
    updatedCoins.forEach(coin => {
      const tokenAddr = coin.tokenAddress || coin.address || "";
      if (coin.symbol) {
        balanceUpdates[coin.symbol.toUpperCase()] = coin.balance;
      }
      if (tokenAddr) {
        balanceUpdates[tokenAddr.toLowerCase()] = coin.balance;
      }
    });
    
    setRealTimeBalances(prev => ({
      ...prev,
      ...balanceUpdates
    }));
    
  }, []);

  // Initialize selectedCoin with activeToken when it's available
  useEffect(() => {
    // Complete reset when activeToken changes (SelectedTokensBar selection)
    console.log("ActiveToken changed to:", activeToken?.symbol);
    
    // Reset all trading state
    setQuoteResult(null);
    setQuoteData(null);
    setDexQuotes([]);
    setOutputAmount("");
    setExpectedPrice("N/A");
    setLastCalculatedAmount("");
    setInputValue(""); // Clear the input field
    setIsTokenTradeable(true); // Reset tradeable status until checked
    setPlatformFee(null);
    setStatusMessage("");
    
    // The existing flow to match activeToken with coins will run in the next useEffect
  }, [activeToken]); // Only depend on activeToken for this reset effect

  // Keep the existing useEffect that matches activeToken with coins
  useEffect(() => {
    if (!activeToken || !authenticated || !coins || coins.length === 0) return;
    
    try {
        // Find if the activeToken is in our coins list
        const matchedCoin = coins.find(coin => {
          if (!coin || !activeToken) return false;
          
          const coinName = coin.name ? coin.name.toLowerCase() : '';
          const coinSymbol = coin.symbol ? coin.symbol.toLowerCase() : '';
          const tokenName = activeToken.name ? activeToken.name.toLowerCase() : '';
          const tokenSymbol = activeToken.symbol ? activeToken.symbol.toLowerCase() : '';
          
          return (coinName && tokenName && coinName === tokenName) || 
                 (coinSymbol && tokenSymbol && coinSymbol === tokenSymbol);
        });
        
        if (matchedCoin) {
          console.log(`Setting selected coin to match active token: ${activeToken.name || activeToken.symbol}`);
          setSelectedCoin(matchedCoin);
        } else {
          // If we don't have the exact match, try to find a token with same symbol
          const symbolMatch = coins.find(coin => {
            if (!coin || !activeToken || !coin.symbol || !activeToken.symbol) return false;
            return coin.symbol.toLowerCase() === activeToken.symbol.toLowerCase();
          });
          
          if (symbolMatch) {
            setSelectedCoin(symbolMatch);
        } else if (coins.length > 0) {
          // If no match found but we have coins, set the first coin as selected
          setSelectedCoin(coins[0]);
        }
      }
    } catch (error) {
      console.error("Error matching active token to coins:", error);
    }
  }, [activeToken, coins, authenticated]);

  // Effect to fetch quotes whenever inputs change
  useEffect(() => {
    // Only fetch if we have an input value and tokens are selected
    if (inputValue && 
        parseFloat(inputValue) > 0 && 
        selectedCoin && 
        activeToken && 
        authenticated && 
        !isLoadingQuote) {
      
      // Check for same token error first
      if (selectedCoin.address && 
          activeToken.address && 
          selectedCoin.address.toLowerCase() === activeToken.address.toLowerCase()) {
        // Same token, don't fetch a quote
        setIsTokenTradeable(false);
        setStatusMessage("Cannot trade a token with itself");
        return;
      }
      
      console.log("Input or tokens changed, fetching new quote");
      // Clear any existing timeout
      if (quoteTimeout) {
        clearTimeout(quoteTimeout);
      }
      
      // Set a timeout to fetch quotes (to debounce)
      const timeout = setTimeout(() => {
        if (tradeMode === 'buy') {
          // For buying, we send selectedCoin and receive activeToken
          fetchQuote({
            amount: inputValue,
            selectedCoin,
            activeToken,
            setIsLoadingQuote,
            setDexQuotes,
            setQuoteData,
            setPancakeswapQuoteData,
            setIsTokenTradeable,
            setOutputAmount,
            setExpectedPrice,
            setStatusMessage,
            isBuyAction: true
          });
        } else {
          // For selling, we send activeToken and receive selectedCoin
          fetchQuote({
            amount: inputValue,
            selectedCoin: activeToken, // Swap the tokens for sell
            activeToken: selectedCoin, // Swap the tokens for sell
            setIsLoadingQuote,
            setDexQuotes,
            setQuoteData,
            setPancakeswapQuoteData,
            setIsTokenTradeable,
            setOutputAmount,
            setExpectedPrice,
            setStatusMessage,
            isBuyAction: false
          });
        }
      }, 300);
      
      setQuoteTimeout(timeout);
    }
  }, [inputValue, selectedCoin, activeToken, authenticated, tradeMode]);

  // When logout happens, clear the data
  useEffect(() => {
    const handleLogout = () => {
      console.log("TradingPanel: Logout detected, clearing wallet data");
      setCoins([]);
      // Reset our tracking refs on logout
      hasCheckedCoinsRef.current = false;
      previousWalletsRef.current = '';
    };
    
    // Listen for Privy logout events
    window.addEventListener("privy:logout", handleLogout);
    
    return () => {
      window.removeEventListener("privy:logout", handleLogout);
    };
  }, []);

  // Add isAmount state variable for the Amount/Qty toggle
  const [isAmount, setIsAmount] = useState<boolean>(true);

  // Helper function to check ERC20 token balances
  const checkERC20Balance = async (
    provider: ethers.providers.Provider, 
    walletAddress: string, 
    tokenAddress: string, 
    decimals: number
  ): Promise<string> => {
    try {
      const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, provider);
      const balance = await tokenContract.balanceOf(walletAddress);
      // Format the balance with proper decimals
      return ethers.utils.formatUnits(balance, decimals);
    } catch (error) {
      console.error("Error checking ERC20 balance:", error);
      return "0";
    }
  };

  // Simple helper for formatting addresses
  const formatAddressShort = (address?: string) => {
    if (!address || typeof address !== 'string') return 'No Address';
    return address.length > 10 ? `${address.slice(0, 6)}...${address.slice(-4)}` : address;
  };

  // Handler for connect wallet button
  const handleConnectWallet = () => {
      connectWallet();
  };

  // Update the connectPhantomWallet function
  const connectPhantomWallet = async () => {
    try {
        setStatusMessage("Connecting to Phantom wallet...");
      
      // Direct connection as fallback
      try {
        const response = await (window as any).phantom?.solana.connect();
        console.log("Connected to Phantom directly:", response);
        
        // After connection, refresh available coins
        await fetchAvailableCoins();
        
        setStatusMessage("Connected to Phantom wallet!");
      } catch (err) {
        console.error("Error connecting to Phantom directly:", err);
        setStatusMessage("Error connecting to Phantom. Please try again.");
      }
    } catch (error) {
      console.error("Error in connectPhantomWallet:", error);
      setStatusMessage("Failed to connect to Phantom wallet");
    }
  };

  const handleTpslSubmit = (data: { takeProfit: number; stopLoss: number }) => {
    // Set takeProfit and stopLoss
    setTakeProfit(data.takeProfit.toString());
    setStopLoss(data.stopLoss.toString());
    console.log("Received TPSL Data:", data);
    // Handle the data as needed
  };

  // Reset tradeable status when tokens change
  useEffect(() => {
    // Reset quote results when tokens change
    setQuoteResult(null);
    setOutputAmount("");
    setLastCalculatedAmount("");
    
    // Check if tokens have the same address
    if (selectedCoin && activeToken && 
        selectedCoin.address && activeToken.address && 
        selectedCoin.address.toLowerCase() === activeToken.address.toLowerCase()) {
      setStatusMessage("Error: Cannot trade a token with itself");
      setIsTokenTradeable(false);
      return;
    }
    
    if (selectedCoin && activeToken) {
      console.log(`TradingPanel: Both tokens present (${activeToken.symbol} and ${selectedCoin.symbol}), checking if tradeable`);
      
      // Always check if tokens are tradeable when token pair changes
      // This is critical to ensure proper UI state
      fetchTokenData();
      
      // Only fetch quote if user has already entered a VALID amount
      // This is crucial - ensure parseFloat(inputValue) is a real number > 0
      if (inputValue && !isNaN(parseFloat(inputValue)) && parseFloat(inputValue) > 0) {
        console.log(`Auto-fetching quote for ${inputValue} ${selectedCoin.symbol} to ${activeToken.symbol}`);
        
        fetchQuote({
          amount: inputValue,
          selectedCoin,
          activeToken,
          setIsLoadingQuote,
          setDexQuotes,
          setQuoteData: (data) => {
            setQuoteData(data);
            // Check for platform fee in multiple possible fields
            if (data && typeof data !== 'function' && (data.platformFee || (data as any).feeData || (data as any).fee)) {
              const fee = data.platformFee || (data as any).feeData || (data as any).fee;
              console.log("Setting platform fee from quote data:", fee);
              if (typeof fee === 'object' && 'recipient' in fee && 'amount' in fee && 'token' in fee && 'percentage' in fee) {
                setPlatformFee({
                  recipient: String(fee.recipient),
                  amount: String(fee.amount),
                  token: String(fee.token),
                  percentage: typeof fee.percentage === 'number' ? fee.percentage : parseFloat(String(fee.percentage || '0'))
                });
              } else {
                setPlatformFee(null);
              }
            } else {
              setPlatformFee(null);
            }
          },
          setPancakeswapQuoteData,
          setIsTokenTradeable,
          setOutputAmount,
          setExpectedPrice,
          setStatusMessage,
          isBuyAction: true
        });
      }
    } else {
      // If we don't have both tokens, set tradeable to false
      setIsTokenTradeable(false);
    }
  }, [selectedCoin, activeToken]);

  // Add this function to check and approve token allowances
  const renderQuoteDetails = () => {
    if (!isTokenTradeable || !quoteData) return null;
    
    // Use the actual route data from the response
    const routePath = quoteData.path || [];
    
    return (
      <div className="mt-2 p-3 bg-[#1D2226] rounded text-sm">
        <h4 className="text-white mb-2 font-medium">Trading Details:</h4>
        
        {/* Price Impact */}
        {quoteData.priceImpact !== undefined && (
          <div className="flex justify-between py-1 border-b border-gray-700">
            <span className="text-gray-400">Price Impact:</span>
            <span className={`${
              typeof quoteData?.priceImpact === 'string' 
                ? (parseFloat(quoteData.priceImpact) > 3 ? 'text-red-400' : 'text-green-400')
                : typeof quoteData?.priceImpact === 'number'
                  ? (quoteData.priceImpact > 3 ? 'text-red-400' : 'text-green-400')
                  : 'text-white'
            }`}>
              {typeof quoteData.priceImpact === 'string' 
                ? parseFloat(quoteData.priceImpact).toFixed(2) 
                : typeof quoteData.priceImpact === 'number' 
                  ? quoteData.priceImpact.toFixed(2) 
                  : quoteData.priceImpact
              }%
            </span>
          </div>
        )}
        
        {/* Exchange Rate */}
        <div className="flex justify-between py-1 border-b border-gray-700">
          <span className="text-gray-400">Exchange Rate:</span>
          <span className="text-white">
            {/* Use executionPrice directly from API response */}
            1 {quoteData.tokenIn?.symbol || selectedCoin?.symbol || 'Token'} = {
              quoteData.executionPrice 
                ? (typeof quoteData.executionPrice === 'string' 
                   ? quoteData.executionPrice
                   : quoteData.executionPrice.toString())
                : (quoteData?.price 
                   ? (typeof quoteData.price === 'string'
                      ? quoteData.price
                      : quoteData.price.toString())
                   : 'N/A')
            } {quoteData.tokenOut?.symbol || activeToken?.symbol || ''}
          </span>
        </div>
               {/* Liquidity Source - Enhanced with DEX Selection */}
        <div className="py-2 border-b border-gray-700">
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-400 font-medium">Liquidity Source:</span>
          </div>
          
          {/* Improved DEX Selector - Always visible */}
          {dexQuotes && dexQuotes.length > 0 ? (
            <DexSelector
              dexQuotes={dexQuotes as any} // Type assertion to resolve compatibility issues
              selectedDex={selectedDex}
              bestDex={bestDex}
              onSelectDex={(dexId) => {
                setSelectedDex(dexId);
                
                // Update quote data when a new DEX is selected
                const selectedQuote = dexQuotes.find(q => q.dexId === dexId || (q as any).source === dexId);
                if (selectedQuote) {
                  console.log('Selected DEX quote:', selectedQuote);
                  
                  // Update output amount if available
                  if (selectedQuote.amountOut || 
                      (selectedQuote.quote && (selectedQuote.quote.amountOut))) {
                    const newAmount = selectedQuote.amountOut || 
                                     ((selectedQuote as any).quote ? ((selectedQuote as any).quote.amountOut) : 'N/A');
                    setOutputAmount(typeof newAmount === 'string' || typeof newAmount === 'number' ? newAmount.toString() : 'N/A');
                  }
                  
                  // Update expected price if available
                  if (selectedQuote.executionPrice || selectedQuote.price) {
                    const price = selectedQuote.executionPrice || selectedQuote.price;
                    setExpectedPrice(price ? price.toString() : 'N/A');
                  }
                }
              }}
            />
          ) : (
            <div className="p-2 bg-gray-800 bg-opacity-50 rounded-lg text-gray-400 text-sm">
              No DEX options available for this pair
            </div>
          )}
        </div>
        
        {/* Platform Fee */}
        {(quoteData?.platformFee || (quoteData as any)?.feeData || (quoteData as any)?.fee || platformFee) && (
          <div className="flex justify-between py-1 border-b border-gray-700">
            <span className="text-gray-400">Platform Fee:</span>
            <span className="text-white">
              {(() => {
                // Use either the fee from quoteData or from the separate platformFee state
                const fee = quoteData?.platformFee || (quoteData as any)?.feeData || (quoteData as any)?.fee || platformFee;
                console.log("Platform Fee object to render:", JSON.stringify(fee, null, 2));
                
                if (!fee) {
                  return '0.1%'; // Fallback
                }
                
                if (typeof fee === 'object' && fee) {
                  const amount = fee.amount || '0';
                  
                  // Get token symbol
                  let tokenSymbol = '';
                  if (typeof fee.token === 'object' && fee.token && fee.token.symbol) {
                    tokenSymbol = fee.token.symbol;
                  } else if (typeof fee.token === 'string') {
                    if (quoteData?.tokenOut && quoteData.tokenOut.address === fee.token) {
                      tokenSymbol = quoteData.tokenOut.symbol;
                    } else if (quoteData?.tokenIn && quoteData.tokenIn.address === fee.token) {
                      tokenSymbol = quoteData.tokenIn.symbol;
                    } else {
                      tokenSymbol = activeToken?.symbol || 'tokens';
                    }
                  }
                  
                  // Format percentage
                  const pct = typeof fee.percentage === 'number' 
                    ? fee.percentage   // Change from toFixed(2) to toFixed(1) to display 0.1% correctly
                    : typeof fee.percentage === 'string'
                      ? fee.percentage
                      : '0.1';
                  
                  return `${amount} ${tokenSymbol} (${pct}%)`;
                }
                
                // Display direct value if it's not an object
                if (typeof fee === 'string') {
                  return fee;
                } else if (typeof fee === 'number') {
                  return `${(fee * 100).toFixed(2)}%`;
                }
                
                return '0.1%'; // Fallback
              })()}
            </span>
          </div>
        )}
        
        {/* Slippage Tolerance */}
        <div className="flex justify-between py-1 border-b border-gray-700">
          <span className="text-gray-400">Slippage Tolerance:</span>
          <span className="text-white">
            {slippageType === 'Auto' ? 'Auto' : `${slippageValue}%`}
          </span>
        </div>
        
        {/* Input Amount */}
        {quoteData.amountIn && (
          <div className="flex justify-between py-1 border-b border-gray-700">
            <span className="text-gray-400">Input Amount:</span>
            <span className="text-white">
              {quoteData.amountIn} {quoteData.tokenIn?.symbol || selectedCoin?.symbol || 'tokens'}
            </span>
          </div>
        )}
        
        {/* Output Amount */}
        {quoteData.amountOut && (
          <div className="flex justify-between py-1 border-b border-gray-700">
            <span className="text-gray-400">Output Amount:</span>
            <span className="text-white">
              {quoteData.amountOut} {quoteData.tokenOut?.symbol || activeToken?.symbol || 'tokens'}
            </span>
          </div>
        )}
        
        {/* Route - Display token symbols based on addresses */}
        {routePath && routePath.length > 0 && (
          <div className="flex justify-between py-1">
            <span className="text-gray-400">Route:</span>
            <span className="text-xs text-gray-300">
              {(() => {
                // Use tokenIn and tokenOut from API response if available
                if (quoteData.tokenIn && quoteData.tokenOut) {
                  return `${quoteData.tokenIn.symbol} → ${quoteData.tokenOut.symbol}`;
                }
                
                // Otherwise try to map addresses to symbols
                if (routePath.length >= 2) {
                  // For simplicity, just show the first and last tokens in the route
                  const firstAddr = routePath[0];
                  const lastAddr = routePath[routePath.length - 1];
                  
                  // First token symbol
                  const firstSymbol = 
                    (firstAddr === selectedCoin?.address || 
                     firstAddr === selectedCoin?.tokenAddress) ? 
                    selectedCoin?.symbol : 'USDT';
                  
                  // Last token symbol
                  const lastSymbol = 
                    (lastAddr === activeToken?.address || 
                     lastAddr === activeToken?.tokenAddress) ? 
                    activeToken?.symbol : 'token';
                  
                  return `${firstSymbol} → ${lastSymbol}`;
                }
                
                // Fallback
                return `${selectedCoin?.symbol || 'Token'} → ${activeToken?.symbol || 'Token'}`;
              })()}
            </span>
          </div>
        )}
      </div>
    );
  };

  // Filter coins by chain ID
  const getFilteredCoins = () => {
    if (!activeChainId || !coins || coins.length === 0) return coins;
    
    // Include coins from the selected chain and those without chain info
    return coins.filter(coin => 
      !coin.chain || 
      coin.chain === 'Various' || 
      getChainIdFromName(coin.chain) === activeChainId
    );
  };

  // Helper to convert chain name to chain ID
  const getChainIdFromName = (chainName: string): number | string | null => {
    if (!chainName) return null;
    
    const chainMap: Record<string, number | string> = {
      'Ethereum': 1,
      'Polygon': 137,
      'BSC': 56,
      'Avalanche': 43114,
      'Optimism': 10,
      'Arbitrum': 42161,
      'Base': 8453,
      'Solana': 'solana'
    };
    
    return chainMap[chainName] || null;
  };

  // Helper to normalize platformFee to the expected format
  const normalizedPlatformFee = platformFee ? {
    recipient: platformFee.recipient,
    amount: platformFee.amount,
    token: platformFee.token,
    percentage: platformFee.percentage
  } : undefined;

  // Format the output display with the correct token symbols
  const formatOutputDisplay = () => {
    if (!outputAmount || outputAmount === 'N/A' || outputAmount === '') {
      // Show correct token symbol based on mode
      const displaySymbol = tradeMode === 'sell' ? selectedCoin?.symbol : activeToken?.symbol;
      return `~ 0 ${displaySymbol || 'Token'}`;
    }
    
    // Display the output amount with the correct token symbol based on the current mode
    const displaySymbol = tradeMode === 'sell' ? selectedCoin?.symbol : activeToken?.symbol;
    
    // Display the output amount with the correct token symbol
    return `~ ${outputAmount} ${displaySymbol || 'Token'}`;
  };

  // Add this function to check and approve token allowances
  const checkAndApproveTokenAllowance = async (
    privySendTransaction: typeof sendTransaction, 
    token: TokenBase, 
    routerAddress: string | undefined, 
    amount: string | number
  ): Promise<{ success: boolean; message: string }> => {
    // Early exit if routerAddress is invalid
    if (!routerAddress || !ethers.utils.isAddress(routerAddress)) {
        console.error(`Invalid or missing router address: ${routerAddress}`);
        return { success: false, message: "Invalid router address provided for approval check." };
    }

    // Add null check for selectedWallet earlier
    if (!selectedWallet || !selectedWallet.address) {
      console.error("Cannot check allowance: No selected wallet or wallet address.");
      return { success: false, message: "Wallet not connected or address missing." };
    }
    const userAddress = selectedWallet.address; // Now safe to access

    try {
      // The rest of the null checks for token and userAddress are here...
      // ... (rest of the initial checks)

      console.log(`Checking allowance for ${token.symbol} (${token.address || token.tokenAddress}) from ${userAddress} to router ${routerAddress}`);
      
      // Skip allowance check for native tokens
      // ... (native token check logic) ...
      
      const tokenAddress = token.address || token.tokenAddress;
      if (!tokenAddress || !ethers.utils.isAddress(tokenAddress)) {
         console.error(`Invalid token address: ${tokenAddress}`);
        return { success: false, message: `Invalid address for ${token.symbol}` };
      }
      
      // --- Get Provider (Use Public RPC for Read-Only Calls) ---
      let provider: ethers.providers.Provider | undefined;
      try {
          // Determine chain ID dynamically
          // Prioritize token.chainId, then selectedWallet.chainId, default to 56 (BSC)
          let chainId: number | string | undefined = token.chainId || selectedWallet?.chainId;
          if (!chainId) {
              console.warn(`No chainId found on token or wallet, defaulting to 56 (BSC) for ${token.symbol}.`);
              chainId = 56;
          }
          const numericChainId = typeof chainId === 'string' ? parseInt(chainId, 10) : chainId;

          let rpcUrl: string | undefined;

          // Define RPC URLs - ADD MORE AS NEEDED
          const RPC_URLS: { [key: number]: string } = {
              1: 'https://mainnet.infura.io/v3/YOUR_INFURA_ID', // Replace with your Infura ID or other provider
              56: 'https://bsc-dataseed.binance.org/',
              137: 'https://polygon-rpc.com/',
              // Add other supported chain RPCs here
          };

          rpcUrl = RPC_URLS[numericChainId];

          if (!rpcUrl) {
               console.error(`Unsupported chain ID (${numericChainId}) for public RPC lookup.`);
               return { success: false, message: `Unsupported network (Chain ID: ${numericChainId}) for allowance check.` };
          }
          
          console.log(`Using public RPC for read operations on chain ${numericChainId}: ${rpcUrl}`);
          provider = new ethers.providers.JsonRpcProvider(rpcUrl);

          // Verify provider connection
          const network = await provider.getNetwork();
          if (network.chainId !== numericChainId) {
              console.error(`Public RPC provider connected to wrong network: ${network.chainId}, expected ${numericChainId}`);
              throw new Error(`Public RPC provider connected to wrong network (${network.chainId})`);
          }
          console.log(`Public provider connected successfully to network: ${network.name} (${network.chainId})`);

      } catch (providerError) {
          console.error("Error initializing public RPC provider:", providerError);
          return { success: false, message: "Error setting up connection for allowance check." };
      }
      // -----------------------------------------------------

      // Define ERC20 Interface for encoding
      const erc20Interface = new ethers.utils.Interface([
        "function allowance(address owner, address spender) view returns (uint256)",
        "function approve(address spender, uint256 amount) returns (bool)",
        "function decimals() view returns (uint8)"
      ]);

      // Get actual decimals using the public provider
      let actualDecimals = token.decimals || 18; 
      try {
          const decimalsContract = new ethers.Contract(tokenAddress, ["function decimals() view returns (uint8)"], provider);
          actualDecimals = await decimalsContract.decimals();
          console.log(`Got decimals from contract: ${actualDecimals}`);
      } catch (e) {
          console.warn(`Couldn't get decimals from contract for ${token.symbol}, using provided: ${actualDecimals}`);
      }

      // Check current allowance using the public provider
      const viewContract = new ethers.Contract(tokenAddress, erc20Interface, provider);
      let currentAllowance: ethers.BigNumber;
      try {
          currentAllowance = await viewContract.allowance(userAddress, routerAddress);
          console.log(`Current allowance: ${ethers.utils.formatUnits(currentAllowance, actualDecimals)} ${token.symbol}`);
      } catch (allowanceError) {
          console.error("Error checking allowance:", allowanceError);
          const errMsg = allowanceError instanceof Error ? allowanceError.message : 'Unknown error during allowance check';
          // If allowance check fails with public RPC, it's likely a genuine issue
          return { success: false, message: `Failed to check allowance for ${token.symbol}: ${errMsg}` };
      }

      // Convert amount to wei
      const amountInWei = ethers.utils.parseUnits(amount.toString(), actualDecimals);

      // If allowance is insufficient, request approval using selectedWallet.sendTransaction
      if (currentAllowance.lt(amountInWei)) {
          console.log(`Insufficient allowance for ${token.symbol}, constructing approval transaction.`);
          
          // Check if the PRIVY sendTransaction function is available (it should be)
          if (typeof privySendTransaction !== 'function') { 
            console.error("Privy sendTransaction function is not available from the hook.");
            return { success: false, message: "Wallet transaction function unavailable. Cannot approve token." };
          }
  
          try {
            // Log the token object right before checking its chainId
            console.log("Token object before chainId assignment:", JSON.stringify(token));
            
            const maxUint256 = ethers.constants.MaxUint256;
            console.log(`Attempting to approve max uint256 for ${token.symbol}...`);
  
            const encodedApproveData = erc20Interface.encodeFunctionData("approve", [
              routerAddress, 
              maxUint256
            ]);
  
            // Construct the transaction object FOR PRIVY'S sendTransaction
            // It expects chainId directly in the main object if needed
            const tx: { to: string, data: string, chainId?: number } = {
              to: tokenAddress,
              data: encodedApproveData,
              chainId: undefined // Initialize
            };
  
            const walletChainId = selectedWallet?.chainId;
            let numericChainId: number | undefined = undefined;

            if (walletChainId) {
               numericChainId = typeof walletChainId === 'string' && walletChainId.startsWith('0x') 
                                      ? parseInt(walletChainId, 16) 
                                      : Number(walletChainId); // Ensure it's a number
               console.log("Using chainId from wallet:", numericChainId);
            } else if(token.chainId) {
               numericChainId = typeof token.chainId === 'string' ? parseInt(token.chainId, 10) : Number(token.chainId); // Ensure it's a number
               console.log("Using chainId from token data:", numericChainId);
            } else if(token.chain === 'BSC' || token.network === 'BNB Smart Chain (BEP20)') {
               // Fallback for BSC tokens that don't have chainId but have chain or network identifier
               numericChainId = 56; // BSC mainnet
               console.log("Falling back to BSC chainId (56) based on token chain/network:", token.chain || token.network);
            } else {
               // Additional attempt to determine chainId from token address if it follows BSC pattern
               const tokenLowerAddress = tokenAddress.toLowerCase();
               if (tokenLowerAddress.startsWith('0x') && 
                   // Check if token is in our coins list where we know the chainId
                   coins.some(c => (c.address?.toLowerCase() === tokenLowerAddress || 
                                   c.tokenAddress?.toLowerCase() === tokenLowerAddress))) {
                   numericChainId = 56; // Most likely BSC if we have it in our coin list
                   console.log("Falling back to BSC chainId (56) based on token address match in coins array");
               } else {
                   console.error("Cannot determine chainId for transaction!");
                   return { success: false, message: "Could not determine network chain ID for approval." };
               }
            }
            
            // Assign the potentially parsed number to tx.chainId
            if (numericChainId !== undefined && !isNaN(numericChainId)) {
                tx.chainId = numericChainId;
            } else {
                console.error("Failed to parse a valid numeric chainId.");
                 return { success: false, message: "Invalid network chain ID for approval." };
            }

            console.log("Approval Transaction Object for Privy:", tx);
            setTransactionStatus("Requesting approval signature...");
            
            // Call the sendTransaction function from the hook
            // The hook handles specifying the wallet address if needed via options
            const txResponse = await privySendTransaction(tx);
            
            // Privy's hook directly returns { hash: string } on success
            const txHash = txResponse?.hash;
            if (!txHash) {
                console.error("Privy sendTransaction did not return a hash:", txResponse);
                throw new Error("Approval sent, but transaction hash was missing.");
            }
            console.log("Approval transaction sent via Privy, hash:", txHash);
    
            setTransactionStatus("Waiting for approval confirmation...");
            
            // Wait using the public provider
            const receipt = await provider.waitForTransaction(txHash);
            
            if (receipt.status === 0) {
               console.error("Approval transaction failed (receipt status 0)", receipt);
               throw new Error("Approval transaction confirmed but failed on-chain.");
            }
  
            console.log(`Approval successful for ${token.symbol}`, receipt);
            setTransactionStatus("");
            
            // Verify allowance increase after confirmation (using public provider)
            try {
                const postApprovalAllowance = await viewContract.allowance(userAddress, routerAddress);
                 console.log(`Post-approval allowance: ${ethers.utils.formatUnits(postApprovalAllowance, actualDecimals)} ${token.symbol}`);
                 if (postApprovalAllowance.lt(amountInWei)) {
                     console.warn("Allowance did not increase sufficiently after approval transaction.");
                 }
            } catch(verifyError) {
                console.warn("Could not verify allowance post-approval:", verifyError);
            }
  
            return { success: true, message: `Successfully approved ${token.symbol}` };
  
          } catch (approveError) {
              // ... (error handling for sendTransaction remains the same) ...
               console.error("Error sending approval transaction:", approveError);
               setTransactionStatus("");
               const errMsg = approveError instanceof Error ? approveError.message : 'Unknown error during approval';
               if (errMsg.includes('rejected') || errMsg.includes('denied')) {
                    return { success: false, message: "Approval transaction rejected by user." };
               }
               return { success: false, message: `Failed to approve ${token.symbol}: ${errMsg}` };
          }
      } else {
          console.log(`${token.symbol} already has sufficient allowance`);
          return { success: true, message: "Token already has sufficient allowance" };
      }

    } catch (error: unknown) {
        // ... (overall error handling remains the same) ...
         console.error("Overall error in checkAndApproveTokenAllowance:", error);
         setTransactionStatus("");
         const errorMessage = error instanceof Error ? error.message : 'Unknown allowance error';
         return { 
           success: false, 
           message: `Allowance error: ${errorMessage}` 
         };
    }
  };

  // Update the handleTrade function to handle allowance checks
  const handleTrade = async (action: 'buy' | 'sell') => {
    console.log(`Trade action triggered: ${action}`);
    console.log('Current state:', {
      authenticated,
      selectedWallet: selectedWallet?.address,
      selectedCoin: selectedCoin?.symbol,
      activeToken: activeToken?.symbol,
      inputValue,
      quoteData: !!quoteData
    });

    // Only validate the quote data, as this could change between button render and click
    if (!quoteData || !quoteData.routerAddress) {
      setStatusMessage("Unable to get quote data for this pair. Please refresh the quote and try again.");
      return;
    }

    // Safety check - the button should prevent this, but let's be sure
    if (!selectedCoin || !activeToken || !selectedWallet) {
      setStatusMessage("Missing required data for trade. Please refresh and try again.");
      return;
    }

    try {
      setIsTransacting(true);
      setTransactionStatus("Preparing transaction...");

      // Determine which is the base and quote token based on the action
      // IMPORTANT: For PancakeSwap, we need to be consistent with their API expectations
      // tokenIn: The token you're selling (sending from your wallet)
      // tokenOut: The token you're buying (receiving to your wallet)
      let tokenIn: any, tokenOut: any, amount: string;
      
      if (action === 'buy') {
        // For buy: we're spending selectedCoin to buy activeToken
        tokenIn = selectedCoin;  // What we're selling (e.g., USDT)
        tokenOut = activeToken;  // What we're buying (e.g., BTC)
        amount = inputValue;
        console.log(`Buy action: sending ${amount} ${tokenIn.symbol} to receive ${tokenOut.symbol}`);
        
        // Ensure tokenOut has required properties
        const tokenOutAddress = tokenOut.address?.toLowerCase() || tokenOut.tokenAddress?.toLowerCase();
        if (tokenOutAddress) {
          const matchingOutCoin = coins.find(coin => 
            (coin.address?.toLowerCase() === tokenOutAddress || coin.tokenAddress?.toLowerCase() === tokenOutAddress) && 
            coin.symbol === tokenOut.symbol
          );
          
          if (matchingOutCoin) {
            console.log(`Found matching coin in coins array for tokenOut with address ${matchingOutCoin.address || matchingOutCoin.tokenAddress}`);
            tokenOut = { 
              ...tokenOut, 
              address: matchingOutCoin.address || matchingOutCoin.tokenAddress || tokenOut.address || tokenOut.tokenAddress,
              decimals: matchingOutCoin.decimals || tokenOut.decimals || 18
            };
          }
        }
      } else {
        // For sell: we're spending activeToken to buy selectedCoin
        tokenIn = activeToken;   // What we're selling (e.g., BTC)
        tokenOut = selectedCoin; // What we're buying (e.g., USDT)
        amount = inputValue;
        console.log(`Sell action: sending ${amount} ${tokenIn.symbol} to receive ${tokenOut.symbol}`);
        
        // For sell actions, ensure we have the correct chainId from the coins array
        const tokenInAddress = tokenIn.address?.toLowerCase() || tokenIn.tokenAddress?.toLowerCase();
        const matchingCoin = coins.find(c => 
          (c.address?.toLowerCase() === tokenInAddress || c.tokenAddress?.toLowerCase() === tokenInAddress) && 
          c.symbol === tokenIn.symbol
        );
        
        if (matchingCoin && matchingCoin.chainId) {
          console.log(`Found matching coin in coins array with chainId ${matchingCoin.chainId}`);
          tokenIn = { ...tokenIn, chainId: matchingCoin.chainId };
        }
      }

      // Log the configured tokens for the trade
      console.log('Trade configuration:', {
        tokenIn: {
          symbol: tokenIn.symbol,
          address: tokenIn.address || tokenIn.contract || tokenIn.tokenAddress
        },
        tokenOut: {
          symbol: tokenOut.symbol,
          address: tokenOut.address || tokenOut.contract || tokenOut.tokenAddress
        },
        amount,
        recipient: selectedWallet.address,
        slippageTolerance: parseFloat(slippageValue) || 0.5
      });

      // Get the recipient address (user's address)
      const recipient = selectedWallet.address;

      // Set slippage from the settings
      const slippageTolerance = parseFloat(slippageValue) || 0.5;

      // Check if the wallet has the proper transaction capabilities
      if (!selectedWallet.sendTransaction && 
          !selectedWallet.ethersProvider && 
          !selectedWallet.provider && 
          !window.ethereum &&
          !selectedWallet.embeddedWallet &&
          !selectedWallet.walletClient &&
          !smartWalletClient) {
        setStatusMessage("Your wallet doesn't support direct transactions. Please use a web3 wallet.");
        setIsTransacting(false);
        return;
      }

      // Extract platform fee from quote data if present
      const platformFee = quoteData.platformFee || 
                          (quoteData as any).feeData || 
                          (quoteData as any).fee || 
                          (quoteData.quote && (quoteData.quote.platformFee || (quoteData.quote as any).feeData || (quoteData.quote as any).fee));
      
      // Determine which chain we're on based on token data
      const isTokenSolana = tokenIn.chain === 'Solana' || tokenOut.chain === 'Solana' || 
                           tokenIn.chainId === 'solana' || tokenOut.chainId === 'solana';
      
      // Get the DEX ID from the selected DEX or the best DEX or from quote data
      const dexId = selectedDex || bestDex || quoteData.dexId || (quoteData as any).source || 'pancakeswap';
      
      console.log(`Executing ${action} on ${dexId} (chain: ${isTokenSolana ? 'Solana' : 'BSC/EVM'})`);
      
      // Create a pairAddress if we have it from the quote
      const pairAddress = quoteData?.pairAddress || (pancakeswapQuoteData ? pancakeswapQuoteData.pairAddress : undefined);

      // Check if we're on Solana, since those transactions are handled differently
      let tradeResult: any = {
        success: false,
        message: "Transaction not initialized",
        data: null
      };
      
      if (isTokenSolana) {
        // Solana transactions don't support bundled transactions with platform fees
        setTransactionStatus(`Executing ${action} on Solana ${dexId.charAt(0).toUpperCase() + dexId.slice(1)}...`);
        
        try {
          if (dexId.toLowerCase() === 'raydium') {
            console.log('Executing Raydium trade on Solana');
            tradeResult = await completeTradeRaydium(
              tokenIn,
              tokenOut,
              amount,
              recipient || selectedWallet?.address || '',
              async (tx) => {
                const result = await (smartWalletClient ? smartWalletClient.sendTransaction(tx) : sendTransaction(tx));
                return typeof result === 'string' ? { hash: result } : result;
              },
            );
          } else if (dexId.toLowerCase() === 'meteora') {
            console.log('Executing Meteora trade on Solana');
            tradeResult = await completeTradeMeteora(
              tokenIn,
              tokenOut,
              amount,
              recipient || selectedWallet?.address || '',
              async (tx) => {
                const result = await (smartWalletClient ? smartWalletClient.sendTransaction(tx) : sendTransaction(tx));
                return typeof result === 'string' ? { hash: result } : result;
              },
            );
          } else {
            // Default Solana swap if DEX not specifically handled
            console.log('Executing generic Solana swap');
            
            // Create a prepared swap object for Solana transaction
            const preparedSwap = {
              tokenIn,
              tokenOut,
              amountIn: amount,
              recipient: recipient || selectedWallet?.address || '',
              dex: dexId,
              transaction: {} // This would normally be populated with actual transaction data
            };
            
            tradeResult = await execSolanaSwap(
              preparedSwap,
              async (tx) => {
                const result = await (smartWalletClient ? smartWalletClient.sendTransaction(tx) : sendTransaction(tx));
                return typeof result === 'string' ? { hash: result } : result;
              },
              { chainId: 'solana' }
            );
          }
        } catch (error) {
          console.error(`Error executing Solana trade on ${dexId}:`, error);
          throw new Error(`Failed to execute trade on ${dexId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      } else {
        // For all EVM transactions, use regular transactions instead of bundled transactions
        setTransactionStatus(`Executing ${action} with regular transaction...`);
        
        if (!smartWalletClient) {
          throw new Error("Smart wallet is required for transactions. Please reconnect your wallet.");
        }
        
        // Double check wallet has sufficient funds before proceeding
        if (action === 'buy' && !hasSufficientFunds('buy')) {
          console.log(`Insufficient ${selectedCoin?.symbol} balance to complete purchase`);
          throw new Error(`Insufficient ${selectedCoin?.symbol} balance for this transaction`);
        } else if (action === 'sell' && !hasActualTokenBalance()) {
          console.log(`Insufficient ${activeToken?.symbol} balance to complete sale`);
          throw new Error(`Insufficient ${activeToken?.symbol} balance for this transaction`);
        }
        
        // Normalize platform fee
        const normalizedPlatformFee = platformFee && typeof platformFee === 'object' && 'recipient' in platformFee && 'amount' in platformFee && 'token' in platformFee && 'percentage' in platformFee
          ? {
              recipient: String(platformFee.recipient),
              amount: String(platformFee.amount),
              token: String(platformFee.token),
              percentage: typeof platformFee.percentage === 'number' 
                ? platformFee.percentage 
                : parseFloat(String(platformFee.percentage || '0'))
            }
          : undefined;
        
        // Log platform fee details before transaction
        console.log('PLATFORM FEE DEBUG: Normalized platform fee for transaction:', 
          normalizedPlatformFee ? JSON.stringify(normalizedPlatformFee) : 'No platform fee');
        
        try {
          // Set transaction status
          setTransactionStatus(`Executing regular transaction...`);

          // Execute regular PancakeSwap transaction
          const swapResponse = await completeTradePancakeswap(
            tokenIn,
            tokenOut,
            amount,
            recipient || selectedWallet?.address || '',
            async (tx) => {
              const result = await (smartWalletClient ? smartWalletClient.sendTransaction(tx) : sendTransaction(tx));
              return typeof result === 'string' ? { hash: result } : result;
            },
            { 
              slippageTolerance,
              platformFee: normalizedPlatformFee || null,
              useSmartWalletClient: !!smartWalletClient,
              smartWalletClient
            }
          );
          
          if (!swapResponse.success) {
            throw new Error(`Transaction failed: ${swapResponse.error}`);
          }
          
          // Display success message
          setIsLoading(false);
          if (swapResponse.data?.hash) {
            setTransactionStatus(`Transaction sent: ${swapResponse.data.hash.slice(0, 6)}...${swapResponse.data.hash.slice(-4)}`);
            
            // Store trade history in Supabase
            await storeTradeHistory({
              wallet_address: selectedWallet?.address || '',
              ticker: `${tokenIn.symbol}/${tokenOut.symbol}`,
              trade_type: action === 'buy' ? 'Buy' : 'Sell',
              qty: amount,
              value: parseFloat(amount) * parseFloat(expectedPrice || '0'),
              filled_price: expectedPrice || '0',
              tx_hash: swapResponse.data.hash,
              dex: selectedDex || bestDex || 'pancakeswap',
              token_in_symbol: tokenIn.symbol,
              token_out_symbol: tokenOut.symbol,
              token_in_address: tokenIn.address || tokenIn.tokenAddress || '',
              token_out_address: tokenOut.address || tokenOut.tokenAddress || '',
              amount_in: amount,
              amount_out: outputAmount,
              platform_fee_included: !!normalizedPlatformFee,
              bundled_transaction: false,
              timestamp: new Date().toISOString()
            });
            
            // Set txHash for success popup
            const txHash = swapResponse.data.hash;
            console.log("Transaction success! Hash:", txHash);
            console.log("Transaction data:", swapResponse.data);
            
            // Open the success popup with transaction details
            console.log("Setting success popup with:", {
              isOpen: true,
              txHash: txHash || '',
              tokenIn: tokenIn?.symbol,
              tokenOut: tokenOut?.symbol,
              amountIn: amount,
              amountOut: outputAmount
            });
            
            setSuccessPopup({
              isOpen: true,
              txHash: txHash || '',
              tokenIn: tokenIn,
              tokenOut: tokenOut,
              amountIn: amount,
              amountOut: outputAmount
            });
            
            // Force the popup to appear with multiple retry attempts
            const showPopupDirectly = () => {
              console.log("POPUP DEBUG: Forcing popup visibility directly");
              
              // Try to find existing popup element
              let popupElement = document.querySelector('.popup-success-container');
              
              // If it exists, force visibility
              if (popupElement) {
                console.log("POPUP DEBUG: Found existing popup, forcing visibility");
                (popupElement as HTMLElement).style.display = 'flex';
                (popupElement as HTMLElement).style.visibility = 'visible';
                (popupElement as HTMLElement).style.opacity = '1';
                (popupElement as HTMLElement).style.zIndex = '9999';
              } else {
                console.log("POPUP DEBUG: No popup found, retrying...");
                // Try setting state again as a fallback
                setSuccessPopup({
                  isOpen: true,
                  txHash: txHash || '',
                  tokenIn: tokenIn,
                  tokenOut: tokenOut,
                  amountIn: amount,
                  amountOut: outputAmount
                });
              }
            };
            
            // Try multiple times with increasing delays
            setTimeout(showPopupDirectly, 500);
            setTimeout(showPopupDirectly, 1000);
            setTimeout(showPopupDirectly, 2000);
          }
          
          // Set the trade result for further processing
          tradeResult = {
            success: true,
            data: swapResponse.data
          };
          
          return true;
        } catch (error: any) {
          console.error("Error executing transaction:", error);
          throw error;
        }
      }

      console.log('Trade result:', tradeResult);

      // Check if we need token allowance approval
      if (!tradeResult.success && tradeResult.needsAllowance) {
        // Don't show allowance-specific message, keep it general
        setStatusMessage(`Transaction failed. Please try again.`);
      } else if (tradeResult.success) {
        // Clear the input value
        setInputValue('');
        setOutputAmount('');
        
        // Get transaction data
        const txHash = tradeResult.data?.transactionHash || tradeResult.data?.transaction?.transactionHash || tradeResult.data?.hash;
        
            // Open the success popup with transaction details
            setSuccessPopup({
              isOpen: true,
              txHash: txHash || '',
              tokenIn: tokenIn,
              tokenOut: tokenOut,
              amountIn: amount,
              amountOut: outputAmount
            });
        
        // Still set status message for the main UI
        setStatusMessage(
          `Trade successful! ${txHash ? `Transaction: ${txHash.slice(0, 6)}...${txHash.slice(-4)}` : ''}`
        );
        
        // Store trade history in Supabase
        try {
          // Helper function to clean numeric values that might have token symbols
          const cleanNumericValue = (value: string | number): string => {
            if (typeof value === 'number') return value.toString();
            // Extract just the numeric part if there's a token symbol attached
            return value.toString().split(' ')[0];
          };
          
          await storeTradeHistory({
            wallet_address: selectedWallet.address ?? '',
            ticker: `${tokenIn.symbol}/${tokenOut.symbol}`,
            trade_type: action === 'buy' ? 'Buy' : 'Sell',
            qty: cleanNumericValue(amount),
            value: parseFloat(cleanNumericValue(amount)) * parseFloat(expectedPrice || '0'),
            filled_price: expectedPrice || '0',
            tx_hash: txHash || '',
            dex: selectedDex || bestDex || dexId || 'pancakeswap',
            token_in_symbol: tokenIn.symbol,
            token_out_symbol: tokenOut.symbol, 
            token_in_address: tokenIn.address || tokenIn.tokenAddress || '',
            token_out_address: tokenOut.address || tokenOut.tokenAddress || '',
            amount_in: cleanNumericValue(amount),
            amount_out: cleanNumericValue(outputAmount),
            platform_fee_included: Boolean(platformFee), // Record whether platform fee was included
            bundled_transaction: !!tradeResult.data?.bundledTransaction, // Record if bundled transaction was used
            timestamp: new Date().toISOString()
          });
          console.log('Trade history saved to Supabase');
          
          // Dispatch custom event to trigger history reload
          window.dispatchEvent(new Event(TRADE_COMPLETED_EVENT));

          // Add these lines after the above code:
          console.log('About to dispatch trade completed event');
          window.dispatchEvent(new Event(TRADE_COMPLETED_EVENT));
          console.log('Trade completed event dispatched, checking success popup state:', successPopup);
          
          // Force trigger success popup if it didn't open for some reason
          if (!successPopup.isOpen && txHash) {
            console.log('Success popup not open, forcing it to open with txHash:', txHash);
            setTimeout(() => {
              setSuccessPopup({
                isOpen: true,
                txHash: txHash || '',
                tokenIn: tokenIn,
                tokenOut: tokenOut,
                amountIn: amount,
                amountOut: outputAmount
              });
            }, 500);
          }
        } catch (error) {
          console.error('Failed to store trade history:', error);
          // Don't disrupt the user experience if storing fails
        }
        
        // After a successful trade, fetch updated token data
        setTimeout(() => {
          //fetchTokenData();
        }, 3000);
      } else {
        // Display the error message
        setStatusMessage(`Trade failed: ${tradeResult.error || tradeResult.message || "Unknown error"}`);
      }
    } catch (error: unknown) {
      console.error('Trade execution error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setStatusMessage(`Transaction error: ${errorMessage}`);
    } finally {
      setIsTransacting(false);
      setTransactionStatus("");
    }
  };

  // Improved function to check if user has sufficient funds
  const hasSufficientFunds = (action: 'buy' | 'sell'): boolean => {
    // Use the provided action parameter instead of tradeMode
    const effectiveAction = action;
    
    console.log(`Checking funds for ${effectiveAction} mode`);
    
    // If we're not logged in, we don't have enough funds
    if (!authenticated || !selectedCoin || !activeToken) {
      console.log('Not authenticated or missing tokens');
      return false;
    }
    
    // If the input value is not a valid number, we can't check
    if (!inputValue || isNaN(parseFloat(inputValue)) || parseFloat(inputValue) <= 0) {
      console.log('Invalid input value: ' + inputValue);
      return false;
    }
    
    // Log the current tokens and their balances
    console.log('Current token balances:');
    console.log(`Selected Coin: ${selectedCoin.symbol}, Balance: ${selectedCoin.balance}`);
    console.log(`Active Token: ${activeToken.symbol}, Balance: ${activeToken.balance}`);
    
    // For buying, we need enough selectedCoin (e.g., USDT)
    if (effectiveAction === 'buy') {
      // For buying, check if the user has enough of the selected coin balance
      if (!selectedCoin.balance) {
        console.log('Buy check: No selected coin balance available');
        return false;
      }
      
      try {
        const selectedCoinBalance = parseFloat(selectedCoin.balance);
        const needed = parseFloat(inputValue);
        const hasEnough = selectedCoinBalance >= needed;
        console.log(`Buy check: Balance ${selectedCoinBalance} ${selectedCoin.symbol}, need ${needed}, sufficient: ${hasEnough}`);
        return hasEnough;
      } catch (error) {
        console.error('Error checking buy funds:', error);
        return false;
      }
    }
    
    // For selling, we need enough activeToken (e.g., BTC, ETH)
    if (effectiveAction === 'sell') {
      // For selling, check if the user has enough of the active token balance
      if (!activeToken.balance) {
        console.log('Sell check: No active token balance available');
        return false;
      }
      
      try {
        const activeTokenBalance = parseFloat(activeToken.balance);
        const needed = parseFloat(inputValue);
        const hasEnough = activeTokenBalance >= needed;
        console.log(`Sell check: Balance ${activeTokenBalance} ${activeToken.symbol}, need ${needed}, sufficient: ${hasEnough}`);
        return hasEnough;
      } catch (error) {
        console.error('Error checking sell funds:', error);
        return false;
      }
    }
    
    console.log('No matching action found');
    return false;
  };

  // Function to check the actual token balance by accessing all available sources
  const hasActualTokenBalance = (): boolean => {
    if (!activeToken || !inputValue || isNaN(parseFloat(inputValue)) || parseFloat(inputValue) <= 0) {
      console.log(`Invalid input (${inputValue}) or missing activeToken (${activeToken?.symbol})`);
      return false;
    }
    
    const tokenAddress = (activeToken.address?.toLowerCase() || activeToken.tokenAddress?.toLowerCase() || "");
    if (!tokenAddress) {
      console.log("No token address available");
      return false;
    }
    
    const needed = parseFloat(inputValue);
    console.log(`Checking balance for ${activeToken.symbol} (${tokenAddress}), need: ${needed}`);
    
    // First check coins array from BalanceDropdown
    const coinMatch = coins.find(c => {
      const coinAddress = (c.address?.toLowerCase() || c.tokenAddress?.toLowerCase() || "");
      return coinAddress === tokenAddress || c.symbol === activeToken.symbol;
    });

    if (coinMatch && coinMatch.balance) {
      const balance = parseFloat(coinMatch.balance);
      console.log(`Found balance in coins array: ${balance} for token ${coinMatch.symbol}`);
      if (!isNaN(balance) && balance >= needed) {
          return true;
      }
    }

    // Then check realTimeBalances using token address
    const realTimeBalance = realTimeBalances[tokenAddress] || 
                           (activeToken.symbol ? realTimeBalances[activeToken.symbol.toUpperCase()] : undefined);
    if (realTimeBalance) {
      const balance = parseFloat(realTimeBalance);
      console.log(`Found realTimeBalance: ${balance}`);
      if (!isNaN(balance) && balance >= needed) {
        return true;
      }
    }

    // Finally check activeToken balance
    if (activeToken.balance) {
      const balance = parseFloat(activeToken.balance);
      console.log(`Found activeToken balance: ${balance}`);
      if (!isNaN(balance) && balance >= needed) {
        return true;
      }
    }

    console.log(`Insufficient balance found for ${activeToken.symbol}. Need: ${needed}`);
    return false;
  };

  // --- Button State Logic ---
  // Moved these definitions here, after hasSufficientFunds and hasActualTokenBalance
  const canBuy = useMemo(() => 
    hasSufficientFunds('buy') && 
    !isLoadingQuote && 
    inputValue && 
    parseFloat(inputValue) > 0 && 
    !!quoteData && 
    isTokenTradeable,
    [hasSufficientFunds, isLoadingQuote, inputValue, quoteData, isTokenTradeable, tradeMode]
  );

  const canSell = useMemo(() => 
    hasActualTokenBalance() && 
    !isLoadingQuote && 
    inputValue && 
    parseFloat(inputValue) > 0 && 
    !!quoteData &&
    isTokenTradeable,
    [hasActualTokenBalance, isLoadingQuote, inputValue, quoteData, isTokenTradeable, tradeMode]
  );

  const noLiquidityPool = useMemo(() => 
    !isLoadingQuote && 
    inputValue && 
    parseFloat(inputValue) > 0 && 
    (!quoteData || !quoteData.routerAddress) && // Check if quote data or router address is missing
    isTokenTradeable, // Only consider it no liquidity if the tokens *should* be tradeable
     [isLoadingQuote, inputValue, quoteData, isTokenTradeable]
  );
  // -------------------------

  // Buy Button Rendering (Reference restored variables)
  const renderBuyButton = () => {
    return (
      <div className="flex-1 flex flex-col">
        <button
            className={`py-4 px-3 rounded-lg font-bold text-center ${
              !isTokenTradeable
                ? "text-gray-500 border-2 border-gray-700 bg-gray-800 cursor-not-allowed opacity-70"
                : canBuy && !noLiquidityPool
                  ? "text-[#14FFA2] border-2 border-[#14FFA2] bg-[#214638] cursor-pointer hover:bg-[#1a3b2e] transition-colors"
                  : "text-gray-500 border-2 border-gray-700 bg-gray-800 cursor-not-allowed opacity-70"
            }`}
            onClick={() => {
              if (isTokenTradeable && canBuy && !noLiquidityPool) {
                handleTrade('buy');
              }
            }}
            disabled={!isTokenTradeable || !canBuy || noLiquidityPool || isTransacting || isLoadingQuote}
          >
            {isTransacting ? "PROCESSING..." :
             isLoadingQuote ? "LOADING..." :
             !isTokenTradeable ? "PAIR NOT TRADEABLE" :
             noLiquidityPool ? "NO LIQUIDITY" :
             !hasSufficientFunds('buy') && inputValue && parseFloat(inputValue) > 0 ? `INSUFFICIENT ${selectedCoin?.symbol || 'FUNDS'}` :
             "BUY"}
          </button>
      </div>
    );
  };

  // Sell Button Rendering (Reference restored variables)
  const renderSellButton = () => {
    return (
      <div className="flex-1 flex flex-col">
         <button
            className={`py-4 px-3 rounded-lg font-bold text-center ${
              !isTokenTradeable
                ? "text-gray-500 border-2 border-gray-700 bg-gray-800 cursor-not-allowed opacity-70"
                : canSell && !noLiquidityPool
                  ? "text-[#FF329B] border-2 border-[#FF329B] bg-[#311D27] cursor-pointer hover:bg-[#2a1822] transition-colors"
                  : "text-gray-500 border-2 border-gray-700 bg-gray-800 cursor-not-allowed opacity-70"
            }`}
            onClick={() => {
              if (isTokenTradeable && canSell && !noLiquidityPool) {
                handleTrade('sell');
              }
            }}
            disabled={!isTokenTradeable || !canSell || noLiquidityPool || isTransacting || isLoadingQuote}
          >
             {isTransacting ? "PROCESSING..." :
             isLoadingQuote ? "LOADING..." :
             !isTokenTradeable ? "PAIR NOT TRADEABLE" :
             noLiquidityPool ? "NO LIQUIDITY" :
             !hasActualTokenBalance() && inputValue && parseFloat(inputValue) > 0 ? `INSUFFICIENT ${activeToken?.symbol || 'FUNDS'}` :
             "SELL"}
          </button>
      </div>
    );
  };
  // -------------------------

  // Helper function to get supported pools message
  const getSupportedPoolsMessage = () => {
    // Get the list of supported DEXes for the current pair
    const supportedDEXes: string[] = [];
    
    if (dexQuotes && dexQuotes.length > 0) {
      dexQuotes.forEach((quote: any) => {
        if (quote.name && !supportedDEXes.includes(quote.name)) {
          supportedDEXes.push(quote.name);
        }
      });
    }
    
    if (supportedDEXes.length === 0) {
      return "None available";
    }
    
    return supportedDEXes.join(", ");
  };

  // Add fetchTokenData function
  const fetchTokenData = async () => {
    try {
      if (!selectedCoin || !activeToken) {
        console.log("Missing selectedCoin or activeToken, can't fetch token data");
        return;
      }
      console.log(`Selected Coin: ${selectedCoin.address}, Active Token:${activeToken.address} `);
      console.log(`Checking if ${activeToken.symbol} can be traded with ${selectedCoin.symbol}`);
      
      // Use the new getPairList function to check if the trading pair exists
      const pairlistResponse = await getPairList(selectedCoin, activeToken);
      
      // Process the pairlist response
      if (pairlistResponse.success && pairlistResponse.matchedPair) {
        // A matching pair was found, set token as tradeable
        console.log(`Found trading pair: ${pairlistResponse.matchedPair.baseToken.symbol}/${pairlistResponse.matchedPair.quoteToken.symbol} on ${pairlistResponse.matchedPair.dexId}`);
        setIsTokenTradeable(true);
        
        // Store available options in case user needs alternatives
        if (pairlistResponse.availableTokens && pairlistResponse.availableTokens.length > 0) {
          setQuoteData(prevData => {
            return {
              ...prevData,
              availableOptions: pairlistResponse.availableTokens
            } as QuoteData;
          });
        }
        
        setStatusMessage("");
      } else {
        // No matching pair found, token is not tradeable
        console.log(`No trading pair found for ${selectedCoin.symbol} and ${activeToken.symbol}`);
        setIsTokenTradeable(false);
        
        // Still provide available options for UI if any exist
        if (pairlistResponse.availableTokens && pairlistResponse.availableTokens.length > 0) {
          console.log(`Found ${pairlistResponse.availableTokens.length} alternative trading options`);
          setQuoteData({
            availableOptions: pairlistResponse.availableTokens
          } as QuoteData);
          
          // Suggest alternative tokens
          const altTokens = pairlistResponse.availableTokens.slice(0, 3).join(', ');
          setStatusMessage(`${selectedCoin.symbol} and ${activeToken.symbol} cannot be traded directly. Try: ${altTokens}`);
        } else {
          setStatusMessage(`${selectedCoin.symbol} and ${activeToken.symbol} cannot be traded directly.`);
        }
      }
    } catch (error) {
      console.error("Error checking pair availability:", error);
      setStatusMessage("Error checking pair availability");
      setIsTokenTradeable(false);
    }
  };

  // Improved fetchAvailableCoins function with better error handling
  const fetchAvailableCoins = async () => {
    if (isFetchingCoinsRef.current) {
      console.log("Already fetching coins, skipping");
      return;
    }
    
    isFetchingCoinsRef.current = true;
    setIsLoading(true);
    
    try {
      if (!authenticated) {
        console.log("Not authenticated, skipping fetch");
        setCoins([]);
        setIsLoading(false);
        return;
      }
      
      // Get wallet address from selected wallet
      const walletAddress = selectedWallet?.address;
      if (!walletAddress) {
        console.log("No wallet address available for fetching balances");
        setCoins([]);
        setIsLoading(false);
        return;
      }
      
      console.log(`Fetching balances for wallet: ${walletAddress.slice(0, 6)}...${walletAddress.slice(-4)}`);
      
      // Fetch balances from API
      const response = await walletAPI.getAllBalances(walletAddress);
      
      // Check for successful response
      if (!response.success) {
        console.error("Error fetching balances:", response.message);
        setStatusMessage(`Failed to load tokens: ${response.message || "Unknown error"}`);
        setCoins([]);
        setIsLoading(false);
        return;
      }
      
      console.log("Received balance data from API");
      
      // Process the tokens from response
      const processedCoins: TokenBase[] = [];
      
      if (response.data && response.data.balances) {
        // Process Ethereum tokens
        if (response.data.balances.ethereum && Array.isArray(response.data.balances.ethereum)) {
          response.data.balances.ethereum.forEach((token: any) => {
            if (token) {
              const processedToken = {
                name: token.name || token.symbol || "Unknown",
                symbol: token.symbol || "UNKNOWN",
                address: token.address || "",
                decimals: token.decimals || 18,
                balance: token.balance || "0",
                chain: token.chain || getChainName(token.chainId) || 'Ethereum',
                chainId: token.chainId || 1
              };
              processedCoins.push(processedToken);
              // Update realTimeBalances
              if (processedToken.symbol) {
                setRealTimeBalances(prev => ({
                  ...prev,
                  [processedToken.symbol.toUpperCase()]: processedToken.balance
                }));
              }
            }
          });
        }
        
        // Process BSC tokens
        if (response.data.balances.bsc && Array.isArray(response.data.balances.bsc)) {
          response.data.balances.bsc.forEach((token: any) => {
            if (token) {
              const processedToken = {
                name: token.name || token.symbol || "Unknown",
                symbol: token.symbol || "UNKNOWN",
                address: token.tokenAddress || token.address || "",
                decimals: token.decimals || 18,
                balance: token.balance || "0",
                chain: 'BSC',
                chainId: 56
              };
              processedCoins.push(processedToken);
              // Update realTimeBalances
              if (processedToken.symbol) {
                setRealTimeBalances(prev => ({
                  ...prev,
                  [processedToken.symbol.toUpperCase()]: processedToken.balance
                }));
              }
            }
          });
        }
        
        // Process Solana tokens
        if (response.data.balances.solana && Array.isArray(response.data.balances.solana)) {
          response.data.balances.solana.forEach((token: any) => {
            if (token) {
              const processedToken = {
                name: token.name || token.symbol || "Unknown",
                symbol: token.symbol || "UNKNOWN",
                address: token.address || token.mint || "",
                decimals: token.decimals || 9,
                balance: token.balance || "0",
                chain: 'Solana',
                chainId: 'solana'
              };
              processedCoins.push(processedToken);
              // Update realTimeBalances
              if (processedToken.symbol) {
                setRealTimeBalances(prev => ({
                  ...prev,
                  [processedToken.symbol.toUpperCase()]: processedToken.balance
                }));
              }
            }
          });
        }
      }
      
      console.log(`Found ${processedCoins.length} tokens with balances:`, 
        processedCoins.map(c => `${c.symbol}: ${c.balance}`));
      
      // Update the coins state
      setCoins(processedCoins);
      
      // Auto-select a coin if we have them but none is selected
      if (processedCoins.length > 0 && !selectedCoin) {
        console.log(`Auto-selecting first available coin: ${processedCoins[0].symbol}`);
        setSelectedCoin(processedCoins[0]);
      }
      
      setStatusMessage("");
    } catch (error) {
      console.error("Error fetching available coins:", error);
      setStatusMessage(`Failed to load tokens: ${error instanceof Error ? error.message : "Unknown error"}`);
      setCoins([]);
    } finally {
      setIsLoading(false);
      isFetchingCoinsRef.current = false;
    }
  };

  // Add formatPlatformFee function
  const formatPlatformFee = (): string => {
    if (!platformFee) {
      return "N/A";
    }
    
    return `${platformFee.amount} ${platformFee.token} (${(platformFee.percentage * 100).toFixed(1)}%)`;
  };

  // Add fixedHandleInputChange function - completely rewritten
  const fixedHandleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Strip non-numeric characters except decimal point
    let inputVal = e.target.value.replace(/[^0-9.]/g, '');
    
    // Prevent multiple decimal points
    const decimalCount = (inputVal.match(/\./g) || []).length;
    if (decimalCount > 1) {
      // Find the position of the first decimal point
      const firstDecimalPos = inputVal.indexOf('.');
      // Keep only up to that decimal point, then append the rest without decimal points
      inputVal = inputVal.substring(0, firstDecimalPos + 1) + 
                 inputVal.substring(firstDecimalPos + 1).replace(/\./g, '');
    }
    
    // Empty input case
    if (inputVal === '') {
      setInputValue('');
      setOutputAmount('');
      return;
    }
    
    // Decimal point only case
    if (inputVal === '.') {
      inputVal = '0.';
    }
    
    // Format number to have maximum 8 decimal places
    const parts = inputVal.split('.');
    if (parts.length > 1 && parts[1].length > 8) {
      inputVal = `${parts[0]}.${parts[1].substring(0, 8)}`;
    }
    
    // Set the input value
    setInputValue(inputVal);
    
    // Only fetch quote if all required data is available
    if (selectedCoin && activeToken && authenticated && inputVal && parseFloat(inputVal) > 0) {
      console.log("Inputs ready, attempting to fetch quote...");
      
      if (!isTokenTradeable) {
        console.log("Token not tradeable, skipping quote fetch");
        return;
      }
      
      // Clear any existing timeout
      if (quoteTimeout) {
        clearTimeout(quoteTimeout);
      }
      
      // Set a small debounce
      const timeout = setTimeout(() => {
        console.log("Debounce complete, fetching quote...");
        
        // Fetch the appropriate quote based on the current trade mode
        if (tradeMode === 'buy') {
          // For buying, we send in selectedCoin and receive activeToken
          fetchQuote({
            amount: inputVal,
            selectedCoin, // The token we're spending (input token)
            activeToken,  // The token we're receiving (output token)
            setIsLoadingQuote,
            setDexQuotes,
            setQuoteData,
            setPancakeswapQuoteData,
            setIsTokenTradeable,
            setOutputAmount,
            setExpectedPrice,
            setStatusMessage,
            isBuyAction: true // This is a buy action
          });
        } else {
          // For selling, we send in activeToken and receive selectedCoin
          fetchQuote({
            amount: inputVal,
            selectedCoin: activeToken, // Swap the tokens for sell
            activeToken: selectedCoin, // Swap the tokens for sell
            setIsLoadingQuote,
            setDexQuotes,
            setQuoteData,
            setPancakeswapQuoteData,
            setIsTokenTradeable,
            setOutputAmount,
            setExpectedPrice,
            setStatusMessage,
            isBuyAction: false // This is a sell action
          });
        }
      }, 300);
      
      setQuoteTimeout(timeout);
    } else {
      console.log("Missing required data for quote", { selectedCoin, activeToken, authenticated, inputVal });
      setOutputAmount('');
    }
  };

  // Add renderAvailableOptions function
  const renderAvailableOptions = () => {
    const availableOptions = quoteData && 'availableOptions' in quoteData 
      ? (quoteData.availableOptions as string[]) || []
      : [];
    
    if (!availableOptions || availableOptions.length === 0) {
      return undefined;
    }

    return (
      <div className="mt-2 p-3 bg-[#1D2226] rounded text-sm">
        <h4 className="text-white mb-2">Available Trading Options:</h4>
        <div className="flex flex-wrap gap-2">
          {availableOptions.slice(0, 8).map((token: string, index: number) => (
            <div 
              key={index} 
              className="px-2 py-1 bg-[#292d32] rounded-md text-blue-400 hover:bg-[#353a40] cursor-pointer"
              onClick={() => handleSelectAvailableToken(token)}
            >
              {token}
            </div>
          ))}
          {availableOptions.length > 8 && (
            <div className="text-gray-400 text-xs mt-1 ml-1">
              +{availableOptions.length - 8} more
            </div>
          )}
        </div>
      </div>
    );
  };

  // Add handleSelectAvailableToken function
  const handleSelectAvailableToken = (tokenSymbol: string) => {
    // Find the token in the coins list
    const token = coins.find(coin => 
      coin.symbol && coin.symbol.toUpperCase() === tokenSymbol.toUpperCase()
    );
    
    if (token) {
      console.log(`Selecting available token: ${tokenSymbol}`);
      setSelectedCoin(token);
    } else {
      console.log(`Token ${tokenSymbol} not found in available coins`);
      setStatusMessage(`${tokenSymbol} is available for trading, but not in your list. Please add it first.`);
    }
  };

  // Add state for success popup
  const [successPopup, setSuccessPopup] = useState<{
    isOpen: boolean;
    txHash: string;
    tokenIn: TokenBase | null | undefined;
    tokenOut: TokenBase | null | undefined;
    amountIn: string;
    amountOut: string;
  }>({
    isOpen: false,
    txHash: '',
    tokenIn: null,
    tokenOut: null,
    amountIn: '',
    amountOut: ''
  });

  // Update the useEffect that fetches token data
  useEffect(() => {
    // Add logging here to check token state before calling fetchTokenData
    console.log(`useEffect [selectedCoin, activeToken] triggered.`);
    console.log(`Current selectedCoin: ${selectedCoin?.symbol || 'null'}`);
    console.log(`Current activeToken: ${activeToken?.symbol || 'null'}`);

    if (selectedCoin && activeToken) {
      // Clear any previous quote data
      setQuoteResult(null);
      setOutputAmount("");
      setExpectedPrice("");
      
      // Check if this token pair is tradeable
      fetchTokenData();
    }
  }, [selectedCoin, activeToken]);

  // Add effect to sync balances when they change
  useEffect(() => {
    if (!activeToken) return;
    
    const symbol = activeToken.symbol;
    if (symbol) {
      console.log(`Syncing balance for ${symbol}: ${activeToken.balance}`);
      setRealTimeBalances(prev => ({
        ...prev,
        [symbol.toUpperCase()]: activeToken.balance || "0"
      }));
    }
  }, [activeToken?.balance, activeToken?.symbol]);

  useEffect(() => {
    if (!selectedCoin) return;
    
    const symbol = selectedCoin.symbol;
    if (symbol) {
      console.log(`Syncing balance for ${symbol}: ${selectedCoin.balance}`);
      setRealTimeBalances(prev => ({
        ...prev,
        [symbol.toUpperCase()]: selectedCoin.balance || "0"
      }));
    }
  }, [selectedCoin?.balance, selectedCoin?.symbol]);

  // Add CoinType interface at the top with other interfaces
  interface CoinType {
    name: string;
    symbol: string;
    chain: string;
    icon: string;
    balance: string;
    decimals: number;
    tokenAddress?: string;  // Make optional since it might not always be present
    address?: string;       // Add address field as it's used in some places
    walletAddress?: string; // Make optional since it might not always be needed
    chainId?: number;       // Add chainId field as it's used in some places
  }

  return (
    <div className="h-full w-full p-3 rounded-xl text-white flex flex-col overflow-y-auto">
      {/* Settings Modal */}
      <SettingsModal
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        slippageType={slippageType}
        setSlippageType={setSlippageType}
        slippageValue={slippageValue}
        setSlippageValue={setSlippageValue}
      />

      {/* Market & Limit Tabs */}
      <div className="flex w-full justify-between items-center mb-4">
      <div className="flex space-x-4">
    <button
      className={`font-bold ${activeTab === "Market" ? "text-white  border-b-2 border-white " : "text-[#BBBBBB]"}`}
      onClick={() => setActiveTab("Market")}
    >
      Market
    </button>
    <button
      className={`font-bold ${activeTab === "Limit" ? "text-white border-b-2 border-white" : "text-[#BBBBBB]"}`}
      onClick={() => setActiveTab("Limit")}
    >
      Limit
    </button>
  </div>

        {/* Auto & Settings Buttons */}
        <div className="flex space-x-2">
          <div className="flex items-center bg-[#141416] rounded-lg p-1.5">
            <button 
              className={`px-2 py-1.5 rounded-md font-medium text-xs ${isPriceMode ? "bg-[#14FFA2] text-black" : "text-gray-400"}`}
              onClick={() => setIsPriceMode(true)}
            >
              Price
          </button>
            <button 
              className={`px-2 py-1.5 rounded-md font-medium text-xs ${!isPriceMode ? "bg-[#14FFA2] text-black" : "text-gray-400"}`}
              onClick={() => setIsPriceMode(false)}
            >
              Slippage
            </button>
          </div>
          
          <button
            onClick={() => setIsSettingsOpen(true)}
            className="bg-[#151515] rounded-lg p-1.5"
          >
            <IoMdSettings className="text-[#BBBBBB] text-xl" />
          </button>
        </div>
      </div>

      {!authenticated && (
        <div className="flex flex-col gap-2 mb-4">
          <button
            onClick={handleConnectWallet}
            className="bg-gradient-to-r from-[#14FFA2] to-[#32E6A4] text-black font-bold py-3 px-4 rounded-lg hover:opacity-90 transition w-full"
          >
            Connect Wallet
          </button>
          
          {/* Add Phantom-specific connection button */}
          <button
            onClick={connectPhantomWallet}
            className="bg-gradient-to-r from-[#9945FF] to-[#8752F3] text-white font-bold py-3 px-4 rounded-lg hover:opacity-90 transition w-full flex items-center justify-center gap-2"
          >
            <img 
              src="https://phantom.app/img/logos/phantom-icon-purple.png" 
              alt="Phantom" 
              className="w-5 h-5" 
            />
            Connect with Phantom
          </button>
        </div>
      )}
      
      {statusMessage && (
        <div className={`mb-2 px-3 py-2 rounded-lg text-sm ${statusMessage.includes("Error") ? "bg-red-900/30 text-red-400" : "bg-blue-900/30 text-blue-400"}`}>
          {statusMessage}
        </div>
      )}
      
      {/* Add the WalletSelector component before BalanceDropdown */}
      {authenticated && (
        <CustomWalletSelector 
          onSelectWallet={handleWalletSelection}
          selectedWallet={selectedWallet}
          onConnectWallet={connectWallet}
        />
      )}
      
      {/* Balance Dropdown - only accessible when authenticated */}
      {authenticated && selectedWallet && (
        <BalanceDropdown 
          coins={coins.filter(token => token && token.symbol).map(token => {
            // Only use token.icon if explicitly provided, otherwise use empty string
            const iconUrl = token.icon || ''; 

            return {
              name: token.name || token.symbol || "Unknown",
              symbol: token.symbol || "UNKNOWN",
              chain: token.chain || getChainName(token.chainId) || "Unknown",
              icon: iconUrl, // Use explicit icon or empty string
              balance: token.balance || "0",
              decimals: token.decimals || 18,
              tokenAddress: token.address || token.tokenAddress || "",
              walletAddress: selectedWallet.address || ""
            };
          })}
          selectedCoin={selectedCoin ? {
            name: selectedCoin.name || selectedCoin.symbol || "Unknown Token",
            symbol: selectedCoin.symbol || "UNKNOWN",
            chain: selectedCoin.chain || "Unknown",
            // Apply same logic for selectedCoin
            icon: selectedCoin.icon || '', 
            balance: selectedCoin.balance || "0",
            decimals: selectedCoin.decimals || 18,
            tokenAddress: selectedCoin.address || selectedCoin.tokenAddress || "",
            walletAddress: selectedWallet.address || ""
          } : null}
          setSelectedCoin={(coin: CoinType | null) => {
            if (coin) {
              console.log("TradingPanel: Coin selected from BalanceDropdown:", coin);
              // Update realTimeBalances when a coin is selected
              const tokenAddr = coin.tokenAddress || coin.address || "";
              setRealTimeBalances((prev: Record<string, string>) => ({
                ...prev,
                [coin.symbol.toUpperCase()]: coin.balance,
                [tokenAddr.toLowerCase()]: coin.balance
              }));
              setSelectedCoin({
                name: coin.name,
                symbol: coin.symbol,
                chain: coin.chain,
                chainId: getChainIdFromName(coin.chain) || undefined,
                address: tokenAddr,
                tokenAddress: tokenAddr,
                balance: coin.balance,
                decimals: coin.decimals,
                icon: coin.icon
              });
            }
          }}
          activeChainId={activeChainId}
          selectedWallet={selectedWallet.address || null}
          onCoinsUpdate={handleCoinsUpdate}
        />
      )}

      {/* Buy/Sell Toggle */}
      <div className="mt-4 rounded-lg">
        {/* Buy/Sell Toggle */}
        <div className="flex justify-center mb-4">
          <div className="flex items-center bg-[#18191D] rounded-xl p-1 w-64">
            <button 
              className={`flex-1 py-2 px-4 rounded-lg text-center font-medium transition-all duration-200 ${
                tradeMode === 'buy' 
                  ? "bg-[#214638] text-[#14FFA2] shadow-md" 
                  : "text-gray-400 hover:text-white"
              }`}
              onClick={() => toggleTradeMode('buy')}
            >
              Buy
            </button>
            <button 
              className={`flex-1 py-2 px-4 rounded-lg text-center font-medium transition-all duration-200 ${
                tradeMode === 'sell' 
                  ? "bg-[#311D27] text-[#FF329B] shadow-md" 
                  : "text-gray-400 hover:text-white"
              }`}
              onClick={() => toggleTradeMode('sell')}
            >
              Sell
            </button>
          </div>
        </div>
        
        {/* Label */}
        <div className="flex gap-2 items-center text-gray-400 text-lg mb-1">
          <span>{isAmount ? "Amount" : "Qty"}</span>
          <button onClick={() => setIsAmount(!isAmount)}>
            <FaExchangeAlt className="text-gray-400 hover:text-white transition" />
          </button>
        </div>

        {/* Input Box with better output display */}
        <div className={`flex justify-between items-center p-3 rounded-lg ${!isTokenTradeable ? 'bg-[#101012] border border-red-900/30' : 'bg-[#141416]'}`}>
          <input
            type="text"
            value={inputValue}
            onChange={fixedHandleInputChange}
            // Add onFocus and onBlur events to force fetch on focus
            onFocus={() => console.log("Input focused")}
            onBlur={() => {
              console.log("Input blurred");
              // Only force a quote fetch when the user is authenticated 
              // and there's a value and we have a selected coin
              if (inputValue && parseFloat(inputValue) > 0 && selectedCoin && authenticated && !isLoadingQuote) {
                console.log("Conditions met for manual quote fetch");
                // The quote fetch will be handled automatically by the useEffect
              }
            }}
            placeholder={isTokenTradeable 
              ? `0 ${tradeMode === 'sell' ? activeToken?.symbol : selectedCoin?.symbol || 'Token'}` 
              : `${tradeMode === 'sell' ? activeToken?.symbol : selectedCoin?.symbol || 'Token'} not available for trading`}
            className={`bg-transparent outline-none w-full ${!isTokenTradeable ? 'text-gray-500 cursor-not-allowed opacity-50' : 'text-white'}`}
            disabled={Boolean(!authenticated || !selectedCoin || !isTokenTradeable || (selectedCoin && activeToken && selectedCoin.address && activeToken.address && selectedCoin.address.toLowerCase() === activeToken.address.toLowerCase()))}
          />
          <div className="text-right flex flex-col items-end">
            <div className="text-gray-400 text-sm">
              {authenticated ? formatOutputDisplay() : "Connect wallet"}
            </div>
            {selectedCoin && activeToken && authenticated && (
              <div className="text-xs text-green-400 mt-1">
                {tradeMode === 'sell' 
                  ? `${activeToken.symbol} → ${selectedCoin.symbol}`
                  : `${selectedCoin.symbol} → ${activeToken.symbol}`}
              </div>
            )}
          </div>
        </div>
      
      {/* Warning message when tokens aren't tradeable */}
      {!isTokenTradeable && selectedCoin && activeToken && (
        <div className="mt-2 px-3 py-2 bg-red-900/20 border border-red-900/30 rounded-lg text-xs text-red-400">
          <div className="flex items-center gap-2">
            <IoWarningOutline className="text-red-400 text-lg" />
            {selectedCoin.address && activeToken.address && 
             selectedCoin.address.toLowerCase() === activeToken.address.toLowerCase() ? (
              <span>
                <strong>Cannot trade a token with itself. Please select different tokens.</strong>
              </span>
             ) : (
              <span>
                <strong>{selectedCoin.symbol} to {activeToken.symbol}</strong> trading pair is not available.
              </span>
             )}
          </div>
        </div>
      )}
      
      {/* Show exchange rate if available */}
      {quoteData && (quoteData.executionPrice || quoteData.price) && authenticated && (
        <div className="mt-1 text-xs text-gray-400 flex justify-end">
          Exchange rate: 1 {quoteData.tokenIn?.symbol || selectedCoin?.symbol} = {
            quoteData.executionPrice 
              ? (typeof quoteData.executionPrice === 'string' 
                 ? quoteData.executionPrice
                 : quoteData.executionPrice.toString())
              : (quoteData.price 
                 ? (typeof quoteData.price === 'string'
                    ? quoteData.price
                    : quoteData.price.toString())
                 : 'N/A')
          } {quoteData.tokenOut?.symbol || activeToken?.symbol}
        </div>
      )}
    </div>

      {/* Slider - Only show for authenticated users with a selected coin */}
      {authenticated && selectedCoin && (
      <div className="mt-4 w-full">
      {/* Slider Label */}
      <div className="flex justify-between text-[#BBBBBB] Au-sm mb-2">
        <span>0%</span>
        <span>25%</span>
        <span>50%</span>
        <span>75%</span>
        <span>100%</span>
      </div>

      {/* Slider Container */}
      <div className="relative">
        {/* Percentage Indicator Above Slider */}
        <div className="absolute left-1/2 transform -translate-x-1/2 -top-6 text-white font-semibold">
        </div>

        {/* Styled Range Input */}
        <input
          type="range"
          min="0"
          max="100"
          value={range}
              onChange={(e) => {
                const rangeValue = e.target.value;
                setRange(rangeValue);
                
                // Calculate the percentage of the balance to use
                if (selectedCoin && selectedCoin.balance) {
                  try {
                    const balance = parseFloat(selectedCoin.balance);
                    if (!isNaN(balance) && balance > 0) {
                      const amountToUse = (balance * (parseInt(rangeValue) / 100)).toString();
                      setInputValue(amountToUse);
                    }
                  } catch (error) {
                    console.error("Error calculating balance percentage:", error);
                  }
                }
              }}
              disabled={!isTokenTradeable}
              className={`slider-thumb w-full h-2 rounded-lg appearance-none cursor-pointer transition-all bg-gradient-to-r from-gray-500 via-yellow-400 to-green-500 ${
                !isTokenTradeable ? 'opacity-50 cursor-not-allowed' : ''
              }`}
          style={{
            outline: "none",
            WebkitAppearance: "none",
          }}
        />

            {/* Custom Thumb Styling is now in TradingPanelStyles.css */}
      </div>
    </div>
      )}

      {/* Take Profit Toggle */}
  


{/* Render Target Price Only for Limit Tab */}
{activeTab === "Limit" && (
   <div className="p-2 rounded-lg">
   <div className="flex justify-between text-[#BBBBBB] text-sm mb-1">
    <div className="flex gap-2">

     <span>{isPriceMode ? "Target Price" : "Target MarketCap"}</span>
     <button onClick={toggleMode} className="text-gray-400 hover:text-white transition">
       <FaExchangeAlt />
     </button>
     </div>
     {/*Put percent here*/}
     <span className="text-green-400 underline">
       {isPriceMode ? "19.6% Lower" : "20% Lower"}
     </span>
   </div>
   <div className="flex justify-between bg-[#141416] p-2 rounded-lg text-white">
     <input
       type="text"
       value={isPriceMode ? price : marketCap}
       onChange={(e) =>
         isPriceMode ? setPrice(e.target.value) : setMarketCap(e.target.value)
       }
       className="bg-transparent outline-none w-full"
     />
     <span className="opacity-50">
       {isPriceMode ? `~${(parseFloat(price) * 1e6).toFixed(2)} MKTCAP` : `~ ${(parseFloat(marketCap) / 1e9).toFixed(3)} USD`}
     </span>
   </div>
 </div>
)}
     <div className="my-2 bg-[#1D2226] p-3 text-lg rounded-lg">
  {/* If data exists, show Take Profit & Stop Loss */}
  {takeProfit || stopLoss ? (
    <>
         <div className="flex items-center">
      <Switch
        checked={isTakeProfit}
        onChange={() => {
          if (isTakeProfit) {
            // Reset values when toggling off
            setTakeProfit('');
            setStopLoss('');
          }
          setIsTakeProfit(!isTakeProfit);
        }}
        className={`${isTakeProfit ? "bg-green-500" : "bg-[#BBBBBB]"} relative inline-flex h-6 w-11 items-center rounded-full mr-3`}
      >
        <span className="sr-only">Enable Take Profit</span>
        <span
          className={`${isTakeProfit ? "translate-x-6" : "translate-x-1"} inline-block h-4 w-4 transform bg-white rounded-full`}
        />
      </Switch>
      <span className="text-white">Take Profit • Stop Loss</span>
    </div>
    <div className="flex flex-col space-y-2 text-gray-400">
      {takeProfit && (
        <>
                
        <div className="flex justify-between">
          <span>Take Profit</span>
          <span className="text-white">
            0 SOL <span className="text-green-400">+{takeProfit}%</span>
          </span>
        </div>
        </>
      )}
      {stopLoss && (
        <>
       
        <div className="flex justify-between">
          <span>Stop Loss</span>
          <span className="text-white">
            0 SOL <span className="text-red-400">-{stopLoss}%</span>
          </span>
        </div>
        </>
      )}
    </div>
    </>
  ) : (
    // Show the switch if no data exists
    <div className="flex items-center">
      <Switch
        checked={isTakeProfit}
        onChange={handleToggleTakeProfit}
        className={`${isTakeProfit ? "bg-green-500" : "bg-[#BBBBBB]"} relative inline-flex h-6 w-11 items-center rounded-full mr-3`}
      >
        <span className="sr-only">Enable Take Profit</span>
        <span
          className={`${isTakeProfit ? "translate-x-6" : "translate-x-1"} inline-block h-4 w-4 transform bg-white rounded-full`}
        />
      </Switch>
      <span className="text-white">Take Profit • Stop Loss</span>
    </div>
  )}
</div>



     

      {/* All Quotes Comparison */}
      {showAllQuotes && dexQuotes && dexQuotes.length > 0 && (
        <div className="my-3 bg-gray-900/50 rounded-lg p-3 border border-gray-700 text-xs">
          <h4 className="text-white text-sm font-medium mb-2">Available Liquidity Pools</h4>
          <div className="space-y-2">
            {dexQuotes.map((quoteData, index) => (
              <div 
                key={index}
                className={`flex justify-between items-center p-2 rounded-md ${
                  quoteData.dexId === bestDex 
                    ? 'bg-green-900/30 border border-green-700' 
                    : 'bg-gray-800/50'
                }`}
              >
                <div className="flex items-center">
                  <div className={`w-2 h-2 rounded-full mr-2 ${
                    quoteData.dexId === bestDex ? 'bg-green-500' : 'bg-gray-500'
                  }`}></div>
                  <span className="font-medium capitalize">
                    {quoteData.dexId}
                    {quoteData.dexId === bestDex && <span className="text-green-400 ml-1">(Best)</span>}
                  </span>
                </div>
                <div className="text-right">
                  <div className="text-white">
                    {quoteData && quoteData.quote && 
                      (quoteData.quote.amountOut || quoteData.quote.outputAmount || "N/A")
                    }
                    {(!quoteData || !quoteData.quote) && "N/A"}
                  </div>
                  {quoteData && quoteData.quote && quoteData.quote.priceImpact && (
                    <div className={`text-xs ${
                      (() => {
                        const impact = quoteData.quote.priceImpact;
                        if (typeof impact === 'string') {
                          const numImpact = parseFloat(impact);
                          if (numImpact > 5) return 'text-red-500';
                          if (numImpact > 3) return 'text-yellow-500';
                          return 'text-gray-400';
                        } else if (typeof impact === 'number') {
                          if (impact > 5) return 'text-red-500';
                          if (impact > 3) return 'text-yellow-500';
                          return 'text-gray-400';
                        }
                        return 'text-gray-400';
                      })()
                    }`}>
                      Impact: {(() => {
                        const impact = quoteData.quote.priceImpact;
                        if (typeof impact === 'string') {
                          return parseFloat(impact).toFixed(2);
                        } else if (typeof impact === 'number') {
                          return impact.toFixed(2);
                        }
                        return '0.00';
                      })()}%
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Buy & Sell Buttons */}
      <div className="mt-6 flex flex-col space-y-4">
        {/* Show token not available message here, once for the whole section */}
        {!isTokenTradeable && (
          <div className="py-4 px-3 rounded-lg font-bold text-center text-gray-500 border-2 border-gray-700 bg-gray-800 cursor-not-allowed mb-2">
            TOKEN NOT AVAILABLE FOR TRADING
          </div>
        )}
        
        {/* Conditionally render either Buy or Sell button based on trade mode */}
        {tradeMode === 'buy' ? (
          <button
            className={`py-4 px-3 rounded-lg font-bold text-center ${
              !isTokenTradeable || !hasSufficientFunds('buy') || !inputValue || isNaN(parseFloat(inputValue)) || parseFloat(inputValue) <= 0 || isLoadingQuote
                ? "text-gray-500 border-2 border-gray-700 bg-gray-800 cursor-not-allowed opacity-70"
                : "text-[#14FFA2] border-2 border-[#14FFA2] bg-[#214638] cursor-pointer hover:bg-[#1a3b2e] transition-colors"
            }`}
            onClick={() => {
              if (isTokenTradeable && hasSufficientFunds('buy') && inputValue && parseFloat(inputValue) > 0 && !isLoadingQuote) {
                handleTrade('buy');
              }
            }}
            disabled={!isTokenTradeable || !hasSufficientFunds('buy') || !inputValue || isNaN(parseFloat(inputValue)) || parseFloat(inputValue) <= 0 || isLoadingQuote}
          >
            {isTransacting ? "PROCESSING..." :
             !isTokenTradeable ? "BUY" :
             isLoadingQuote ? "LOADING..." : 
             noLiquidityPool ? "NO LIQUIDITY POOL" : 
             !canBuy && inputValue && parseFloat(inputValue) > 0 ? `INSUFFICIENT ${selectedCoin?.symbol || 'FUNDS'}` : 
             "BUY"}
          </button>
        ) : (
          <button
            className={`py-4 px-3 rounded-lg font-bold text-center ${
              !isTokenTradeable || !inputValue || isNaN(parseFloat(inputValue)) || parseFloat(inputValue) <= 0 || isLoadingQuote
                ? "text-gray-500 border-2 border-gray-700 bg-gray-800 cursor-not-allowed opacity-70"
                : hasActualTokenBalance()
                  ? "text-[#FF329B] border-2 border-[#FF329B] bg-[#311D27] cursor-pointer hover:bg-[#2a1822] transition-colors"
                  : "text-gray-500 border-2 border-gray-700 bg-gray-800 cursor-not-allowed opacity-70"
            }`}
            onClick={() => {
              if (isTokenTradeable && hasActualTokenBalance() && inputValue && parseFloat(inputValue) > 0 && !isLoadingQuote) {
                handleTrade('sell');
              }
            }}
            disabled={!isTokenTradeable || !hasActualTokenBalance() || !inputValue || isNaN(parseFloat(inputValue)) || parseFloat(inputValue) <= 0 || isLoadingQuote}
          >
            {isTransacting ? "PROCESSING..." :
             !isTokenTradeable ? "SELL" :
             isLoadingQuote ? "LOADING..." : 
             noLiquidityPool ? "NO LIQUIDITY POOL" : 
             !canSell && inputValue && parseFloat(inputValue) > 0 ? `INSUFFICIENT ${activeToken?.symbol || 'FUNDS'}` : 
             "SELL"}
          </button>
        )}
      </div>
            {/* Render TPSL Modal if open */}
            {isModalOpen && <Tpslmodal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} onSubmit={handleTpslSubmit} />}

            {/* Display status messages */}
            {statusMessage && (
              <div className="my-2 p-2 bg-[#292d32] text-white text-sm rounded">
                {statusMessage}
              </div>
            )}
            
            {isTransacting && transactionStatus && (
              <div className="my-2 p-2 bg-[#1D2226] text-[#14FFA2] text-sm rounded flex items-center justify-center">
                <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-[#14FFA2] rounded-full"></div>
                {transactionStatus}
              </div>
            )}
          
            {/* Render quote details if available */}
            {renderQuoteDetails()}
            
            {/* Display available options when token is not available */}
            <div className="mt-4 mb-2">
              {renderAvailableOptions()}
            </div>
            
            {/* Trade Success Popup */}
            <TradeSuccessPopup
              isOpen={successPopup.isOpen}
              onClose={() => setSuccessPopup(prev => ({ ...prev, isOpen: false }))}
              txHash={successPopup.txHash}
              tokenIn={successPopup.tokenIn}
              tokenOut={successPopup.tokenOut}
              amountIn={successPopup.amountIn}
              amountOut={successPopup.amountOut}
            />
          </div>
        );
};

export default TradingPanel;