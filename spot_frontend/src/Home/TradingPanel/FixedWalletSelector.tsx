import React, { useState, useMemo } from 'react';
import { useWallets, ConnectedWallet } from '@privy-io/react-auth';

type WalletSelectorProps = {
  onWalletSelect: (wallet: ConnectedWallet) => void;
  selectedWallet?: ConnectedWallet | null;
};

export const WalletSelector = ({ onWalletSelect, selectedWallet }: WalletSelectorProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const { wallets } = useWallets();
  
  const connectedWallets = useMemo(() => {
    return wallets.filter(wallet => wallet.address && wallet.connectedAt);
  }, [wallets]);

  const toggleSelector = () => setIsOpen(!isOpen);
  
  const formatAddress = (address?: string) => {
    if (!address) return 'No Address';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  return (
    <div className="wallet-selector">
      <div className="selected-wallet" onClick={toggleSelector}>
        {selectedWallet && selectedWallet.address ? (
          <div>
            <span>{formatAddress(selectedWallet.address)}</span>
            <span className="dropdown-arrow">▼</span>
          </div>
        ) : (
          <div>
            <span>Select Wallet</span>
            <span className="dropdown-arrow">▼</span>
          </div>
        )}
      </div>
      
      {isOpen && connectedWallets.length > 0 && (
        <div className="wallet-dropdown">
          {connectedWallets.map((wallet) => (
            wallet.address && (
              <div 
                key={wallet.address}
                className={`wallet-option ${selectedWallet?.address === wallet.address ? 'selected' : ''}`}
                onClick={() => {
                  onWalletSelect(wallet);
                  setIsOpen(false);
                }}
              >
                {formatAddress(wallet.address)}
              </div>
            )
          ))}
        </div>
      )}
    </div>
  );
}; 