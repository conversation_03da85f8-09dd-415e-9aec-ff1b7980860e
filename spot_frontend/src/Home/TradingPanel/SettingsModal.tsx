import React, { useState } from "react";
import { IoMdSettings } from "react-icons/io";

const SettingsModal = ({ isOpen, onClose, slippageType, setSlippageType, slippageValue, setSlippageValue }) => {
  const [selectedTradeSize, setSelectedTradeSize] = useState<null>(null);
  const [gasPreset, setGasPreset] = useState<string>("Quick");
  const [customGas, setCustomGas] = useState<string>("");
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex justify-center items-center bg-black/80  z-50">
      <div className="bg-[#181C20] border border-gray-600 max-w-lg w-full p-6 rounded-xl shadow-2xl relative">
        {/* Close Button */}
        <button onClick={onClose} className="absolute top-4 right-4 text-gray-400 hover:text-white text-2xl">
          ✖
        </button>

        {/* Modal Title */}
        <div className="flex items-center space-x-3 text-white text-2xl font-bold mb-6">
        <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19.3462 7.3503L18.9802 7.1463L18.8672 7.08229C18.5946 6.91868 18.3649 6.69242 18.1972 6.4223C18.1792 6.3953 18.1632 6.3663 18.1312 6.3103C17.9156 5.96422 17.8109 5.56053 17.8312 5.1533L17.8372 4.7283C17.8492 4.0483 17.8552 3.7063 17.7592 3.4003C17.6742 3.12804 17.5321 2.87709 17.3422 2.6643C17.1282 2.4243 16.8312 2.2523 16.2362 1.9103L15.7422 1.6253C15.1502 1.2843 14.8532 1.1133 14.5382 1.0483C14.2599 0.990707 13.9725 0.993093 13.6952 1.0553C13.3822 1.1253 13.0892 1.3013 12.5042 1.6513L12.5012 1.6533L12.1472 1.8643C12.0912 1.8983 12.0622 1.9143 12.0342 1.9303C11.7562 2.0853 11.4462 2.1703 11.1272 2.1803C11.0952 2.1823 11.0622 2.1823 10.9972 2.1823L10.8672 2.1813C10.5481 2.17113 10.236 2.08472 9.95717 1.9293C9.92917 1.9143 9.90217 1.8973 9.84617 1.8633L9.48917 1.6493C8.90017 1.2953 8.60517 1.1193 8.29017 1.0483C8.01183 0.985991 7.72337 0.983946 7.44417 1.0423C7.12817 1.1083 6.83217 1.2803 6.23917 1.6243L6.23617 1.6253L5.74817 1.9083L5.74317 1.9123C5.15517 2.2523 4.86017 2.4243 4.64817 2.6633C4.45925 2.87569 4.31777 3.12592 4.23317 3.3973C4.13817 3.7043 4.14317 4.0463 4.15517 4.7303L4.16217 5.1543C4.16217 5.2193 4.16517 5.2513 4.16417 5.2823C4.159 5.64562 4.05494 6.00066 3.86317 6.3093C3.83017 6.3653 3.81517 6.3933 3.79817 6.4193C3.62956 6.69147 3.39808 6.91918 3.12317 7.0833L3.01117 7.1463L2.65017 7.34629C2.04817 7.67929 1.74717 7.84629 1.52917 8.08429C1.33535 8.29413 1.18873 8.54305 1.09917 8.8143C0.999174 9.1213 0.999174 9.4643 1.00017 10.1523L1.00217 10.7153C1.00317 11.3983 1.00517 11.7393 1.10617 12.0443C1.19536 12.3139 1.34092 12.5613 1.53317 12.7703C1.75117 13.0063 2.04917 13.1723 2.64617 13.5043L3.00417 13.7033C3.06517 13.7373 3.09617 13.7533 3.12517 13.7713C3.43855 13.9591 3.69448 14.2293 3.86517 14.5523L3.93217 14.6723C4.10109 14.9912 4.18056 15.3499 4.16217 15.7103L4.15517 16.1173C4.14317 16.8033 4.13817 17.1473 4.23417 17.4543C4.31917 17.7263 4.46117 17.9773 4.65117 18.1903C4.86517 18.4303 5.16317 18.6013 5.75717 18.9443L6.25117 19.2293C6.84417 19.5703 7.14017 19.7413 7.45517 19.8063C7.73348 19.8639 8.02086 19.8615 8.29817 19.7993C8.61217 19.7293 8.90517 19.5533 9.49217 19.2013L9.84617 18.9893L9.95917 18.9233C10.2372 18.7693 10.5472 18.6833 10.8662 18.6733L10.9962 18.6723H11.1262C11.4442 18.6823 11.7562 18.7693 12.0362 18.9243L12.1282 18.9793L12.5042 19.2053C13.0942 19.5593 13.3882 19.7353 13.7032 19.8053C13.9814 19.8683 14.2698 19.871 14.5492 19.8133C14.8642 19.7473 15.1622 19.5743 15.7552 19.2303L16.2502 18.9433C16.8382 18.6013 17.1332 18.4303 17.3452 18.1913C17.5352 17.9783 17.6752 17.7283 17.7602 17.4573C17.8552 17.1523 17.8502 16.8133 17.8382 16.1393L17.8302 15.6993V15.5723C17.8349 15.2087 17.9386 14.8533 18.1302 14.5443L18.1952 14.4343C18.3638 14.1621 18.5953 13.9344 18.8702 13.7703L18.9802 13.7093L18.9822 13.7083L19.3432 13.5083C19.9452 13.1743 20.2462 13.0083 20.4652 12.7703C20.6592 12.5603 20.8052 12.3103 20.8942 12.0403C20.9942 11.7353 20.9942 11.3933 20.9922 10.7133L20.9902 10.1393C20.9892 9.4563 20.9882 9.1143 20.8872 8.8093C20.7976 8.54 20.6517 8.29286 20.4592 8.08429C20.2422 7.84829 19.9442 7.68229 19.3482 7.35129L19.3462 7.3503Z" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M6.99609 10.4273C6.99609 11.4882 7.41752 12.5056 8.16767 13.2557C8.91781 14.0059 9.93523 14.4273 10.9961 14.4273C12.057 14.4273 13.0744 14.0059 13.8245 13.2557C14.5747 12.5056 14.9961 11.4882 14.9961 10.4273C14.9961 9.36644 14.5747 8.34903 13.8245 7.59888C13.0744 6.84873 12.057 6.42731 10.9961 6.42731C9.93523 6.42731 8.91781 6.84873 8.16767 7.59888C7.41752 8.34903 6.99609 9.36644 6.99609 10.4273Z" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

          <h2>Settings</h2>
        </div>

        {/* Slippage Section */}
        <p className="text-gray-400 text-lg">Slippage</p>
        <div className="flex items-center mt-3 space-x-4 ">
          {/* Auto / Fixed Toggle */}
          <div className="bg-[#23262F] flex rounded-full overflow-hidden ">
            <button
              className={`px-8 py-3 text-lg ${slippageType === "Auto" ? "bg-white text-[#323639]" : "text-white bg-[#323639]"}`}
              onClick={() => setSlippageType("Auto")}
            >
              Auto
            </button>
            <button
              className={`px-8 py-3 text-lg ${slippageType === "Fixed" ? "bg-white text-[#323639]" : "text-white bg-[#323639]"}`}
              onClick={() => setSlippageType("Fixed")}
            >
              Fixed
            </button>
          </div>

          {/* Percentage Input (for Fixed) */}
          <div className="flex items-center">
            <input
              type="text"
              placeholder="5%"
              className="bg-[#323639] text-white text-xl w-24 text-center p-3 rounded-full"
              value={slippageValue}
              onChange={(e) => {
                let value = e.target.value.replace(/[^0-9.]/g, ""); // Allow only numbers & decimals
                if (value === "") {
                  setSlippageValue(""); // Allow empty input
                } else {
                  let num = parseFloat(value);
                  if (num < 1) num = 1;
                  if (num > 100) num = 100;
                  setSlippageValue(num);
                }
              }}
            
              min="1"
              max="100"
              disabled={slippageType === "Auto"}
            />
            <span className="text-white text-xl ml-2">%</span>
          </div>
        </div>

        {/* Custom Trade Sizes */}
        <p className="text-gray-400 text-lg mt-8">Custom Trade Sizes</p>
        <div className="flex gap-6 mt-3">
          {[10, 50, 100].map((size) => (
            <button
              key={size}
              className={`px-8 py-3 text-lg rounded-full ${
                selectedTradeSize === size ? "bg-white text-[#323639]" : "text-white bg-[#323639]"
              }`}
              onClick={() => setSelectedTradeSize(size)}
            >
              ${size}
            </button>
          ))}
        </div>
        <div className="mt-4">
      {/* Heading */}
      <p className="text-gray-400 text-lg mb-2">Gas Preset</p>

      {/* Preset Options */}
      <div className="flex bg-[#1D2226] rounded-lg p-1 w-fit">
        {["Basic", "Quick", "Priority", "Custom"].map((preset) => (
          <button
            key={preset}
            className={`px-4 py-2 rounded-md  text-lg ${
              gasPreset === preset ? "bg-white text-[#1D2226]" : "text-white"
            }`}
            onClick={() => setGasPreset(preset)}
          >
            {preset}
          </button>
        ))}
      </div>
    
      {gasPreset === "Custom" && (
        <input
          type="number"
          value={customGas}
          onChange={(e) => setCustomGas(e.target.value)}
          placeholder="Enter custom gas fee"
          className="bg-[#1D2226] text-white text-lg w-full p-3 mt-3 rounded-lg outline-none"
        />
      )}
    </div>      
        {/* Confirm Button */}
        <button
          className="bg-white text-[#181C20] w-full py-4 mt-10 rounded-2xl text-xl font-bold"
          onClick={onClose}
        >
          Confirm
        </button>
      </div>
    </div>
  );
};

export default SettingsModal;
