import React, { useState } from "react";
import { Dialog, Switch } from "@headlessui/react";
import { FaExchangeAlt } from "react-icons/fa";

interface TpslModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: { takeProfit: number; stopLoss: number }) => void;
}

const Tpslmodal = ({ isOpen, onClose, onSubmit }: TpslModalProps) => {
  const [isTakeProfit, setIsTakeProfit] = useState<boolean>(false);
  const [isStopLoss, setIsStopLoss] = useState<boolean>(false);
  const [takeProfitByPrice, setTakeProfitByPrice] = useState<boolean>(false);
  const [stopLossByPrice, setStopLossByPrice] = useState<boolean>(false);
  const [takeProfitValue, setTakeProfitValue] = useState<string>("");
  const [stopLossValue, setStopLossValue] = useState<string>("");

  // Toggle Modes
  const toggleTakeProfitMode = () => setTakeProfitByPrice(!takeProfitByPrice);
  const toggleStopLossMode = () => setStopLossByPrice(!stopLossByPrice);

  // Handle "Add" Button Click
  const handleAdd = () => {
    // Convert string values to numbers before submitting
    const takeProfitNumber = isTakeProfit && takeProfitValue ? 
      parseFloat(takeProfitValue) || 0 : 0;
    
    const stopLossNumber = isStopLoss && stopLossValue ? 
      parseFloat(stopLossValue) || 0 : 0;
    
    onSubmit({
      takeProfit: isTakeProfit ? takeProfitNumber : 0,
      stopLoss: isStopLoss ? stopLossNumber : 0,
    });
    onClose();
  };

  // Disable Add Button if Both are OFF
  const isAddDisabled = !isTakeProfit && !isStopLoss;

  return (
    <Dialog open={isOpen} onClose={onClose} className="fixed inset-0 flex items-center justify-center z-50 bg-[#181C20] bg-opacity-50">
      <div className="bg-[#181C20] border border-gray-600 p-5 rounded-lg w-[400px] shadow-lg">
        {/* Header */}
        <div className="flex justify-between text-white/50 text-2xl mb-4">
          <span>Add Take Profit • Stop Loss</span>
          <button onClick={onClose} className="text-gray-400 hover:text-white">✕</button>
        </div>

        {/* Read-Only Input Box */}
        <div className="mb-4">
          <input type="text" placeholder="Enter value..." readOnly className="w-full bg-[#1D2226] text-gray-300 px-3 py-9 rounded-lg outline-none" />
        </div>

        {/* Take Profit */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <Switch checked={isTakeProfit} onChange={setIsTakeProfit} className={`${isTakeProfit ? "bg-green-500" : "bg-gray-600"} relative inline-flex h-6 w-11 items-center rounded-full`}>
              <span className="sr-only">Enable Take Profit</span>
              <span className={`${isTakeProfit ? "translate-x-6" : "translate-x-1"} inline-block h-4 w-4 transform bg-white rounded-full`} />
            </Switch>
            <span className="text-white ml-3">{takeProfitByPrice ? "Take Profit By Price" : "Take Profit By P&L"}</span>
          </div>
          <button onClick={toggleTakeProfitMode} className="text-gray-400 hover:text-white">
            <FaExchangeAlt />
          </button>
        </div>

        {/* Take Profit Input */}
        {isTakeProfit && (
          <div className="mb-4">
            <div className="border border-gray-600 p-2 rounded-lg flex items-center justify-between">
              <input type="text" value={takeProfitValue} onChange={(e) => setTakeProfitValue(e.target.value)} placeholder={takeProfitByPrice ? "0.4537 USD" : "20%"} className="bg-transparent text-green-400 text-lg outline-none flex-1" />
              <span className="text-gray-400 text-lg whitespace-nowrap">{takeProfitByPrice ? "MktCap" : "0 SOL"}</span>
            </div>
            <p className="text-gray-400 my-2 text-sm">
              {takeProfitByPrice? <> <span>Target P&L:0 SOL •</span><span className="text-green-500">+2%</span></> : <><span>Target Price:<span className="text-white">0.001748</span> •</span> <span className="text-white">1.9M</span> MktCap</>}
            </p>
          </div>
        )}

        {/* Stop Loss */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <Switch checked={isStopLoss} onChange={setIsStopLoss} className={`${isStopLoss ? "bg-red-500" : "bg-gray-600"} relative inline-flex h-6 w-11 items-center rounded-full`}>
              <span className="sr-only">Enable Stop Loss</span>
              <span className={`${isStopLoss ? "translate-x-6" : "translate-x-1"} inline-block h-4 w-4 transform bg-white rounded-full`} />
            </Switch>
            <span className="text-white ml-3">{stopLossByPrice ? "Stop Loss By Price" : "Stop Loss By P&L"}</span>
          </div>
          <button onClick={toggleStopLossMode} className="text-gray-400 hover:text-white">
            <FaExchangeAlt />
          </button>
        </div>

        {/* Stop Loss Input */}
        {isStopLoss && (
          <div className="mb-4">
            <div className="border border-gray-600 p-2 rounded-lg flex items-center justify-between">
              <input type="text" value={stopLossValue} onChange={(e) => setStopLossValue(e.target.value)} placeholder={stopLossByPrice ? "0.4537 USD" : "20%"} className="bg-transparent text-red-400 text-lg outline-none flex-1" />
              <span className="text-gray-400 text-lg">{stopLossByPrice ? "MktCap" : "0 SOL"}</span>
            </div>
            <p className="text-gray-400 my-2 text-sm">
              {stopLossByPrice ?<> <span>Target P&L:0 SOL •</span><span className="text-red-500">+2%</span></> : <><span>Target Price:<span className="text-white">0.001748</span> •</span> <span className="text-white">1.9M</span> MktCap</>}
            </p>
          </div>
        )}

        {/* Add Button (Disabled when both switches are OFF) */}
        <button
          className={`w-full py-3 mt-2 rounded-full text-[#181C20] transition ${
            "bg-[#BBBBBB] "
          }`}
          onClick={handleAdd}
          disabled={isAddDisabled}
        >
          Add
        </button>
      </div>
    </Dialog>
  );
};

export default Tpslmodal;
