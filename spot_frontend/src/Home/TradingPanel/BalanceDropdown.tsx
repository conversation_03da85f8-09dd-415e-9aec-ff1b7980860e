import React, { useState, useEffect, useCallback, memo, useMemo, useRef } from "react";
import { ChevronUpIcon, ChevronDownIcon } from "lucide-react";
import { CheckIcon, RefreshCwIcon } from "lucide-react";
import { usePrivy, useWallets, LinkedAccountWithMetadata, ConnectedWallet } from "@privy-io/react-auth";
import { walletAPI } from "../../utils/api"; // Import our wallet API utility
import "react-loading-skeleton/dist/skeleton.css";
import { Switch } from "@headlessui/react";

// Define interfaces for our component
interface CoinType {
  name: string;
  symbol: string;
  chain: string;
  icon: string;
  balance: string;
  decimals: number;
  tokenAddress?: string;
  walletAddress?: string;
  walletType?: string;
}

interface WalletAddressType {
  address: string;
  type: string;
  chainName: string;
  walletType: string;
}

interface BalanceDropdownProps {
  coins: CoinType[];
  selectedCoin: CoinType | null;
  setSelectedCoin: (coin: CoinType) => void;
  activeChainId: number | string | null;
  selectedWallet: string | null;
  onCoinsUpdate?: (coins: CoinType[]) => void;
}

// Using our new API endpoint instead of directly using axios
const BalanceDropdown = ({ coins, selectedCoin, setSelectedCoin, activeChainId, selectedWallet, onCoinsUpdate }: BalanceDropdownProps) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [tokenBalances, setTokenBalances] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showAllChains, setShowAllChains] = useState<boolean>(false);
  const [allCoins, setAllCoins] = useState<CoinType[]>([]);
  const [dataLoaded, setDataLoaded] = useState<boolean>(false); // Track if data has been loaded
  const [autoFetch, setAutoFetch] = useState<boolean>(true); // Default to auto-fetch when wallet changes
  const [walletAddresses, setWalletAddresses] = useState<WalletAddressType[]>([]); // Initialize walletAddresses
  const [selectedWalletAddress, setSelectedWalletAddress] = useState<string | null>(null);
  
  // Add refs at the top level of the component
  const isFirstRunRef = useRef(true);
  const prevWalletIdsRef = useRef('');
  const isFetchingRef = useRef(false); // Track if fetch is in progress
  const prevSelectedWalletRef = useRef<string | null>(null); // Track previous selected wallet
  const fetchTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null); // For debouncing fetches
  // Add new refs for rate limiting
  const lastPhantomConnectAttemptRef = useRef(0);
  const phantomConnectionInProgressRef = useRef(false);
  
  // Add a new ref to track wallet initialization
  const walletInitializedRef = useRef(false);
  
  const { authenticated, connectWallet, logout, user } = usePrivy();
  const { wallets } = useWallets();
  
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // Helper function to format address
  const formatAddress = (address?: string): string => {
    if (!address || typeof address !== 'string') return 'No Address';
    return address.length > 10 ? `${address.slice(0, 6)}...${address.slice(-4)}` : address;
  };
  
  // Log props for debugging
  useEffect(() => {
    console.log("BalanceDropdown props:", { 
      coins: coins.length,
      selectedCoin: selectedCoin?.symbol,
      selectedWallet: selectedWallet ? formatAddress(selectedWallet) : 'none',
      activeChainId
    });
  }, [coins, selectedCoin, selectedWallet, activeChainId]);
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Get a more reliable wallet type
  const getActualWalletType = (wallet: ConnectedWallet | any): string => {
    if (!wallet) return "unknown";
    
    // First, check if this is a Solana wallet by examining the address format
    const isSolanaAddress = wallet.address && !wallet.address.startsWith('0x');
    
    // Check for Phantom wallet specifically
    const isPhantomWallet = 
      wallet.walletClientType === "phantom" ||
      (isSolanaAddress && window.phantom && window.phantom.solana) ||
      (wallet.connector && wallet.connector.name === "phantom");
    
    if (isPhantomWallet) {
      return "phantom";
    }
    
    // Check for MetaMask wallet
    const isMetaMask = 
      (wallet.walletClientType === "injected" && window.ethereum?.isMetaMask) ||
      (wallet.walletClientType === "metamask") ||
      (wallet.connector && wallet.connector.name === "metamask");
    
    if (isMetaMask) {
      return "metamask";
    }
    
    // For Privy embedded wallets
    if (wallet.embedded || wallet.walletClientType === "embedded") {
      return "embedded";
    }
    
    // For other wallets, use their client type
    return wallet.walletClientType || "external";
  };
  
  // Helper function similar to the one in Navbar.jsx to get wallet addresses
  const getWalletAddresses = useCallback((): WalletAddressType[] => {
    // Track addresses for deduplication
    const addresses: WalletAddressType[] = [];
    const seenAddresses = new Set<string>();
    
    console.log("Getting wallet addresses from all sources...");
    
    // Helper to add an address to our results
    const addUniqueAddress = (address: string, type: string, chainName: string, walletType: string): boolean => {
      if (!address || typeof address !== 'string') return false;
      
      // Don't process email addresses
      if (address.includes('@')) {
        console.log(`Skipping email-like address: ${address}`);
        return false;
      }
      
      // Use different normalization for Solana addresses
      const isSolanaAddress = !address.startsWith('0x');
      const normalizedAddress = isSolanaAddress ? address : address.toLowerCase();
      
      // Skip if we've already added this address
      if (seenAddresses.has(normalizedAddress)) {
        console.log(`Skipping duplicate address: ${address.slice(0, 6)}...${address.slice(-4)}`);
        return false;
      }
      
      console.log(`Adding unique address: ${address.slice(0, 6)}...${address.slice(-4)} (${chainName})`);
      seenAddresses.add(normalizedAddress);
      
      addresses.push({
        address: address,
        type: type,
        chainName: chainName,
        walletType: walletType
      });
      
      return true;
    };
    
    // First check Privy user object for embedded wallet
    if (authenticated && user?.wallet?.address) {
      console.log("Found embedded wallet in user object:", user.wallet.address.slice(0, 6) + "..." + user.wallet.address.slice(-4));
      addUniqueAddress(user.wallet.address, 'embedded', 'Ethereum', 'embedded');
    }
    
    // Check specifically for Phantom Solana wallet in user accounts
    if (authenticated && user?.linkedAccounts && user.linkedAccounts.length > 0) {
      console.log(`Checking ${user.linkedAccounts.length} linked accounts in user object`);
      
      // First pass: look specifically for Phantom/Solana wallets
      for (const account of user.linkedAccounts) {
        // Check if account is a wallet account with an address property
        const accountAny = account as any;
        if (accountAny.address && !accountAny.address.includes('@')) {
          const isSolanaAddress = !accountAny.address.startsWith('0x');
          const isPhantomOrSolana = 
            accountAny.type === 'phantom' || 
            accountAny.type === 'solana' ||
            (isSolanaAddress && (accountAny.type === 'wallet' || !accountAny.type));
          
          if (isPhantomOrSolana) {
            console.log(`Found Solana/Phantom wallet: ${accountAny.address.slice(0, 6)}...${accountAny.address.slice(-4)}`);
            addUniqueAddress(accountAny.address, 'solana', 'Solana', 'phantom');
          }
        }
      }
      
      // Second pass: handle other wallet types
      for (const account of user.linkedAccounts) {
        const accountAny = account as any;
        if (accountAny.address && !accountAny.address.includes('@')) {
          // Special handling for wallets to filter out non-wallet types
          if (accountAny.type === 'wallet' || accountAny.type === 'ethereum' || accountAny.type === 'solana') {
            // Determine blockchain type from address format
            const isSolanaAddress = !accountAny.address.startsWith('0x');
            const chainName = isSolanaAddress ? 'Solana' : 'Ethereum';
            const type = isSolanaAddress ? 'solana' : 'ethereum';
            
            console.log(`Found linked wallet account: ${accountAny.address.slice(0, 6)}...${accountAny.address.slice(-4)} (${chainName})`);
            addUniqueAddress(accountAny.address, type, chainName, accountAny.type);
          } else {
            console.log(`Skipping non-wallet linked account of type: ${accountAny.type}`);
          }
        }
      }
    }
    
    // Process wallets from useWallets hook - enhanced Phantom detection
    if (wallets && wallets.length > 0) {
      console.log(`Found ${wallets.length} wallets from useWallets hook`);
      
      // First, prioritize processing Phantom/Solana wallets
      wallets.forEach((wallet: { address: string; walletClientType: string; }) => {
        if (wallet.address && !wallet.address.includes('@')) {
          const isSolanaAddress = !wallet.address.startsWith('0x');
          
          // Enhanced Phantom detection logic
          const walletAny = wallet as any;
          const isPhantomWallet = 
            wallet.walletClientType === "phantom" || 
            (walletAny.walletClient?.name?.toLowerCase() === "phantom") ||
            (walletAny.connector?.name?.toLowerCase() === "phantom") ||
            (isSolanaAddress && window.phantom && window.phantom.solana);
          
          if (isPhantomWallet || isSolanaAddress) {
            console.log(`Processing Solana/Phantom wallet: ${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)}`);
            addUniqueAddress(wallet.address, 'solana', 'Solana', 'phantom');
          }
        }
      });
      
      // Then process other wallets
      wallets.forEach((wallet: { address: string; chainId: any; }) => {
        if (wallet.address && !wallet.address.includes('@')) {
          // Skip Solana addresses - already processed
          if (!wallet.address.startsWith('0x')) return;
          
          // Set chain based on address format and chainId
          const chainName = 'Ethereum'; // Default
          const type = 'ethereum';
          const walletType = getActualWalletType(wallet);
          
          // For Ethereum addresses, we can further specify the exact EVM chain
          let actualChainName = chainName;
          if (wallet.chainId) {
            const chainIdStr = String(wallet.chainId);
            if (chainIdStr === '8453') {
              actualChainName = 'Base';
            } else if (chainIdStr === '137') {
              actualChainName = 'Polygon';
            }
          }
          
          // Add the wallet
          addUniqueAddress(wallet.address, type, actualChainName, walletType);
        }
      });
    }
    
    console.log(`Final unique addresses: ${addresses.length}`);
    return addresses;
  }, [authenticated, user, wallets, getActualWalletType]);
  
  // Fetch all balances using our wallet API
  const fetchAllBalances = useCallback(async () => {
    // Don't run if already fetching to prevent duplicate calls
    if (isFetchingRef.current) {
      console.log("Fetch already in progress, skipping duplicate call");
      return;
    }
    
    if (!authenticated || !wallets || wallets.length === 0) {
      // If not authenticated, we don't fetch any balances
      setAllCoins([]);
      setTokenBalances({});
      return;
    }
    
    console.log("Fetching balances for all chains...");
    setLoading(true);
    setError(null);
    isFetchingRef.current = true; // Mark as fetching
    
    // Clear existing data to prevent showing stale information
    setAllCoins([]);
    setTokenBalances({});
    
    try {
      const processedCoins: CoinType[] = [];
      
      // Check if we have a specifically selected wallet address
      if (selectedWallet) {
        console.log(`Using selected wallet address: ${selectedWallet.slice(0, 6)}...${selectedWallet.slice(-4)}`);
        
        // Directly fetch balances for the selected wallet only
        console.log(`--> FETCHING BALANCES FOR WALLET: ${selectedWallet} <--`);
        
        try {
          // Call our wallet API that checks balances across all chains
          const response = await walletAPI.getAllBalances(selectedWallet);
          
          // Force refresh the dropdown if it's empty - this is a workaround
          console.log("Checking if dropdown needs to be force opened...");
          if (!isOpen && allCoins.length === 0) {
            console.log("Force opening dropdown due to empty state");
            setTimeout(() => setIsOpen(true), 300);
          }
          
          // Log full response for debugging
          console.log("Raw API response:", response);
          console.log("Full API response data:", JSON.stringify(response.data, null, 2));
          
          if (response.success && response.data) {
            const walletData = response.data;
            console.log(`Wallet data received for ${selectedWallet}:`, walletData);
            
            // Debug: Log exact structure of walletData
            console.log("walletData structure:", Object.keys(walletData));
            
            // Handle different API response structures
            let balancesData: Record<string, any[]> | null = null;
            
            // Case 1: Check if balances are nested inside data.data
            if (walletData.data && walletData.data.balances) {
              console.log("Found balances in data.data.balances:", Object.keys(walletData.data.balances));
              balancesData = walletData.data.balances as Record<string, any[]>;
            }
            // Case 2: Check if balances directly inside walletData
            else if (walletData.balances) {
              console.log("Found balances property directly in walletData:", Object.keys(walletData.balances));
              balancesData = walletData.balances as Record<string, any[]>;
            } 
            // Case 3: Check if balances directly inside data (no balances property)
            else if ((walletData as any).ethereum || (walletData as any).bsc || (walletData as any).solana) {
              console.log("Found chain data directly in response");
              balancesData = walletData as unknown as Record<string, any[]>; // Use walletData directly as it contains chain data
            }
            // Case 4: Check if data property contains an array (flat structure)
            else if (Array.isArray(walletData.data)) {
              console.log("Found tokens array in data.data");
              
              // Group tokens by chain
              const groupedTokens: Record<string, any[]> = {};
              walletData.data.forEach((token: any) => {
                if (token.chain) {
                  const chainKey = token.chain.toLowerCase();
                  if (!groupedTokens[chainKey]) {
                    groupedTokens[chainKey] = [];
                  }
                  groupedTokens[chainKey].push(token);
                }
              });
              
              balancesData = groupedTokens;
            }
            // Case 5: Look for any array properties that might contain tokens
            else {
              console.log("Searching for token arrays in response");
              const possibleArrayProps = Object.keys(walletData).filter(key => 
                Array.isArray(walletData[key]) && walletData[key].length > 0
              );
              
              if (possibleArrayProps.length > 0) {
                console.log("Found possible token arrays:", possibleArrayProps);
                balancesData = {};
                
                // Check first array to determine structure
                const firstArray = walletData[possibleArrayProps[0]];
                if (firstArray.length > 0 && firstArray[0].symbol) {
                  // Looks like a token array, assign to ethereum by default
                  balancesData.ethereum = firstArray;
                  console.log("Using first array as ethereum tokens");
                }
              }
            }
            
            // Log the balances data we found
            console.log("Processed balances data:", balancesData);
            
            // Process tokens from each chain (if we have balances data)
            if (balancesData) {
              console.log("Processing balances data:", balancesData);
              
              // Process Ethereum balances
              if (balancesData.ethereum && Array.isArray(balancesData.ethereum)) {
                console.log(`Processing ${balancesData.ethereum.length} Ethereum tokens`);
                balancesData.ethereum.forEach((token: any) => {
                  // Changed from parseFloat(token.balance) > 0 to include all tokens
                  processedCoins.push({
                    name: token.name || token.symbol,
                    symbol: token.symbol,
                    chain: "Ethereum",
                    icon: token.image || "https://cryptologos.cc/logos/ethereum-eth-logo.svg?v=024",
                    balance: token.balance,
                    decimals: token.decimals || 18,
                    tokenAddress: token.tokenAddress || token.token,
                    walletAddress: selectedWallet,
                    walletType: "external" // Default type, doesn't matter much here
                  });
                });
              }
              
              // Process BSC balances
              if (balancesData.bsc && Array.isArray(balancesData.bsc)) {
                console.log(`Processing ${balancesData.bsc.length} BSC tokens:`, balancesData.bsc);
                balancesData.bsc.forEach((token: any) => {
                  console.log(`Adding BSC token ${token.symbol} with balance ${token.balance}`);
                  // Log the icon URL specifically for AITECH or all BSC tokens
                  if (token.symbol && token.symbol.toUpperCase() === 'AITECH') {
                      console.log(`>>> AITECH Icon URL from API: ${token.image}`);
                  }
                  // Include all tokens
                  processedCoins.push({
                    name: token.name || token.symbol,
                    symbol: token.symbol,
                    chain: "BSC",
                    icon: token.image || "https://cryptologos.cc/logos/bnb-bnb-logo.svg?v=024",
                    balance: token.balance,
                    decimals: token.decimals || 18,
                    tokenAddress: token.tokenAddress || token.token,
                    walletAddress: selectedWallet,
                    walletType: "external" // Default type, doesn't matter much here
                  });
                });
              }
              
              // Process Solana balances
              if (balancesData.solana && Array.isArray(balancesData.solana)) {
                console.log(`Processing ${balancesData.solana.length} Solana tokens`);
                balancesData.solana.forEach((token: any) => {
                  // Include all tokens
                  processedCoins.push({
                    name: token.name || token.symbol,
                    symbol: token.symbol,
                    chain: "Solana",
                    icon: token.image || "https://cryptologos.cc/logos/solana-sol-logo.svg?v=024",
                    balance: token.balance,
                    decimals: token.decimals || 9,
                    tokenAddress: token.tokenAddress,
                    walletAddress: selectedWallet,
                    walletType: "phantom" // Solana wallets are typically Phantom
                  });
                });
              }
            } else {
              console.warn(`No valid balance data structure found for wallet ${selectedWallet}`);
            }
          } else {
            console.warn(`No balances found for wallet ${selectedWallet}:`, response);
          }
          
        } catch (walletError) {
          console.error(`Error fetching balances for wallet ${selectedWallet}:`, walletError);
        }
      } else {
        // Use existing logic to process multiple wallets
        // Get all wallet addresses and store them in state if needed
        if (walletAddresses.length === 0) {
          const addresses = getWalletAddresses();
          setWalletAddresses(addresses);
        }
        
        // Use existing wallet addresses from state
        const addressesToUse = walletAddresses.length > 0 ? walletAddresses : getWalletAddresses();
        
        if (addressesToUse.length === 0) {
          console.log("No wallet addresses found");
          setError("No wallet addresses found. Please connect a wallet.");
          setLoading(false);
          isFetchingRef.current = false; // Reset fetching flag
          return;
        }
        
        // Process each address
        for (const walletData of addressesToUse) {
          const { address, type, chainName, walletType } = walletData;
          
          console.log(`Fetching balances for ${walletType} wallet: ${address.slice(0, 6)}... (${chainName})`);
          
          try {
            // Call our wallet API that checks balances across all chains
            const response = await walletAPI.getAllBalances(address);
            
            console.log("Raw API response:", response);
            console.log("Full API response data for wallet", address.slice(0, 6), ":", JSON.stringify(response.data, null, 2));
            
            if (response.success && response.data) {
              const walletData = response.data;
              console.log(`Wallet data received for ${address}:`, walletData);
              
              // Debug: Log exact structure of walletData
              console.log("walletData structure:", Object.keys(walletData));
              
              // Handle different API response structures
              let balancesData: Record<string, any[]> | null = null;
              
              // Case 1: Check if balances are nested inside data.data
              if (walletData.data && walletData.data.balances) {
                console.log("Found balances in data.data.balances:", Object.keys(walletData.data.balances));
                balancesData = walletData.data.balances as Record<string, any[]>;
              }
              // Case 2: Check if balances directly inside walletData
              else if (walletData.balances) {
                console.log("Found balances property directly in walletData:", Object.keys(walletData.balances));
                balancesData = walletData.balances as Record<string, any[]>;
              } 
              // Case 3: Check if balances directly inside data (no balances property)
              else if ((walletData as any).ethereum || (walletData as any).bsc || (walletData as any).solana) {
                console.log("Found chain data directly in response");
                balancesData = walletData as unknown as Record<string, any[]>; // Use walletData directly as it contains chain data
              }
              // Case 4: Check if data property contains an array (flat structure)
              else if (Array.isArray(walletData.data)) {
                console.log("Found tokens array in data.data");
                
                // Group tokens by chain
                const groupedTokens: Record<string, any[]> = {};
                walletData.data.forEach((token: any) => {
                  if (token.chain) {
                    const chainKey = token.chain.toLowerCase();
                    if (!groupedTokens[chainKey]) {
                      groupedTokens[chainKey] = [];
                    }
                    groupedTokens[chainKey].push(token);
                  }
                });
                
                balancesData = groupedTokens;
              }
              // Case 5: Look for any array properties that might contain tokens
              else {
                console.log("Searching for token arrays in response");
                const possibleArrayProps = Object.keys(walletData).filter(key => 
                  Array.isArray(walletData[key]) && walletData[key].length > 0
                );
                
                if (possibleArrayProps.length > 0) {
                  console.log("Found possible token arrays:", possibleArrayProps);
                  balancesData = {};
                  
                  // Check first array to determine structure
                  const firstArray = walletData[possibleArrayProps[0]];
                  if (firstArray.length > 0 && firstArray[0].symbol) {
                    // Looks like a token array, assign to ethereum by default
                    balancesData.ethereum = firstArray;
                    console.log("Using first array as ethereum tokens");
                  }
                }
              }
              
              // Log the balances data we found
              console.log("Processed balances data for wallet", address.slice(0, 6), ":", balancesData);
              
              // Process tokens from each chain (if we have balances data)
              if (balancesData) {
                console.log("Processing balances data:", balancesData);
                
                // Process Ethereum balances
                if (balancesData.ethereum && Array.isArray(balancesData.ethereum)) {
                  console.log(`Processing ${balancesData.ethereum.length} Ethereum tokens`);
                  balancesData.ethereum.forEach((token: any) => {
                    // Changed from parseFloat(token.balance) > 0 to include all tokens
                    processedCoins.push({
                      name: token.name || token.symbol,
                      symbol: token.symbol,
                      chain: "Ethereum",
                      icon: token.image || "https://cryptologos.cc/logos/ethereum-eth-logo.svg?v=024",
                      balance: token.balance,
                      decimals: token.decimals || 18,
                      tokenAddress: token.tokenAddress || token.token,
                      walletAddress: address,
                      walletType
                    });
                  });
                }
                
                // Process BSC balances
                if (balancesData.bsc && Array.isArray(balancesData.bsc)) {
                  console.log(`Processing ${balancesData.bsc.length} BSC tokens:`, balancesData.bsc);
                  balancesData.bsc.forEach((token: any) => {
                    console.log(`Adding BSC token ${token.symbol} with balance ${token.balance}`);
                    // Log the icon URL specifically for AITECH or all BSC tokens
                    if (token.symbol && token.symbol.toUpperCase() === 'AITECH') {
                        console.log(`>>> AITECH Icon URL from API: ${token.image}`);
                    }
                    // Include all tokens
                    processedCoins.push({
                      name: token.name || token.symbol,
                      symbol: token.symbol,
                      chain: "BSC",
                      icon: token.image || "https://cryptologos.cc/logos/bnb-bnb-logo.svg?v=024",
                      balance: token.balance,
                      decimals: token.decimals || 18,
                      tokenAddress: token.tokenAddress || token.token,
                      walletAddress: address,
                      walletType
                    });
                  });
                }
                
                // Process Solana balances
                if (balancesData.solana && Array.isArray(balancesData.solana)) {
                  console.log(`Processing ${balancesData.solana.length} Solana tokens`);
                  balancesData.solana.forEach((token: any) => {
                    // Include all tokens
                    processedCoins.push({
                      name: token.name || token.symbol,
                      symbol: token.symbol,
                      chain: "Solana",
                      icon: token.image || "https://cryptologos.cc/logos/solana-sol-logo.svg?v=024",
                      balance: token.balance,
                      decimals: token.decimals || 9,
                      tokenAddress: token.tokenAddress,
                      walletAddress: address,
                      walletType
                    });
                  });
                }
              } else {
                console.warn(`No valid balance data structure found for wallet ${address}`);
              }
            } else {
              console.warn(`No balances found for wallet ${address}:`, response);
            }
          } catch (walletError) {
            console.error(`Error fetching balances for wallet ${address}:`, walletError);
          }
        }
      }
      
      console.log(`Fetched ${processedCoins.length} token balances across all chains`);
      
      // Add detailed logging of each processed coin
      processedCoins.forEach(coin => {
        console.log(`Processed token: ${coin.symbol} (${coin.chain}) - Balance: ${coin.balance}`);
      });
      
      // Update allCoins with the fetched tokens
      if (processedCoins.length > 0) {
        console.log("Setting tokens in state:", processedCoins);
        setAllCoins(processedCoins);
        // Call the callback to update parent component if it exists
        if (onCoinsUpdate) {
          console.log("Notifying parent component of updated coins");
          onCoinsUpdate(processedCoins);
        }
        setError(null); // Clear any previous errors since we have tokens
      } else {
        console.log("No tokens found, clearing state");
        setAllCoins([]);
        // Also notify parent of empty array
        if (onCoinsUpdate) {
          onCoinsUpdate([]);
        }
        // Change error message to be more informative
        setError("No tokens found. Please check your wallet connection or add funds.");
      }
      
      // Create a balance mapping for quick lookups
      const newBalances: Record<string, string> = {};
      processedCoins.forEach(token => {
        const balanceKey = `${token.symbol}-${token.chain}-${token.walletAddress}`;
        newBalances[balanceKey] = token.balance;
        newBalances[token.symbol] = token.balance; // Also keep a simple lookup by symbol
      });
      
      setTokenBalances(newBalances);
      setDataLoaded(true); // Mark data as loaded
      
    } catch (error) {
      console.error("Error in fetchAllBalances:", error);
      setError("Failed to fetch balances");
      setAllCoins([]);
      setTokenBalances({});
    } finally {
      setLoading(false);
      isFetchingRef.current = false; // Reset fetching flag
    }
  }, [authenticated, getWalletAddresses, selectedWallet, walletAddresses, wallets]);
  
  // Add initial fetch when component mounts
  useEffect(() => {
    // Don't fetch if not authenticated or no selected wallet
    if (!authenticated) {
      console.log("BalanceDropdown: Not fetching on mount - no authentication");
      return;
    }
    
    if (!selectedWallet) {
      console.log("BalanceDropdown: No wallet selected yet");
      return;
    }
    
    console.log(`BalanceDropdown: Component mounted with wallet ${selectedWallet.slice(0, 6)}..., initiating balance fetch`);
    
    // Set a short delay to ensure everything is ready
    const initialFetchTimeout = setTimeout(() => {
      if (!dataLoaded && !isFetchingRef.current) {
        console.log(`BalanceDropdown: Initial fetch for wallet ${selectedWallet.slice(0, 6)}...`);
        fetchAllBalances();
      }
    }, 300);
    
    return () => {
      clearTimeout(initialFetchTimeout);
    };
  }, [authenticated, selectedWallet, dataLoaded, fetchAllBalances]);
  
  // Auto-select first token when coins are loaded and no token is selected
  useEffect(() => {
    if (allCoins.length > 0 && !selectedCoin && !loading && !error) {
      console.log("BalanceDropdown: Auto-selecting first available token");
      const firstCoin = allCoins[0];
      setSelectedCoin(firstCoin);
      setIsOpen(false);
    }
  }, [allCoins, selectedCoin, loading, error]);
  
  // Add effect to track selected wallet changes
  useEffect(() => {
    if (!selectedWallet) return;
    
    // Check if wallet has actually changed
    if (prevSelectedWalletRef.current !== selectedWallet) {
      console.log(`BalanceDropdown: Selected wallet changed to ${selectedWallet.slice(0, 6)}...`);
      prevSelectedWalletRef.current = selectedWallet;
      
      // Reset data loaded state
      setDataLoaded(false);
      
      // Auto-fetch if enabled
      if (autoFetch) {
        // Ensure we don't have multiple fetches
        if (fetchTimeoutRef.current) {
          clearTimeout(fetchTimeoutRef.current);
        }
        
        fetchTimeoutRef.current = setTimeout(() => {
          console.log(`BalanceDropdown: Auto-fetching for wallet ${selectedWallet.slice(0, 6)}...`);
          fetchAllBalances();
        }, 500);
      }
    }
    
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, [selectedWallet, autoFetch, fetchAllBalances]);
  
  // Custom event interface
  interface WalletChangedEvent extends Event {
    detail: {
      wallet: {
        address: string;
      };
      timestamp: number;
      clearCache: boolean;
    };
  }

  // Listen for wallet-changed events
  useEffect(() => {
    // Handler for the wallet-changed event
    const handleWalletChanged = (event: Event) => {
      const customEvent = event as WalletChangedEvent;
      const { wallet, timestamp, clearCache } = customEvent.detail;
      console.log(`BalanceDropdown: Detected wallet change event at ${new Date(timestamp).toLocaleTimeString()}`);
      
      // Reset token data when wallet changes
      setAllCoins([]);
      setTokenBalances({});
      setDataLoaded(false);
      
      // If clearCache flag is present, clear any references to previous data
      if (clearCache) {
        console.log("BalanceDropdown: Clearing all cache as requested by wallet change event");
        prevSelectedWalletRef.current = null;
        
        // Reset tracking flags
        isFirstRunRef.current = true;
        
        // Clear any in-progress fetch operation
        if (isFetchingRef.current) {
          console.log("BalanceDropdown: Aborting in-progress fetch");
          isFetchingRef.current = false;
        }
      }
      
      if (autoFetch) {
        console.log("BalanceDropdown: Auto-fetch enabled, will fetch balances for new wallet");
        
        // Clear any existing timeout
        if (fetchTimeoutRef.current) {
          clearTimeout(fetchTimeoutRef.current);
        }
        
        // Set a short timeout to prevent multiple fetches in rapid succession
        fetchTimeoutRef.current = setTimeout(() => {
          console.log(`BalanceDropdown: Fetching balances for new wallet: ${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)}`);
          fetchAllBalances();
        }, 500);
      } else {
        console.log("BalanceDropdown: Auto-fetch disabled, waiting for manual refresh");
        setError("Wallet changed - click 'Refresh' to load balances");
      }
    };
    
    // Add event listener
    window.addEventListener('wallet-changed', handleWalletChanged as EventListener);
    
    // Clean up the event listener when component unmounts
    return () => {
      window.removeEventListener('wallet-changed', handleWalletChanged as EventListener);
      
      // Clear any pending timeout
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, [autoFetch, fetchAllBalances]); // Removed fetchAllBalances from dependencies to avoid circular reference
  
  // Get real balance for a coin, or show appropriate message
  const getCoinBalance = (coin: CoinType | null): string => {
    if (!coin) return "No coin selected";
    
    const key = `${coin.symbol}-${coin.chain}-${coin.walletAddress}`;
    
    if (!authenticated) {
      return "Connect wallet";
    }
    
    if (loading) {
      return "Loading...";
    }
    
    // First try the specific key with chain and wallet information
    if (tokenBalances[key] && tokenBalances[key] !== "0") {
      return tokenBalances[key];
    }
    
    // Then try just the coin symbol (backward compatibility)
    if (tokenBalances[coin.symbol] && tokenBalances[coin.symbol] !== "0") {
      return tokenBalances[coin.symbol];
    }
    
    // Return 0 if no balance found
    return "0";
  };
  
  // Filter coins based on chain ID if provided
  const filteredCoins = useMemo(() => {
    console.log("Computing filteredCoins with coins:", coins);
    console.log("All coins state variable:", allCoins);
    
    // First, check which source to use (coins passed as prop or allCoins from state)
    const sourceCoins = allCoins.length > 0 ? allCoins : coins;
    console.log("Using source coins:", sourceCoins);
    
    if (!activeChainId) {
      console.log("No activeChainId, returning all source coins");
      return sourceCoins;
    }
    
    const filtered = sourceCoins.filter(coin => {
      // Skip filtering if no chain info is available
      if (!coin.chain) return true;
      
      const coinChainId = 
        (coin.chain === 'Ethereum' ? 1 : 
         coin.chain === 'BSC' ? 56 : 
         coin.chain === 'Polygon' ? 137 : 
         coin.chain === 'Solana' ? 'solana' : null);
      
      return coinChainId === activeChainId;
    });
    
    console.log("Filtered coins by activeChainId:", filtered);
    return filtered;
  }, [coins, activeChainId, allCoins]);
  
  // Memoize the grouping of coins by chain to prevent recalculation on every render
  const groupedCoins = useMemo(() => {
    const groups: Record<string, CoinType[]> = {};
    
    console.log("Computing groupedCoins with filteredCoins:", filteredCoins);
    
    // First, separate Ethereum tokens and other tokens
    const ethereumCoins: CoinType[] = [];
    const otherCoins: CoinType[] = [];
    
    filteredCoins.forEach(coin => {
      if (coin.chain === 'Ethereum') {
        ethereumCoins.push(coin);
      } else {
        otherCoins.push(coin);
      }
    });
    
    // Now process the groups with Ethereum first
    if (ethereumCoins.length > 0) {
      groups['Ethereum'] = ethereumCoins;
    }
    
    // Process other chains
    otherCoins.forEach(coin => {
      const chainName = coin.chain || 'Unknown';
      console.log(`Adding coin to group ${chainName}:`, coin);
      if (!groups[chainName]) {
        groups[chainName] = [];
      }
      groups[chainName].push(coin);
    });
    
    console.log("Final grouped coins:", groups);
    return groups;
  }, [filteredCoins]);

  // When logout happens, clear the data loaded state and wallet IDs
  useEffect(() => {
    const handleLogout = () => {
      console.log("Logout detected, clearing wallet data");
      setDataLoaded(false);
      setAllCoins([]);
      setTokenBalances({});
    };
    
    // Listen for Privy logout events
    window.addEventListener("privy:logout", handleLogout);
    
    return () => {
      window.removeEventListener("privy:logout", handleLogout);
    };
  }, []);
  
  // Convert activeChainId to a chain name for display
  const getChainName = (chainId: string | number | null): string | null => {
    if (!chainId) return null;
    
    const chainMap: Record<string | number, string> = {
      1: "Ethereum",
      137: "Polygon",
      56: "BSC",
      43114: "Avalanche",
      10: "Optimism",
      42161: "Arbitrum",
      8453: "Base",
      "solana": "Solana"
    };
    
    return chainMap[chainId] || "Unknown";
  };
  
  // Convert chain name to chain ID for comparison
  const getChainIdFromName = (chainName: string | null): number | string | null => {
    if (!chainName) return null;
    
    const chainMap: Record<string, number | string> = {
      'Ethereum': 1,
      'Polygon': 137,
      'BSC': 56,
      'Avalanche': 43114,
      'Optimism': 10,
      'Arbitrum': 42161,
      'Base': 8453,
      'Solana': 'solana',
      'Sui': 'sui'
    };
    
    return chainMap[chainName] || null;
  };
  
  // Add a visible toggle for auto-fetch option
  const toggleAutoFetch = () => {
    setAutoFetch(prev => !prev);
    console.log(`BalanceDropdown: Auto-fetch ${!autoFetch ? 'enabled' : 'disabled'}`);
  };

  // Enhance the UI by adding a more prominent auto-fetch toggle in the component's render function
  const renderAutoFetchToggle = () => {
    return (
      <div className="flex items-center justify-between mb-2 px-2 py-1 bg-[#1D2226] rounded">
        <div className="text-sm text-white">Auto-fetch balances</div>
        <div 
          onClick={toggleAutoFetch}
          className={`relative inline-block w-10 h-5 transition-colors duration-200 ease-in-out rounded-full cursor-pointer ${
            autoFetch ? 'bg-green-500' : 'bg-gray-600'
          }`}
        >
          <span
            className={`absolute left-0.5 top-0.5 w-4 h-4 transition-transform duration-200 ease-in-out transform bg-white rounded-full ${
              autoFetch ? 'translate-x-5' : 'translate-x-0'
            }`}
          />
        </div>
      </div>
    );
  };

  // Modify the wallet addresses initialization useEffect
  useEffect(() => {
    if (authenticated && wallets && wallets.length > 0 && !walletInitializedRef.current) {
      console.log("BalanceDropdown: Initializing wallet addresses (first time only)");
      walletInitializedRef.current = true; // Mark as initialized to prevent loops
      const addresses = getWalletAddresses();
      setWalletAddresses(addresses);
      
      // Auto-select first wallet address if none is selected
      if (addresses.length > 0 && !selectedWalletAddress) {
        setSelectedWalletAddress(addresses[0].address);
      }
    }
  }, [authenticated, wallets, getWalletAddresses, selectedWalletAddress]);

  // Modify the Phantom fallback detection to avoid loops
  useEffect(() => {
    // Only proceed if authenticated and not yet initialized
    if (!authenticated || !window.phantom?.solana) {
      return;
    }
    
    // Skip if we've already added wallet addresses
    if (walletAddresses.length === 0) {
      return; // Don't try to check if we don't have addresses yet
    }
    
    // Check if there are any Solana wallets in our wallet list - only once
    const hasSolanaWallet = walletAddresses.some(wa => !wa.address.startsWith('0x'));
    
    if (!hasSolanaWallet && !phantomConnectionInProgressRef.current) {
      console.log("BalanceDropdown: No Solana wallets found in wallet list, trying direct connection");
      
      // Add rate limiting check
      const now = Date.now();
      const timeSinceLastAttempt = now - lastPhantomConnectAttemptRef.current;
      if (timeSinceLastAttempt < 10000) { // 10 second cooldown
        console.log(`BalanceDropdown: Skipping Phantom connection attempt - rate limited (tried ${timeSinceLastAttempt}ms ago)`);
        return;
      }
      
      // Prevent concurrent connection attempts
      if (phantomConnectionInProgressRef.current) {
        console.log("BalanceDropdown: Connection attempt already in progress, skipping");
        return;
      }
      
      // Mark connection attempt
      lastPhantomConnectAttemptRef.current = now;
      phantomConnectionInProgressRef.current = true;
      
      try {
        // Try to get connection directly from Phantom
        if (window.phantom?.solana?.isConnected) {
          console.log("BalanceDropdown: Phantom is already connected");
          
          // Use a timeout to break render cycles
          const timeoutId = setTimeout(() => {
            window.phantom?.solana?.connect({ onlyIfTrusted: true })
              .then(({ publicKey }: any) => {
                if (publicKey) {
                  const solanaAddress = publicKey.toString();
                  console.log("BalanceDropdown: Got Solana address from Phantom:", solanaAddress);
                  
                  // Add this address to our walletAddresses list if not already there
                  if (!walletAddresses.some(wa => wa.address === solanaAddress)) {
                    console.log("BalanceDropdown: Adding Phantom wallet to address list");
                    setWalletAddresses(prev => [
                      ...prev,
                      {
                        address: solanaAddress,
                        chainName: 'Solana',
                        type: 'solana',
                        walletType: 'phantom'
                      }
                    ]);
                    
                    // If no wallet is selected, select this one
                    if (!selectedWalletAddress) {
                      setSelectedWalletAddress(solanaAddress);
                    }
                  }
                }
              })
              .catch((err: Error) => {
                console.log("BalanceDropdown: Could not auto-connect to Phantom:", err.message);
              })
              .finally(() => {
                phantomConnectionInProgressRef.current = false;
              });
          }, 100);
          
          return () => clearTimeout(timeoutId);
        } else {
          console.log("BalanceDropdown: Phantom is not connected");
          phantomConnectionInProgressRef.current = false;
        }
      } catch (e) {
        console.error("BalanceDropdown: Error accessing Phantom wallet:", e);
        phantomConnectionInProgressRef.current = false;
      }
    }
  }, [authenticated, walletAddresses, selectedWalletAddress]);

  // Replace the localStorage check effect with a one-time initialization that won't loop
  useEffect(() => {
    if (authenticated && walletAddresses.length === 0 && !walletInitializedRef.current) {
      // One-time initialization
      walletInitializedRef.current = true;
      console.log("BalanceDropdown: First-time initialization of wallet addresses");
      
      // Get wallet addresses from Privy only
      const addresses = getWalletAddresses();
      
      // Only update if we found something
      if (addresses.length > 0) {
        console.log("BalanceDropdown: Found wallet addresses from Privy:", addresses.length);
        setWalletAddresses(addresses);
        
        // Auto-select first address if none selected
        if (!selectedWalletAddress && addresses.length > 0) {
          setSelectedWalletAddress(addresses[0].address);
        }
      }
    }
  }, [authenticated]);  // Keep minimal dependencies

  return (
    <div className="relative">
      <div
        className="flex items-center space-x-2 bg-[#1D2226] rounded-lg p-2 cursor-pointer border border-gray-700 hover:border-gray-500 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
        onMouseEnter={() => {
          if (selectedWallet && !dataLoaded) {
            console.log("BalanceDropdown: Mouse enter - forcing data fetch");
            fetchAllBalances();
          }
        }}
      >
        <div className="w-7 h-7 rounded-full flex items-center justify-center bg-gradient-to-tr from-blue-500 to-purple-600">
          {selectedCoin ? (
            <img 
              src={selectedCoin.icon} 
              alt={selectedCoin.symbol} 
              className="w-full h-full rounded-full object-cover"
              onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                const target = e.target as HTMLImageElement;
                target.onerror = null;
                target.src = ""; // Use empty string to prevent 403 errors
              }} 
            />
          ) : (
            <span className="text-xs text-white font-bold">$</span>
          )}
        </div>
        <div className="flex-1">
          {selectedCoin ? (
            <>
              <div className="text-sm text-white font-medium flex items-center">
                {selectedCoin.symbol}
                <span className="text-xs text-gray-400 ml-1">({selectedCoin.chain})</span>
              </div>
              <div className="text-xs text-gray-400">
                {parseFloat(selectedCoin.balance).toLocaleString(undefined, {
                  maximumFractionDigits: 6,
                  minimumFractionDigits: 0
                })}
              </div>
            </>
          ) : (
            <>
              <div className="text-sm text-white font-medium">Wallet Balances</div>
              <div className="text-xs text-gray-400">
                {loading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Fetching...
                  </span>
                ) : dataLoaded ? (
                  allCoins.length > 0 ? 
                  `${allCoins.length} tokens` : 
                  "No tokens found"
                ) : (
                  "Click to load"
                )}
              </div>
            </>
          )}
        </div>
        <div className="text-gray-400">
          {isOpen ? <ChevronUpIcon className="w-4 h-4" /> : <ChevronDownIcon className="w-4 h-4" />}
        </div>
      </div>

      {isOpen && (
        <div className="absolute left-0 right-0 z-10 mt-2 max-h-[400px] overflow-y-auto bg-[#1D2226] rounded-lg shadow-lg border border-gray-700">
          {/* Header controls */}
          <div className="p-3 border-b border-gray-700">
            {/* Auto-fetch toggle */}
            {renderAutoFetchToggle()}
            
            {/* Refresh button and chain selection */}
            <div className="flex items-center justify-between">
              <button 
                onClick={(e: { stopPropagation: () => void; }) => {
                  e.stopPropagation();
                  fetchAllBalances();
                }}
                className="flex items-center space-x-1 text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md transition"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>{loading ? "Loading..." : "Refresh"}</span>
              </button>
              
              <div className="flex items-center space-x-2">
                <Switch
                  checked={showAllChains}
                  onChange={setShowAllChains}
                  className={`${showAllChains ? 'bg-blue-600' : 'bg-gray-700'} relative inline-flex h-5 w-10 items-center rounded-full transition-colors`}
                >
                  <span
                    className={`${showAllChains ? 'translate-x-5' : 'translate-x-1'} inline-block h-3 w-3 transform rounded-full bg-white transition-transform`}
                  />
                </Switch>
                <span className="text-xs text-gray-400">Show all chains</span>
              </div>
            </div>
          </div>
          
          {/* Loading indicator */}
          {loading && (
            <div className="p-4 text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
              <div className="mt-2 text-sm text-gray-400">Fetching wallet balances...</div>
            </div>
          )}

          {/* Error message */}
          {error && !loading && (
            <div className="p-4 text-center text-red-400 text-sm">
              {error}
              <div className="mt-2 flex justify-center gap-2">
                <button
                  onClick={(e: { stopPropagation: () => void; }) => {
                    e.stopPropagation();
                    fetchAllBalances();
                  }}
                  className="text-blue-500 hover:text-blue-400 underline text-xs"
                >
                  Try again
                </button>
              </div>
            </div>
          )}
          
          {/* Empty state */}
          {!loading && !error && allCoins.length === 0 && (
            <div className="p-4 text-center text-gray-400 text-sm">
              <div className="text-white mb-2">No tokens found</div>
              {selectedWallet && (
                <div className="mb-3">
                  <span className="text-gray-400">This wallet doesn't have any tokens on the supported chains.</span>
                </div>
              )}
              <div className="text-gray-500 text-xs mt-3">
                <span>Try connecting a different wallet or adding funds to this wallet.</span>
              </div>
              <div className="flex justify-center gap-2 mt-3">
                <button 
                  onClick={(e: { stopPropagation: () => void; }) => {
                    e.stopPropagation();
                    fetchAllBalances();
                  }}
                  className="text-blue-500 hover:bg-blue-900/30 px-3 py-1 rounded-md text-xs"
                >
                  Refresh Balances
                </button>
              </div>
            </div>
          )}

          {/* Token list - show even with empty balances */}
          {!loading && allCoins.length > 0 && (
            <div className="divide-y divide-gray-700">
              {Object.entries(groupedCoins).map(([chainName, coins]) => (
                <div key={chainName} className="p-2">
                  {/* Chain header */}
                  <div className="text-xs text-gray-400 mb-1 px-2 py-1 bg-gray-800/30 rounded">
                    {chainName}
                  </div>
                  
                  {/* Coins in this chain */}
                  {coins.map((coin: CoinType) => {
                    const isZeroBalance = parseFloat(coin.balance) === 0;
                    return (
                      <div
                        key={`${coin.symbol}-${chainName}-${coin.walletAddress}`}
                        className={`flex items-center justify-between p-2 hover:bg-gray-700/40 rounded-md cursor-pointer`}
                        onClick={(e: { stopPropagation: () => void; }) => {
                          e.stopPropagation();
                          setSelectedCoin(coin);
                          setIsOpen(false);
                        }}
                      >
                        <div className="flex items-center space-x-2">
                          <img 
                            src={coin.icon} 
                            alt={coin.symbol} 
                            onError={(e) => (e.currentTarget.src = '')}
                            className="w-6 h-6 mr-2"
                          />
                          <div>
                            <div className="text-sm text-white font-medium">{coin.symbol}</div>
                            <div className="text-xs text-gray-400">{coin.name}</div>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-3">
                          <div className="text-right">
                            <div className={`text-sm font-medium ${isZeroBalance ? 'text-gray-500' : 'text-white'}`}>
                              {parseFloat(coin.balance).toLocaleString(undefined, {
                                maximumFractionDigits: 6,
                                minimumFractionDigits: 0
                              })}
                              {isZeroBalance && <span className="text-xs text-gray-500 ml-1">(empty)</span>}
                            </div>
                            <div className="text-xs text-gray-400">
                              {coin.walletAddress ? `${coin.walletAddress.slice(0, 4)}...${coin.walletAddress.slice(-4)}` : 'Unknown'}
                            </div>
                          </div>

                          {selectedCoin && selectedCoin.symbol === coin.symbol && 
                            selectedCoin.chain === coin.chain && 
                            selectedCoin.walletAddress === coin.walletAddress && (
                            <CheckIcon className="w-5 h-5 text-blue-500" />
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default memo(BalanceDropdown);