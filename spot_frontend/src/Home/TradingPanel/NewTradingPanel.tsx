import React, { useState, useEffect } from 'react';
import { usePrivy, ConnectedWallet } from '@privy-io/react-auth';
import { WalletSelector } from './FixedWalletSelector';

// Types
interface TokenBase {
  symbol: string;
  name: string;
  chainId: number;
  decimals: number;
  logoURI?: string;
}

interface TradingPanelProps {
  activeToken: TokenBase | null;
}

// Utility functions
const formatPrice = (price: number | string | null | undefined): string => {
  if (price === null || price === undefined) return 'N/A';
  return typeof price === 'number' ? price.toFixed(2) : price;
};

const formatTokenPair = (token: TokenBase | null): string => {
  if (!token) return 'Unknown/Unknown';
  return `${token.symbol || 'Unknown'}/USDC`;
};

const getChainName = (chainId: number | undefined): string => {
  if (!chainId) return 'Unknown Chain';
  
  switch (chainId) {
    case 1: return 'Ethereum';
    case 137: return 'Polygon';
    case 56: return 'BSC';
    case 43114: return 'Avalanche';
    case 42161: return 'Arbitrum';
    default: return `Chain ${chainId}`;
  }
};

// Main component
const NewTradingPanel = ({ activeToken }: TradingPanelProps) => {
  const { user, authenticated, login } = usePrivy();
  const [selectedWallet, setSelectedWallet] = useState<ConnectedWallet | null>(null);
  const [amount, setAmount] = useState<string>('');
  const [isTakeProfit, setIsTakeProfit] = useState<boolean>(false);
  const [takeProfit, setTakeProfit] = useState<string>('');
  const [isStopLoss, setIsStopLoss] = useState<boolean>(false);
  const [stopLoss, setStopLoss] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleWalletSelect = (wallet: ConnectedWallet) => {
    setSelectedWallet(wallet);
  };

  const handleLogin = async () => {
    if (!authenticated && login) {
      try {
        await login();
      } catch (error) {
        console.error('Login failed:', error);
      }
    }
  };

  const handleTrade = async () => {
    if (!activeToken || !selectedWallet || !selectedWallet.address) {
      console.error('Missing token or wallet information');
      return;
    }

    setIsLoading(true);
    try {
      // Trade logic would go here
      console.log('Trading', {
        token: activeToken.symbol,
        amount,
        walletAddress: selectedWallet.address,
        takeProfit: isTakeProfit ? takeProfit : undefined,
        stopLoss: isStopLoss ? stopLoss : undefined
      });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Reset form
      setAmount('');
      setTakeProfit('');
      setStopLoss('');
      setIsTakeProfit(false);
      setIsStopLoss(false);
    } catch (error) {
      console.error('Trade failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="trading-panel">
      <div className="trading-header">
        <h2>{formatTokenPair(activeToken)}</h2>
        {activeToken && (
          <span className="chain-badge">{getChainName(activeToken.chainId)}</span>
        )}
      </div>

      <div className="wallet-section">
        {authenticated ? (
          <WalletSelector 
            onWalletSelect={handleWalletSelect}
            selectedWallet={selectedWallet}
          />
        ) : (
          <button className="connect-button" onClick={handleLogin}>
            Connect Wallet
          </button>
        )}
      </div>

      <div className="trading-form">
        <div className="form-group">
          <label>Amount</label>
          <input
            type="number"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            placeholder="0.00"
          />
          <span className="currency">USDC</span>
        </div>

        <div className="form-group checkbox">
          <input
            type="checkbox"
            checked={isTakeProfit}
            onChange={() => setIsTakeProfit(!isTakeProfit)}
            id="take-profit"
          />
          <label htmlFor="take-profit">Take Profit</label>
          {isTakeProfit && (
            <input
              type="number"
              value={takeProfit}
              onChange={(e) => setTakeProfit(e.target.value)}
              placeholder="0.00"
            />
          )}
        </div>

        <div className="form-group checkbox">
          <input
            type="checkbox"
            checked={isStopLoss}
            onChange={() => setIsStopLoss(!isStopLoss)}
            id="stop-loss"
          />
          <label htmlFor="stop-loss">Stop Loss</label>
          {isStopLoss && (
            <input
              type="number"
              value={stopLoss}
              onChange={(e) => setStopLoss(e.target.value)}
              placeholder="0.00"
            />
          )}
        </div>

        <button
          className="trade-button"
          onClick={handleTrade}
          disabled={isLoading || !authenticated || !selectedWallet || !amount}
        >
          {isLoading ? 'Processing...' : 'Trade'}
        </button>
      </div>
    </div>
  );
};

export default NewTradingPanel; 