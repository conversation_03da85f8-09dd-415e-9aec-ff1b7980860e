import React, { useState, useEffect, useCallback, useRef, useMemo, memo } from "react";
import { Switch } from "@headlessui/react";
import { ChevronUpIcon, ChevronDownIcon, CheckIcon } from "@heroicons/react/24/outline";
import { IoMdSettings } from "react-icons/io"; // Import settings icon
import BalanceDropdown from "./BalanceDropdown"; // Ensure this component is imported
import SettingsModal from "./SettingsModal";
import { FaExchangeAlt, FaChevronDown, FaChevronUp, FaCheck, FaPlus } from "react-icons/fa";
import Tpslmodal from "./Tpslmodal";
import { usePrivy, useWallets } from "@privy-io/react-auth"; // Import Privy hook
import { ethers } from "ethers";
// Import from our API files
import { walletAPI } from "../../utils/api";
import { getQuoteDataForToken, getBestDEXQuote } from "../../utils/trading-api";
import { quotePancakeswap } from "../../utils/quote";
import { completeTradePancakeswap } from "../../utils/swap"; // Import completeTradePancakeswap from swap.js
import "./TradingPanelStyles.css"; // Add this import for the slider styles
import CustomWalletSelector from './CustomWalletSelector'; // Use our custom wallet selector
import { fetchQuote } from "../../utils/fetchQuote";

// Import our type definitions
import { 
  TokenBase, 
  QuoteData, 
  WalletBase, 
  TradingPanelProps, 
  WalletSelectorProps,
  UseWalletsInterface,
  PancakeswapQuoteData,
  QuoteErrorResponse
} from "../../types/trading";

// ERC20 ABI for token balance checks
const ERC20_ABI = [
  // balanceOf function
  {
    "constant": true,
    "inputs": [{"name": "_owner", "type": "address"}],
    "name": "balanceOf",
    "outputs": [{"name": "balance", "type": "uint256"}],
    "type": "function"
  },
  // decimals function
  {
    "constant": true,
    "inputs": [],
    "name": "decimals",
    "outputs": [{"name": "", "type": "uint8"}],
    "type": "function"
  }
];

// Add these styles at the top where other styles are defined
const styles = {
  // ... existing styles ...
  
  tokenPairDisplay: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '8px 12px',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: '8px',
    marginBottom: '12px',
    fontSize: '14px',
  },
  
  priceInfo: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    padding: '12px',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: '8px',
    marginBottom: '16px',
  },
  
  priceRow: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    fontSize: '14px',
  },
  
  priceLabel: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  
  priceValue: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500',
  }
};

// Create a memoized version of the WalletSelector to prevent unnecessary re-renders
const MemoizedWalletSelector = memo(({ onSelectWallet, selectedWallet }: WalletSelectorProps) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const { wallets } = useWallets();
  const { user } = usePrivy();
  const dropdownRef = useRef<HTMLDivElement | null>(null);
  
  // Store previous prop values to avoid unnecessary updates
  const prevSelectedWalletRef = useRef(selectedWallet);
  
  // Debug logging for wallets in selector
  useEffect(() => {
    if (wallets && wallets.length > 0) {
      console.log("WalletSelector: Available wallets:", wallets.map((w: any) => ({
        address: w.address,
        connected: w.connectedAt ? true : false,
        type: w.walletClientType || 'unknown'
      })));
    } else {
      console.log("WalletSelector: No wallets available from useWallets");
    }
  }, [wallets]);
  
  // Only update when the wallet actually changes
  useEffect(() => {
    prevSelectedWalletRef.current = selectedWallet;
  }, [selectedWallet]);

  // Get connected wallets directly from Privy, similar to Navbar
  const connectedWallets = useMemo(() => {
    const uniqueWallets: WalletBase[] = [];
    const seenAddresses = new Set();
    
    // Get wallets ONLY from Privy SDK - no localStorage
    if (wallets && wallets.length > 0) {
      // Filter to only include connected wallets with valid addresses
      // This ensures we only get wallets that are actually connected through Privy
      const filteredWallets = (wallets as any[]).filter(w => 
        w && 
        w.connectedAt && // Must have a connection timestamp
        w.address && 
        typeof w.address === 'string' &&
        !w.address.includes('@') // Filter out email addresses
      );
      
      console.log("Filtered wallets from Privy:", filteredWallets.map(w => ({
        address: w.address ? `${w.address.slice(0, 6)}...${w.address.slice(-4)}` : 'unknown',
        type: w.walletClientType,
        connectedAt: w.connectedAt ? new Date(w.connectedAt).toISOString() : 'unknown'
      })));
      
      // Add filtered wallets ensuring no duplicates
      filteredWallets.forEach(wallet => {
        if (wallet.address && !seenAddresses.has(wallet.address)) {
          seenAddresses.add(wallet.address);
          uniqueWallets.push(wallet);
        }
      });
    }
    
    // Also check user object for any embedded wallet provided by Privy
    if (user?.wallet?.address && !seenAddresses.has(user.wallet.address)) {
      console.log(`Adding embedded wallet from Privy user object: ${user.wallet.address.slice(0, 6)}...${user.wallet.address.slice(-4)}`);
      seenAddresses.add(user.wallet.address);
      uniqueWallets.push({
        address: user.wallet.address,
        connected: true,
        walletClientType: "embedded",
        connectedAt: new Date().toISOString() // Add a connectedAt value
      });
    }
    
    console.log(`WalletSelector: Found ${uniqueWallets.length} connected wallets from Privy SDK (no localStorage used)`);
    return uniqueWallets;
  }, [wallets, user]);

  // Improved address formatter that handles undefined addresses
  const formatAddress = (address?: string) => {
    if (!address || typeof address !== 'string') return 'No Address';
    return address.length > 10 ? `${address.slice(0, 6)}...${address.slice(-4)}` : address;
  };

  // Helper to determine if an address is a Solana address
  const isSolanaAddress = (address?: string) => {
    return address && typeof address === 'string' && !address.startsWith('0x') && address.length > 30;
  };

  // Get wallet ID to use for selection and tracking
  const getWalletId = useCallback((wallet: WalletBase) => {
    if (!wallet) return null;
    
    // Determine wallet type based on wallet attributes
    let walletType = "unknown";
    
    // First, check if this is a Solana wallet by examining the address format
    const isSolana = isSolanaAddress(wallet.address);
    
    if (wallet.walletClientType === "phantom" || 
        (isSolana && window.phantom && window.phantom.solana) ||
        (wallet as any)?.connector?.name === "phantom") {
      walletType = "phantom";
    } else if ((wallet.walletClientType === "injected" && window.ethereum?.isMetaMask) ||
              (wallet.walletClientType === "metamask") ||
              (wallet as any)?.connector?.name === "metamask") {
      walletType = "metamask";
    } else if ((wallet as any)?.embedded || wallet.walletClientType === "embedded") {
      walletType = "embedded";
    } else {
      walletType = wallet.walletClientType || "external";
    }
    
    const walletId = wallet.address || wallet.id || "";
    return `${walletType}:${walletId}`;
  }, []);

  // Track initialization state with a ref instead of triggering re-renders
  const hasAutoSelectedRef = useRef(false);
  
  // Auto-select first wallet with better logic
  useEffect(() => {
    // Only run once, and only if we have connected wallets but no selection
    if (hasAutoSelectedRef.current || selectedWallet || !connectedWallets.length) {
      return;
    }
    
    hasAutoSelectedRef.current = true;
    console.log("WalletSelector: Auto-selecting first wallet from Privy");
    
    // Delay to break the render cycle
    const timeoutId = setTimeout(() => {
      onSelectWallet(connectedWallets[0]);
    }, 0);
    
    return () => clearTimeout(timeoutId);
  }, [connectedWallets, selectedWallet, onSelectWallet]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative mb-3" ref={dropdownRef}>
      <div 
        className="bg-[#1D2226] p-3 rounded-lg cursor-pointer border border-gray-700 hover:border-gray-500 transition flex justify-between items-center"
        onClick={(e) => {
          e.stopPropagation();
          setIsOpen(!isOpen);
        }}
      >
        {selectedWallet && selectedWallet.address ? (
          <div className="flex items-center space-x-2">
            <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${
              isSolanaAddress(selectedWallet.address) ? "bg-[#9945FF]" : "bg-[#627EEA]"
            }`}>
              {isSolanaAddress(selectedWallet.address) ? "SOL" : "ETH"}
            </div>
            <div>
              <span className="text-white">Wallet</span>
              <div className="text-sm text-gray-400">{formatAddress(selectedWallet.address)}</div>
            </div>
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center">
              <span className="text-white text-xs">?</span>
            </div>
            <span className="text-white">Select Wallet</span>
          </div>
        )}
        <ChevronDownIcon className="w-5 h-5 text-white" />
      </div>

      {isOpen && (
        <div className="absolute mt-1 w-full bg-[#181C20] rounded-lg shadow-lg z-20 p-2 max-h-60 overflow-y-auto border border-gray-700">
          <div className="flex justify-between items-center bg-[#14FFA2]/10 p-2 rounded-t-md mb-2">
            <div className="text-white text-sm font-medium">Select Wallet</div>
            <div className="text-[#14FFA2] text-xs">{connectedWallets.length} Connected</div>
          </div>
          
          {connectedWallets.length > 0 ? (
            <div className="grid grid-cols-1 gap-1">
              {connectedWallets.map((wallet, index) => {
                const walletId = getWalletId(wallet);
                const walletType = walletId ? walletId.split(':')[0] : "unknown";
                const isSolana = isSolanaAddress(wallet.address);
                
                const isSelected = selectedWallet && selectedWallet.address === wallet.address;
                
                return (
                  <div 
                    key={index} 
                    className={`flex items-center justify-between p-2 rounded-lg cursor-pointer 
                      ${isSelected ? 'bg-[#2A3038] border border-[#14FFA2]' : 'bg-[#1D2226] hover:bg-[#2A3038] border border-gray-800'}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log(`Selecting wallet: ${formatAddress(wallet.address)}`);
                      
                      // Use a setTimeout to break any potential render cycles
                      setTimeout(() => {
                        onSelectWallet(wallet);
                        setIsOpen(false);
                      }, 0);
                    }}
                  >
                    <div className="flex items-center space-x-2">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${
                        isSolana ? "bg-[#9945FF]" : 
                        walletType === "embedded" ? "bg-[#6C5DD3]" :
                        "bg-[#627EEA]"
                      }`}>
                        {isSolana ? "SOL" : "ETH"}
                      </div>
                      <div>
                        <span className="text-white text-sm">{formatAddress(wallet.address)}</span>
                        <div className="flex items-center space-x-1">
                          <span className={`text-xs px-1.5 py-0.5 rounded-full ${
                            isSolana ? "bg-purple-900/30 text-purple-400" : 
                            walletType === "embedded" ? "bg-indigo-900/30 text-indigo-400" :
                            "bg-blue-900/30 text-blue-400"
                          }`}>
                            {isSolana ? "Solana" : "Ethereum"}
                          </span>
                          <span className="text-xs text-gray-500">
                            {walletType === "embedded" ? "Privy" : 
                             walletType === "phantom" ? "Phantom" : 
                             walletType}
                          </span>
                        </div>
                      </div>
                    </div>
                    {isSelected && (
                      <div className="bg-[#14FFA2] text-black text-xs px-1.5 py-0.5 rounded-full">
                        Active
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-3 text-gray-400">
              No wallets connected
            </div>
          )}
        </div>
      )}
    </div>
  );
});

// Add missing utility functions for formatting quote data
const formatPrice = (price: string | number | undefined): string => {
  if (!price || price === '--') return 'N/A';
  
  try {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    if (isNaN(numPrice)) return 'N/A';
    
    if (numPrice < 0.01) {
      return numPrice.toFixed(8);
    } else if (numPrice < 1) {
      return numPrice.toFixed(6);
    } else if (numPrice < 1000) {
      return numPrice.toFixed(4);
    } else {
      return numPrice.toFixed(2);
    }
  } catch (e) {
    console.error("Error formatting price:", e);
    return 'N/A';
  }
};

const formatTokenPair = (tokenA: TokenBase | null, tokenB: TokenBase | null): string => {
  if (!tokenA || !tokenB) return '';
  
  // Extract symbols safely
  const symbolA = tokenA.symbol || 'Unknown';
  const symbolB = tokenB.symbol || 'Unknown';
  
    return `${symbolA}/${symbolB}`;
};

// Get chain name from chain ID (complement to getChainIdFromName)
const getChainName = (chainId: number | string | undefined): string | null => {
  if (chainId === null || chainId === undefined) return null;
  
  const chainIdMap: Record<string | number, string> = {
    1: 'Ethereum',
    137: 'Polygon',
    56: 'BSC',
    43114: 'Avalanche',
    10: 'Optimism',
    42161: 'Arbitrum',
    8453: 'Base',
    'solana': 'Solana'
  };
  
  return chainIdMap[chainId] || `Chain #${chainId}`;
};

// ** Main Trading Panel **
const TradingPanel = ({ activeToken }: TradingPanelProps) => {
  // console.log("activeToken", activeToken.address);
  const [amount, setAmount] = useState<string>(''); // Changed from number to string to match NewTradingPanel
  // Consolidated TP/SL state variables
  const [isTakeProfit, setIsTakeProfit] = useState<boolean>(false);
  const [isStopLoss, setIsStopLoss] = useState<boolean>(false);
  const [takeProfit, setTakeProfit] = useState<string>(''); // Changed from string|number to string
  const [stopLoss, setStopLoss] = useState<string>(''); // Changed from string|number to string
  const [range, setRange] = useState<string>("0"); // Add missing range state for slider
  
  // Privy hooks and state
  const { user, authenticated, ready } = usePrivy();
  
  // Properly destructure the wallet methods from Privy
  // This ensures we're only using the Privy SDK directly
  const { wallets = [], connectWallet }: { 
    wallets: WalletBase[];
    connectWallet: (options?: any) => Promise<any> 
  } = useWallets() as any;
  
  // Log the available wallets for debugging
  useEffect(() => {
    if (wallets && wallets.length > 0) {
      console.log("TradingPanel: Privy wallets available:", wallets.map((w: any) => ({
        address: w.address ? `${w.address.slice(0, 6)}...${w.address.slice(-4)}` : 'unknown',
        type: w.walletClientType,
        connected: w.connectedAt ? true : false
      })));
    } else {
      console.log("TradingPanel: No wallets available from Privy");
    }
  }, [wallets]);
  
  const [coins, setCoins] = useState<TokenBase[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  
  // State for selected coin and trading data
  const [selectedCoin, setSelectedCoin] = useState<TokenBase | null>(null);
  const [quoteData, setQuoteData] = useState<QuoteData | null>(null);
  const [isLoadingQuote, setIsLoadingQuote] = useState<boolean>(false);
  const [dexQuotes, setDexQuotes] = useState<QuoteData[] | null>(null);
  const [bestDex, setBestDex] = useState<string | null>(null);
  const [expectedPrice, setExpectedPrice] = useState<string>("N/A");
  const [priceImpact, setPriceImpact] = useState<string | null>("N/A");
  const [showAllQuotes, setShowAllQuotes] = useState<boolean>(false);
  const [tokenPair, setTokenPair] = useState<string>('');
  const [outputAmount, setOutputAmount] = useState<string>('N/A');
  const [outputTokenSymbol, setOutputTokenSymbol] = useState<string>('');
  const [platformFee, setPlatformFee] = useState<any>(null);
  const [activeChainId, setActiveChainId] = useState<null>(null);
  const [statusMessage, setStatusMessage] = useState<string>("");
  const [realTimeBalances, setRealTimeBalances] = useState<Record<string, any>>({});
  const [inputValue, setInputValue] = useState<string>('');
  const [outputValue, setOutputValue] = useState<string>('');
  
  // Wallet selection state
  const [selectedWallet, setSelectedWallet] = useState<WalletBase | null>(null);
  
  // Track initialization state with a ref instead of triggering re-renders
  const isInitializedRef = useRef(false);
  const walletUpdateTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const hasCheckedCoinsRef = useRef<boolean>(false);
  const skipFirstRenderRef = useRef(true);
  const previousWalletsRef = useRef<string>('');
  const isFirstRenderRef = useRef(true);
  const isMountedRef = useRef(true);
  const isFetchingCoinsRef = useRef(false);
  const fetchTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const currentValueRef = useRef(''); // Add this ref for tracking input value
  
  // Add missing state variables for quote result
  const [quoteResult, setQuoteResult] = useState<null>(null);
  const [lastCalculatedAmount, setLastCalculatedAmount] = useState<string>("");
  const [isTokenTradeable, setIsTokenTradeable] = useState<boolean>(true);
  
  // Add a state to store the original pancakeswap quote data
  const [pancakeswapQuoteData, setPancakeswapQuoteData] = useState<PancakeswapQuoteData | null>(null);
  const [isPancakeswapPair, setIsPancakeswapPair] = useState<boolean>(false);
  
  // Add debounce functionality for input changes
  const debounceTimeoutRef = useRef<number | null>(null);
  
  // Add state for transaction status
  const [isTransacting, setIsTransacting] = useState<boolean>(false);
  const [transactionStatus, setTransactionStatus] = useState<string>("");

  // Add missing state variables 
  const [isSettingsOpen, setIsSettingsOpen] = useState<boolean>(false);
  const [slippageType, setSlippageType] = useState<string>("Auto");
  const [slippageValue, setSlippageValue] = useState<string>("0.5");
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  
  // Add missing state variables for Limit Tab
  const [price, setPrice] = useState<string>("");
  const [marketCap, setMarketCap] = useState<string>("");
  const [isPriceMode, setIsPriceMode] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<string>("Market");
  
  // Function to toggle between price and market cap mode
  const toggleMode = () => setIsPriceMode(!isPriceMode);
  
  // Toggle take profit handler
  const handleToggleTakeProfit = () => {
    setIsTakeProfit(!isTakeProfit);
    if (!isTakeProfit) {
      // Open the TP/SL modal when enabling
      setIsModalOpen(true);
    } else {
      // Reset values when turning off
      setTakeProfit('');
      setStopLoss('');
    }
  };

  // This is the only handleWalletSelection function we should have
  const handleWalletSelection = useCallback((wallet: WalletBase) => {
    if (!wallet || !wallet.address) {
      console.log("Invalid wallet passed to handleWalletSelection");
      return;
    }
    
    // Helper function to format addresses safely
    const formatWalletAddress = (address?: string) => {
      if (!address || typeof address !== 'string') return 'No Address';
      return address.length > 10 ? `${address.slice(0, 6)}...${address.slice(-4)}` : address;
    };
    
    // Log the selected wallet for debugging
    console.log("TradingPanel - Received wallet selection:", {
      address: formatWalletAddress(wallet.address),
      type: wallet.walletClientType || 'unknown',
      connected: !!wallet.connectedAt
    });
    
    // Skip unnecessary updates
    if (selectedWallet && selectedWallet.address && selectedWallet.address === wallet.address) {
      console.log("Selected wallet unchanged, skipping update");
      return;
    }
    
    console.log(`Setting selected wallet to: ${formatWalletAddress(wallet.address)} (${wallet.walletClientType || 'unknown type'})`);
    
    // Make sure we clear any existing data before changing wallets
    setCoins([]);
    setSelectedCoin(null);
    setStatusMessage("");
    hasCheckedCoinsRef.current = false;
    
    // Create a clean wallet object to avoid reference issues
    const cleanWallet: WalletBase = {
      id: wallet.id,
      address: wallet.address,
      walletClientType: wallet.walletClientType,
      connectedAt: wallet.connectedAt || new Date().toISOString(),
      connected: true,
      // Copy any other needed properties
      ethersProvider: wallet.ethersProvider,
      provider: wallet.provider,
      sendTransaction: wallet.sendTransaction,
      embedded: wallet.embedded
    };
    
    // Set the selected wallet - do this BEFORE fetching coin data
    setSelectedWallet(cleanWallet);
    
    try {
      // Clear any API caches
      if (walletAPI && typeof (walletAPI as any)._balancesCache?.clear === 'function') {
        (walletAPI as any)._balancesCache.clear();
        console.log("Cleared wallet API balance cache on wallet change");
      }
      
      // Also clear pending requests
      if (walletAPI && typeof (walletAPI as any)._pendingRequests?.clear === 'function') {
        (walletAPI as any)._pendingRequests.clear();
        console.log("Cleared wallet API pending requests on wallet change");
      }
    } catch (e) {
      console.warn("Error clearing cache:", e);
    }
    
    // Schedule a fetch of wallet data with a slight delay
    // This helps reduce race conditions with wallet selection
      if (walletUpdateTimeoutRef.current) {
      clearTimeout(walletUpdateTimeoutRef.current as unknown as number);
    }
    
    const timeoutId = setTimeout(() => {
      // Ensure the wallet was actually set before fetching
      if (cleanWallet.address) {
        console.log(`Fetching available coins for wallet: ${formatWalletAddress(cleanWallet.address)}`);
        fetchAvailableCoins();
      }
    }, 500);
    
    // Store timeout ID for cleanup
    walletUpdateTimeoutRef.current = timeoutId;
  }, [selectedWallet, wallets]);

  // Initialize selectedCoin with activeToken when it's available
  useEffect(() => {
    if (!activeToken || !authenticated || !coins || coins.length === 0) return;
    
    try {
        // Find if the activeToken is in our coins list
        const matchedCoin = coins.find(coin => {
          if (!coin || !activeToken) return false;
          
          const coinName = coin.name ? coin.name.toLowerCase() : '';
          const coinSymbol = coin.symbol ? coin.symbol.toLowerCase() : '';
          const tokenName = activeToken.name ? activeToken.name.toLowerCase() : '';
          const tokenSymbol = activeToken.symbol ? activeToken.symbol.toLowerCase() : '';
          
          return (coinName && tokenName && coinName === tokenName) || 
                 (coinSymbol && tokenSymbol && coinSymbol === tokenSymbol);
        });
        
        if (matchedCoin) {
          console.log(`Setting selected coin to match active token: ${activeToken.name || activeToken.symbol}`);
          setSelectedCoin(matchedCoin);
        } else {
          // If we don't have the exact match, try to find a token with same symbol
          const symbolMatch = coins.find(coin => {
            if (!coin || !activeToken || !coin.symbol || !activeToken.symbol) return false;
            return coin.symbol.toLowerCase() === activeToken.symbol.toLowerCase();
          });
          
          if (symbolMatch) {
            setSelectedCoin(symbolMatch);
        } else if (coins.length > 0) {
          // If no match found but we have coins, set the first coin as selected
          setSelectedCoin(coins[0]);
        }
      }
    } catch (error) {
      console.error("Error matching active token to coins:", error);
    }
  }, [activeToken, coins, authenticated]);

  // Update the useEffect that fetches token data
  useEffect(() => {
    // Skip when no coin is selected
    if (!selectedCoin) {
      return;
    }
    
    // Skip redundant fetches if this is the same coin we just fetched data for
    if (quoteData && quoteData.name === selectedCoin.name) {
      return;
    }
    
    // Skip if not authenticated
    if (!authenticated) {
      return;
    }
    
    // Skip if we're loading other data
    if (isLoadingQuote) {
      return;
    }

    // Only fetch if conditions are met
    fetchTokenData();

    // Cleanup function
    return () => {
      // Clear any pending debounce timeouts
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current as unknown as number);
        debounceTimeoutRef.current = null;
      }
    };
  }, [selectedCoin, authenticated, quoteData?.name, isLoadingQuote, coins, activeToken]);

  // When logout happens, clear the data
  useEffect(() => {
    const handleLogout = () => {
      console.log("TradingPanel: Logout detected, clearing wallet data");
      setCoins([]);
      // Reset our tracking refs on logout
      hasCheckedCoinsRef.current = false;
      previousWalletsRef.current = '';
    };
    
    // Listen for Privy logout events
    window.addEventListener("privy:logout", handleLogout);
    
    return () => {
      window.removeEventListener("privy:logout", handleLogout);
    };
  }, []);

  // Add isAmount state variable for the Amount/Qty toggle
  const [isAmount, setIsAmount] = useState<boolean>(true);

  // Helper function to check ERC20 token balances
  const checkERC20Balance = async (
    provider: ethers.providers.Provider, 
    walletAddress: string, 
    tokenAddress: string, 
    decimals: number
  ): Promise<string> => {
    try {
      const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, provider);
      const balance = await tokenContract.balanceOf(walletAddress);
      // Format the balance with proper decimals
      return ethers.utils.formatUnits(balance, decimals);
    } catch (error) {
      console.error("Error checking ERC20 balance:", error);
      return "0";
    }
  };

  // Simple helper for formatting addresses
  const formatAddressShort = (address?: string) => {
    if (!address || typeof address !== 'string') return 'No Address';
    return address.length > 10 ? `${address.slice(0, 6)}...${address.slice(-4)}` : address;
  };

  // Handler for connect wallet button
  const handleConnectWallet = () => {
      connectWallet();
  };

  // Update the connectPhantomWallet function
  const connectPhantomWallet = async () => {
    try {
        setStatusMessage("Connecting to Phantom wallet...");
      
      // Direct connection as fallback
      try {
        const response = await (window as any).phantom?.solana.connect();
        console.log("Connected to Phantom directly:", response);
        
        // After connection, refresh available coins
        await fetchAvailableCoins();
        
        setStatusMessage("Connected to Phantom wallet!");
      } catch (err) {
        console.error("Error connecting to Phantom directly:", err);
        setStatusMessage("Error connecting to Phantom. Please try again.");
      }
    } catch (error) {
      console.error("Error in connectPhantomWallet:", error);
      setStatusMessage("Failed to connect to Phantom wallet");
    }
  };

  const handleTpslSubmit = (data: { takeProfit: number; stopLoss: number }) => {
    // Set takeProfit and stopLoss
    setTakeProfit(data.takeProfit.toString());
    setStopLoss(data.stopLoss.toString());
    console.log("Received TPSL Data:", data);
    // Handle the data as needed
  };

  // Reset tradeable status when tokens change
  useEffect(() => {
    // Reset quote results when tokens change
    setQuoteResult(null);
    setOutputAmount("");
    setLastCalculatedAmount("");
    // Reset tradeable status - will be updated by fetchTokenData
    setIsTokenTradeable(true);
    
    if (selectedCoin && activeToken) {
      console.log(`TradingPanel: Both tokens present (${activeToken.symbol} and ${selectedCoin.symbol}), fetching data`);
      fetchTokenData();
    }
  }, [selectedCoin, activeToken]);

  // Add a new function to render quote details including PancakeSwap data if available
  const renderQuoteDetails = () => {
    if (!isTokenTradeable || !quoteData) return null;
    
    return (
      <div className="mt-2 p-3 bg-[#1D2226] rounded text-sm">
        <h4 className="text-white mb-2 font-medium">Trading Details:</h4>
        
        {/* Price Impact */}
        {quoteData.priceImpact !== undefined && (
          <div className="flex justify-between py-1 border-b border-gray-700">
            <span className="text-gray-400">Price Impact:</span>
            <span className={`${
              typeof quoteData?.priceImpact === 'string' 
                ? (parseFloat(quoteData.priceImpact) > 3 ? 'text-red-400' : 'text-green-400')
                : typeof quoteData?.priceImpact === 'number'
                  ? (quoteData.priceImpact > 3 ? 'text-red-400' : 'text-green-400')
                  : 'text-white'
            }`}>
              {typeof quoteData.priceImpact === 'string' 
                ? parseFloat(quoteData.priceImpact).toFixed(2) 
                : typeof quoteData.priceImpact === 'number' 
                  ? quoteData.priceImpact.toFixed(2) 
                  : quoteData.priceImpact
              }%
            </span>
          </div>
        )}
        
        {/* Exchange Rate */}
        <div className="flex justify-between py-1 border-b border-gray-700">
          <span className="text-gray-400">Exchange Rate:</span>
          <span className="text-white">
            1 {activeToken?.symbol || 'Token'} = {
              quoteData?.executionPrice 
                ? (typeof quoteData.executionPrice === 'string' 
                   ? parseFloat(quoteData.executionPrice).toFixed(6) 
                   : quoteData.executionPrice.toFixed(6))
                : (quoteData?.price 
                   ? (typeof quoteData.price === 'string'
                      ? parseFloat(quoteData.price).toFixed(6)
                      : quoteData.price.toFixed(6))
                   : 'N/A')
            } {quoteData?.symbol || ''}
          </span>
        </div>
        
        {/* Platform Fee if available */}
        {quoteData.platformFee && (
          <div className="flex justify-between py-1 border-b border-gray-700">
            <span className="text-gray-400">Platform Fee:</span>
            <span className="text-white">
              {formatPlatformFee()}
            </span>
          </div>
        )}
        
        {/* Liquidity Source */}
        <div className="flex justify-between py-1 border-b border-gray-700">
          <span className="text-gray-400">Liquidity Source:</span>
          <span className="text-white capitalize">
            {quoteData.dex || 'PancakeSwap'} ({quoteData.chain || 'Bsc'})
          </span>
        </div>
        
        {/* Router Address for PancakeSwap */}
        {quoteData.routerAddress && (
          <div className="flex justify-between py-1 border-b border-gray-700">
            <span className="text-gray-400">Router:</span>
            <span className="text-xs text-gray-300 truncate max-w-[200px]">
              {quoteData.routerAddress.slice(0, 6)}...{quoteData.routerAddress.slice(-4)}
            </span>
          </div>
        )}
        
        {/* Route if available */}
        {quoteData.path && quoteData.path.length > 0 && (
          <div className="flex justify-between py-1">
            <span className="text-gray-400">Route:</span>
            <span className="text-xs text-gray-300 truncate max-w-[200px]">
              {quoteData.path.map((addr, i) => (
                <span key={i}>
                  {addr.slice(0, 6)}...{addr.slice(-4)}
                  {quoteData.path && i < quoteData.path.length - 1 ? ' → ' : ''}
                </span>
              ))}
            </span>
          </div>
        )}
      </div>
    );
  };

  // Filter coins by chain ID
  const getFilteredCoins = () => {
    if (!activeChainId || !coins || coins.length === 0) return coins;
    
    // Include coins from the selected chain and those without chain info
    return coins.filter(coin => 
      !coin.chain || 
      coin.chain === 'Various' || 
      getChainIdFromName(coin.chain) === activeChainId
    );
  };

  // Helper to convert chain name to chain ID
  const getChainIdFromName = (chainName: string): number | string | null => {
    if (!chainName) return null;
    
    const chainMap: Record<string, number | string> = {
      'Ethereum': 1,
      'Polygon': 137,
      'BSC': 56,
      'Avalanche': 43114,
      'Optimism': 10,
      'Arbitrum': 42161,
      'Base': 8453,
      'Solana': 'solana'
    };
    
    return chainMap[chainName] || null;
  };

  // Format the output display with the correct token symbols
  const formatOutputDisplay = () => {
    if (!outputAmount || outputAmount === 'N/A' || outputAmount === '') {
      return `~ 0 ${selectedCoin?.symbol || 'Token'}`;
    }
    
    // Use the selected coin's symbol as it's what we're buying/selling
    const displaySymbol = selectedCoin?.symbol || 'Token';
    
    // Display the output amount with the correct token symbol
    return `~ ${outputAmount} ${displaySymbol}`;
  };

  // Add this function to check and approve token allowances
  const checkAndApproveTokenAllowance = async (
    token: TokenBase, 
    routerAddress: string, 
    amount: string | number
  ): Promise<{ success: boolean; message: string }> => {
    try {
      if (!selectedWallet || !token || !routerAddress) {
        return { success: false, message: "Missing wallet, token, or router address" };
      }
      
      console.log(`Checking allowance for ${token.symbol} to router ${routerAddress}`);
      
      // Skip allowance check for native tokens (ETH, BNB, etc.)
      if (token.symbol === 'ETH' || token.symbol === 'BNB' || token.symbol === 'MATIC') {
        console.log(`${token.symbol} is a native token, no approval needed`);
        return { success: true, message: "Native token, no approval needed" };
      }
      
      // Get token address
      const tokenAddress = token.address || token.tokenAddress || token.contract;
      if (!tokenAddress) {
        return { success: false, message: `No address found for ${token.symbol}` };
      }
      
      // Get token decimals
      const decimals = token.decimals || 18;
      
      // Get user address from wallet
      const userAddress = selectedWallet.address;
      
      // Create a provider from the wallet
      let provider;
      if (selectedWallet.ethersProvider) {
        provider = selectedWallet.ethersProvider;
      } else if (window.ethereum) {
        provider = new ethers.providers.Web3Provider(window.ethereum);
      } else {
        return { success: false, message: "No provider available" };
      }
      
      // Create token contract instance
      const tokenContract = new ethers.Contract(
        tokenAddress,
        [
          "function allowance(address owner, address spender) view returns (uint256)",
          "function approve(address spender, uint256 amount) returns (bool)"
        ],
        provider.getSigner()
      );
      
      // Check current allowance
      const currentAllowance = await tokenContract.allowance(userAddress, routerAddress);
      console.log(`Current allowance: ${ethers.utils.formatUnits(currentAllowance, decimals)} ${token.symbol}`);
      
      // Convert amount to wei
      const amountInWei = ethers.utils.parseUnits(amount.toString(), decimals);
      
      // If allowance is insufficient, request approval
      if (currentAllowance.lt(amountInWei)) {
        console.log(`Insufficient allowance, requesting approval for ${token.symbol}`);
        setTransactionStatus(`Requesting approval for ${token.symbol}...`);
        
        // Use max uint256 for unlimited approval
        const maxUint256 = ethers.constants.MaxUint256;
        
        // Send approval transaction
        const approveTx = await tokenContract.approve(routerAddress, maxUint256);
        setTransactionStatus(`Waiting for approval transaction...`);
        
        // Wait for transaction to be mined
        await approveTx.wait();
        console.log(`Approval successful for ${token.symbol}`);
        return { success: true, message: "Token already has sufficient allowance" };
      } else {
        console.log(`${token.symbol} already has sufficient allowance`);
        return { success: true, message: "Token already has sufficient allowance" };
      }
    } catch (error: unknown) {
      console.error("Error checking/approving token allowance:", error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return { 
        success: false, 
        message: errorMessage || `Failed to approve ${token?.symbol || 'token'}` 
      };
    }
  };

  // Update the handleTrade function to handle allowance checks
  const handleTrade = async (action: 'buy' | 'sell') => {
    console.log(`Trade action triggered: ${action}`);
    console.log('Current state:', {
      authenticated,
      selectedWallet: selectedWallet?.address,
      selectedCoin: selectedCoin?.symbol,
      activeToken: activeToken?.symbol,
      inputValue,
      isPancakeswapPair,
      pancakeswapQuoteData: !!pancakeswapQuoteData
    });

    if (!authenticated || !selectedWallet) {
      setStatusMessage("Please connect your wallet first");
      return;
    }

    if (!selectedCoin || !activeToken) {
      setStatusMessage("Please select tokens to trade");
      return;
    }

    if (!inputValue || parseFloat(inputValue) <= 0) {
      setStatusMessage("Please enter a valid amount");
      return;
    }

    // For production use, remove this testing code
    if (!isPancakeswapPair || !pancakeswapQuoteData) {
      setStatusMessage("This pair is not supported on PancakeSwap");
      return;
    }

    try {
      setIsTransacting(true);
      setTransactionStatus("Preparing transaction...");

      // Determine which is the base and quote token based on the action
      // IMPORTANT: For PancakeSwap, we need to be consistent with their API expectations
      // tokenIn: The token you're selling (sending from your wallet)
      // tokenOut: The token you're buying (receiving to your wallet)
      let tokenIn, tokenOut, amount;
      
      if (action === 'buy') {
        // For buy: we're spending selectedCoin to buy activeToken
        tokenIn = selectedCoin;  // What we're selling (e.g., USDT)
        tokenOut = activeToken;  // What we're buying (e.g., BTC)
        amount = inputValue;
        console.log(`Buy action: sending ${amount} ${tokenIn.symbol} to receive ${tokenOut.symbol}`);
      } else {
        // For sell: we're spending activeToken to buy selectedCoin
        tokenIn = activeToken;   // What we're selling (e.g., BTC)
        tokenOut = selectedCoin; // What we're buying (e.g., USDT)
        amount = inputValue;
        console.log(`Sell action: sending ${amount} ${tokenIn.symbol} to receive ${tokenOut.symbol}`);
      }

      // Log the configured tokens for the trade
      console.log('Trade configuration:', {
        tokenIn: {
          symbol: tokenIn.symbol,
          address: tokenIn.address || tokenIn.contract || tokenIn.tokenAddress
        },
        tokenOut: {
          symbol: tokenOut.symbol,
          address: tokenOut.address || tokenOut.contract || tokenOut.tokenAddress
        },
        amount,
        recipient: selectedWallet.address,
        slippageTolerance: parseFloat(slippageValue) || 0.5
      });

      // Get the recipient address (user's address)
      const recipient = selectedWallet.address;

      // Set slippage from the settings
      const slippageTolerance = parseFloat(slippageValue) || 0.5;

      // Check if the wallet has the proper transaction capabilities
      if (!selectedWallet.sendTransaction && 
          !selectedWallet.ethersProvider && 
          !selectedWallet.provider && 
          !window.ethereum &&
          !selectedWallet.embeddedWallet &&
          !selectedWallet.walletClient) {
        setStatusMessage("Your wallet doesn't support direct transactions. Please use a web3 wallet.");
        setIsTransacting(false);
        return;
      }

      // Get PancakeSwap router address from quote data or use default
      const routerAddress = quoteData?.routerAddress || "******************************************";
      
      // Check and approve token allowance for the token we're selling (tokenIn)
      // Skip for native tokens (ETH, BNB)
      if (tokenIn.address && tokenIn.symbol !== 'ETH' && tokenIn.symbol !== 'BNB') {
        setTransactionStatus(`Checking allowance for ${tokenIn.symbol}...`);
        const allowanceResult = await checkAndApproveTokenAllowance(tokenIn, routerAddress, amount);
        
        if (!allowanceResult.success) {
          setStatusMessage(`Allowance error: ${allowanceResult.message}`);
          setIsTransacting(false);
          return;
        }
      }

      setTransactionStatus("Executing trade on PancakeSwap...");
      
      // IMPORTANT: For completeTradePancakeswap:
      // First arg = tokenOut (what we're buying)
      // Second arg = tokenIn (what we're selling)
      const tradeResult = await completeTradePancakeswap(
        tokenOut,    // The token we're buying
        tokenIn,     // The token we're selling
        amount,      // Amount of tokenIn we're spending
        recipient || selectedWallet?.address || '',   // Our wallet address to receive tokens
        selectedWallet,
        {
          slippageTolerance: slippageTolerance,
          pairAddress: pancakeswapQuoteData.pairAddress,
          gasLimit: 500000 // Add a manual gas limit to avoid estimation errors
        }
      );

      console.log('Trade result:', tradeResult);

      // Check if we need token allowance approval
      if (!tradeResult.success && tradeResult.needsAllowance) {
        setStatusMessage(`Failed: Token approval required. Please try again.`);
      } else if (tradeResult.success) {
        // Clear the input value
        setInputValue('');
        setOutputAmount('');
        
        // Show success message with transaction hash
        const txHash = tradeResult.data.transaction?.transactionHash;
        setStatusMessage(
          `Trade successful! ${txHash ? `Transaction: ${txHash.slice(0, 6)}...${txHash.slice(-4)}` : ''}`
        );
        
        // After a successful trade, fetch updated token data
        setTimeout(() => {
          fetchTokenData();
        }, 3000);
      } else {
        // Display the error message
        setStatusMessage(`Trade failed: ${tradeResult.message}`);
      }
    } catch (error: unknown) {
      console.error('Trade execution error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setStatusMessage(`Transaction error: ${errorMessage}`);
    } finally {
      setIsTransacting(false);
      setTransactionStatus("");
    }
  };

  // Render Buy Button with conditional styling
  const renderBuyButton = () => {
    const canBuy = hasSufficientFunds('buy') && !isLoadingQuote && inputValue && parseFloat(inputValue) > 0;
    const noLiquidityPool = !isLoadingQuote && inputValue && parseFloat(inputValue) > 0 && (!dexQuotes || dexQuotes.length === 0);
    
    // Remove duplicate "TOKEN NOT AVAILABLE" message from individual buttons
    // and let a single message handle it at the parent level
    return (
      <div className="flex-1 flex flex-col">
        <div 
          className={`py-4 px-3 rounded-lg font-bold text-center
            ${!isTokenTradeable 
              ? "text-gray-500 border-2 border-gray-700 bg-gray-800 cursor-not-allowed opacity-70"
              : canBuy && !noLiquidityPool
              ? "text-[#14FFA2] border-2 border-[#14FFA2] bg-[#214638] cursor-pointer" 
              : "text-gray-500 border-2 border-gray-700 bg-gray-800 cursor-not-allowed opacity-70"
            }`}
          onClick={() => {
            if (isTokenTradeable && canBuy && !noLiquidityPool) {
              console.log("Buy action triggered");
              handleTrade('buy');
            }
          }}
        >
          {isTransacting ? "PROCESSING..." :
           !isTokenTradeable ? "BUY" :
           isLoadingQuote ? "LOADING..." : 
           noLiquidityPool ? "LIQUIDITY POOL NOT AVAILABLE" : 
           canBuy ? "BUY" : `INSUFFICIENT ${activeToken?.symbol || 'FUNDS'}`}
        </div>
        
        {/* Show supported pools message when no liquidity pool is available */}
        {noLiquidityPool && isTokenTradeable && (
          <div className="text-xs text-gray-400 mt-1 text-center">
            Supported pools: {getSupportedPoolsMessage()}
          </div>
        )}
      </div>
    );
  };

  // Render Sell Button with conditional styling
  const renderSellButton = () => {
    const canSell = hasSufficientFunds('sell') && !isLoadingQuote && inputValue && parseFloat(inputValue) > 0;
    const noLiquidityPool = !isLoadingQuote && inputValue && parseFloat(inputValue) > 0 && (!dexQuotes || dexQuotes.length === 0);
    
    // Remove duplicate "TOKEN NOT AVAILABLE" message from individual buttons
    // and let a single message handle it at the parent level
    return (
      <div className="flex-1 flex flex-col">
        <div 
          className={`py-4 px-3 rounded-lg font-bold text-center
            ${!isTokenTradeable 
              ? "text-gray-500 border-2 border-gray-700 bg-gray-800 cursor-not-allowed opacity-70"
              : canSell && !noLiquidityPool 
              ? "text-[#FF329B] border-2 border-[#FF329B] bg-[#311D27] cursor-pointer" 
              : "text-gray-500 border-2 border-gray-700 bg-gray-800 cursor-not-allowed opacity-70"
            }`}
          onClick={() => {
            if (isTokenTradeable && canSell && !noLiquidityPool) {
              console.log("Sell action triggered");
              handleTrade('sell');
            }
          }}
        >
          {isTransacting ? "PROCESSING..." :
           !isTokenTradeable ? "SELL" :
           isLoadingQuote ? "LOADING..." : 
           noLiquidityPool ? "LIQUIDITY POOL NOT AVAILABLE" : 
           canSell ? "SELL" : `INSUFFICIENT ${selectedCoin?.symbol || 'FUNDS'}`}
        </div>
        
        {/* Show supported pools message when no liquidity pool is available */}
        {noLiquidityPool && isTokenTradeable && (
          <div className="text-xs text-gray-400 mt-1 text-center">
            Supported pools: {getSupportedPoolsMessage()}
          </div>
        )}
      </div>
    );
  };

  // Improved function to check if user has sufficient funds
  const hasSufficientFunds = (action: 'buy' | 'sell'): boolean => {
    if (!authenticated || !selectedCoin || !inputValue || isNaN(parseFloat(inputValue)) || parseFloat(inputValue) <= 0) {
      return false;
    }
    
    const numericValue = parseFloat(inputValue);
    
    if (action === 'buy') {
      // For buying, check if the user has the active token 
      // (which is what we're exchanging for the selected coin)
      if (!activeToken || !activeToken.balance) {
        console.log(`Buy check: No active token balance available`);
        return false;
      }
      
      try {
        const activeTokenBalance = parseFloat(activeToken.balance);
        const hasEnough = activeTokenBalance >= numericValue;
        console.log(`Buy check: Balance ${activeTokenBalance} ${activeToken.symbol}, need ${numericValue}, sufficient: ${hasEnough}`);
        return hasEnough;
      } catch (error) {
        console.error('Error checking buy funds:', error);
        return false;
      }
    } else if (action === 'sell') {
      // For selling, check if the user has enough of the selected coin
      if (!selectedCoin.balance) {
        console.log(`Sell check: No selected coin balance available`);
        return false;
      }
      
      try {
        const coinBalance = parseFloat(selectedCoin.balance);
        const hasEnough = coinBalance >= numericValue;
        console.log(`Sell check: Balance ${coinBalance} ${selectedCoin.symbol}, need ${numericValue}, sufficient: ${hasEnough}`);
        return hasEnough;
      } catch (error) {
        console.error('Error checking sell funds:', error);
        return false;
      }
    }
    
    return false;
  };

  // Add a function to show supported pools for the current chain
  const getSupportedPoolsMessage = () => {
    // Determine chain for activeToken if we have the info
    let chainId = activeChainId;
    
    // Use a simplified chain name based on the chainId
    let chainName = "unknown";
    if (chainId === 1 || chainId === 'ethereum' || chainId === 'eth') {
      chainName = "Ethereum";
    } else if (chainId === 56 || chainId === 'bsc') {
      chainName = "BSC";
    } else if (chainId === 'solana') {
      chainName = "Solana";
    }
    
    // Map of supported DEXes by chain
    const supportedDexes = {
      "Ethereum": "Uniswap, SushiSwap",
      "BSC": "PancakeSwap",
      "Solana": "Raydium, Meteora"
    };
    
    return supportedDexes[chainName as keyof typeof supportedDexes] || "No pools available for this token";
  };

  // Add fetchTokenData function
  const fetchTokenData = async () => {
    try {
      if (!selectedCoin || !activeToken) {
        console.log("Missing selectedCoin or activeToken, can't fetch token data");
        return;
      }

      console.log(`Fetching token data for ${selectedCoin.name || selectedCoin.symbol}`);
      
      // Change the parameter order - activeToken first, selectedCoin second
      // activeToken is the base token we want to find pairs for
      // selectedCoin is the token we want to check if it's a valid trading pair
      console.log(`Checking if ${activeToken.symbol} can be traded with ${selectedCoin.symbol}`);
      
      const response = await getQuoteDataForToken(activeToken, selectedCoin);
      console.log('Quote data:', response);
      
      // Process the response
      setIsTokenTradeable(response.success);
      if (response.success) {
        setQuoteData(response.data);
        setStatusMessage("");
      } else {
        setStatusMessage(response.message || "Token not available for trading");
        setIsTokenTradeable(false);
      }
    } catch (error) {
      console.error("Error fetching token data:", error);
      setStatusMessage("Error fetching token data");
      setIsTokenTradeable(false);
    }
  };

  // Improved fetchAvailableCoins function with better error handling
  const fetchAvailableCoins = async () => {
    if (isFetchingCoinsRef.current) {
      console.log("Already fetching coins, skipping");
      return;
    }
    
    isFetchingCoinsRef.current = true;
    setIsLoading(true);
    
    try {
      if (!authenticated) {
        console.log("Not authenticated, skipping fetch");
        setCoins([]);
        setIsLoading(false);
        return;
      }
      
      // Get wallet address from selected wallet
      const walletAddress = selectedWallet?.address;
      if (!walletAddress) {
        console.log("No wallet address available for fetching balances");
        setCoins([]);
        setIsLoading(false);
        return;
      }
      
      console.log(`Fetching balances for wallet: ${walletAddress.slice(0, 6)}...${walletAddress.slice(-4)}`);
      
      // Fetch balances from API
      const response = await walletAPI.getAllBalances(walletAddress);
      
      // Check for successful response
      if (!response.success) {
        console.error("Error fetching balances:", response.message);
        setStatusMessage(`Failed to load tokens: ${response.message || "Unknown error"}`);
        setCoins([]);
        setIsLoading(false);
        return;
      }
      
      console.log("Received balance data from API");
      
      // Process the tokens from response
      const processedCoins: TokenBase[] = [];
      
      if (response.data && response.data.balances) {
        // Process Ethereum tokens
        if (response.data.balances.ethereum && Array.isArray(response.data.balances.ethereum)) {
          response.data.balances.ethereum.forEach((token: any) => {
            // Only add tokens with positive balances and valid data
            if (token && token.balance && parseFloat(token.balance) > 0) {
              processedCoins.push({
                name: token.name || token.symbol || "Unknown",
                symbol: token.symbol || "UNKNOWN",
                address: token.address || "",
                decimals: token.decimals || 18,
                balance: token.balance,
                chain: 'Ethereum',
                chainId: 1
              });
            }
          });
        } else {
          console.log("No Ethereum tokens found or invalid Ethereum tokens array");
        }
        
        // Process BSC tokens
        if (response.data.balances.bsc && Array.isArray(response.data.balances.bsc)) {
          response.data.balances.bsc.forEach((token: any) => {
            if (token && token.balance) {
              processedCoins.push({
                name: token.name || token.symbol || "Unknown",
                symbol: token.symbol || "UNKNOWN",
                address: token.tokenAddress || token.address || "",
                decimals: token.decimals || 18,
                balance: token.balance,
                chain: 'BSC',
                chainId: 56
              });
            }
          });
        } else {
          console.log("No BSC tokens found or invalid BSC tokens array");
        }
        
        // Process Solana tokens
        if (response.data.balances.solana && Array.isArray(response.data.balances.solana)) {
          response.data.balances.solana.forEach((token: any) => {
            if (token && token.balance && parseFloat(token.balance) > 0) {
              processedCoins.push({
                name: token.name || token.symbol || "Unknown",
                symbol: token.symbol || "UNKNOWN",
                address: token.address || token.mint || "",
                decimals: token.decimals || 9,  // Solana default is 9 decimals
                balance: token.balance,
                chain: 'Solana',
                chainId: 'solana'
              });
            }
          });
        } else {
          console.log("No Solana tokens found or invalid Solana tokens array");
        }
      } else {
        console.warn("Invalid or empty balance data in response", response);
      }
      
      console.log(`Found ${processedCoins.length} tokens with non-zero balances:`);
      processedCoins.forEach(coin => {
        console.log(`- ${coin.symbol}: ${coin.balance} (${coin.chain})`);
      });
      
      setCoins(processedCoins);
      
      // Auto-select a coin if we have them but none is selected
      if (processedCoins.length > 0 && !selectedCoin) {
        console.log(`Auto-selecting first available coin: ${processedCoins[0].symbol}`);
        setSelectedCoin(processedCoins[0]);
      }
      
      setStatusMessage("");
    } catch (error) {
      console.error("Error fetching available coins:", error);
      setStatusMessage(`Failed to load tokens: ${error instanceof Error ? error.message : "Unknown error"}`);
      setCoins([]);
    } finally {
      setIsLoading(false);
      isFetchingCoinsRef.current = false;
    }
  };

  // Add formatPlatformFee function
  const formatPlatformFee = (): string => {
    if (!platformFee) {
      return "N/A";
    }
    
    // Check if platformFee is an object with amount and token properties
    if (typeof platformFee === 'object' && 'amount' in platformFee) {
      // Format with token symbol and percentage
      const feeAmount = platformFee.amount;
      
      // Get token symbol from the object or use the selected coin's symbol
      let tokenSymbol;
      if (typeof platformFee.token === 'object' && platformFee.token && 'symbol' in platformFee.token) {
        tokenSymbol = platformFee.token.symbol;
      } else if (platformFee.token && typeof platformFee.token === 'string') {
        // If it's just an address, we'll use the quoteData symbol
        tokenSymbol = quoteData?.symbol || 'tokens';
      } else {
        tokenSymbol = quoteData?.symbol || 'tokens';
      }
      
      // If percentage is available, include it
      if ('percentage' in platformFee && platformFee.percentage !== undefined) {
        const percentValue = typeof platformFee.percentage === 'string' 
          ? parseFloat(platformFee.percentage) 
          : platformFee.percentage;
        
        const percentFormatted = (typeof percentValue === 'number' ? percentValue * 100 : 0).toFixed(1);
        return `${feeAmount} ${tokenSymbol} (${percentFormatted}%)`;
      }
      
      return `${feeAmount} ${tokenSymbol}`;
    }
    
    // Handle legacy format (number or string)
    let legacyFeeValue = platformFee;
    if (typeof platformFee === 'string') {
      legacyFeeValue = parseFloat(platformFee);
    }
    
    if (typeof legacyFeeValue === 'number' && !isNaN(legacyFeeValue)) {
      return `${legacyFeeValue.toFixed(2)}%`;
    }
    
    return "N/A";
  };

  // Add fetchQuote function
  const fetchQuote = async (amount: string) => {
    if (!selectedCoin || !activeToken || !amount || parseFloat(amount) <= 0) {
      return;
    }
    
    try {
      console.log(`Fetching quote for ${amount} ${selectedCoin.symbol} to ${activeToken.symbol}`);
      setIsLoadingQuote(true);
      
      // Use getBestDEXQuote to get quotes from multiple DEXes
      const response = await getBestDEXQuote(activeToken, selectedCoin, amount);
      console.log('Quote response:', response);
      
      if (response.success && response.data) {
        // Store all quotes
        setDexQuotes(response.data);
        
        // Get the best quote
        if (response.bestQuote) {
          const bestQuote = response.bestQuote;
          console.log('Best quote data:', bestQuote);
          
          // Set quote data
          setQuoteData({
            name: activeToken.name || activeToken.symbol || 'Unknown',
            symbol: activeToken.symbol || 'UNKNOWN',
            price: bestQuote.price || bestQuote.executionPrice,
            executionPrice: bestQuote.executionPrice,
            priceImpact: bestQuote.priceImpact || bestQuote.quote?.priceImpact,
            dex: bestQuote.dex || 'pancakeswap',
            chain: bestQuote.chain || 'bsc',
            routerAddress: bestQuote.routerAddress,
            path: bestQuote.path,
            pairAddress: bestQuote.pairAddress,
          });
          
          // Store the PancakeSwap specific data
          if (bestQuote.dex === 'pancakeswap' || bestQuote.dexId === 'pancakeswap') {
            setPancakeswapQuoteData({
              pairAddress: bestQuote.pairAddress || '',
              // Add the required properties according to the PancakeswapQuoteData type
              baseToken: {
                address: activeToken.address || activeToken.tokenAddress || '',
                decimals: activeToken.decimals || 18,
                symbol: activeToken.symbol || '',
                name: activeToken.name || activeToken.symbol || ''
              },
              quoteToken: {
                address: selectedCoin.address || selectedCoin.tokenAddress || '',
                decimals: selectedCoin.decimals || 18,
                symbol: selectedCoin.symbol || '',
                name: selectedCoin.name || selectedCoin.symbol || ''
              }
            });
          }
          
          // Set token as tradeable
          setIsTokenTradeable(true);
          
          // Update the output amount calculation
          if (bestQuote.quote && (bestQuote.quote.amountOut || bestQuote.quote.outputAmount)) {
            const outputAmount = bestQuote.quote.amountOut || bestQuote.quote.outputAmount;
            setOutputAmount(outputAmount.toString());
            setExpectedPrice(`${outputAmount} ${activeToken.symbol || ''}`);
          } else if (bestQuote.price || bestQuote.executionPrice) {
            const price = bestQuote.price || bestQuote.executionPrice;
            const priceNum = typeof price === 'string' ? parseFloat(price) : price;
            const outputAmount = parseFloat(amount) * priceNum;
            const formattedOutput = outputAmount.toFixed(6);
            setOutputAmount(formattedOutput);
            setExpectedPrice(`${formattedOutput} ${activeToken.symbol || ''}`);
          }
        }
      } else {
        // Handle no quotes
        setDexQuotes([]);
        setQuoteData(null);
        setIsTokenTradeable(false);
        console.log('No quotes available:', response.message);
      }
    } catch (error) {
      console.error('Error fetching quote:', error);
      setStatusMessage(`Quote error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsTokenTradeable(false);
    } finally {
      setIsLoadingQuote(false);
    }
  };

  // Update the fixedHandleInputChange function
  const fixedHandleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    
    // Allow numbers, decimals, and empty strings
    if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
      setInputValue(value);
      
      // Skip further processing if value is empty or 0
      if (value === '' || parseFloat(value) === 0) {
        setOutputAmount('');
        setExpectedPrice('N/A');
        return;
      }
      
      // Only process if we have tokens selected and they are tradeable
      if (!selectedCoin || !activeToken) {
        return;
      }
      
      // Clear any existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current as unknown as number);
      }
      
      // Set a debounce timeout to avoid too many quote requests
      debounceTimeoutRef.current = setTimeout(() => {
        // Use the imported fetchQuote utility
        fetchQuote({
          amount: value,
          selectedCoin,
          activeToken,
          setIsLoadingQuote,
          setDexQuotes,
          setQuoteData,
          setPancakeswapQuoteData,
          setIsTokenTradeable,
          setOutputAmount,
          setExpectedPrice,
          setStatusMessage
        });
      }, 500) as unknown as number; // 500ms debounce
      
      // Update output amount based on current price if we already have it
      if (quoteData && quoteData.price) {
        const price = typeof quoteData.price === 'string'
          ? parseFloat(quoteData.price)
          : quoteData.price;
        const outputAmount = parseFloat(value) * price;
        const formattedOutput = outputAmount.toFixed(6);
        setOutputAmount(formattedOutput);
        setExpectedPrice(`${formattedOutput} ${quoteData.symbol || ''}`);
      } else {
        // We'll get the output from the quote response
        setOutputAmount('Calculating...');
        setExpectedPrice('Fetching price...');
      }
    }
  };

  // Add renderAvailableOptions function
  const renderAvailableOptions = () => {
    const availableOptions = quoteData && 'availableOptions' in quoteData 
      ? (quoteData.availableOptions as string[]) || []
      : [];
    
    if (!availableOptions || availableOptions.length === 0) {
      return null;
    }

    return (
      <div className="mt-2 p-3 bg-[#1D2226] rounded text-sm">
        <h4 className="text-white mb-2">Available Trading Options:</h4>
        <div className="flex flex-wrap gap-2">
          {availableOptions.slice(0, 8).map((token: string, index: number) => (
            <div 
              key={index} 
              className="px-2 py-1 bg-[#292d32] rounded-md text-blue-400 hover:bg-[#353a40] cursor-pointer"
              onClick={() => handleSelectAvailableToken(token)}
            >
              {token}
            </div>
          ))}
          {availableOptions.length > 8 && (
            <div className="text-gray-400 text-xs mt-1 ml-1">
              +{availableOptions.length - 8} more
            </div>
          )}
        </div>
      </div>
    );
  };

  // Add handleSelectAvailableToken function
  const handleSelectAvailableToken = (tokenSymbol: string) => {
    // Find the token in the coins list
    const token = coins.find(coin => 
      coin.symbol && coin.symbol.toUpperCase() === tokenSymbol.toUpperCase()
    );
    
    if (token) {
      console.log(`Selecting available token: ${tokenSymbol}`);
      setSelectedCoin(token);
    } else {
      console.log(`Token ${tokenSymbol} not found in available coins`);
      setStatusMessage(`${tokenSymbol} is available for trading, but not in your list. Please add it first.`);
    }
  };

  return (
    <div className="h-full w-full p-3 rounded-xl text-white flex flex-col overflow-y-auto">
      {/* Settings Modal */}
      <SettingsModal
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        slippageType={slippageType}
        setSlippageType={setSlippageType}
        slippageValue={slippageValue}
        setSlippageValue={setSlippageValue}
      />

      {/* Market & Limit Tabs */}
      <div className="flex w-full justify-between items-center mb-4">
      <div className="flex space-x-4">
    <button
      className={`font-bold ${activeTab === "Market" ? "text-white  border-b-2 border-white " : "text-[#BBBBBB]"}`}
      onClick={() => setActiveTab("Market")}
    >
      Market
    </button>
    <button
      className={`font-bold ${activeTab === "Limit" ? "text-white border-b-2 border-white" : "text-[#BBBBBB]"}`}
      onClick={() => setActiveTab("Limit")}
    >
      Limit
    </button>
  </div>

        {/* Auto & Settings Buttons */}
        <div className="flex space-x-2">
          <div className="flex items-center bg-[#141416] rounded-lg p-1.5">
            <button 
              className={`px-2 py-1.5 rounded-md font-medium text-xs ${isPriceMode ? "bg-[#14FFA2] text-black" : "text-gray-400"}`}
              onClick={() => setIsPriceMode(true)}
            >
              Price
          </button>
            <button 
              className={`px-2 py-1.5 rounded-md font-medium text-xs ${!isPriceMode ? "bg-[#14FFA2] text-black" : "text-gray-400"}`}
              onClick={() => setIsPriceMode(false)}
            >
              Slippage
            </button>
          </div>
          
          <button
            onClick={() => setIsSettingsOpen(true)}
            className="bg-[#151515] rounded-lg p-1.5"
          >
            <IoMdSettings className="text-[#BBBBBB] text-xl" />
          </button>
        </div>
      </div>

      {!authenticated && (
        <div className="flex flex-col gap-2 mb-4">
          <button
            onClick={handleConnectWallet}
            className="bg-gradient-to-r from-[#14FFA2] to-[#32E6A4] text-black font-bold py-3 px-4 rounded-lg hover:opacity-90 transition w-full"
          >
            Connect Wallet
          </button>
          
          {/* Add Phantom-specific connection button */}
          <button
            onClick={connectPhantomWallet}
            className="bg-gradient-to-r from-[#9945FF] to-[#8752F3] text-white font-bold py-3 px-4 rounded-lg hover:opacity-90 transition w-full flex items-center justify-center gap-2"
          >
            <img 
              src="https://phantom.app/img/logos/phantom-icon-purple.png" 
              alt="Phantom" 
              className="w-5 h-5" 
            />
            Connect with Phantom
          </button>
        </div>
      )}
      
      {statusMessage && (
        <div className={`mb-2 px-3 py-2 rounded-lg text-sm ${statusMessage.includes("Error") ? "bg-red-900/30 text-red-400" : "bg-blue-900/30 text-blue-400"}`}>
          {statusMessage}
        </div>
      )}
      
      {/* Add the WalletSelector component before BalanceDropdown */}
      {authenticated && (
        <CustomWalletSelector 
          onSelectWallet={handleWalletSelection}
          selectedWallet={selectedWallet}
          onConnectWallet={connectWallet}
        />
      )}
      
      {/* Balance Dropdown - only accessible when authenticated */}
      {authenticated && selectedWallet ? (
      <BalanceDropdown 
          coins={(() => {
            const mappedCoins = coins.map(token => ({
              name: token.name || token.symbol || "Unknown Token",
              symbol: token.symbol || "UNKNOWN",
              chain: token.chain || getChainName(token.chainId) || "Unknown",
              icon: token.icon || `https://cryptologos.cc/logos/${token.symbol?.toLowerCase()}-${token.symbol?.toLowerCase()}-logo.svg?v=024`,
              balance: token.balance || "0",
              decimals: token.decimals || 18,
              tokenAddress: token.address || token.tokenAddress,
              walletAddress: selectedWallet.address || ""
            }));
            console.log("TradingPanel: Passing coins to BalanceDropdown:", mappedCoins);
            console.log("TradingPanel: Original coins data:", coins);
            return mappedCoins;
          })()}
          selectedCoin={selectedCoin ? {
            name: selectedCoin.name || selectedCoin.symbol || "Unknown Token",
            symbol: selectedCoin.symbol || "UNKNOWN",
            chain: selectedCoin.chain || getChainName(selectedCoin.chainId) || "Unknown",
            icon: selectedCoin.icon || `https://cryptologos.cc/logos/${selectedCoin.symbol?.toLowerCase()}-${selectedCoin.symbol?.toLowerCase()}-logo.svg?v=024`,
            balance: selectedCoin.balance || "0",
            decimals: selectedCoin.decimals || 18,
            tokenAddress: selectedCoin.address || selectedCoin.tokenAddress,
            walletAddress: selectedWallet.address || ""
          } : null}
          setSelectedCoin={(coin) => {
            if (coin) {  // Add null check
              console.log("TradingPanel: Coin selected from BalanceDropdown:", coin);
              setSelectedCoin({
                name: coin.name,
                symbol: coin.symbol,
                chain: coin.chain,
                chainId: getChainIdFromName(coin.chain) || undefined, // Convert null to undefined
                address: coin.tokenAddress,
                tokenAddress: coin.tokenAddress,
                balance: coin.balance,
                decimals: coin.decimals,
                icon: coin.icon
              });
            }
          }}
        activeChainId={activeChainId} 
          selectedWallet={selectedWallet?.address || ""}
      />
      ) : (
        <div className="py-3 px-4 bg-[#1D2226] rounded-lg cursor-not-allowed text-gray-600 flex items-center space-x-2 mb-4">
          <div className="w-7 h-7 rounded-full flex items-center justify-center bg-gray-700">
            <span className="text-xs text-gray-400">$</span>
          </div>
          <div>
            <div className="text-sm text-gray-400">Connect Wallet</div>
            <div className="text-xs text-gray-500">to see your balances</div>
          </div>
        </div>
      )}

      {/* Amount Input */}
      <div className="mt-4 rounded-lg">
      {/* Label */}
      <div className="flex gap-2 items-center text-gray-400 text-lg mb-1">
        <span>{isAmount ? "Amount" : "Qty"}</span>
        <button onClick={() => setIsAmount(!isAmount)}>
          <FaExchangeAlt className="text-gray-400 hover:text-white transition" />
        </button>
      </div>

        {/* Input Box with better output display */}
      <div className="flex justify-between items-center bg-[#141416] p-3 rounded-lg">
        <input
          type="text"
          value={inputValue}
          onChange={fixedHandleInputChange}
          // Add onFocus and onBlur events to force fetch on focus
          onFocus={() => console.log("Input focused")}
          onBlur={() => {
            console.log("Input blurred");
            // Only force a quote fetch when the user is authenticated 
            // and there's a value and we have a selected coin
            if (inputValue && parseFloat(inputValue) > 0 && selectedCoin && authenticated && !isLoadingQuote) {
              console.log("Conditions met for manual quote fetch");
              // The quote fetch will be handled automatically by the useEffect
            }
          }}
          placeholder={isTokenTradeable 
            ? `0 ${selectedCoin?.name || (activeToken?.symbol || 'Token')}` 
            : `${selectedCoin?.symbol || 'Token'} not available for trading`}
          className={`bg-transparent outline-none w-full ${!isTokenTradeable ? 'text-gray-500 cursor-not-allowed' : 'text-white'}`}
          disabled={!authenticated || !selectedCoin || !isTokenTradeable}
        />
        <div className="text-right">
          <div className="text-gray-400 text-sm">
            {authenticated ? formatOutputDisplay() : "Connect wallet"}
          </div>
          {tokenPair && authenticated && (
            <div className="text-xs text-gray-500">{tokenPair}</div>
          )}
        </div>
      </div>
    </div>

      {/* Slider - Only show for authenticated users with a selected coin */}
      {authenticated && selectedCoin && (
      <div className="mt-4 w-full">
      {/* Slider Label */}
      <div className="flex justify-between text-[#BBBBBB] text-sm mb-2">
        <span>0%</span>
        <span>25%</span>
        <span>50%</span>
        <span>75%</span>
        <span>100%</span>
      </div>

      {/* Slider Container */}
      <div className="relative">
        {/* Percentage Indicator Above Slider */}
        <div className="absolute left-1/2 transform -translate-x-1/2 -top-6 text-white font-semibold">
        </div>

        {/* Styled Range Input */}
        <input
          type="range"
          min="0"
          max="100"
          value={range}
              onChange={(e) => {
                const rangeValue = e.target.value;
                setRange(rangeValue);
                
                // Calculate the percentage of the balance to use
                if (selectedCoin && selectedCoin.balance) {
                  try {
                    const balance = parseFloat(selectedCoin.balance);
                    if (!isNaN(balance) && balance > 0) {
                      const amountToUse = (balance * (parseInt(rangeValue) / 100)).toString();
                      setInputValue(amountToUse);
                    }
                  } catch (error) {
                    console.error("Error calculating balance percentage:", error);
                  }
                }
              }}
              disabled={!isTokenTradeable}
              className={`slider-thumb w-full h-2 rounded-lg appearance-none cursor-pointer transition-all bg-gradient-to-r from-gray-500 via-yellow-400 to-green-500 ${
                !isTokenTradeable ? 'opacity-50 cursor-not-allowed' : ''
              }`}
          style={{
            outline: "none",
            WebkitAppearance: "none",
          }}
        />

            {/* Custom Thumb Styling is now in TradingPanelStyles.css */}
      </div>
    </div>
      )}

      {/* Take Profit Toggle */}
  


{/* Render Target Price Only for Limit Tab */}
{activeTab === "Limit" && (
   <div className="p-2 rounded-lg">
   <div className="flex justify-between text-[#BBBBBB] text-sm mb-1">
    <div className="flex gap-2">

     <span>{isPriceMode ? "Target Price" : "Target MarketCap"}</span>
     <button onClick={toggleMode} className="text-gray-400 hover:text-white transition">
       <FaExchangeAlt />
     </button>
     </div>
     {/*Put percent here*/}
     <span className="text-green-400 underline">
       {isPriceMode ? "19.6% Lower" : "20% Lower"}
     </span>
   </div>
   <div className="flex justify-between bg-[#141416] p-2 rounded-lg text-white">
     <input
       type="text"
       value={isPriceMode ? price : marketCap}
       onChange={(e) =>
         isPriceMode ? setPrice(e.target.value) : setMarketCap(e.target.value)
       }
       className="bg-transparent outline-none w-full"
     />
     <span className="opacity-50">
       {isPriceMode ? `~${(parseFloat(price) * 1e6).toFixed(2)} MKTCAP` : `~ ${(parseFloat(marketCap) / 1e9).toFixed(3)} USD`}
     </span>
   </div>
 </div>
)}
     <div className="my-2 bg-[#1D2226] p-3 text-lg rounded-lg">
  {/* If data exists, show Take Profit & Stop Loss */}
  {takeProfit || stopLoss ? (
    <>
         <div className="flex items-center">
      <Switch
        checked={isTakeProfit}
        onChange={() => {
          if (isTakeProfit) {
            // Reset values when toggling off
            setTakeProfit('');
            setStopLoss('');
          }
          setIsTakeProfit(!isTakeProfit);
        }}
        className={`${isTakeProfit ? "bg-green-500" : "bg-[#BBBBBB]"} relative inline-flex h-6 w-11 items-center rounded-full mr-3`}
      >
        <span className="sr-only">Enable Take Profit</span>
        <span
          className={`${isTakeProfit ? "translate-x-6" : "translate-x-1"} inline-block h-4 w-4 transform bg-white rounded-full`}
        />
      </Switch>
      <span className="text-white">Take Profit • Stop Loss</span>
    </div>
    <div className="flex flex-col space-y-2 text-gray-400">
      {takeProfit && (
        <>
                
        <div className="flex justify-between">
          <span>Take Profit</span>
          <span className="text-white">
            0 SOL <span className="text-green-400">+{takeProfit}%</span>
          </span>
        </div>
        </>
      )}
      {stopLoss && (
        <>
       
        <div className="flex justify-between">
          <span>Stop Loss</span>
          <span className="text-white">
            0 SOL <span className="text-red-400">-{stopLoss}%</span>
          </span>
        </div>
        </>
      )}
    </div>
    </>
  ) : (
    // Show the switch if no data exists
    <div className="flex items-center">
      <Switch
        checked={isTakeProfit}
        onChange={handleToggleTakeProfit}
        className={`${isTakeProfit ? "bg-green-500" : "bg-[#BBBBBB]"} relative inline-flex h-6 w-11 items-center rounded-full mr-3`}
      >
        <span className="sr-only">Enable Take Profit</span>
        <span
          className={`${isTakeProfit ? "translate-x-6" : "translate-x-1"} inline-block h-4 w-4 transform bg-white rounded-full`}
        />
      </Switch>
      <span className="text-white">Take Profit • Stop Loss</span>
    </div>
  )}
</div>



     

      {/* All Quotes Comparison */}
      {showAllQuotes && dexQuotes && dexQuotes.length > 0 && (
        <div className="my-3 bg-gray-900/50 rounded-lg p-3 border border-gray-700 text-xs">
          <h4 className="text-white text-sm font-medium mb-2">Available Liquidity Pools</h4>
          <div className="space-y-2">
            {dexQuotes.map((quoteData, index) => (
              <div 
                key={index}
                className={`flex justify-between items-center p-2 rounded-md ${
                  quoteData.dexId === bestDex 
                    ? 'bg-green-900/30 border border-green-700' 
                    : 'bg-gray-800/50'
                }`}
              >
                <div className="flex items-center">
                  <div className={`w-2 h-2 rounded-full mr-2 ${
                    quoteData.dexId === bestDex ? 'bg-green-500' : 'bg-gray-500'
                  }`}></div>
                  <span className="font-medium capitalize">
                    {quoteData.dexId}
                    {quoteData.dexId === bestDex && <span className="text-green-400 ml-1">(Best)</span>}
                  </span>
                </div>
                <div className="text-right">
                  <div className="text-white">
                    {quoteData && quoteData.quote && 
                      (quoteData.quote.amountOut || quoteData.quote.outputAmount || "N/A")
                    }
                    {(!quoteData || !quoteData.quote) && "N/A"}
                  </div>
                  {quoteData && quoteData.quote && quoteData.quote.priceImpact && (
                    <div className={`text-xs ${
                      (() => {
                        const impact = quoteData.quote.priceImpact;
                        if (typeof impact === 'string') {
                          const numImpact = parseFloat(impact);
                          if (numImpact > 5) return 'text-red-500';
                          if (numImpact > 3) return 'text-yellow-500';
                          return 'text-gray-400';
                        } else if (typeof impact === 'number') {
                          if (impact > 5) return 'text-red-500';
                          if (impact > 3) return 'text-yellow-500';
                          return 'text-gray-400';
                        }
                        return 'text-gray-400';
                      })()
                    }`}>
                      Impact: {(() => {
                        const impact = quoteData.quote.priceImpact;
                        if (typeof impact === 'string') {
                          return parseFloat(impact).toFixed(2);
                        } else if (typeof impact === 'number') {
                          return impact.toFixed(2);
                        }
                        return '0.00';
                      })()}%
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Buy & Sell Buttons */}
      <div className="mt-6 flex flex-col space-y-4">
        {/* Show token not available message here, once for the whole section */}
        {!isTokenTradeable && (
          <div className="py-4 px-3 rounded-lg font-bold text-center text-gray-500 border-2 border-gray-700 bg-gray-800 cursor-not-allowed mb-2">
            TOKEN NOT AVAILABLE FOR TRADING
          </div>
        )}
        
        <div className="flex space-x-4">
        {/* BUY Button */}
        {renderBuyButton()}

        {/* SELL Button */}
        {renderSellButton()}
        </div>
      </div>
            {/* Render TPSL Modal if open */}
            {isModalOpen && <Tpslmodal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} onSubmit={handleTpslSubmit} />}

            {/* Display status messages */}
            {statusMessage && (
              <div className="my-2 p-2 bg-[#292d32] text-white text-sm rounded">
                {statusMessage}
              </div>
            )}
            
            {/* Display transaction status if actively transacting */}
            {isTransacting && transactionStatus && (
              <div className="my-2 p-2 bg-[#1D2226] text-[#14FFA2] text-sm rounded flex items-center justify-center">
                <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-[#14FFA2] rounded-full"></div>
                {transactionStatus}
              </div>
            )}
          

            {/* Render quote details if available */}
            {renderQuoteDetails()}
              {/* Display available options when token is not available */}
            <div className="mt-4 mb-2">
              {renderAvailableOptions()}
            </div>
    </div>
  );
};

export default TradingPanel;