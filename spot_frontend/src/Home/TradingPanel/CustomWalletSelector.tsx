import React, { useState, useMemo, useEffect, useCallback, useRef } from 'react';
import { useWallets, usePrivy } from '@privy-io/react-auth';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

interface WalletBase {
  id?: string;
  address?: string;
  connected?: boolean;
  walletClientType?: string;
  connector?: any;
  embedded?: boolean;
  provider?: any;
  ethersProvider?: any;
  sendTransaction?: any;
  embeddedWallet?: any;
  walletClient?: any;
  connectedAt?: Date | string | number;
  chainId?: string | number;
  chainName?: string;
  type?: string;
  connectorType?: string;
  smartWalletType?: string;
  chain?: string;
}

interface WalletByChain {
  ethereum: WalletBase[];
  solana: WalletBase[];
}

interface CustomWalletSelectorProps {
  selectedWallet: any;
  onSelectWallet: (wallet: any) => void;
  onConnectWallet?: () => void;
  smartWalletClient?: any;
  wallets?: any[];
}

// At the top of the file, add TypeScript interface for Privy linked accounts
interface WalletAccount {
  type: string;
  address: string;
  chainType?: string;
  verifiedAt?: string;
  latestVerifiedAt?: string;
  firstVerifiedAt?: string;
  connectorType?: string;
  walletClientType?: string;
  smartWalletType?: string;
  [key: string]: any;
}

const CustomWalletSelector = ({ 
  onSelectWallet, 
  selectedWallet,
  onConnectWallet,
  smartWalletClient,
  wallets
}: CustomWalletSelectorProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const { wallets: useWalletsWallets } = useWallets();
  const { user, authenticated } = usePrivy();
  
  // Track processed wallets with a ref to avoid stale data
  const processedWalletsRef = useRef<Set<string>>(new Set());
  
  // Clear cache on mount
  useEffect(() => {
    // Clear any cached wallet data
    try {
      localStorage.removeItem('cachedWallets');
      localStorage.removeItem('lastSelectedWallet');
      console.log("Cleared wallet cache on component mount");
    } catch (e) {
      console.error("Error clearing wallet cache:", e);
    }
    
    // Clear our processed wallets ref on unmount
    return () => {
      processedWalletsRef.current.clear();
    };
  }, []);
  
  // Improved address formatter that handles undefined addresses
  const formatAddress = useCallback((address?: string) => {
    if (!address || typeof address !== 'string') return 'No Address';
    return address.length > 10 ? `${address.slice(0, 6)}...${address.slice(-4)}` : address;
  }, []);

  // Helper to determine if an address is a Solana address
  const isSolanaAddress = useCallback((address?: string) => {
    return address && typeof address === 'string' && !address.startsWith('0x') && address.length > 30;
  }, []);
  
  // Helper function to validate if a string looks like a valid blockchain address
  const isValidBlockchainAddress = useCallback((address?: string) => {
    if (!address || typeof address !== 'string') return false;
    
    // Ethereum addresses are 42 chars (0x + 40 hex chars)
    if (address.startsWith('0x')) {
      return /^0x[a-fA-F0-9]{40}$/.test(address);
    }
    
    // Solana addresses are 32-44 chars (base58 encoding)
    // Exclude anything that looks like an email
    if (address.includes('@') || address.includes('.com') || address.includes('.io') || address.includes('.org')) {
      console.log(`Rejecting email-like address: ${address}`);
      return false;
    }
    
    // Basic check for Solana addresses - typically 32-44 chars base58
    return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address);
  }, []);
  
  // Helper function to determine blockchain type from address format
  const getBlockchainTypeFromAddress = useCallback((address?: string) => {
    if (!address || typeof address !== 'string') return null;
    
    // Debug log for address detection
    console.log(`Analyzing address format for: ${address.slice(0, 6)}...${address.slice(-4)}`);
    
    // Ethereum addresses always start with 0x followed by 40 hex chars
    if (/^0x[a-fA-F0-9]{40}$/.test(address)) {
      console.log(`Identified as Ethereum address: ${address.slice(0, 6)}...${address.slice(-4)}`);
      return {
        type: 'ethereum',
        chainName: 'Ethereum'
      };
    }
    
    // Specific handling for known Solana addresses
    if (address.startsWith('HzHsCX')) {
      console.log(`Known Solana address format detected: ${address.slice(0, 6)}...${address.slice(-4)}`);
      return {
        type: 'solana',
        chainName: 'Solana'
      };
    }
    
    // Solana addresses don't start with 0x and typically use base58 encoding
    if (!address.startsWith('0x')) {
      console.log(`Non-0x address detected, likely Solana: ${address.slice(0, 6)}...${address.slice(-4)}`);
      return {
        type: 'solana',
        chainName: 'Solana'
      };
    }
    
    console.log(`Cannot determine blockchain type for address: ${address}`);
    return null;
  }, []);

  // Get connected wallets from various sources
  const connectedWallets = useMemo(() => {
    const uniqueWallets: WalletBase[] = [];
    const seenAddresses = new Set();
    
    console.log("CustomWalletSelector: Getting connected wallets");
    
    // First add any wallets provided via props
    if (wallets && Array.isArray(wallets)) {
      console.log("CustomWalletSelector: Processing wallets from props:", wallets.length);
      wallets.forEach(wallet => {
        if (wallet && wallet.address && !seenAddresses.has(wallet.address.toLowerCase())) {
          console.log(`Adding wallet from props: ${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)}`);
          seenAddresses.add(wallet.address.toLowerCase());
          uniqueWallets.push({
            ...wallet,
            id: wallet.id || `prop-wallet-${wallet.address}`,
            connectedAt: wallet.connectedAt || new Date().toISOString(),
            connected: true
          });
        }
      });
    }

    // IMPORTANT CHANGE: Process smartWallet before other accounts
    if (user?.smartWallet?.address && !seenAddresses.has(user.smartWallet.address.toLowerCase())) {
      const smartWalletAddress = user.smartWallet.address;
      console.log(`Found smart wallet in user.smartWallet: ${smartWalletAddress.slice(0, 6)}...${smartWalletAddress.slice(-4)}`);
      
      seenAddresses.add(smartWalletAddress.toLowerCase());
      uniqueWallets.push({
        id: `smart-wallet-${smartWalletAddress}`,
        address: smartWalletAddress,
        walletClientType: 'smart',
        connectedAt: new Date().toISOString(),
        connected: true,
        chainName: smartWalletAddress.startsWith('0x') ? 'BSC' : 'Solana',
        type: smartWalletAddress.startsWith('0x') ? 'ethereum' : 'solana',
        smartWalletType: user.smartWallet.smartWalletType || 'light_account'
      });
    }
    
    // Process linkedAccounts from user object - include ALL wallet types
    if (user?.linkedAccounts && Array.isArray(user.linkedAccounts)) {
      console.log(`Processing ${user.linkedAccounts.length} linked accounts from user object`);
      
      user.linkedAccounts.forEach(account => {
        // Add type assertion to avoid TypeScript errors
        const walletAccount = account as unknown as WalletAccount;
        
        // Skip if not a blockchain wallet or missing key data
        if (!walletAccount || 
            !['wallet', 'smart_wallet'].includes(walletAccount.type) || 
            !walletAccount.address || 
            typeof walletAccount.address !== 'string' || 
            walletAccount.address.includes('@')) {
          return;
        }
        
        // Skip if we've already added this address
        if (seenAddresses.has(walletAccount.address.toLowerCase())) {
          return;
        }
        
        console.log(`Processing linked account: ${walletAccount.address.slice(0,6)}...${walletAccount.address.slice(-4)} (${walletAccount.type})`);
        
        const isEthereum = walletAccount.address.startsWith('0x');
        const isSolana = !isEthereum;
        
        seenAddresses.add(walletAccount.address.toLowerCase());
        uniqueWallets.push({
          id: walletAccount.address,
          address: walletAccount.address,
          walletClientType: walletAccount.type === 'smart_wallet' ? 'smart' : 
                           walletAccount.walletClientType || 
                           (isSolana ? 'phantom' : 'privy'),
          connectedAt: walletAccount.latestVerifiedAt || walletAccount.verifiedAt || new Date().toISOString(),
          connected: true,
          chainName: isSolana ? 'Solana' : (walletAccount.chainType === 'ethereum' ? 'BSC' : walletAccount.chainType || 'BSC'),
          type: isSolana ? 'solana' : 'ethereum',
          embedded: walletAccount.connectorType === 'embedded',
          // These are added as custom properties that may not be in the WalletBase interface
          connectorType: walletAccount.connectorType
        });
      });
    }
    
    // Check user.wallet and add if not already included (embedded wallet)
    if (user?.wallet?.address && !seenAddresses.has(user.wallet.address.toLowerCase())) {
      const userWalletAddress = user.wallet.address;
      
      // Skip email addresses
      if (!userWalletAddress.includes('@')) {
        const isEthereum = userWalletAddress.startsWith('0x');
        const isSolana = !isEthereum;
        
        console.log(`Adding wallet from user.wallet: ${userWalletAddress.slice(0, 6)}...${userWalletAddress.slice(-4)} (${isSolana ? 'solana' : 'ethereum'})`);
        
        seenAddresses.add(userWalletAddress.toLowerCase());
        uniqueWallets.push({
          id: `embedded-${userWalletAddress}`,
          address: userWalletAddress,
          walletClientType: 'embedded',
          connectedAt: new Date().toISOString(),
          connected: true,
          chainName: isSolana ? 'Solana' : 'BSC',
          type: isSolana ? 'solana' : 'ethereum'
        });
      }
    }
    
    // Check for smart wallet from client and add it with priority
    if (smartWalletClient) {
      // Get the smart wallet address
      let smartWalletAddress = null;
      
      if (smartWalletClient.address) {
        smartWalletAddress = smartWalletClient.address;
      } else if (smartWalletClient.account && smartWalletClient.account.address) {
        smartWalletAddress = smartWalletClient.account.address;
      }
      
      if (smartWalletAddress && !seenAddresses.has(smartWalletAddress.toLowerCase())) {
        console.log(`Found smart wallet from client: ${smartWalletAddress.slice(0, 6)}...${smartWalletAddress.slice(-4)}`);
        
        // Determine the blockchain type from the address format
        const isEthereum = smartWalletAddress.startsWith('0x');
        const isSolana = !isEthereum;
        let chainName = isEthereum ? "BSC" : "Solana";
        
        // For Ethereum addresses, get specific chain
        if (isEthereum && smartWalletClient.chainId) {
          const numericChainId = Number(smartWalletClient.chainId);
          if (numericChainId === 56) {
            chainName = "BSC";
          } else if (numericChainId === 137) {
            chainName = "Polygon";
          } else if (numericChainId === 8453) {
            chainName = "Base";
          }
        }
        
        seenAddresses.add(smartWalletAddress.toLowerCase());
        uniqueWallets.push({
          id: `smart-wallet-client-${smartWalletAddress}`,
          address: smartWalletAddress,
          walletClientType: 'smart',
          connectedAt: new Date().toISOString(),
          connected: true,
          chainId: smartWalletClient.chainId,
          chainName: chainName,
          type: isSolana ? 'solana' : 'ethereum'
        });
      }
    }
    
    // Check wallets from useWallets hook - INCLUDE ALL WALLETS, not just smart and Solana
    if (useWalletsWallets && useWalletsWallets.length > 0) {
      // First let's sort by connectedAt so the most recently connected wallet shows first
      const sortedWallets = [...useWalletsWallets].sort((a, b) => {
        if (!a.connectedAt) return 1;
        if (!b.connectedAt) return -1;
        // @ts-ignore - Convert to timestamps and sort newest first
        return new Date(b.connectedAt).getTime() - new Date(a.connectedAt).getTime();
      });
      
      // FIXED FILTER: Include all wallet types, not just Solana
      const filteredWallets = sortedWallets.filter(wallet => {
        // Basic validations
        if (!wallet.address || wallet.address.includes('@') || !wallet.connectedAt) {
          return false;
        }
        
        // Skip addresses we've already added
        if (seenAddresses.has(wallet.address.toLowerCase())) {
          return false;
        }
        
        // Include ALL wallet types
        return true;
      });
      
      console.log(`CustomWalletSelector: Found ${filteredWallets.length} wallets from useWallets hook`);
      
      // Add each wallet
      filteredWallets.forEach(wallet => {
        const walletAddress = wallet.address;
        if (!walletAddress) {
          return;
        }
        
        seenAddresses.add(walletAddress.toLowerCase());
        const isEthereum = walletAddress.startsWith('0x');
        const isSolana = !isEthereum;
        
        uniqueWallets.push({
          ...wallet,
          type: isSolana ? 'solana' : 'ethereum',
          chainName: isSolana ? 'Solana' : wallet.chainId ? `Chain ${wallet.chainId}` : 'BSC'
        });
        
        console.log(`Added wallet from useWallets: ${walletAddress.slice(0, 6)}...${walletAddress.slice(-4)} (${wallet.walletClientType || 'unknown'})`);
      });
    }
    
    // Log final result
    console.log(`Found ${uniqueWallets.length} unique wallets from all sources:`);
    uniqueWallets.forEach((wallet, index) => {
      if (wallet.address) {
        console.log(`  ${index+1}. ${wallet.address.slice(0,6)}...${wallet.address.slice(-4)} - ${wallet.walletClientType || 'unknown'} - ${wallet.chainName || wallet.type || 'unknown'}`);
      } else {
        console.log(`  ${index+1}. No address - ${wallet.walletClientType || 'unknown'} - ${wallet.chainName || wallet.type || 'unknown'}`);
      }
    });
    
    return uniqueWallets;
  }, [useWalletsWallets, user, smartWalletClient, wallets]);

  // Group addresses by chain
  const getAddressesByChain = useCallback(() => {
    const result: WalletByChain = {
      ethereum: [],
      solana: []
    };
    
    const walletAddresses = connectedWallets;
    console.log("Grouping addresses by chain, total:", walletAddresses.length);
    
    // Check if we have any smart or embedded wallets
    const hasSmartWallet = walletAddresses.some(w => 
      w.walletClientType === 'smart' || 
      w.type === 'smart_wallet'
    );
    
    const hasEmbeddedWallet = walletAddresses.some(w => 
      w.walletClientType === 'embedded' || 
      w.embedded === true || 
      w.connectorType === 'embedded' || 
      w.type === 'embedded'
    );
    
    console.log(`Wallet status: Smart wallet available: ${hasSmartWallet}, Embedded wallet available: ${hasEmbeddedWallet}`);
    
    walletAddresses.forEach(wallet => {
      if (!wallet.address) return;
      
      const isEthereum = wallet.address.startsWith('0x');
      const isSolana = !isEthereum;
      const isSmartWallet = wallet.walletClientType === 'smart' || wallet.type === 'smart_wallet';
      const isEmbeddedWallet = wallet.walletClientType === 'embedded' || 
                             wallet.embedded === true || 
                             wallet.connectorType === 'embedded' || 
                             wallet.type === 'embedded';
      
      // If we have smart wallets, only show smart and embedded wallets
      if (hasSmartWallet || hasEmbeddedWallet) {
        if (isSmartWallet) {
          console.log(`Including smart wallet: ${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)}`);
          if (isEthereum) {
            result.ethereum.push(wallet);
          } else {
            result.solana.push(wallet);
          }
        }
        else if (isEmbeddedWallet) {
          console.log(`Including embedded wallet: ${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)}`);
          if (isEthereum) {
            result.ethereum.push(wallet);
          } else {
            result.solana.push(wallet);
          }
        }
        else {
          console.log(`Skipping external wallet since smart/embedded wallet exists: ${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)}`);
        }
      }
      // If no smart/embedded wallets, show all wallets
      else {
        console.log(`No smart/embedded wallets, including all: ${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)}`);
        if (isEthereum) {
          result.ethereum.push(wallet);
        } else {
          result.solana.push(wallet);
        }
      }
    });
    
    console.log("Final categorized addresses:", {
      ethereum: result.ethereum.length,
      solana: result.solana.length
    });
    
    return result;
  }, [connectedWallets]);
  
  const toggleSelector = () => setIsOpen(!isOpen);
  
  // Handle wallet selection with improved logging and consistency
  const handleSelectWallet = useCallback((wallet: WalletBase) => {
    if (!wallet || !wallet.address) {
      console.log("Cannot select wallet: No wallet or no address");
      return;
    }
    
    console.log(`Selecting wallet: ${formatAddress(wallet.address)} (${wallet.walletClientType || wallet.type || 'unknown type'})`);
    
    // Force a clean copy of the wallet to avoid reference issues
    const cleanWallet: WalletBase = {
      id: wallet.id,
      address: wallet.address,
      connected: true,
      walletClientType: wallet.walletClientType || wallet.type,
      type: wallet.type,
      chainName: wallet.chainName,
      connectedAt: wallet.connectedAt || new Date().toISOString(),
      // Include other properties that might be needed
      ethersProvider: wallet.ethersProvider,
      provider: wallet.provider,
      sendTransaction: wallet.sendTransaction,
      embedded: wallet.embedded,
      connectorType: wallet.connectorType
    };
    
    onSelectWallet(cleanWallet);
    setIsOpen(false);
  }, [formatAddress, onSelectWallet]);

  // Debug the wallets we're receiving from all sources
  useEffect(() => {
    // Calculate addresses on each render instead of memoizing
    const addressesByChain = getAddressesByChain();
    
    console.log("CustomWalletSelector - Addresses by chain:", {
      ethereum: addressesByChain.ethereum.length,
      solana: addressesByChain.solana.length
    });
    
    // Also log user object to check for embedded wallet
    if (user?.wallet?.address) {
      console.log("CustomWalletSelector - User has embedded wallet:", formatAddress(user.wallet.address));
    } else {
      console.log("CustomWalletSelector - No embedded wallet in user object");
    }
  }, [getAddressesByChain, user, formatAddress]);
  
  // Debug the selected wallet
  useEffect(() => {
    if (selectedWallet) {
      console.log("CustomWalletSelector - Selected wallet:", {
        address: selectedWallet.address ? formatAddress(selectedWallet.address) : 'no-address',
        type: selectedWallet.walletClientType || selectedWallet.type || 'unknown',
        connectedAt: selectedWallet.connectedAt ? 'yes' : 'no'
      });
    } else {
      console.log("CustomWalletSelector - No wallet selected");
    }
  }, [selectedWallet, formatAddress]);

  // Auto-select first wallet if none selected yet
  useEffect(() => {
    // Calculate addresses directly when needed
    const addressesByChain = getAddressesByChain();
    const allWallets = [...addressesByChain.ethereum, ...addressesByChain.solana];
    
    if (allWallets.length > 0 && !selectedWallet) {
      console.log(`Auto-selecting first wallet: ${formatAddress(allWallets[0].address)}`);
      
      // Use setTimeout to avoid potential render issues
      setTimeout(() => {
        handleSelectWallet(allWallets[0]);
      }, 0);
    }
  }, [getAddressesByChain, selectedWallet, handleSelectWallet, formatAddress]);

  // Render the dropdown menu items
  const renderWalletItems = () => {
    const addressesByChain = getAddressesByChain();
    
    return Object.keys(addressesByChain).map(chain => {
      const wallets = addressesByChain[chain as keyof WalletByChain];
      
      if (!wallets || wallets.length === 0) return null;
      
      return (
        <div key={chain} className="mb-2">
          <div className="text-xs text-gray-400 uppercase mb-1 px-2">{chain}</div>
          <div className="space-y-1">
            {wallets.map((wallet: WalletBase, index: number) => {
              const isSolana = chain === 'solana' || (wallet.address && !wallet.address.startsWith('0x'));
              const isEmbedded = wallet.walletClientType === 'embedded' || wallet.connectorType === 'embedded';
              const isSelected = selectedWallet && selectedWallet.address === wallet.address;
              
              return (
                <div
                  key={index}
                  className={`flex items-center justify-between p-2 rounded-md cursor-pointer ${
                    isSelected ? 'bg-[#14FFA2]/10 border border-[#14FFA2]/30' : 'hover:bg-[#1F2124] bg-[#191B1F]'
                  }`}
                  onClick={() => handleSelectWallet(wallet)}
                >
                  <div className="flex items-center">
                    <div className={`w-7 h-7 flex items-center justify-center rounded-full mr-2 ${
                        isSolana && isEmbedded ? "bg-purple-900 border border-purple-500" : 
                        isSolana ? "bg-[#9945FF]" : 
                        wallet.walletClientType === 'smart' ? "bg-[#14FFA2]/20 border border-[#14FFA2]" : 
                        "bg-blue-900"
                    }`}>
                      <span className={`text-xs font-medium ${
                        isSolana && isEmbedded ? "text-purple-300" :
                        isSolana ? "text-white" :
                        wallet.walletClientType === 'smart' ? "text-[#14FFA2]" :
                        "text-blue-400"
                      }`}>
                        {isSolana 
                          ? (isEmbedded ? "SE" : "SOL") 
                          : wallet.walletClientType === 'smart' 
                            ? "AA" 
                            : "ETH"
                        }
                      </span>
                    </div>
                    <div>
                      <div className="text-sm text-white">{formatAddress(wallet.address)}</div>
                      <div className="flex items-center">
                        <span className={`text-xs mr-1 px-1 py-0.5 rounded ${
                          isSolana && isEmbedded ? "bg-purple-900/50 text-purple-300" :
                          isSolana ? "bg-purple-900/30 text-purple-400" :
                          wallet.walletClientType === 'smart' ? "bg-green-900/30 text-green-400" :
                          "bg-blue-900/30 text-blue-400"
                        }`}>
                          {isSolana && isEmbedded 
                            ? "Embedded" 
                            : wallet.walletClientType === 'smart' 
                              ? "Smart Wallet" 
                              : wallet.walletClientType || "External"}
                        </span>
                      </div>
                    </div>
                  </div>
                  {isSelected && (
                    <div className="w-2 h-2 rounded-full bg-[#14FFA2]"></div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      );
    });
  };

  return (
    <div className="relative mb-3">
      <div 
        className="bg-[#1D2226] p-3 rounded-lg cursor-pointer border border-gray-700 hover:border-gray-500 transition flex justify-between items-center"
        onClick={toggleSelector}
      >
        {selectedWallet && selectedWallet.address ? (
          <div className="flex items-center space-x-2">
            <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${
              selectedWallet.walletClientType === 'smart' 
                ? "bg-[#14FFA2] text-black" :
              isSolanaAddress(selectedWallet.address) 
                ? "bg-[#9945FF]" 
                : "bg-[#627EEA]"
            }`}>
              {selectedWallet.walletClientType === 'smart' 
                ? "AA" :
               isSolanaAddress(selectedWallet.address) 
                ? "SOL" 
                : "ETH"}
            </div>
            <div>
              <span className="text-white">Wallet</span>
              <div className="flex items-center space-x-1">
                <span className="text-sm text-gray-400">{formatAddress(selectedWallet.address)}</span>
                {selectedWallet.walletClientType === 'smart' && (
                  <span className="text-xs px-1.5 py-0.5 rounded-full bg-green-900/30 text-green-400">
                    Smart
                  </span>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center">
              <span className="text-white text-xs">?</span>
            </div>
            <span className="text-white">Select Wallet</span>
          </div>
        )}
        <ChevronDownIcon className="w-5 h-5 text-white" />
      </div>
      
      {isOpen && (
        <div className="absolute mt-1 w-full bg-[#181C20] rounded-lg shadow-lg z-50 p-2 max-h-60 overflow-y-auto border border-gray-700">
          <div className="flex justify-between items-center bg-[#14FFA2]/10 p-2 rounded-t-md mb-2">
            <div className="text-white text-sm font-medium">Select Wallet</div>
            <div className="text-[#14FFA2] text-xs">
              {(() => {
                const addressesByChain = getAddressesByChain();
                return addressesByChain.ethereum.length + addressesByChain.solana.length;
              })()} Connected
            </div>
          </div>
          
          {(() => {
            const addressesByChain = getAddressesByChain();
            return addressesByChain.ethereum.length > 0 || addressesByChain.solana.length > 0 ? (
              <div className="grid grid-cols-1 gap-2">
                {renderWalletItems()}
              </div>
            ) : (
              <div className="text-center py-3 text-gray-400">
                {onConnectWallet ? (
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      onConnectWallet();
                      setIsOpen(false);
                    }}
                    className="px-3 py-1.5 bg-[#14FFA2] text-black rounded-lg text-sm font-medium"
                  >
                    Connect Wallet
                  </button>
                ) : (
                  "No wallets connected"
                )}
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
};

export default CustomWalletSelector; 