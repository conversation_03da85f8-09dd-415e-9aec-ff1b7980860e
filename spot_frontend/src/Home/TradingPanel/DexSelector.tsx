import React, { useState } from 'react';
import { CheckIcon, ChevronUpDownIcon, GlobeAltIcon } from "@heroicons/react/24/outline";
import { QuoteData } from "../../types/trading";
import { Listbox, Transition } from '@headlessui/react';

// Define a more specific interface for the QuoteData we expect in DEX selection
interface DexQuoteData extends QuoteData {
  dexId?: string;
  source?: string;
  name?: string;
  chain?: string;
  outputAmount?: number;
  amountOut?: string;
  data?: {
    amountOut?: string;
  };
}

interface DexSelectorProps {
  dexQuotes: DexQuoteData[];
  selectedDex: string | null;
  bestDex: string | null;
  onSelectDex: (dexId: string) => void;
}

const DexSelector: React.FC<DexSelectorProps> = ({ 
  dexQuotes, 
  selectedDex, 
  bestDex, 
  onSelectDex 
}) => {
  const [isOpen, setIsOpen] = useState(false);

  if (!dexQuotes || dexQuotes.length === 0) {
    return null;
  }

  // Get available DEXes from quotes and sort by output amount (best first)
  const availableDexes = dexQuotes.map(quote => {
    // Extract amount out from different possible structures in the quote data
    let outputAmount = 0;
    if (typeof quote.outputAmount === 'number') {
      outputAmount = quote.outputAmount;
    } else if (quote.amountOut && typeof quote.amountOut === 'string') {
      outputAmount = parseFloat(quote.amountOut) || 0;
    } else if (quote.data?.amountOut && typeof quote.data.amountOut === 'string') {
      outputAmount = parseFloat(quote.data.amountOut) || 0;
    }
    
    return {
      id: quote.dexId || quote.source || '',
      name: quote.name || quote.dexId || quote.source || 'Unknown DEX',
      chain: quote.chain || 'unknown',
      outputAmount: outputAmount
    };
  });
  
  availableDexes.sort((a, b) => b.outputAmount - a.outputAmount);

  // Find currently selected DEX object
  const selected = availableDexes.find(dex => dex.id === selectedDex) || availableDexes[0];

  // Function to get chain-specific color
  const getChainColor = (chain: string) => {
    switch(chain.toLowerCase()) {
      case 'bsc': return 'text-yellow-500';
      case 'ethereum': return 'text-blue-400';
      case 'solana': return 'text-purple-400';
      case 'polygon': return 'text-indigo-400';
      default: return 'text-gray-400';
    }
  };

  // Function to get DEX-specific icon (placeholder for now)
  const getDexIcon = (dexId: string) => {
    // In a real implementation, you would use actual DEX logos
    return <GlobeAltIcon className="h-4 w-4 mr-2" />
  };

  return (
    <div className="relative w-full">
      <Listbox value={selected?.id} onChange={(value) => {
        onSelectDex(value);
        setIsOpen(false);
      }}>
        {({ open }) => (
          <>
            <Listbox.Button 
              className="relative w-full flex items-center justify-between bg-[#1D2226] rounded-lg border border-gray-700 pl-3 pr-2 py-2 text-left shadow-sm focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500 hover:border-green-600 transition-all duration-150"
              onClick={() => setIsOpen(!isOpen)}
            >
              <div className="flex items-center">
                {getDexIcon(selected?.id || '')}
                <span className="text-white font-medium">{selected?.name}</span>
                <span className={`ml-2 text-sm ${getChainColor(selected?.chain || 'unknown')}`}>({selected?.chain})</span>
                {selected?.id === bestDex && (
                  <span className="ml-2 text-xs px-1.5 py-0.5 bg-green-900/60 text-green-300 rounded-full border border-green-700/50 font-medium">Best Rate</span>
                )}
              </div>
              <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </Listbox.Button>

            <Transition
              show={open || isOpen}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Listbox.Options className="absolute z-10 mt-1 w-full bg-[#1D2226] shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm border border-gray-700">
                {availableDexes.map((dex) => (
                  <Listbox.Option
                    key={dex.id}
                    className={({ active }) =>
                      `${active ? 'bg-[#2a2d36]' : ''}
                       ${selectedDex === dex.id ? 'bg-gray-800/40' : ''}
                       relative cursor-pointer select-none py-2 pl-3 pr-9 group`
                    }
                    value={dex.id}
                  >
                    {({ selected, active }) => (
                      <>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            {getDexIcon(dex.id)}
                            <span className="text-white group-hover:text-green-400 transition-colors duration-150 font-medium">{dex.name}</span>
                            <span className={`ml-2 text-xs ${getChainColor(dex.chain)}`}>({dex.chain})</span>
                          </div>
                          <div className="flex items-center mr-8">
                            {dex.id === bestDex && (
                              <span className="text-xs px-1.5 py-0.5 bg-green-900/60 text-green-300 rounded-full border border-green-700/50 font-medium">Best</span>
                            )}
                          </div>
                        </div>

                        {selectedDex === dex.id && (
                          <span className="absolute inset-y-0 right-0 flex items-center pr-3">
                            <CheckIcon className="h-4 w-4 text-green-500" aria-hidden="true" />
                          </span>
                        )}
                      </>
                    )}
                  </Listbox.Option>
                ))}
              </Listbox.Options>
            </Transition>
          </>
        )}
      </Listbox>
    </div>
  );
};

export default DexSelector;
