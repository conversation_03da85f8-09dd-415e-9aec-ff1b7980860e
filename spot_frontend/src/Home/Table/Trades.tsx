import React, { useState, useEffect, useCallback } from "react";
import { Switch } from "@headlessui/react";
import { fetchCompletedTrades, fetchOrders, fetchTradeHistory } from "../../services/trade-history-service";
import { Trade, Order, TradeHistory } from "../../types/trade-history";
import { usePrivy } from "@privy-io/react-auth";
import TradingInterface from "./InstantTrade";

// Create a custom event for trade completion
export const TRADE_COMPLETED_EVENT = 'trade_completed';

// Define types for sorting configuration
interface SortConfig {
  key: string | null;
  direction: 'ascending' | 'descending';
}

const TradesTable: React.FC = () => {
  const { user } = usePrivy();
  const [activeTab, setActiveTab] = useState<string>("Trades");
  const [groupByTicker, setGroupByTicker] = useState<boolean>(false);
  const [marketType, setMarketType] = useState<string>("Market");
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [modalOpen, setModalOpen] = useState(false);
  // States for real data
  const [trades, setTrades] = useState<Trade[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [history, setHistory] = useState<TradeHistory[]>([]);
  
  // Sorting state
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: null,
    direction: 'ascending'
  });

  // Create a memoized fetchData function that we can call from multiple places
  const fetchTradeData = useCallback((walletAddress: string) => {
    if (walletAddress) {
      fetchData(walletAddress);
    }
  }, []);

  // Fetch data when component mounts or wallet changes
  useEffect(() => {
    if (user?.wallet?.address) {
      fetchTradeData(user.wallet.address);
    } else {
      // Clear data if no wallet connected
      setTrades([]);
      setOrders([]);
      setHistory([]);
      setIsLoading(false);
    }
  }, [user?.wallet?.address, fetchTradeData]);
  
  // Listen for trade completed events
  useEffect(() => {
    // Define the event handler
    const handleTradeCompleted = () => {
      console.log('TradesTable: Trade completed event received, refreshing data');
      if (user?.wallet?.address) {
        fetchTradeData(user.wallet.address);
      }
    };
    
    // Add event listener
    window.addEventListener(TRADE_COMPLETED_EVENT, handleTradeCompleted);
    
    // Cleanup function
    return () => {
      window.removeEventListener(TRADE_COMPLETED_EVENT, handleTradeCompleted);
    };
  }, [user?.wallet?.address, fetchTradeData]);

  // State for error messages
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Function to fetch all trade data
  const fetchData = async (walletAddress: string) => {
    setIsLoading(true);
    setErrorMessage(null);
    
    // Track missing tables
    let missingTables = [];
    
    try {
      // Fetch trades
      try {
        const tradesData = await fetchCompletedTrades(walletAddress);
        setTrades(tradesData || []);
      } catch (error: any) {
        console.error('Error fetching trades:', error);
        setTrades([]);
        if (error?.message?.includes('42P01')) {
          // PostgreSQL error code for undefined_table
          console.warn('trades table does not exist');
          missingTables.push('trades');
        }
      }

      // Fetch orders
      try {
        const ordersData = await fetchOrders(walletAddress);
        setOrders(ordersData || []);
      } catch (error: any) {
        console.error('Error fetching orders:', error);
        setOrders([]);
        if (error?.message?.includes('42P01')) {
          console.warn('orders table does not exist');
          missingTables.push('orders');
        }
      }

      // Fetch history
      try {
        const historyData = await fetchTradeHistory(walletAddress);
        setHistory(historyData || []);
      } catch (error: any) {
        console.error('Error fetching history:', error);
        setHistory([]);
        if (error?.message?.includes('42P01')) {
          console.warn('trade_history table does not exist');
          missingTables.push('trade_history');
        }
      }
      
      // If all tables are missing, display a helpful message
      if (missingTables.length === 3) {
        setErrorMessage('Required tables do not exist in the Supabase database. Please create the tables to store trade data.');
      } else if (missingTables.length > 0) {
        setErrorMessage(`Some tables are missing: ${missingTables.join(', ')}. Create these tables to enable full functionality.`);
      }

    } catch (error) {
      console.error('Error fetching trade data:', error);
      setErrorMessage('Error loading trade data. Please check the console for details.');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle sorting
  const requestSort = (key: string) => {
    let direction: 'ascending' | 'descending' = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  // Format currency for display
  const formatCurrency = (value: string | number): string => {
    if (!value) return '$0.00';
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(num);
  };

  // Format number for display
  const formatNumber = (value: string | number, digits = 4): string => {
    if (!value) return '0';
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return num.toLocaleString('en-US', {
      maximumFractionDigits: digits
    });
  };

  // Helper function to sort data
  const getSortedData = <T extends Trade | Order | TradeHistory>(data: T[]): T[] => {
    if (!sortConfig.key) return data;
    
    return [...data].sort((a, b) => {
      let aValue: any, bValue: any;
      
      // Ensure sortConfig.key is not null before using it
      const key = sortConfig.key;
      if (!key) return 0; // Should not happen due to the check above, but satisfies TS

      // Helper to get property based on key name mapping between UI and API
      const getProperty = (item: T, key: string): any => {
        // Handle possible null or undefined item
        if (!item) return null;
        
        // Create a type-safe mapping between UI column names and API data properties
        const mappedKey = ((): keyof T | null => {
          // Default mappings that work across all item types
          if (key === 'Ticker') return 'ticker' as keyof T;
          if (key === 'Time') return 'timestamp' as keyof T;
          if (key === 'Qty') return 'qty' as keyof T;
          
          // Type-specific mappings
          if (activeTab === 'Trades') {
            if (key === 'Entry Price') return 'entry_price' as keyof T;
            if (key === 'Exit Price') return 'exit_price' as keyof T;
            if (key === 'Entry Value') return 'entry_value' as keyof T;
            if (key === 'Exit Value') return 'exit_value' as keyof T;
            if (key === 'Closed P&L') return 'closed_pnl' as keyof T;
          } else if (activeTab === 'Orders') {
            if (key === 'Type') return 'type' as keyof T;
            if (key === 'Share') return 'share' as keyof T;
            if (key === 'Value') return 'value' as keyof T;
            if (key === 'Target Price') return 'target_price' as keyof T;
            if (key === 'Target P&L') return 'target_pnl' as keyof T;
            if (key === 'Status') return 'status' as keyof T;
          } else if (activeTab === 'History') {
            if (key === 'Type') return 'trade_type' as keyof T;
            if (key === 'Value') return 'value' as keyof T;
            if (key === 'Filled Price') return 'filled_price' as keyof T;
            if (key === 'DEX') return 'dex' as keyof T;
          }
          
          return null;
        })();
        
        // Safely access the property if it exists
        if (mappedKey && mappedKey in item) {
          return item[mappedKey];
        }
        
        return null;
      };

      aValue = getProperty(a, key);
      bValue = getProperty(b, key);

      // Special handling for number values stored as strings
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        if (aValue.startsWith('$') || bValue.startsWith('$')) {
          aValue = parseFloat(aValue.replace(/[^0-9.-]+/g, ''));
          bValue = parseFloat(bValue.replace(/[^0-9.-]+/g, ''));
        }
      }

      if (aValue < bValue) return sortConfig.direction === 'ascending' ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === 'ascending' ? 1 : -1;
      return 0;
    });
  };

  // Get the right data set based on active tab
  const getActiveData = () => {
    if (activeTab === "Trades") {
      return getSortedData(trades);
    } else if (activeTab === "Orders") {
      return getSortedData(orders);
    } else {
      return getSortedData(history);
    }
  };

  // Get column headers based on active tab
  const getColumnHeaders = (): string[] => {
    if (activeTab === "Trades") {
      return ["Ticker", "Qty", "Entry Price", "Exit Price", "Entry Value", "Exit Value", "Closed P&L", "Time"];
    } else if (activeTab === "Orders") {
      return ["Ticker", "Type", "Qty", "Share", "Value", "Target Price", "Target P&L", "Status", "Time"];
    } else {
      return ["Ticker", "Type", "Qty", "Value", "Filled Price", "Time", "DEX"];
    }
  };

  // Get sort indicator arrow
  const getSortIndicator = (columnName: string): string => {
    if (sortConfig.key !== columnName) {
      return "";
    }
    return sortConfig.direction === 'ascending' ? " ↑" : " ↓";
  };

  // Format date for display
  const formatDate = (dateString: string | Date): string => {
    if (!dateString) return '';
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleString();
  };

  return (
    <div className="bg-[#181C20] border-[#3B3F46]  rounded-xl h-full flex flex-col">
      {/* Tabs and Filters Section */}
    
      <div className="border-b border-[#3B3F46] flex justify-between items-center">
  <div className="flex">
    {["Trades", "Orders", "History"].map((tab) => (
      <button
        key={tab}
        className={`relative px-4 py-3 text-sm font-semibold transition-all duration-300 ${
          activeTab === tab
            ? 'text-white border-b-2 border-white'
            : 'text-[#A1A1A5] border-b-2 border-transparent'
        }`}
        onClick={() => setActiveTab(tab)}
      >
        {tab}
      </button>
    ))}
  </div>


        
        {/* Conditional Filter Settings */}
        {activeTab === "Orders" && (
          <div className="flex space-x-4 items-center">
                    
            <div className="flex bg-[#0F1011] rounded-full overflow-hidden p-1">
              <button
                className={`px-4 py-1 text-xs rounded-full transition-all duration-300 ${marketType === "Market" ? 'bg-[#181C20] text-white' : 'text-[#404446]'}`}
                onClick={() => setMarketType("Market")}
              >
                Market
              </button>
              <button
                className={`px-4 py-1 text-xs rounded-full transition-all duration-300 ${marketType === "Limit" ? 'bg-[#181C20] text-white' : 'text-[#404446]'}`}
                onClick={() => setMarketType("Limit")}
              >
                Limit
              </button>
            </div>
            <div className="flex space-x-2 pr-4">
              <span className="text-[#404446] text-sm font-semibold">Current Ticker Only</span>
              <Switch
                checked={groupByTicker}
                onChange={setGroupByTicker}
                className={`${groupByTicker ? "bg-green-500" : "bg-gray-600"} 
                  relative inline-flex h-6 w-11 items-center rounded-full transition-all duration-300`}
              >
                <span
                  className={`${
                    groupByTicker ? "translate-x-6" : "translate-x-1"
                  } inline-block h-4 w-4 transform bg-white rounded-full transition-all`}
                />
              </Switch>
            </div>
          </div>
        )}
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex-grow flex items-center justify-center">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}

      {/* Error State - Missing Tables */}
      {!isLoading && errorMessage && (
        <div className="flex-grow flex items-center justify-center flex-col p-6">
          <p className="text-yellow-500 mb-4">{errorMessage}</p>
          <div className="bg-[#1F2329] p-4 rounded-lg text-sm text-gray-300 max-w-2xl">
            <p className="font-semibold mb-2 text-white">Required Tables in Supabase:</p>
            <ul className="list-disc pl-5 space-y-1">
              <li><span className="text-blue-400">trades</span> - For completed trades</li>
              <li><span className="text-blue-400">orders</span> - For active and filled orders</li>
              <li><span className="text-blue-400">trade_history</span> - For trade execution history</li>
            </ul>
            <div className="mt-4 p-3 bg-[#181C20] rounded border border-gray-700">
              <p className="text-xs text-gray-400 mb-2">Create the tables in Supabase dashboard or run SQL:</p>
              <code className="text-xs text-green-300 block overflow-auto max-h-32">
                CREATE TABLE trades (<br/>
                &nbsp;&nbsp;id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),<br/>
                &nbsp;&nbsp;wallet_address TEXT NOT NULL,<br/>
                &nbsp;&nbsp;ticker TEXT NOT NULL,<br/>
                &nbsp;&nbsp;qty NUMERIC NOT NULL,<br/>
                &nbsp;&nbsp;entry_price NUMERIC NOT NULL,<br/>
                &nbsp;&nbsp;exit_price NUMERIC NOT NULL,<br/>
                &nbsp;&nbsp;entry_value NUMERIC NOT NULL,<br/>
                &nbsp;&nbsp;exit_value NUMERIC NOT NULL,<br/>
                &nbsp;&nbsp;closed_pnl NUMERIC NOT NULL,<br/>
                &nbsp;&nbsp;dex TEXT NOT NULL,<br/>
                &nbsp;&nbsp;tx_hash TEXT,<br/>
                &nbsp;&nbsp;timestamp TIMESTAMPTZ DEFAULT NOW()<br/>
                );
              </code>
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {!isLoading && !errorMessage && getActiveData().length === 0 && (
        <div className="flex-grow flex items-center justify-center text-gray-500">
          <p>{user?.wallet?.address ? `No ${activeTab.toLowerCase()} found` : 'Connect your wallet to view your trades'}</p>
        </div>
      )}

      {/* Table Container */}
      {!isLoading && getActiveData().length > 0 && (
        <div className="flex-grow overflow-auto">
          <table className="w-full text-left text-white">
            {/* Table Header */}
            <thead className="text-[#BBBBBB]">
              <tr>
                {getColumnHeaders().map((col) => (
                  <th 
                    key={col} 
                    className="py-4 px-[2rem] whitespace-nowrap text-sm cursor-pointer hover:text-white"
                    onClick={() => requestSort(col)}
                  >
                    {col}{getSortIndicator(col)}
                  </th>
                ))}
              </tr>
            </thead>

            {/* Table Body */}
            <tbody>
              {activeTab === "Trades" &&
                (getActiveData() as Trade[]).map((trade: Trade, index: number) => (
                  <tr key={trade.id || index} className="border-b border-gray-700 text-sm">
                    <td className="px-[2rem] py-3">{trade.ticker}</td>
                    <td className="px-[2rem] py-2">{formatNumber(trade.qty)}</td>
                    <td className="px-[2rem] py-2">{formatCurrency(trade.entry_price)}</td>
                    <td className="px-[2rem] py-2">{formatCurrency(trade.exit_price)}</td>
                    <td className="px-[2rem] py-2">{formatCurrency(trade.entry_value)}</td>
                    <td className="px-[2rem] py-2">{formatCurrency(trade.exit_value)}</td>
                    <td className={`px-[2rem] py-2 ${parseFloat(trade.closed_pnl?.toString() || '0') >= 0 ? "text-green-400" : "text-red-400"}`}>
                      {formatCurrency(trade.closed_pnl)}
                    </td>
                    <td className="px-[2rem] py-2 whitespace-nowrap">{formatDate(trade.timestamp)}</td>
                  </tr>
                ))
              }

              {activeTab === "Orders" &&
                (getActiveData() as Order[]).map((order: Order, index: number) => (
                  <tr key={order.id || index} className="border-b border-gray-700 text-sm">
                    <td className="px-[2rem] py-3">{order.ticker}</td>
                    <td className="px-[2rem] py-2">{order.type}</td>
                    <td className="px-[2rem] py-2">{formatNumber(order.qty)}</td>
                    <td className="px-[2rem] py-2">{formatNumber(order.share)}%</td>
                    <td className="px-[2rem] py-2">{formatCurrency(order.value)}</td>
                    <td className="px-[2rem] py-2">{formatCurrency(order.target_price)}</td>
                    <td className="px-[2rem] py-2">{formatCurrency(order.target_pnl)}</td>
                    <td className="px-[2rem] py-2">{order.status}</td>
                    <td className="px-[2rem] py-2 whitespace-nowrap">{formatDate(order.timestamp)}</td>
                  </tr>
                ))
              }

              {activeTab === "History" &&
                (getActiveData() as TradeHistory[]).map((historyItem: TradeHistory, index: number) => (
                  <tr key={historyItem.id || index} className="border-b border-gray-700 text-sm">
                    <td className="px-[2rem] py-3">{historyItem.ticker}</td>
                    <td className={`px-[2rem] py-2 ${historyItem.trade_type === 'Buy' ? 'text-green-400' : 'text-red-400'}`}>
                      {historyItem.trade_type}
                    </td>
                    <td className="px-[2rem] py-2">{formatNumber(historyItem.qty)}</td>
                    <td className="px-[2rem] py-2">{formatCurrency(historyItem.value)}</td>
                    <td className="px-[2rem] py-2">{formatNumber(historyItem.filled_price)} {historyItem.token_out_symbol}</td>
                    <td className="px-[2rem] py-2 whitespace-nowrap">{formatDate(historyItem.timestamp)}</td>
                    <td className="px-[2rem] py-2 capitalize">{historyItem.dex}</td>
                  </tr>
                ))
              }
            </tbody>
          </table>
        </div>
      )}
 {modalOpen && <TradingInterface onClose={() => setModalOpen(false)} />}
    </div>
    
  );
};

export default TradesTable;
