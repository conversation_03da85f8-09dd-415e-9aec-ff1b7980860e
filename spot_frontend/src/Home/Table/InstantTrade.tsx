import { useState } from 'react';
import { Settings, X, ArrowLeftRight } from 'lucide-react';

export default function TradingInterface({ onClose }) {
  const [activeTab, setActiveTab] = useState('P3');
  const [buyAmount, setBuyAmount] = useState(0);
  const [sellMode, setSellMode] = useState('percent'); // 'percent' or 'sol'
  const [modalOpen, setModalOpen] = useState(true);
  const SOL= "https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png";
  
  // Toggle between percent and SOL modes
  const toggleSellMode = () => {
    setSellMode(sellMode === 'percent' ? 'sol' : 'percent');
  };
  
  if (!modalOpen) return null;
  
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 ">
      <div className="flex flex-col w-full max-w-md bg-[#181C20] text-white rounded-lg overflow-hidden border border-gray-600 shadow-xl">
        {/* Top Navigation */}
        <div className="flex items-center justify-between px-3 py-2 border-b border-gray-800">
          <div className="flex items-center space-x-4">
           
            <div className="flex space-x-2">
              <button className="px-2 py-1 text-gray-400">P1</button>
              <button className="px-2 py-1 text-gray-400">P2</button>
              <button className="px-2 py-1 text-white font-medium">P3</button>

            </div>
          </div>
          <div className="flex items-center space-x-3">
      
          
            <button className="text-gray-400" onClick={onClose}>
              <X size={20} />
            </button>
          </div>
        </div>
        
        {/* Buy Section */}
        <div className="p-3 border-b border-gray-800">
          <div className="flex items-center justify-between mb-3">
            <span className="text-lg font-medium">Buy</span>
            <div className="py-2 px-1 border-r border-gray-800 flex items-center justify-center">
    <img src={SOL} alt="cyan bars" className="w-5 h-5 mr-1" />
    <span className="text-white">0</span>
  </div>
          </div>
          
          <div className="grid grid-cols-4 gap-2 mb-3">
            <button className="bg-transparent border border-emerald-500 rounded-full py-2 text-center text-emerald-500">0.01</button>
            <button className="bg-transparent border border-emerald-500 rounded-full py-2 text-center text-emerald-500">0.1</button>
            <button className="bg-transparent border border-emerald-500 rounded-full py-2 text-center text-emerald-500">1</button>
            <button className="bg-transparent border border-emerald-500 rounded-full py-2 text-center text-emerald-500">10</button>
          </div>
          
          <div className="flex items-center space-x-2 text-sm">
            <div className="flex items-center">
              <span className="text-gray-400 mr-1">↯</span>
              <span className="text-gray-300">20%</span>
            </div>
            <div className="flex items-center">
              <span className="text-yellow-500 mr-1">⊞</span>
              <span className="text-yellow-500">0.001</span>
              <span className="text-yellow-500 ml-1">⚠</span>
            </div>
            <div className="flex items-center">
              <span className="text-yellow-500 mr-1">◎</span>
              <span className="text-yellow-500">0.001</span>
              <span className="text-yellow-500 ml-1">⚠</span>
            </div>
            <div className="flex items-center">
              <span className="text-gray-400 mr-1">⌀</span>
              <span className="text-gray-300">Off</span>
            </div>
          </div>
        </div>
        
        {/* Sell Section */}
        <div className="p-3 border-b border-gray-800">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <span className="text-lg font-medium mr-2">Sell</span>
              <span className="text-gray-400 mr-1">{sellMode === 'percent' ? '%' : 'SOL'}</span>
              <button onClick={toggleSellMode} className="text-gray-400">
                <ArrowLeftRight size={16} />
              </button>
            </div>
            <div className="text-right">
  <span className="inline-flex items-center text-gray-400">
    0 CKI · $0 ·
    <img src={SOL} alt="solana icon" className="w-5 h-5 ml-1 mr-1" />
    <span className="text-white">0</span>
  </span>
</div>






          </div>
          
          {sellMode === 'percent' ? (
            <div className="grid grid-cols-4 gap-2 mb-3">
              <button className="bg-transparent border border-rose-500 rounded-full py-2 text-center text-rose-500">10%</button>
              <button className="bg-transparent border border-rose-500 rounded-full py-2 text-center text-rose-500">25%</button>
              <button className="bg-transparent border border-rose-500 rounded-full py-2 text-center text-rose-500">50%</button>
              <button className="bg-transparent border border-rose-500 rounded-full py-2 text-center text-rose-500">100%</button>
            </div>
          ) : (
            <div className="grid grid-cols-4 gap-2 mb-3">
              <button className="bg-transparent border border-rose-500 rounded-full py-2 text-center text-rose-500">0.01</button>
              <button className="bg-transparent border border-rose-500 rounded-full py-2 text-center text-rose-500">0.1</button>
              <button className="bg-transparent border border-rose-500 rounded-full py-2 text-center text-rose-500">1</button>
              <button className="bg-transparent border border-rose-500 rounded-full py-2 text-center text-rose-500">10</button>
            </div>
          )}
          
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <div className="flex items-center">
                <span className="text-gray-400 mr-1">↯</span>
                <span className="text-gray-300">40%</span>
              </div>
              <div className="flex items-center">
                <span className="text-yellow-500 mr-1">⊞</span>
                <span className="text-yellow-500">0.001</span>
                <span className="text-yellow-500 ml-1">⚠</span>
              </div>
              <div className="flex items-center">
                <span className="text-yellow-500 mr-1">◎</span>
                <span className="text-yellow-500">0.001</span>
                <span className="text-yellow-500 ml-1">⚠</span>
              </div>
              <div className="flex items-center">
                <span className="text-gray-400 mr-1">⌀</span>
                <span className="text-gray-300">Off</span>
              </div>
            </div>
            <span className="text-rose-400">Sell Init.</span>
          </div>
        </div>
        
        {/* Bottom Stats */}
        <div className="grid grid-cols-4 text-center border-t border-gray-800">
  <div className="py-2 px-1 border-r border-gray-800 flex items-center justify-center">
    <img src={SOL} alt="cyan bars" className="w-5 h-5 mr-1" />
    <span className="text-white">0</span>
  </div>
  <div className="py-2 px-1 border-r border-gray-800 flex items-center justify-center">
    <img src={SOL} alt="cyan bars" className="w-5 h-5 mr-1" />
    <span className="text-white">0</span>
  </div>
  <div className="py-2 px-1 border-r border-gray-800 flex items-center justify-center">
    <img src={SOL} alt="cyan bars" className="w-5 h-5 mr-1" />
    <span className="text-white">0</span>
  </div>
  <div className="py-2 px-1 flex items-center justify-center">
    <img src={SOL} alt="cyan bars" className="w-5 h-5 mr-1" />
    <span className="text-white">+0(+0%)</span>
  </div>
</div>

      </div>
    </div>
  );
}