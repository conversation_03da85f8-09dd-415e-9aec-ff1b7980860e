import React, { MouseEvent } from "react";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { homeAPI } from "../../utils/api"; // Import the homeAPI from your utils
import useTokenSelection from "../../hooks/useTokenSelection"; // Removed TokenInfo import

// Define interface for the token data specific to Radar
interface RadarToken {
  id?: string; // Assuming ID might exist
  image?: string;
  name?: string;
  symbol?: string;
  contract?: string;
  current_price?: number | null;
  price?: number | null; // Alternative price field?
  price_change_percentage_24h?: number | null;
  price_change_percentage?: number | null; // Alternative change field?
  market_cap?: number | null;
  fdv?: number | null;
  fully_diluted_valuation?: number | null; // Alternative FDV field?
  holders?: number | null;
  top_10_holders?: number | null;
  top_100_holders?: number | null;
  total_volume?: number | null;
  volume_24h?: number | null; // Alternative volume field?
  transactions_24h?: number | null;
  traders_24h?: number | null;
  age_days?: number | null;
  liquidity?: number | null;
  social_links?: {
    website?: string | null;
    twitter?: string | null;
    telegram?: string | null;
  } | null;
  // Add any other fields that might be present
  [key: string]: any; // Allow other potential properties
}

// Define interface for component props
interface RadarProps {
  selectedNetwork: string;
  timeframe: string;
  activeStatus: string | null; // Assuming activeStatus can be null or string
}

// Define interface for sorting state
interface SortConfig {
  key: keyof RadarToken | null;
  direction: "asc" | "desc";
}
const cryptoIcons = {
  ethereum: "https://mobulastorage.blob.core.windows.net/mobula-assets/assets/logos/d90e937d915ab0c865ff6e335361386c56524d4d33fb66f2f04defe1500082eb.png",
  bitcoin: "https://coin-images.coingecko.com/coins/images/825/large/bnb-icon2_2x.png?1696501970",
  Solana: "https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png",
  "BNB Smart Chain (BEP20)":"https://coin-images.coingecko.com/coins/images/825/large/bnb-icon2_2x.png?1696501970"
};
const Radar: React.FC<RadarProps> = ({ selectedNetwork, timeframe, activeStatus }) => {
  const [data, setData] = useState<RadarToken[]>([]); // Use RadarToken type
  const [filteredData, setFilteredData] = useState<RadarToken[]>([]); // Use RadarToken type
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>("");
  // Use SortConfig type for state
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: "asc" });
  
  // Use the custom token selection hook - apply type assertion
  const { 
    selectedTokens, 
    handleTokenSelect, 
    isTokenSelected,
    // toggleSelection // Assuming toggleSelection might not be used here based on original code
  } = useTokenSelection(); // Pass onSelectToken prop if needed by hook, otherwise remove

  const navigate = useNavigate();

  // SVG Icons
  const DownArrow = () => (
    <svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3.86961 5.46501C3.48635 6.14002 2.51365 6.14002 2.13039 5.46501L0.384009 2.38918C0.00550654 1.72253 0.487018 0.895432 1.25362 0.895432L4.74638 0.895432C5.51298 0.895432 5.99449 1.72253 5.61599 2.38917L3.86961 5.46501Z" fill="#ED2626"/>
    </svg>
  );
  
  const UpArrow = () => (
    <svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M2.13039 0.535C2.51365 -0.14 3.48635 -0.14 3.86961 0.535L5.61599 3.61083C5.99449 4.27747 5.51298 5.10457 4.74638 5.10457H1.25362C0.487018 5.10457 0.00550654 4.27747 0.384009 3.61082L2.13039 0.535Z" fill="#16A34A"/>
    </svg>
  );

  const SortArrows = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="white"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="white"/>
    </svg>
  );

  const ActiveUpArrow = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="#444"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="#16A34A"/>
    </svg>
  );

  const ActiveDownArrow = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="#ED2626"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="#444"/>
    </svg>
  );

  // Add token selection icons
  const ActiveTokenSelectIcon = () => (
    <svg width="11" height="12" viewBox="0 0 11 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.42857 0.770645H1.57143C0.704956 0.770645 0 1.48749 0 2.36857V8.05007C0 8.93116 0.704956 9.648 1.57143 9.648H5.10714V11.1572H3.28903C3.07207 11.1572 2.89617 11.336 2.89617 11.5567C2.89617 11.7773 3.07207 11.9561 3.28903 11.9561H7.71097C7.92812 11.9561 8.10383 11.7773 8.10383 11.5567C8.10383 11.336 7.92812 11.1572 7.71097 11.1572H5.89286V9.648H9.42857C10.2952 9.648 11 8.93116 11 8.05007V2.36857C11 1.48749 10.2952 0.770645 9.42857 0.770645Z" fill="#0A7AFF"/>
      <path d="M7.85714 6.79506H3.14286C2.9259 6.79506 2.75 6.97393 2.75 7.19454C2.75 7.41515 2.9259 7.59402 3.14286 7.59402H7.85714C8.07429 7.59402 8.25 7.41515 8.25 7.19454C8.25 6.97393 8.07429 6.79506 7.85714 6.79506ZM4.28843 6.0669C4.36305 6.15916 4.47431 6.21261 4.59171 6.21261C4.70911 6.21261 4.82036 6.15916 4.89498 6.0669L5.59073 5.20747L5.81689 5.48718C5.89017 5.57769 5.99874 5.63094 6.11384 5.63289C6.22433 5.62528 6.33943 5.5851 6.41539 5.49713L8.15217 3.48782C8.29527 3.32202 8.27954 3.06961 8.11649 2.9241C7.95382 2.77839 7.70522 2.79419 7.56212 2.96038L6.13034 4.61683L5.89401 4.32502C5.81958 4.23275 5.70813 4.17931 5.59073 4.17931C5.47334 4.17931 5.36208 4.23275 5.28746 4.32502L4.59171 5.18445L4.11445 4.59498C3.97672 4.4245 3.72907 4.39992 3.56161 4.54036C3.39396 4.68042 3.36998 4.93224 3.5079 5.10253L4.28843 6.0669Z" fill="#0A7AFF"/>
    </svg>
  );

  const InactiveTokenSelectIcon = () => (
    <svg width="11" height="12" viewBox="0 0 11 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9.42857 0.770645H1.57143C0.704956 0.770645 0 1.48749 0 2.36857V8.05007C0 8.93116 0.704956 9.648 1.57143 9.648H5.10714V11.1572H3.28903C3.07207 11.1572 2.89617 11.336 2.89617 11.5567C2.89617 11.7773 3.07207 11.9561 3.28903 11.9561H7.71097C7.92812 11.9561 8.10383 11.7773 8.10383 11.5567C8.10383 11.336 7.92812 11.1572 7.71097 11.1572H5.89286V9.648H9.42857C10.2952 9.648 11 8.93116 11 8.05007V2.36857C11 1.48749 10.2952 0.770645 9.42857 0.770645ZM10.2143 8.05007C10.2143 8.49071 9.86171 8.84904 9.42857 8.84904H1.57143C1.1381 8.84904 0.785714 8.49071 0.785714 8.05007V2.36857C0.785714 1.92793 1.1381 1.56961 1.57143 1.56961H9.42857C9.86171 1.56961 10.2143 1.92793 10.2143 2.36857V8.05007Z" fill="white"/>
      <path d="M7.85714 6.79506H3.14286C2.9259 6.79506 2.75 6.97393 2.75 7.19454C2.75 7.41515 2.9259 7.59402 3.14286 7.59402H7.85714C8.07429 7.59402 8.25 7.41515 8.25 7.19454C8.25 6.97393 8.07429 6.79506 7.85714 6.79506ZM4.28843 6.0669C4.36305 6.15916 4.47431 6.21261 4.59171 6.21261C4.70911 6.21261 4.82036 6.15916 4.89498 6.0669L5.59073 5.20747L5.81689 5.48718C5.89017 5.57769 5.99874 5.63094 6.11384 5.63289C6.22433 5.62528 6.33943 5.5851 6.41539 5.49713L8.15217 3.48782C8.29527 3.32202 8.27954 3.06961 8.11649 2.9241C7.95382 2.77839 7.70522 2.79419 7.56212 2.96038L6.13034 4.61683L5.89401 4.32502C5.81958 4.23275 5.70813 4.17931 5.59073 4.17931C5.47334 4.17931 5.36208 4.23275 5.28746 4.32502L4.59171 5.18445L4.11445 4.59498C3.97672 4.4245 3.72907 4.39992 3.56161 4.54036C3.39396 4.68042 3.36998 4.93224 3.5079 5.10253L4.28843 6.0669Z" fill="white"/>
    </svg>
  );

  // Fetch token radar data using the homeAPI utility
  useEffect(() => {
    const fetchTokenRadarData = async () => {
      try {
        setLoading(true);
        const network = selectedNetwork === "Global" ? "universal" : selectedNetwork.toLowerCase();
        const result = await homeAPI.getTokenRadar({
          limit: 30,
          network,
          timeframe
        });

        // Type seenSymbols explicitly
        const uniqueTokens: RadarToken[] = []; 
        const seenSymbols: Record<string, boolean> = {}; 
        
        if (result && Array.isArray(result)) { // Ensure result is an array
          // Use type assertion for the result array if needed, or ensure API returns RadarToken[]
          const typedResult = result as RadarToken[]; 
          typedResult.forEach((token: RadarToken) => { // Type token
            if (token.symbol) {
              const symbol = token.symbol.toUpperCase();
              if (!seenSymbols[symbol]) {
                seenSymbols[symbol] = true;
                uniqueTokens.push(token);
              }
            } else {
              uniqueTokens.push(token); // Keep tokens with no symbol
            }
          });
          setData(uniqueTokens);
        } else {
          setData([]); // Set empty array if data is not array or invalid
        }
        setError(""); // Clear error on success
      } catch (err: unknown) { // Catch as unknown
         // Type guard for error message
        let message = "Failed to load token radar data.";
        if (err instanceof Error) {
           message = err.message;
        }
        setError(message);
        setData([]); // Reset data on error
      } finally {
        setLoading(false);
      }
    };

    fetchTokenRadarData();

    const intervalId = setInterval(() => {
      fetchTokenRadarData();
    }, 300000);
    
    return () => {
      clearInterval(intervalId);
    };
    
  }, [selectedNetwork, timeframe]); 

  // Filter data based on activeStatus
  useEffect(() => {
    if (!data) {
        setFilteredData([]);
        return;
    };

    const filtered = data.filter((token) => { // Keep implicit 'any' or adjust RadarToken if needed
      const age = token.age_days ? parseFloat(String(token.age_days)) : 0; // Ensure age is number
      
      if (!activeStatus || activeStatus === "All") return true;
      
      switch (activeStatus) {
        case "New":
          return age < 7;
        case "Blooming":
          return age >= 7 && age <= 30;
        case "Thriving":
          return age > 30;
        default:
          return true;
      }
    });

    setFilteredData(filtered);
  }, [data, activeStatus]);

  useEffect(() => {
    // Storing selected tokens might need adjustment if using TokenInfo vs RadarToken
    // localStorage.setItem("selectedTokens", JSON.stringify(Array.from(selectedTokens)));
  }, [selectedTokens]);

  // This might need adjustment if TokenInfo vs RadarToken is different
  const handleTokenClick = (token: RadarToken) => { // Use RadarToken
    handleTokenSelect(null, token as any); // Use assertion if hook expects different type
    navigate('/trade', { state: { selectedTokens: [token], activeToken: token } });
  };

  // Add type for key parameter
  const requestSort = (key: keyof RadarToken) => { 
    let direction: "asc" | "desc" = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });

    // Sort filteredData instead of data
    const sortedData = [...filteredData].sort((a, b) => {
      const valA = a[key] ?? 0;
      const valB = b[key] ?? 0;

      if (valA === null || valA === undefined) return direction === "asc" ? -1 : 1;
      if (valB === null || valB === undefined) return direction === "asc" ? 1 : -1;

      if (a[key] < b[key]) return direction === "asc" ? -1 : 1;
      if (a[key] > b[key]) return direction === "asc" ? 1 : -1;
      return 0;
    });
    setFilteredData(sortedData); // Update filtered data
  };

  // Add type for column parameter
  const getSortArrow = (column: keyof RadarToken) => { 
    if (sortConfig.key === column) {
      return sortConfig.direction === "asc" ? <ActiveUpArrow /> : <ActiveDownArrow />;
    }
    return <SortArrows />;
  };

  // Add types for parameters
  const handleRowClick = async (e: MouseEvent<HTMLDivElement>, token: RadarToken) => {
    handleTokenSelect(e, token);
    localStorage.setItem("activeToken", JSON.stringify(token));
    navigate('/trade');
  };

  // Format number - Add type
  const formatNumber = (num: number | null | undefined): string => {
    if (num === null || num === undefined || isNaN(num)) return "--";
    if (num >= 1e12) return (num / 1e12).toFixed(2) + "T"; 
    if (num >= 1e9) return (num / 1e9).toFixed(2) + "B";   
    if (num >= 1e6) return (num / 1e6).toFixed(2) + "M";   
    return num.toLocaleString(); 
  };

  // Format contract - Add type
  const formatContract = (contract: string | null | undefined): string => {
    if (!contract) return "--";
    return `${contract.slice(0, 6)}...${contract.slice(-4)}`;
  };

  // Format age - Add type
  const formatAge = (days: number | null | undefined): string => {
    if (days === null || days === undefined || isNaN(days)) return "--";
    
    if (days >= 365) {
      const years = Math.floor(days / 365);
      const remainingDays = Math.round(days % 365);
      return `${years}y${remainingDays > 0 ? ` ${remainingDays}d` : ''}`;
    }
    if (days >= 30) {
      const months = Math.floor(days / 30);
      const remainingDays = Math.round(days % 30);
      return `${months}m${remainingDays > 0 ? ` ${remainingDays}d` : ''}`;
    }
    return `${Math.round(days)}d`;
  };

  // Get explorer URL - Add type
  const getExplorerUrl = (contract: string | null | undefined): string | null => {
    if (!contract) return null;
    switch (selectedNetwork.toLowerCase()) {
      case 'ethereum':
        return `https://etherscan.io/address/${contract}`;
      case 'solana':
        return `https://solscan.io/account/${contract}`;
      case 'base':
        return `https://basescan.org/address/${contract}`;
      case 'binance-smart-chain':
        return `https://bscscan.com/address/${contract}`;
      default:
        return null;
    }
  };

  // Handle contract click - Add type
  const handleContractClick = (e: React.MouseEvent, contract: string | null | undefined) => {
    e.preventDefault();
    e.stopPropagation(); 
    const explorerUrl = getExplorerUrl(contract);
    if (explorerUrl) {
      window.open(explorerUrl, '_blank', 'noopener,noreferrer');
    }
  };

  // Add type for url
  const handleSocialLinkClick = (e: React.MouseEvent, url: string | null | undefined) => {
    e.preventDefault();
    e.stopPropagation(); 
    if (url) {
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  // Deduplicate tokens - Add types
  const deduplicateTokens = (tokens: RadarToken[]): RadarToken[] => {
    const seen: Record<string, boolean> = {}; // Type seen
    return tokens.filter((token: RadarToken) => { // Type token
      if (!token.symbol) return true;
      const symbol = token.symbol.toUpperCase();
      if (seen[symbol]) return false;
      seen[symbol] = true;
      return true;
    });
  };

  // Format price with subscript logic (copied from Market.tsx, needs React import)
  const formatPriceWithSubscript = (price: number | null): React.ReactNode => {
    if (price === null || price === undefined || isNaN(price)) {
      return "$0.00";
    }
    if (price > 0 && price < 0.01) {
      const priceStr = price.toExponential(3); 
      const [coefficient, exponent] = priceStr.split('e');
      const expValue = parseInt(exponent, 10);
      if (expValue <= -3) {
          const numZeros = Math.abs(expValue) - 1;
          const significantDigits = parseFloat(coefficient).toFixed(3).replace('.','');
          if (numZeros > 0) {
               return (
                  <span className="whitespace-nowrap">
                      $0.0<sub>{numZeros}</sub>{significantDigits}
                  </span>
               );
          }
      }
      return `$${price.toFixed(6)}`; 
    } else {
      return `$${price.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })}`;
    }
  };


  return (
    <div className="bg-[#141416] rounded-xl mx-3 mt-3 h-[calc(100vh-150px)] overflow-hidden">
      <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-[#BBBBBB]">
        <table className="w-full text-white border-collapse">
          {/* Sticky Table Header */}
          <thead className="bg-[#181C20] text-white border-b border-white/50 sticky top-0 z-10">
            <tr className="text-left font-extralight">
              {/* Select All Checkbox */}
              <th className="px-6 py-4 text-sm">
                {/* Select All functionality might need implementation */}
                <button>
                  {selectedTokens.length > 0 ? <ActiveTokenSelectIcon /> : <InactiveTokenSelectIcon />}
                </button>
              </th>

              {/* Token */}
              <th className="px-6 py-4 text-sm">
                <div className="flex items-center">
                  <span>Token</span>
                </div>
              </th>

              {/* Contract */}
              <th className="px-6 py-4 text-sm">
                <div className="flex items-center">
                  <span>Contract</span>
                </div>
              </th>

              {/* Price / 24h% */}
              <th className="px-6 py-4 text-sm">
                <div className="flex items-center gap-2">
                  <span className="cursor-pointer flex items-center" onClick={() => requestSort("current_price")}>
                    Price <span className="ml-1">{getSortArrow("current_price")}</span>
                  </span>
                  <span>/</span>
                  <span className="cursor-pointer flex items-center" onClick={() => requestSort("price_change_percentage_24h")}>
                    24h% <span className="ml-1">{getSortArrow("price_change_percentage_24h")}</span>
                  </span>
                </div>
              </th>

              {/* MCap / FDV */}
              <th className="px-6 py-4 text-sm">
                <div className="flex items-center gap-2">
                  <span className="cursor-pointer flex items-center" onClick={() => requestSort("market_cap")}>
                    MCap <span className="ml-1">{getSortArrow("market_cap")}</span>
                  </span>
                  <span>/</span>
                  <span className="cursor-pointer flex items-center" onClick={() => requestSort("fdv")}>
                    FDV <span className="ml-1">{getSortArrow("fdv")}</span>
                  </span>
                </div>
              </th>

              {/* Holders / Top 10 / Top 100 */}
              <th className="px-6 py-4 text-sm">
                <div className="flex items-center gap-2">
                  {/* Sorting might be complex here, only showing title */}
                  <span>Holders</span> 
                  {/* <span>/</span>
                  <span>Top 10</span>
                  <span>/</span>
                  <span>Top 100</span> */} 
                </div>
              </th>

              {/* 24h Vol / Txs / Traders */}
              <th className="px-6 py-4 text-sm">
                <div className="flex items-center gap-2">
                  <span className="cursor-pointer flex items-center" onClick={() => requestSort("total_volume")}>
                    24h Vol <span className="ml-1">{getSortArrow("total_volume")}</span>
                  </span>
                  {/* <span>/</span>
                  <span>Txs</span>
                  <span>/</span>
                  <span>Traders</span> */} 
                </div>
              </th>

              {/* Age */}
              <th className="px-6 py-4 text-sm">
                <div className="flex items-center">
                  <span className="cursor-pointer flex items-center" onClick={() => requestSort("age_days")}>
                    Age <span className="ml-1">{getSortArrow("age_days")}</span>
                  </span>
                </div>
              </th>

              {/* Liquidity */}
              <th className="px-6 py-4 text-sm">
                <div className="flex items-center">
                  <span className="cursor-pointer flex items-center" onClick={() => requestSort("liquidity")}>
                    Liquidity <span className="ml-1">{getSortArrow("liquidity")}</span>
                  </span>
                </div>
              </th>

              {/* Social Media */}
              <th className="px-6 py-4 text-sm">
                <div className="flex items-center">
                  <span>Social Media</span>
                </div>
              </th>
            </tr>
          </thead>

          {/* Scrollable Table Body */}
          <tbody>
          {loading ? (
  <>
    {Array.from({ length: 10 }).map((_, i) => (
      <tr key={i} className="animate-pulse bg-[#181C20]">
        <td className="px-6 py-4">
          <div className="w-6 h-6 rounded-md bg-gray-700" />
        </td>
        <td className="px-6 py-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-full bg-gray-700" />
            <div>
              <div className="w-16 h-4 bg-gray-700 rounded mb-1" />
              <div className="w-24 h-3 bg-gray-700 rounded" />
            </div>
          </div>
        </td>
        <td className="px-6 py-4">
          <div className="w-24 h-4 bg-gray-700 rounded" />
        </td>
        <td className="px-6 py-4">
          <div className="w-20 h-4 bg-gray-700 rounded mb-1" />
          <div className="w-16 h-3 bg-gray-700 rounded" />
        </td>
        <td className="px-6 py-4">
          <div className="w-24 h-4 bg-gray-700 rounded mb-1" />
          <div className="w-24 h-3 bg-gray-700 rounded" />
        </td>
        <td className="px-6 py-4">
          <div className="w-20 h-4 bg-gray-700 rounded" />
        </td>
        <td className="px-6 py-4">
          <div className="w-24 h-4 bg-gray-700 rounded" />
        </td>
        <td className="px-6 py-4">
          <div className="w-16 h-4 bg-gray-700 rounded" />
        </td>
        <td className="px-6 py-4">
          <div className="w-16 h-4 bg-gray-700 rounded" />
        </td>
        <td className="px-6 py-4">
          <div className="flex space-x-2">
            <div className="w-4 h-4 bg-gray-700 rounded-full" />
            <div className="w-4 h-4 bg-gray-700 rounded-full" />
            <div className="w-4 h-4 bg-gray-700 rounded-full" />
          </div>
        </td>
      </tr>
    ))}
  </>
) : error ? (
  // keep your existing error block

              <tr>
                <td colSpan={10} className="text-center py-6 text-red-500 text-lg">{error}</td>
              </tr>
            ) : filteredData.length === 0 ? (
              <tr>
                <td colSpan={10} className="text-center py-6 text-lg">No tokens found matching the selected criteria.</td>
              </tr>
            ) : (
              deduplicateTokens(filteredData).map((token: RadarToken, index: number) => ( // Added types
                <tr
                  key={token.id || token.contract || index} // Use a more stable key if possible
                  onClick={(e) => handleRowClick(e, token)}
                  className={`text-sm px-6 bg-[#181C20] hover:bg-[#25282B] hover:rounded-lg transition-all duration-200 hover:p-4 cursor-pointer`}
                >
                  {/* Token Selection Column */}
                  <td className="px-6 py-4">
                    <button
                      className="p-2 hover:bg-gray-700 rounded-lg transition-colors duration-200"
                      onClick={(e) => handleTokenSelect(e as any, token as any)} // Use assertion if hook types differ
                    >
                      {isTokenSelected(token as any) ? // Use assertion if hook types differ
                        <ActiveTokenSelectIcon /> : 
                        <InactiveTokenSelectIcon />
                      }
                    </button>
                  </td>

                  {/* Token column */}
                  <td className="px-6 py-4 flex items-center space-x-3">
                  <div className="relative w-8 h-8">
  <img src={token.image} alt={token.name} className="w-8 h-8 rounded-full" />
  {token.baseimage && (
    <img
      src={cryptoIcons[token.network]}
      alt="base"
      className="absolute bottom-[-4px] right-[-4px] w-4 h-4 rounded-full border-2 border-[#141416] shadow-md"
    />
    
  )}
</div>
                    <div>
                      <p className="font-bold">{token.symbol?.toUpperCase()}</p>
                      <p className="text-gray-400 text-sm">{token.name}</p>
                    </div>
                  </td>

                  {/* Contract column */}
                  <td className="px-6 py-4">
                    {token.id ? (
                      <button 
                        className="text-gray-400 hover:text-blue-500 cursor-pointer"
                        onClick={(e) => handleContractClick(e, token.id)}
                      >
                        {formatContract(token.id)}
                      </button>
                    ) : (
                      <p className="text-gray-400">--</p>
                    )}
                  </td>
                  
                  {/* Price/24h% column */} 
                  <td className="px-6 py-4">
                    {/* Use formatPriceWithSubscript */}
                    <p>{formatPriceWithSubscript(token.current_price ?? token.price ?? null)}</p> 
                    <p className={`flex items-center ${(token.price_change_percentage_24h ?? token.price_change_percentage ?? 0) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                      {(token.price_change_percentage_24h ?? token.price_change_percentage ?? 0) >= 0 ? <UpArrow /> : <DownArrow />}
                      <span className="ml-1">
                        {Math.abs(token.price_change_percentage_24h ?? token.price_change_percentage ?? 0).toFixed(2)}%
                      </span>
                    </p>
                  </td>
                  
                  {/* Market Cap/FDV column */}
                  <td className="px-6 py-4">
                    <p>${formatNumber(token.market_cap)}</p>
                    <p className="text-gray-400">${formatNumber(token.fdv ?? token.fully_diluted_valuation)}</p>
                  </td>
                  
                  {/* Holders info column */} 
                  <td className="px-6 py-4">
                    <p>{formatNumber(token.holders_count)}</p>
                    {/* Simplified view, add back if data is reliable 
                    <p className="text-gray-400">
                      {token.top_10_holders ? (token.top_10_holders + '%') : '--'} / 
                      {token.top_100_holders ? (token.top_100_holders + '%') : '--'}
                    </p>
                    */}
                  </td>
                  
                  {/* Volume/Txs/Traders column */} 
                  <td className="px-6 py-4">
                    <p>${formatNumber(token.total_volume ?? token.volume_24h)}</p>
                    {/* Simplified view, add back if needed
                    <p className="text-gray-400">
                      {token.transactions_24h ? (
                        <span title={`${token.transactions_24h.toLocaleString()} transactions in past 24h`}>
                          {formatNumber(token.transactions_24h)} txns
                        </span>
                      ) : '--'} / 
                      {token.traders_24h ? (
                        <span title={`${token.traders_24h.toLocaleString()} unique traders in past 24h`}>
                          {formatNumber(token.traders_24h)}
                        </span>
                      ) : '--'}
                    </p>
                     */}
                  </td>
                  
                  {/* Age column */}
                  <td className="px-6 py-4">
                    <p>{formatAge(token.age_days)}</p>
                  </td>
                  
                  {/* Liquidity column */}
                  <td className="px-6 py-4">
                     {/* Displaying liquidity needs clarification based on API data (ratio or absolute value?) */}
                     <p>{token.liquidity ? formatNumber(token.liquidity) : '--'}</p>
                  </td>
                  
                  {/* Social Media column */}
                  <td className="px-6 py-4">
                    <div className="flex space-x-3">
                      {token.social_links?.website && ( // Added optional chaining
                        <a
                          href={token.social_links.website}
                          onClick={(e) => handleSocialLinkClick(e, token.social_links?.website)} // Added optional chaining
                          className="text-gray-300 hover:text-white transition-colors duration-200"
                          title="Website"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                           {/* Website Icon */}
                           <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm7.5-6.923c-.67.204-1.335.82-1.887 1.855A7.97 7.97 0 0 0 5.145 4H7.5V1.077zM4.09 4a9.267 9.267 0 0 1 .64-1.539 6.7 6.7 0 0 1 .597-.933A7.025 7.025 0 0 0 2.255 4H4.09zm-.582 3.5c.03-.877.138-1.718.312-2.5H1.674a6.958 6.958 0 0 0-.656 2.5h2.49zM4.847 5a12.5 12.5 0 0 0-.338 2.5H7.5V5H4.847zM8.5 5v2.5h2.99a12.495 12.495 0 0 0-.337-2.5H8.5zM4.51 8.5a12.5 12.5 0 0 0 .337 2.5H7.5V8.5H4.51zm3.99 0V11h2.653c.187-.765.306-1.608.338-2.5h-2.49a13.65 13.65 0 0 1-.312 2.5zm2.802-3.5a6.959 6.959 0 0 0-.656-2.5H12.18c.174.782.282 1.623.312 2.5h2.49zM11.27 2.461c.247.464.462.98.64 1.539h1.835a7.024 7.024 0 0 0-3.072-2.472c.218.284.418.598.597.933zM10.855 4a7.966 7.966 0 0 0-.468-1.068C9.835 1.897 9.17 1.282 8.5 1.077V4h2.355z"/>
                          </svg>
                        </a>
                      )}
                      {token.social_links?.twitter && ( // Added optional chaining
                        <a
                          href={token.social_links.twitter}
                          onClick={(e) => handleSocialLinkClick(e, token.social_links?.twitter)} // Added optional chaining
                          className="text-gray-300 hover:text-white transition-colors duration-200"
                          title="Twitter/X"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                           {/* Twitter Icon */}
                           <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M12.6.75h2.454l-5.36 6.142L16 15.25h-4.937l-3.867-5.07-4.425 5.07H.316l5.733-6.57L0 .75h5.063l3.495 4.633L12.601.75Zm-.86 13.028h1.36L4.323 2.145H2.865l8.875 11.633Z"/>
                          </svg>
                        </a>
                      )}
                      {token.social_links?.telegram && ( // Added optional chaining
                        <a
                          href={token.social_links.telegram}
                          onClick={(e) => handleSocialLinkClick(e, token.social_links?.telegram)} // Added optional chaining
                          className="text-gray-300 hover:text-white transition-colors duration-200"
                          title="Telegram"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                           {/* Telegram Icon */}
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.287 5.906c-.778.324-2.334.994-4.666 2.01-.378.15-.577.298-.595.442-.03.243.275.339.69.47l.175.055c.408.133.958.288 1.243.294.26.006.549-.1.868-.32 2.179-1.471 3.304-2.214 3.374-2.23.05-.012.12-.026.166.016.047.041.042.12.037.141-.03.129-1.227 1.241-1.846 1.817-.193.18-.33.307-.358.336a8.154 8.154 0 0 1-.188.186c-.38.366-.664.64.015 1.088.327.216.589.393.85.571.284.194.568.387.936.629.093.06.183.125.27.187.331.236.63.448.997.414.214-.02.435-.22.547-.82.265-1.417.786-4.486.906-5.751a1.426 1.426 0 0 0-.013-.315.337.337 0 0 0-.114-.217.526.526 0 0 0-.31-.093c-.3.005-.763.166-2.984 1.09z"/>
                          </svg>
                        </a>
                      )}
                      {(!token.social_links?.website && !token.social_links?.twitter && !token.social_links?.telegram) && ( // Added optional chaining
                        <span className="text-gray-500">--</span>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Radar;
