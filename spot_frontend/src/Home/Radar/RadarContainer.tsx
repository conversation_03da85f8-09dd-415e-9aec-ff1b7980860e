import React from "react";
const deduplicateTokenData = (data) => {
  if (!data || !Array.isArray(data)) return data;
  
  const seen = {};
  return data.filter(token => {
    if (!token.symbol) return true;
    const symbol = token.symbol.toUpperCase();
    if (seen[symbol]) return false;
    seen[symbol] = true;
    return true;
  });
};

// Then in the useEffect or fetch function that loads data, apply the deduplication:
// Example:
// setTokenData(deduplicateTokenData(response.data)); 