import React from "react";
import { useState, useEffect } from "react";
import { Gi<PERSON><PERSON><PERSON><PERSON>, GiS<PERSON><PERSON>, GiThr<PERSON><PERSON>ea<PERSON> } from "react-icons/gi";

interface MenuProps {
  setActiveTable: (table: string) => void;
  selectedNetwork: string;
  setSelectedNetwork: (network: string) => void;
  onTimeframeChange?: (timeframe: string) => void;
  activeStatus: string;
  setActiveStatus: (status: string) => void;
}
const Menu: React.FC<MenuProps> = ({
  setActiveTable,
  selectedNetwork,
  setSelectedNetwork,
  onTimeframeChange,
  activeStatus,
  setActiveStatus,
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isGlobalOpen, setIsGlobalOpen] = useState<boolean>(false);
  const [activeTable, setActiveTableState] = useState<string>("highlight");
  const networks = [
    { name: "Global" },
    { name: "Sol<PERSON>" },
    // { name: "Ethereum" },
    // { name: "Base" },
    { name: "binance-smart-chain" },
  ];
  const [activeTime, setActiveTime] = useState<string>("24h");

  // Load user preferences from sessionStorage on component mount
  useEffect(() => {
    // Load selected network
    const savedNetwork = sessionStorage.getItem('selectedNetwork');
    if (savedNetwork) {
      setSelectedNetwork(savedNetwork);
    }
    
    // Load timeframe preference
    const savedTimeframe = sessionStorage.getItem('activeTimeframe');
    if (savedTimeframe) {
      setActiveTime(savedTimeframe);
      handleTimeframeChange(savedTimeframe, false); // Pass false to avoid infinite loop
    }
    
    // Load active status preference
    const savedStatus = sessionStorage.getItem('activeStatus');
    if (savedStatus) {
      setActiveStatus(savedStatus);
    }
    
    // Load active table preference
    const savedTable = sessionStorage.getItem('activeTable');
    if (savedTable) {
      setActiveTableState(savedTable);
      setActiveTable(savedTable);
    }
  }, [setSelectedNetwork, setActiveStatus, setActiveTable]);

  // Handle timeframe change
  const handleTimeframeChange = (time: string, saveToSession = true) => {
    // Convert time format to match API expectations
    let apiTimeframe;
    switch (time) {
      case "1h":
        apiTimeframe = "1h";
        break;
      case "4h":
        apiTimeframe = "4h";
        break;
      case "24h":
        apiTimeframe = "24h";
        break;
      case "7d":
        apiTimeframe = "7d";
        break;
      case "14d":
        apiTimeframe = "14d";
        break;
      case "30d":
        apiTimeframe = "30d";
        break;
      default:
        apiTimeframe = "24h";
    }
    setActiveTime(time);
    
    // Save to session storage if needed
    if (saveToSession) {
      sessionStorage.setItem('activeTimeframe', time);
    }
    
    onTimeframeChange?.(apiTimeframe);
  };

  // Handle network selection and save to sessionStorage
  const handleNetworkChange = (networkName: string) => {
    setSelectedNetwork(networkName);
    sessionStorage.setItem('selectedNetwork', networkName);
    setIsGlobalOpen(false);
  };
  
  // Handle status change and save to sessionStorage
  const handleStatusChange = (status: string) => {
    setActiveStatus(status);
    sessionStorage.setItem('activeStatus', status);
  };
  
  // Handle table change and save to sessionStorage
  const handleTableChange = (table: string) => {
    if(table === "radar") {
      setIsOpen(true);
    }
    setActiveTableState(table);
    setActiveTable(table);
    sessionStorage.setItem('activeTable', table);
  };

  return (
    <div className="relative bg-[#141416] px-4 md:px-6 py-4 flex flex-col md:flex-row items-center justify-between gap-4">
      {/* Left Section: Highlight & Radar Button */}
      <div className="flex flex-wrap gap-2 items-center w-full md:w-auto">
        {/* Highlight Button */}
        <button
          className={`flex items-center gap-2 px-4 py-3 rounded-3xl text-white ${
            activeTable === "highlight" ? "bg-[#1D2226]" : ""
          }`}
          onClick={() => {
            setIsOpen(false);
            handleTableChange("highlight");
          }}
        >
          <svg width="25" height="26" viewBox="0 0 25 26" fill="none">
            <path
              d="M1 13H8.58401C8.63198 13 8.6732 13.0341 8.68223 13.0812L10.8487 24.3845C10.8701 24.4962 11.0317 24.4912 11.0461 24.3783L13.9539 1.62166C13.9683 1.50883 14.1299 1.50379 14.1513 1.61551L16.3178 12.9188C16.3268 12.9659 16.368 13 16.416 13H24"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
          </svg>
          Highlight
        </button>
        {activeTable!=="radar" && (<button
          className={`flex items-center gap-2 px-4 py-3 rounded-3xl text-white `}
            onClick={() => {
              setIsOpen(true);
              handleTableChange("radar");
            }}
          >
            <svg
              width="22"
              height="22"
              viewBox="0 0 22 22"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13.1467 1.8177C10.7113 1.16514 8.24389 1.54731 6.20801 2.6959C5.84725 2.89943 5.3898 2.77197 5.18627 2.41121C4.98273 2.05045 5.11019 1.59301 5.47095 1.38947C7.83496 0.0557632 10.7051 -0.389421 13.5349 0.368813C19.2697 1.90544 22.6729 7.80006 21.1363 13.5348C19.7519 18.7013 14.8315 21.9744 9.67663 21.45C9.10883 21.3922 8.53851 21.2884 7.97032 21.1362C7.57022 21.029 7.33278 20.6177 7.43999 20.2176C7.54719 19.8175 7.95845 19.5801 8.35855 19.6873C8.84893 19.8187 9.34013 19.908 9.82853 19.9577C14.2629 20.4088 18.4966 17.5908 19.6874 13.1466C21.0096 8.21203 18.0812 3.13991 13.1467 1.8177ZM10.7526 5.50252C10.1711 5.50252 9.61323 5.59676 9.09243 5.77022C8.69942 5.9011 8.27474 5.68862 8.14386 5.29562C8.01298 4.90263 8.22546 4.47795 8.61845 4.34707C9.29023 4.12335 10.008 4.00252 10.7526 4.00252C11.9808 4.00252 13.1345 4.33123 14.1281 4.90598C14.4866 5.11338 14.6092 5.57218 14.4018 5.93073C14.1944 6.28927 13.7356 6.4118 13.377 6.20439C12.6056 5.75815 11.7101 5.50252 10.7526 5.50252ZM1.76136 4.85523C2.33537 3.97977 3.49315 3.91197 4.2264 4.50545L6.34644 6.22136C6.43614 6.24949 6.52202 6.29506 6.599 6.35856C6.67219 6.41893 6.731 6.49027 6.775 6.56823L11.2244 10.1695C11.5464 10.4301 11.5962 10.9024 11.3356 11.2243C11.075 11.5463 10.6027 11.596 10.2807 11.3354L6.24107 8.06583C5.77192 8.85164 5.50262 9.77004 5.50262 10.7525C5.50262 13.652 7.85313 16.0025 10.7526 16.0025C13.6521 16.0025 16.0026 13.652 16.0026 10.7525C16.0026 10.2983 15.9451 9.85864 15.8373 9.43984C15.7341 9.03874 15.9756 8.62984 16.3768 8.52662C16.7779 8.42341 17.1868 8.66493 17.29 9.06604C17.4289 9.60594 17.5026 10.1711 17.5026 10.7525C17.5026 14.4804 14.4805 17.5025 10.7526 17.5025C7.0247 17.5025 4.00262 14.4804 4.00262 10.7525C4.00262 9.41364 4.39308 8.1646 5.06592 7.11469L3.28271 5.67139C3.22686 5.6262 3.16403 5.61262 3.11414 5.61865C3.06872 5.62414 3.03795 5.64387 3.01577 5.6777C2.48977 6.47993 2.08025 7.37901 1.81781 8.35844C0.92306 11.6976 1.97418 15.1008 4.27981 17.3629C4.57548 17.653 4.58 18.1279 4.28991 18.4235C3.99982 18.7192 3.52497 18.7237 3.2293 18.4336C0.55323 15.8081 -0.671465 11.8529 0.36892 7.97021C0.67318 6.83469 1.14895 5.78926 1.76136 4.85523Z"
                fill="white"
              />
            </svg>
            Radar
          </button>
        )}

        {/* Radar Button */}
        {activeTable==="radar" && (
          <div className="flex flex-col md:flex-row items-center bg-[#181C20] text-white px-4 py-2 rounded-3xl w-full md:w-auto">
            <div className="flex items-center space-x-2 gap-2 pr-2">
              <span>
                <svg
                  width="22"
                  height="22"
                  viewBox="0 0 22 22"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M13.1467 1.8177C10.7113 1.16514 8.24389 1.54731 6.20801 2.6959C5.84725 2.89943 5.3898 2.77197 5.18627 2.41121C4.98273 2.05045 5.11019 1.59301 5.47095 1.38947C7.83496 0.0557632 10.7051 -0.389421 13.5349 0.368813C19.2697 1.90544 22.6729 7.80006 21.1363 13.5348C19.7519 18.7013 14.8315 21.9744 9.67663 21.45C9.10883 21.3922 8.53851 21.2884 7.97032 21.1362C7.57022 21.029 7.33278 20.6177 7.43999 20.2176C7.54719 19.8175 7.95845 19.5801 8.35855 19.6873C8.84893 19.8187 9.34013 19.908 9.82853 19.9577C14.2629 20.4088 18.4966 17.5908 19.6874 13.1466C21.0096 8.21203 18.0812 3.13991 13.1467 1.8177ZM10.7526 5.50252C10.1711 5.50252 9.61323 5.59676 9.09243 5.77022C8.69942 5.9011 8.27474 5.68862 8.14386 5.29562C8.01298 4.90263 8.22546 4.47795 8.61845 4.34707C9.29023 4.12335 10.008 4.00252 10.7526 4.00252C11.9808 4.00252 13.1345 4.33123 14.1281 4.90598C14.4866 5.11338 14.6092 5.57218 14.4018 5.93073C14.1944 6.28927 13.7356 6.4118 13.377 6.20439C12.6056 5.75815 11.7101 5.50252 10.7526 5.50252ZM1.76136 4.85523C2.33537 3.97977 3.49315 3.91197 4.2264 4.50545L6.34644 6.22136C6.43614 6.24949 6.52202 6.29506 6.599 6.35856C6.67219 6.41893 6.731 6.49027 6.775 6.56823L11.2244 10.1695C11.5464 10.4301 11.5962 10.9024 11.3356 11.2243C11.075 11.5463 10.6027 11.596 10.2807 11.3354L6.24107 8.06583C5.77192 8.85164 5.50262 9.77004 5.50262 10.7525C5.50262 13.652 7.85313 16.0025 10.7526 16.0025C13.6521 16.0025 16.0026 13.652 16.0026 10.7525C16.0026 10.2983 15.9451 9.85864 15.8373 9.43984C15.7341 9.03874 15.9756 8.62984 16.3768 8.52662C16.7779 8.42341 17.1868 8.66493 17.29 9.06604C17.4289 9.60594 17.5026 10.1711 17.5026 10.7525C17.5026 14.4804 14.4805 17.5025 10.7526 17.5025C7.0247 17.5025 4.00262 14.4804 4.00262 10.7525C4.00262 9.41364 4.39308 8.1646 5.06592 7.11469L3.28271 5.67139C3.22686 5.6262 3.16403 5.61262 3.11414 5.61865C3.06872 5.62414 3.03795 5.64387 3.01577 5.6777C2.48977 6.47993 2.08025 7.37901 1.81781 8.35844C0.92306 11.6976 1.97418 15.1008 4.27981 17.3629C4.57548 17.653 4.58 18.1279 4.28991 18.4235C3.99982 18.7192 3.52497 18.7237 3.2293 18.4336C0.55323 15.8081 -0.671465 11.8529 0.36892 7.97021C0.67318 6.83469 1.14895 5.78926 1.76136 4.85523Z"
                    fill="white"
                  />
                </svg>
              </span>
              <span className="text-xl font-bold">Radar</span>
            </div>
            {/* Status Buttons */}
            <div className="flex items-center space-x-2 ml-auto">
              {[
                {
                  label: "New",
                  icon: <GiPlantSeed className="text-green-400" />,
                },
                {
                  label: "Blooming",
                  icon: <GiSprout className="text-green-400" />,
                },
                {
                  label: "Thriving",
                  icon: <GiThreeLeaves className="text-green-400" />,
                },
              ].map(({ label, icon }) => (
                <button
                  key={label}
                  className={`flex items-center px-3 py-2 rounded-3xl ${
                    activeStatus === label ? "bg-gray-800" : ""
                  }`}
                  onClick={() => handleStatusChange(label)}
                >
                  {icon}
                  <span className="ml-1">{label}</span>
                </button>
              ))}
            </div>

            {/* Time Filters - Hidden on Mobile */}
            <div className="hidden md:flex items-center space-x-2 ml-4">
              {["1h", "4h", "24h", "7d", "14d", "30d"].map((time) => (
                <button
                  key={time}
                  className={`px-3 py-2 rounded-3xl ${
                    activeTime === time ? "bg-gray-800" : ""
                  }`}
                  onClick={() => handleTimeframeChange(time)}
                >
                  {time}
                </button>
              ))}
            </div>
             {/* Time Filters - Mobile */}
             <div className="md:hidden flex items-center space-x-2 mt-2">
              {["1h", "4h", "24h", "7d", "14d", "30d"].map((time) => (
                <button
                  key={time}
                  className={`px-3 py-2 rounded-3xl ${
                    activeTime === time ? "bg-gray-800" : ""
                  }`}
                  onClick={() => handleTimeframeChange(time)}
                >
                  {time}
                </button>
              ))}
            </div>
          </div>
        )}
        <button
          className={`flex items-center gap-2 px-4 py-3 rounded-3xl text-white ${
            activeTable === "favorites" ? "bg-[#1D2226]" : ""
          }`}
          onClick={() => {
            setIsOpen(false);
            handleTableChange("favorites");
          }}
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.20926 2.01062L10.4413 4.49492C10.6093 4.84072 11.0573 5.17252 11.4353 5.23552L13.6677 5.61002C15.0957 5.85012 15.4317 6.89451 14.4027 7.92491L12.6666 9.67491C12.3726 9.97101 12.2116 10.5429 12.3026 10.9524L12.7997 13.1189C13.1917 14.8339 12.2886 15.4968 10.7836 14.6008L8.69055 13.3513C8.31254 13.1259 7.68952 13.1259 7.30451 13.3513L5.21286 14.6008C3.71482 15.4968 2.8048 14.8262 3.19681 13.1189L3.69382 10.9524C3.78482 10.5429 3.62382 9.97101 3.32981 9.67491L1.59377 7.92491C0.572441 6.89381 0.901449 5.85012 2.32879 5.61002L4.56184 5.23552C4.93285 5.17252 5.38086 4.84072 5.54887 4.49492L6.7809 2.01062C7.45291 0.663126 8.54494 0.663126 9.20996 2.01062" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
</svg>

          Favorites
        </button>
      </div>
      {/* Right Section: Global Dropdown */}
      <div className="relative md:order-3"> {/* Make the button a relative container */}
        <button
          className="flex items-center gap-2 bg-[#181C20] px-4 py-3 rounded-2xl text-white"
          onClick={() => setIsGlobalOpen(!isGlobalOpen)}
        >
          <svg
            width="22"
            height="22"
            viewBox="0 0 22 22"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M10.75 1.5C9.0639 1.5 7.48533 1.95043 6.12554 2.73703C5.767 2.94443 5.3082 2.82191 5.1008 2.46337C4.89339 2.10482 5.01591 1.64602 5.37446 1.43862C6.9564 0.52351 8.7932 0 10.75 0C16.6871 0 21.5 4.81294 21.5 10.75C21.5 16.6871 16.6871 21.5 10.75 21.5C4.81294 21.5 0 16.6871 0 10.75C0 8.7932 0.52351 6.9564 1.43862 5.37446C1.64602 5.01591 2.10482 4.89339 2.46337 5.1008C2.82191 5.3082 2.94443 5.767 2.73703 6.12554C1.95043 7.48533 1.5 9.0639 1.5 10.75C1.5 15.8586 5.64137 20 10.75 20C15.8586 20 20 15.8586 20 10.75C20 5.64137 15.8586 1.5 10.75 1.5Z"
              fill="white"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M10.75 20C10.4887 20 10.1512 19.8742 9.7588 19.4678C9.3634 19.0584 8.9676 18.4167 8.61793 17.5425C8.27056 16.6741 7.9885 15.6258 7.79439 14.4542C7.60052 13.2841 7.5 12.0249 7.5 10.75C7.5 9.4751 7.60052 8.21587 7.79439 7.04576C7.9885 5.87424 8.27056 4.8259 8.61793 3.95748C8.9676 3.08326 9.3634 2.44161 9.7588 2.0322C10.1512 1.62579 10.4887 1.5 10.75 1.5C11.0113 1.5 11.3488 1.62579 11.7412 2.0322C12.1366 2.4416 12.5324 3.08326 12.8821 3.95748C13.2294 4.8259 13.5115 5.87424 13.7056 7.04576C13.8995 8.21587 14 9.4751 14 10.75C14 12.0249 13.8995 13.2841 13.7056 14.4542C13.6379 14.8629 13.9143 15.249 14.3229 15.3167C14.7316 15.3845 15.1177 15.1081 15.1854 14.6994C15.3936 13.443 15.5 12.1015 15.5 10.75C15.5 9.3985 15.3936 8.05698 15.1854 6.80057C14.9775 5.54558 14.6703 4.38914 14.2748 3.40039C13.8816 2.41743 13.3955 1.5859 12.8202 0.99021C12.2421 0.39152 11.5393 0 10.75 0C9.9607 0 9.2579 0.39153 8.67976 0.99021C8.10452 1.5859 7.6184 2.41743 7.22521 3.40039C6.82971 4.38914 6.5225 5.54558 6.31457 6.80057C6.1064 8.05698 6 9.3985 6 10.75C6 12.1015 6.1064 13.443 6.31457 14.6994C6.5225 15.9544 6.82972 17.1109 7.22522 18.0996C7.6184 19.0826 8.10452 19.9141 8.67976 20.5098C9.2579 21.1085 9.9607 21.5 10.75 21.5C11.5393 21.5 12.2421 21.1085 12.8202 20.5098C13.3955 19.9141 13.8816 19.0826 14.2748 18.0996C14.4286 17.715 14.2416 17.2785 13.857 17.1247C13.4724 16.9709 13.0359 17.1579 12.8821 17.5425C12.5324 18.4167 12.1366 19.0584 11.7412 19.4678C11.3488 19.8742 11.0113 20 10.75 20Z"
              fill="white"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M0 10.75C0 10.3358 0.33579 10 0.75 10H8.75C9.1642 10 9.5 10.3358 9.5 10.75C9.5 11.1642 9.1642 11.5 8.75 11.5H0.75C0.33579 11.5 0 11.1642 0 10.75ZM12 10.75C12 10.3358 12.3358 10 12.75 10H20.75C21.1642 10 21.5 10.3358 21.5 10.75C21.5 11.1642 21.1642 11.5 20.75 11.5H12.75C12.3358 11.5 12 11.1642 12 10.75Z"
              fill="white"
            />
          </svg>
          {selectedNetwork}
          <svg
            width="8"
            height="5"
            viewBox="0 0 8 5"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.97324 0.193363C7.0864 0.0694889 7.23919 0 7.39843 0C7.55766 0 7.71046 0.0694889 7.82361 0.193363C7.87948 0.254191 7.92384 0.326698 7.95412 0.406662C7.98441 0.486627 8 0.572452 8 0.659143C8 0.745834 7.98441 0.831659 7.95412 0.911623C7.92384 0.991588 7.87948 1.06409 7.82361 1.12492L4.42562 4.80694C4.31221 4.93064 4.15931 5 4 5C3.84069 5 3.68779 4.93064 3.57438 4.80694L0.176388 1.12492C0.120524 1.06409 0.076159 0.991588 0.0458768 0.911623C0.0155947 0.831659 0 0.745834 0 0.659143C0 0.572452 0.0155947 0.486627 0.0458768 0.406662C0.076159 0.326698 0.120524 0.254191 0.176388 0.193363C0.28954 0.0694889 0.442339 0 0.601573 0C0.760806 0 0.913605 0.0694889 1.02676 0.193363L4.0013 3.213L6.97324 0.193363Z"
              fill="white"
            />
          </svg>
        </button>
        {isGlobalOpen && (
          <div className="absolute right-0 top-full w-[20rem] mt-2 bg-[#181C20] p-4 rounded-2xl shadow-lg z-20">
            {networks.map((network) => (
              <button
                key={network.name}
                className="flex justify-between items-center w-full px-2 py-2 hover:bg-gray-700 rounded-lg text-white"
                onClick={() => handleNetworkChange(network.name)}
              >
                <div className="flex w-full items-center gap-3">
                  <div className="w-5 h-5 bg-gray-400 rounded-full"></div>
                  <span>{network.name}</span>
                </div>
                {selectedNetwork === network.name && (
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clipPath="url(#clip0_133_1449)">
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M12 22C6.52381 22 2 17.4762 2 12C2 6.52381 6.51613 2 11.9923 2C17.4685 2 22 6.52381 22 12C22 17.4762 17.4762 22 12 22ZM10.7711 17.023C11.0553 17.023 11.3011 16.8925 11.4854 16.6083L16.6467 8.52074C16.7619 8.35177 16.8618 8.14439 16.8618 7.96006C16.8618 7.553 16.5008 7.29954 16.1244 7.29954C15.9017 7.29954 15.679 7.43011 15.51 7.69124L10.7327 15.2872L8.19048 12.0998C7.99078 11.8464 7.78341 11.7542 7.53763 11.7542C7.16129 11.7542 6.84639 12.0538 6.84639 12.4608C6.84639 12.6528 6.9232 12.8602 7.05376 13.0215L10.0184 16.616C10.2565 16.9078 10.4869 17.023 10.7711 17.023Z"
                        fill="#0A7AFF"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_133_1449">
                        <rect width="20" height="20" fill="white" transform="translate(2 2)" />
                      </clipPath>
                    </defs>
                  </svg>
                )}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Menu;