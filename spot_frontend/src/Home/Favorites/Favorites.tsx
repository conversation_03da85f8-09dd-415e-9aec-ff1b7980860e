import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Sparkline from "./Sparkline";
const Favorites = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [selectedTokens, setSelectedTokens] = useState(new Set());
  const [sortConfig, setSortConfig] = useState({ key: null, direction: "asc" });
  const DownArrow = () => (
    <svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3.86961 5.46501C3.48635 6.14002 2.51365 6.14002 2.13039 5.46501L0.384009 2.38918C0.00550654 1.72253 0.487018 0.895432 1.25362 0.895432L4.74638 0.895432C5.51298 0.895432 5.99449 1.72253 5.61599 2.38917L3.86961 5.46501Z" fill="#ED2626"/>
    </svg>
  );
  
  const UpArrow = () => (
    <svg width="6" height="6" viewBox="0 0 6 6" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M2.13039 0.535C2.51365 -0.14 3.48635 -0.14 3.86961 0.535L5.61599 3.61083C5.99449 4.27747 5.51298 5.10457 4.74638 5.10457H1.25362C0.487018 5.10457 0.00550654 4.27747 0.384009 3.61082L2.13039 0.535Z" fill="#16A34A"/>
    </svg>
  );

  const SortArrows = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="white"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="white"/>
    </svg>
  );

  const ActiveUpArrow = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="#444"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="#16A34A"/>
    </svg>
  );

  const ActiveDownArrow = () => (
    <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M4.83468 10.735C4.43943 11.334 3.56057 11.334 3.16533 10.735L1.55912 8.30074C1.12043 7.63589 1.59725 6.75 2.3938 6.75L5.6062 6.75C6.40275 6.75 6.87957 7.63589 6.44088 8.30074L4.83468 10.735Z" fill="#ED2626"/>
      <path d="M3.16533 1.26499C3.56057 0.665978 4.43943 0.665978 4.83468 1.26499L6.44088 3.69926C6.87957 4.36411 6.40275 5.25 5.6062 5.25L2.3938 5.25C1.59725 5.25 1.12043 4.36411 1.55912 3.69926L3.16533 1.26499Z" fill="#444"/>
    </svg>
  );
  const [favorites, setFavorites] = useState([]);

  useEffect(() => {
    // ✅ Load favorites from localStorage
    const storedFavorites = JSON.parse(localStorage.getItem("favorites")) || [];
    console.log("Fav",storedFavorites)

    setFavorites(storedFavorites);
    setLoading(false);
  }, []);

  // ✅ Remove from favorites
  const toggleFavorite = (id) => {
    const updatedFavorites = favorites.filter((fav) => fav.id !== id);
    setFavorites(updatedFavorites);
    localStorage.setItem("favorites", JSON.stringify(updatedFavorites));
  };

  // Toggle token selection
  // const toggleSelection = (id) => {
  //   setSelectedTokens((prevSelected) => {
  //     const newSelected = new Set(prevSelected);
  //     if (newSelected.has(id)) {
  //       newSelected.delete(id);
  //     } else {
  //       newSelected.add(id);
  //     }
  //     return newSelected;
  //   });
  // };
  const formatNumber = (num) => {
    if (num === undefined || num === null) return "N/A";
    if (num >= 1e12) return (num / 1e12).toFixed(2) + "T"; // Trillion
    if (num >= 1e9) return (num / 1e9).toFixed(2) + "B";   // Billion
    if (num >= 1e6) return (num / 1e6).toFixed(2) + "M";   // Million
    return num.toLocaleString(); // Default
  };
  const requestSort = (key) => {
    let direction = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  
    // Sort the favorites array instead of data
    const sortedData = [...favorites].sort((a, b) => {
      if (a[key] < b[key]) return direction === "asc" ? -1 : 1;
      if (a[key] > b[key]) return direction === "asc" ? 1 : -1;
      return 0;
    });
    
    // Update favorites state instead of data
    setFavorites(sortedData);
  };
  const getSortArrow = (column) => {
    if (sortConfig.key === column) {
      return sortConfig.direction === "asc" ? <ActiveUpArrow /> : <ActiveDownArrow />;
    }
    return <SortArrows />;
  };

  const navigate = useNavigate();

  // const handleRowClick = (token) => {
  //   let selectedData = Array.from(selectedTokens).map(id => data.find(item => item.id === id));
  
  //   // If nothing is selected, send only the clicked row's data
  //   if (selectedData.length === 0) {
  //     selectedData = [token];
  //   } else {
  //     if (!selectedData.some(item => item.id === token.id)) {
  //       selectedData.push(token);
  //     }
  //   }
    
  //   // ✅ Set "Spot" as the active tab in localStorage
  //   localStorage.setItem("activeTab", "Spot");
  
  //   navigate("/trade", { state: { selectedTokens: selectedData } });
  // };
  
  
  return (
    <div className="bg-[#141416] rounded-xl mx-3 mt-3 h-[calc(100vh-150px)] overflow-hidden">
      <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-[#BBBBBB]">
        <table className="w-full text-white border-collapse">
          {/* Sticky Table Header */}
          <thead className="bg-[#181C20] text-white border-b border-white/50 sticky top-0 z-10">
  <tr className="text-left font-extralight">
    
    {/* Favorites Icon */}
    <th className="px-6 py-4 text-sm">
      <button>
      <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.8571 0.438477H2.14286C0.961304 0.438477 0 1.41599 0 2.61747V10.365C0 11.5664 0.961304 12.544 2.14286 12.544H6.96429V14.6019H4.48504C4.18919 14.6019 3.94932 14.8458 3.94932 15.1467C3.94932 15.4475 4.18919 15.6914 4.48504 15.6914H10.515C10.8111 15.6914 11.0507 15.4475 11.0507 15.1467C11.0507 14.8458 10.8111 14.6019 10.515 14.6019H8.03571V12.544H12.8571C14.039 12.544 15 11.5664 15 10.365V2.61747C15 1.41599 14.039 0.438477 12.8571 0.438477ZM13.9286 10.365C13.9286 10.9658 13.4478 11.4545 12.8571 11.4545H2.14286C1.55195 11.4545 1.07143 10.9658 1.07143 10.365V2.61747C1.07143 2.0166 1.55195 1.52797 2.14286 1.52797H12.8571C13.4478 1.52797 13.9286 2.0166 13.9286 2.61747V10.365Z" fill="white"/>
<path d="M10.7143 8.65355H4.28571C3.98987 8.65355 3.75 8.89747 3.75 9.1983C3.75 9.49914 3.98987 9.74305 4.28571 9.74305H10.7143C11.0104 9.74305 11.25 9.49914 11.25 9.1983C11.25 8.89747 11.0104 8.65355 10.7143 8.65355ZM5.84787 7.66061C5.94962 7.78642 6.10134 7.8593 6.26142 7.8593C6.42151 7.8593 6.57322 7.78642 6.67498 7.66061L7.62373 6.48866L7.93213 6.87009C8.03205 6.99351 8.18011 7.06612 8.33705 7.06878C8.48772 7.05841 8.64467 7.00361 8.74826 6.88365L11.1166 4.14369C11.3117 3.91759 11.2903 3.5734 11.0679 3.37497C10.8461 3.17628 10.5071 3.19782 10.312 3.42445L8.35955 5.68324L8.03728 5.28532C7.93579 5.1595 7.78381 5.08662 7.62373 5.08662C7.46364 5.08662 7.31192 5.1595 7.21017 5.28532L6.26142 6.45727L5.61061 5.65345C5.4228 5.42097 5.0851 5.38746 4.85674 5.57897C4.62812 5.76995 4.59542 6.11334 4.7835 6.34555L5.84787 7.66061Z" fill="white"/>
</svg>

      </button>
    </th>

    {/* Token */}
    <th className="px-6 py-4 text-sm">
      <div className="flex items-center">
        <span>Token</span>
      </div>
    </th>

    {/* Contract */}
    <th className="px-6 py-4 text-sm">
      <div className="flex items-center">
        <span>Contract</span>
      </div>
    </th>

    {/* Price / 24h% */}
    <th className="px-6 py-4 text-sm">
      <div className="flex items-center gap-2">
        <span className="cursor-pointer flex items-center" onClick={() => requestSort("current_price")}>
          Price <span className="ml-1">{getSortArrow("current_price")}</span>
        </span>
        {/* <span>/</span> */}
        {/* <span className="cursor-pointer flex items-center" onClick={() => requestSort("price_change_percentage_24h")}>
          24h% <span className="ml-1">{getSortArrow("price_change_percentage_24h")}</span>
        </span> */}
      </div>
    </th>
    <th className="px-6 py-4 text-sm">
      <div className="flex items-center gap-2">
        <span className="cursor-pointer flex items-center" onClick={() => requestSort("market_cap")}>
          1h% <span className="ml-1">{getSortArrow("price_change_percentage_1h")}</span>
        </span>
        
      </div>
    </th> <th className="px-6 py-4 text-sm">
      <div className="flex items-center gap-2">
        <span className="cursor-pointer flex items-center" onClick={() => requestSort("price_change_percentage_24h")}>
          24h% <span className="ml-1">{getSortArrow("price_change_percentage_24H")}</span>
        </span>
        
      </div>
    </th> 
    <th className="px-6 py-4 text-sm">
      <div className="flex items-center gap-2">
        <span className="cursor-pointer flex items-center" onClick={() => requestSort("market_cap")}>
          7D% <span className="ml-1">{getSortArrow("price_change_percentage_7D")}</span>
        </span>
       
      </div>
    </th>
    <th className="px-6 py-4 text-sm">
      <div className="flex items-center gap-2">
        <span className="cursor-pointer flex items-center" >
          Last 7D
        </span>
       
      </div>
    </th>
    {/* MCap / FDV */}
    <th className="px-6 py-4 text-sm">
      <div className="flex items-center gap-2">
        <span className="cursor-pointer flex items-center" onClick={() => requestSort("market_cap")}>
          MCap <span className="ml-1">{getSortArrow("market_cap")}</span>
        </span>
        <span>/</span>
        <span className="cursor-pointer flex items-center" onClick={() => requestSort("fully_diluted_valuation")}>
          FDV <span className="ml-1">{getSortArrow("fully_diluted_valuation")}</span>
        </span>
      </div>
    </th>


   

    {/* 24h Vol / Txs / Traders */}
    <th className="px-6 py-4 text-sm">
      <div className="flex items-center gap-2">
        <span>24h Vol</span>
        <span>/</span>
        <span>Txs</span>
        <span>/</span>
        <span>Traders</span>
      </div>
    </th>

   
    

    {/* Liquidity */}
    <th className="px-6 py-4 text-sm">
      <div className="flex items-center">
        <span>Liquidity</span>
      </div>
    </th>

    

  </tr>
</thead>


          {/* Scrollable Table Body */}
          <tbody>
            {loading ? (
              <tr>
                <td colSpan="10" className="text-center py-6 text-lg">Loading...</td>
              </tr>
            ) : error ? (
              <tr>
                <td colSpan="10" className="text-center py-6 text-red-500 text-lg">{error}</td>
              </tr>
            ) : (
              favorites.map((token, index) => (
                <tr
                  key={index}
               
                  className={`text-sm px-6 bg-[#181C20] hover:bg-[#25282B] hover:rounded-lg transition-all duration-200 hover:p-4  `}
                >
                  {/* Selection Checkbox */}
                  <td className="" onClick={(e) => e.stopPropagation()}>
                
                    <button className="px-6 py-4" onClick={() => toggleFavorite(token.id)}>
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill={favorites.some(fav => fav.id === token.id) ? "yellow" : "transparent"} 
                        stroke="white"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path d="M9.20926 2.01062L10.4413 4.49492C10.6093 4.84072 11.0573 5.17252 11.4353 5.23552L13.6677 5.61002C15.0957 5.85012 15.4317 6.89451 14.4027 7.92491L12.6666 9.67491C12.3726 9.97101 12.2116 10.5429 12.3026 10.9524L12.7997 13.1189C13.1917 14.8339 12.2886 15.4968 10.7836 14.6008L8.69055 13.3513C8.31254 13.1259 7.68952 13.1259 7.30451 13.3513L5.21286 14.6008C3.71482 15.4968 2.8048 14.8262 3.19681 13.1189L3.69382 10.9524C3.78482 10.5429 3.62382 9.97101 3.32981 9.67491L1.59377 7.92491C0.572441 6.89381 0.901449 5.85012 2.32879 5.61002L4.56184 5.23552C4.93285 5.17252 5.38086 4.84072 5.54887 4.49492L6.7809 2.01062C7.45291 0.663126 8.54424 0.663126 9.20926 2.01062Z"
                          stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                      </svg>
                    </button>
                  </td>

                

                  {/* Token */}
                  <td className="px-6 py-4 flex items-center gap-3">
                    <img src={token.image} alt={token.name} className="w-8 h-8 rounded-full" />
                    <span className="font-semibold">{token.symbol?.toUpperCase() ||""}</span>
                  </td>

                  {/* Contract */}
                  <td className="px-6 py-4 text-gray-400">{token.contract_address ? (
    <a
      href={`https://etherscan.io/address/${token.contract_address}`}
      target="_blank"
      rel="noopener noreferrer"
      className="text-blue-400 hover:underline"
    >
      {token.contract_address.slice(0, 6)}...{token.contract_address.slice(-4)}
    </a>
  ) : (
    "--"
  )}</td>

                  {/* Price & 24h Change */}
                  <td className="px-6 py-4">
                    ${token.current_price.toFixed(2)} 
                    {/* <span className={`${token.price_change_percentage_24h < 0 ? "text-red-500" : "text-green-500"} font-semibold`}>
                      {token.price_change_percentage_24h}%
                    </span> */}
                  </td>

                  <td className="px-6 py-4">
                    {/* ${token.current_price.toFixed(2)}  */}
                    <span className={`${token.price_change_percentage_24h < 0 ? "text-[#FF329B]" : "text-[#14FFA2]"} font-semibold`}>
                      {token.price_change_percentage_24h}%
                    </span>
                  </td>

                  <td className="px-6 py-4">
                    {/* ${token.current_price.toFixed(2)}  */}
                    <span className={`${token.price_change_percentage_24h < 0 ? "text-[#FF329B]" : "text-[#14FFA2]"} font-semibold`}>
                      {token.price_change_percentage_24h}%
                    </span>
                  </td>

                  <td className="px-6 py-4">
                    {/* ${token.current_price.toFixed(2)}  */}
                    <span className={`${token.price_change_percentage_24h < 0 ? "text-[#FF329B]" : "text-[#14FFA2]"} font-semibold`}>
                      {token.price_change_percentage_24h}%
                    </span>
                  </td>

                  {/*Last 7 d*/}

                  <td className="px-6 py-4">
  {token.sparkline_in_7d ? (
    <Sparkline data={token.sparkline_in_7d.price.map((price, index) => ({ index, price }))} />
  ) : (
    "--"
  )}
</td>
                  {/* Market Cap & FDV */}
                  <td className="px-6 py-4">
                    ${formatNumber(token.market_cap)} /
                    <span className="text-gray-400"> ${formatNumber(token.fdv)}</span>
                  </td>

                 

                  {/* 24h Volume / Txs / Traders */}
                  <td className="px-6 py-4">
                    ${formatNumber(token.total_volume)} / 4 / 1
                  </td>

                  

                  {/* Liquidity */}
                  <td className="px-6 py-4">
                    ${formatNumber(Math.round(token.market_cap / 100))}
                  </td>

                
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Favorites;
