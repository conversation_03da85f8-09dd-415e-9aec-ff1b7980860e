

import React, { useState, useEffect } from "react";

interface Token {
  id: string;
  name?: string;
  symbol?: string;
  image?: string;
}

interface ActiveCoinProps {
  token?: Token;
}
const cryptoIcons = {
  ethereum: "https://mobulastorage.blob.core.windows.net/mobula-assets/assets/logos/d90e937d915ab0c865ff6e335361386c56524d4d33fb66f2f04defe1500082eb.png",
  bitcoin: "https://coin-images.coingecko.com/coins/images/825/large/bnb-icon2_2x.png?1696501970",
  solana: "https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png",
  "binance-smart-chain":"https://coin-images.coingecko.com/coins/images/825/large/bnb-icon2_2x.png?1696501970"
};
const ActiveCoin: React.FC<ActiveCoinProps> = ({ token }) => {
  const [favorites, setFavorites] = useState<Token[]>([]);
console.log("Active",token);
  useEffect(() => {
    const stored = localStorage.getItem("favorites");
    const storedFavorites: Token[] = stored ? JSON.parse(stored) : [];
    setFavorites(storedFavorites);
  }, []);

  const toggleFavorite = () => {
    if (!token) return;

    let updatedFavorites;
    if (favorites.some((fav) => fav.id === token.id)) {
      updatedFavorites = favorites.filter((fav) => fav.id !== token.id);
    } else {
      updatedFavorites = [...favorites, token];
    }

    setFavorites(updatedFavorites);
    localStorage.setItem("favorites", JSON.stringify(updatedFavorites));
  };

  if (!token) {
    return (
      <div className="px-6 py-1 rounded-xl flex items-center justify-between h-full w-full animate-pulse">
        {/* Skeleton Image */}
        <div className="flex items-center space-x-4">
          <div className="w-10 h-10 bg-gray-700 rounded-full" />
          <div className="space-y-2">
            <div className="w-24 h-4 bg-gray-700 rounded" />
            <div className="w-16 h-3 bg-gray-700 rounded" />
          </div>
        </div>
        {/* Skeleton Buttons */}
        <div className="flex gap-2">
          <div className="w-5 h-5 bg-gray-700 rounded" />
          <div className="w-4 h-4 bg-gray-700 rounded" />
        </div>
      </div>
    );
  }

  return (
    <div className="px-6 py-1 rounded-xl flex items-center justify-between h-full w-full">
      <div className="flex items-center space-x-4">
      <div className="relative w-8 h-8">
  <img src={token.image} alt={token.name} className="w-8 h-8 rounded-full" />
  {token.baseimage && (
    <img
      src={cryptoIcons[token.network.toLowerCase()]}
      alt="base"
      className="absolute bottom-[-4px] right-[-4px] w-4 h-4 rounded-full border-2 border-[#141416] shadow-md"
    />
  )}
</div>

        <div>
          <p className="text-white text-xl font-semibold">
            {token.name || 'Unknown Token'}
          </p>
          <p className="text-gray-400 text-lg">
            {token.symbol ? token.symbol.toUpperCase() : 'N/A'}
          </p>
        </div>
      </div>

      <div className="flex gap-2">
        <button onClick={toggleFavorite}>
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill={favorites.some((fav) => fav.id === token.id) ? "white" : "transparent"}
            stroke="white"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M9.20926 2.01062L10.4413 4.49492C10.6093 4.84072 11.0573 5.17252 11.4353 5.23552L13.6677 5.61002C15.0957 5.85012 15.4317 6.89451 14.4027 7.92491L12.6666 9.67491C12.3726 9.97101 12.2116 10.5429 12.3026 10.9524L12.7997 13.1189C13.1917 14.8339 12.2886 15.4968 10.7836 14.6008L8.69055 13.3513C8.31254 13.1259 7.68952 13.1259 7.30451 13.3513L5.21286 14.6008C3.71482 15.4968 2.8048 14.8262 3.19681 13.1189L3.69382 10.9524C3.78482 10.5429 3.62382 9.97101 3.32981 9.67491L1.59377 7.92491C0.572441 6.89381 0.901449 5.85012 2.32879 5.61002L4.56184 5.23552C4.93285 5.17252 5.38086 4.84072 5.54887 4.49492L6.7809 2.01062C7.45291 0.663126 8.54424 0.663126 9.20926 2.01062Z"
              stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"
            />
          </svg>
        </button>
        <button className="text-gray-400 hover:text-white">
          <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.7132 0.372359C11.1762 0.112336 11.7048 -0.0161086 12.2391 0.0016151C12.7734 0.0193388 13.2918 0.18252 13.7355 0.472625C14.1793 0.76273 14.5305 1.16813 14.7494 1.64278C14.9683 2.11743 15.046 2.6423 14.9738 3.15779C14.9015 3.67327 14.6822 4.1587 14.3406 4.55894C13.9991 4.95919 13.5491 5.2582 13.0417 5.42202C12.5343 5.58583 11.9899 5.60789 11.4703 5.48569C10.9507 5.36348 10.4767 5.10191 10.1022 4.73068L5.6147 7.25234C5.68473 7.49946 5.71926 7.74796 5.71831 7.99785C5.71831 8.24682 5.68473 8.49788 5.6147 8.74337L10.1022 11.265C10.5901 10.7834 11.2416 10.4913 11.9348 10.4431C12.628 10.3949 13.3154 10.5941 13.8687 11.0033C14.422 11.4125 14.8033 12.0039 14.9412 12.6668C15.0792 13.3296 14.9644 14.0187 14.6183 14.6052C14.2711 15.1927 13.7154 15.6367 13.0556 15.8536C12.3958 16.0706 11.6774 16.0457 11.0352 15.7835C10.3929 15.5214 9.87116 15.0401 9.56779 14.43C9.26443 13.8199 9.20036 13.123 9.38763 12.4702L4.90013 9.94787C4.50229 10.3422 3.99285 10.6123 3.43672 10.7238C2.88059 10.8353 2.30294 10.7831 1.77737 10.574C1.25179 10.3648 0.802096 10.0081 0.485565 9.54935C0.169034 9.09057 0 8.55046 0 7.99785C0 7.44524 0.169034 6.90514 0.485565 6.44636C0.802096 5.98759 1.25179 5.6309 1.77737 5.42175C2.30294 5.21259 2.88059 5.16045 3.43672 5.27195C3.99285 5.38345 4.50229 5.65355 4.90013 6.04784L9.38763 3.52618C9.21803 2.93186 9.2562 2.29953 9.49615 1.72863C9.7361 1.15773 10.1642 0.680632 10.7132 0.372359ZM13.3799 2.08662C13.2868 1.92716 13.1621 1.78721 13.0132 1.67482C12.8642 1.56244 12.6939 1.47984 12.512 1.43178C12.3301 1.38372 12.1403 1.37115 11.9533 1.39479C11.7664 1.41843 11.5862 1.47782 11.423 1.56954C11.2597 1.66126 11.1168 1.78349 11.0023 1.9292C10.8878 2.07491 10.8041 2.24122 10.7559 2.41856C10.7077 2.59589 10.6961 2.78075 10.7216 2.96249C10.7471 3.14423 10.8093 3.31926 10.9047 3.4775C11.0955 3.79435 11.4074 4.02487 11.7723 4.11874C12.1371 4.21262 12.5253 4.16224 12.8521 3.9786C13.179 3.79495 13.4179 3.49295 13.5168 3.13849C13.6157 2.78404 13.5665 2.40591 13.3799 2.08662ZM1.48091 8.36435C1.53883 8.56945 1.64427 8.75891 1.78914 8.91817C1.934 9.07744 2.11443 9.20229 2.31659 9.28314C2.51874 9.36398 2.73724 9.39868 2.95531 9.38455C3.17339 9.37043 3.38524 9.30786 3.5746 9.20166C3.80579 9.07161 3.99492 8.88113 4.12054 8.65181C4.24616 8.42249 4.30326 8.1635 4.28536 7.90421C4.26745 7.64491 4.17527 7.39569 4.01924 7.18478C3.86322 6.97388 3.64961 6.80974 3.40262 6.71096C3.15564 6.61219 2.88516 6.58273 2.62185 6.62594C2.35853 6.66914 2.11293 6.78327 1.91287 6.9554C1.71281 7.12752 1.5663 7.35075 1.48995 7.59978C1.41361 7.84881 1.41047 8.11368 1.48091 8.36435ZM12.8569 12.0098C12.6943 11.9171 12.5144 11.8565 12.3277 11.8317C12.141 11.8068 11.951 11.8182 11.7688 11.8651C11.5866 11.9119 11.4157 11.9934 11.266 12.1049C11.1163 12.2163 10.9907 12.3554 10.8964 12.5143C10.8022 12.6731 10.7412 12.8486 10.7169 13.0305C10.6926 13.2124 10.7055 13.3971 10.7549 13.5742C10.8043 13.7512 10.8891 13.9169 11.0046 14.0619C11.1201 14.2069 11.2639 14.3282 11.4277 14.4188C11.7558 14.6004 12.1443 14.6483 12.5085 14.552C12.8727 14.4558 13.1831 14.2233 13.3718 13.9052C13.5604 13.5871 13.6122 13.2093 13.5158 12.8542C13.4193 12.4991 13.1824 12.1956 12.8569 12.0098Z" fill="white"/>
          </svg>
        </button>
      </div>
    </div>
  );
};

export default ActiveCoin;
