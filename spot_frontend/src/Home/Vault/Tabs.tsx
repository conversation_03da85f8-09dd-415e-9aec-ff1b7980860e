import { useState } from "react";
import { ClipboardIcon } from "@heroicons/react/24/outline";
import LiquidityVault from "./LiquidityVault";
import Performance from "./Performance";
// Make sure heroicons is installed

const Tabs = () => {
    const renderContent = () => {
        switch (activeTab) {
          case "(RLP) Liquidity Vault":
            return <LiquidityVault/>
          case "Performance":
            return <Performance/>
         
          default:
            return null;
        }
      };
  const [activeTab, setActiveTab] = useState("(RLP) Liquidity Vault");
  const vaultAddress = "jk807cfhjkdkj";

  const copyToClipboard = () => {
    navigator.clipboard.writeText(vaultAddress);
  };

  const tabs = ["(RLP) Liquidity Vault", "Performance"];

  return (
    <div className="flex flex-col gap-4">
      <div className="mt-4 md:mt-0 flex gap-[5rem] pt-6">
        {tabs.map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`text-2xl font-extrabold ${
              activeTab === tab ? "text-white" : "text-[#BBBBBB]"
            } hover:text-white transition`}
          >
            {tab}
          </button>
        ))}
      </div>
      <div className="flex items-center text-sm text-gray-400">
          <span>{vaultAddress}</span>
          <button
            onClick={copyToClipboard}
            className="ml-2 hover:text-white transition"
            aria-label="Copy address"
          >
            <ClipboardIcon className="w-4 h-4" />
          </button>
        </div>
        {renderContent()}
    </div>
  );
};

export default Tabs;
