import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Check, Info } from 'lucide-react';

// Types
type Network = {
  id: string;
  name: string;
  icon: string;
};

type StakingState = {
  selectedNetwork: Network | null;
  amount: string;
  availableBalance: number;
  isWalletConnected: boolean;
  xpBoost: number;
  currentLiquidity: string;
  apyStats: {
    sevenDay: string;
    thirtyDay: string;
  };
  yourBoost: string;
  xpEarned: string;
  liquidityPercentage: number;
};

const LiquidityVault: React.FC = () => {
  // Sample network data
  const networks: Network[] = [
    { id: '1', name: 'Elixir', icon: 'E' },
    { id: '2', name: '<PERSON>', icon: 'A' },
    { id: '3', name: '<PERSON><PERSON>', icon: 'S' },
    { id: '4', name: '<PERSON><PERSON>', icon: 'R' },
  ];

  // State
  const [state, setState] = useState<StakingState>({
    selectedNetwork: null,
    amount: '',
    availableBalance: 0,
    isWalletConnected: false,
    xpBoost: 8,
    currentLiquidity: '102.95M USDC',
    apyStats: {
      sevenDay: '5.5%',
      thirtyDay: '5.5%',
    },
    yourBoost: '---',
    xpEarned: '---',
    liquidityPercentage: 70,
  });

  const [carouselIndex, setCarouselIndex] = useState(0);
  const [termsAccepted, setTermsAccepted] = useState(false);

  // Carousel effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCarouselIndex((prev) => (prev + 1) % 5);
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  // Event handlers
  const handleNetworkSelect = (network: Network) => {
    setState({ ...state, selectedNetwork: network });
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (/^\d*\.?\d*$/.test(value) || value === '') {
      setState({ ...state, amount: value });
    }
  };

  const handleConnectWallet = () => {
    setState({ ...state, isWalletConnected: true, availableBalance: 1250.75 });
  };

  return (
    <div className="flex flex-col  text-white min-h-screen">
      {/* XP Boost Carousel */}
      <div className="bg-green-800 relative overflow-hidden">
        <div 
          className="flex transition-transform duration-500 ease-in-out"
          style={{ transform: `translateX(-${carouselIndex * 20}%)` }}
        >
          {Array(10).fill(0).map((_, i) => (
            <div key={i} className="flex-none w-full md:w-1/5 px-4 py-3 text-center">
              CURRENT XP BOOST {state.xpBoost}x
            </div>
          ))}
        </div>
        <button 
          className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-green-900 rounded-full p-1"
          onClick={() => setCarouselIndex((prev) => (prev - 1 + 5) % 5)}
        >
          <ChevronLeft size={16} />
        </button>
        <button 
          className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-green-900 rounded-full p-1"
          onClick={() => setCarouselIndex((prev) => (prev + 1) % 5)}
        >
          <ChevronRight size={16} />
        </button>
      </div>

      {/* Main Content */}
      <div className="flex flex-col md:flex-row p-6 gap-6">
        {/* Left Column */}
        <div className="w-full md:w-2/3 space-y-6 bg-[#181C20] py-8 px-6">
          <div>
            <h1 className="text-2xl font-bold mb-2">Stake and Earn</h1>
            <p className="text-[#BBBBBB] mb-4">
              Network stakers will receive APY from trading fees and Liquidity Managers
              (LM) such as Elixir, Amber and Selini. Moreover, stakers will be rewarded with
              Reya Network XP and Elixir Potions.
            </p>
          </div>

          {/* APY Stats */}
          <div className="flex gap-4">
            <div className="bg-[#1D2226] rounded-lg p-4 flex-1">
              <p className="text-[#BBBBBB] text-center">7d APY</p>
              <p className="text-xl  text-center">{state.apyStats.sevenDay}</p>
            </div>
            <div className="bg-[#1D2226] rounded-lg p-4 flex-1">
              <p className="text-[#BBBBBB] text-center">30d APY</p>
              <p className="text-xl  text-center">{state.apyStats.thirtyDay}</p>
            </div>
          </div>

          {/* Current Network Liquidity */}
          <div className="bg-[#1D2226] rounded-lg p-4">
            <p className="text-[#BBBBBB] text-center">Current Network Liquidity</p>
            <p className="text-xl  text-center">{state.currentLiquidity}</p>
          </div>

          {/* Links */}
          <div className="flex gap-4 text-md">
            <button className="text-white underline">How it works</button>
            <button className="text-white underline">LM Performance</button>
          </div>

          {/* Staking Rewards */}
          <div>
            <h2 className="text-lg  mb-2">Staking Rewards</h2>
            <div className="space-y-2">
              <p className="text-lg text-[#BBBBBB]">Elixir Potions</p>
              <p className="text-lg text-[#BBBBBB]">Reya Network XP</p>
            </div>
          </div>

          {/* XP Boost Info */}
          <div className="space-y-4">
            <div className="text-[#14FFA2] inline-block px-4 py-1 rounded-full text-sm">
              CURRENT XP BOOST {state.xpBoost}x
            </div>
            <p className="text-[#BBBBBB]">Deposit boosts change as liquidity grows on network</p>
            
            {/* Progress Bar */}
            <div className="bg-gray-700 h-2 rounded-full overflow-hidden">
              <div 
                className="bg-[#D9D9D9] h-full" 
                style={{ width: `${state.liquidityPercentage}%` }}
              ></div>
            </div>
            <div className="text-right text-[#BBBBBB]">{state.liquidityPercentage}%</div>

            {/* Stats */}
            <div className="flex gap-4">
              <div className="bg-[#1D2226] rounded-lg p-4 flex-1">
                <p className="text-[#BBBBBB] text-center">Your Boost</p>
                <p className="text-xl font-bold text-center">{state.yourBoost}</p>
              </div>
              <div className="bg-[#1D2226] rounded-lg p-4 flex-1">
                <p className="text-[#BBBBBB] text-center">XP Earned</p>
                <p className="text-xl font-bold text-center">{state.xpEarned}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Staking Form */}
        <div className="w-full md:w-1/3 space-y-6 bg-[#181C20] p-8">
          <div className="flex gap-2">
            <button className="flex-1 bg-[#214638] text-[#14FFA2] py-3 font-medium rounded-lg">
              STAKE
            </button>
            <button className="flex-1 bg-[#311D27] text-[#FF329B] py-3 font-medium rounded-lg">
              UNSTAKE
            </button>
          </div>

          {/* Network Selection */}
          <div>
            <label className="block mb-2">Choose network</label>
            <div className="relative">
              <select 
                className="w-full bg-gray-800 text-white p-3 rounded-lg appearance-none"
                value={state.selectedNetwork?.id || ''}
                onChange={(e) => {
                  const network = networks.find(n => n.id === e.target.value);
                  if (network) handleNetworkSelect(network);
                }}
              >
                <option value="">Pick a network</option>
                {networks.map(network => (
                  <option key={network.id} value={network.id}>{network.name}</option>
                ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2" size={20} />
            </div>
          </div>

          {/* Amount Input */}
          <div>
            <label className="block mb-2">Amount</label>
            <input
              type="text"
              placeholder="Enter Amount"
              className="w-full bg-gray-800 text-white p-3 rounded-lg"
              value={state.amount}
              onChange={handleAmountChange}
            />
            <div className="flex justify-between mt-2">
              <span>Available</span>
              <span>{state.isWalletConnected ? state.availableBalance.toFixed(2) : '--'}</span>
            </div>
          </div>

          {/* XP Boost Info */}
          <div className="bg-[#214638]  border border-[#14FFA2] rounded-lg p-4 text-center text-sm">
            {state.xpBoost}x XP boost on deposit +<br />
            weekly loyalty boosts, to 15x in total
          </div>

          {/* Additional Info */}
          <div>
            <div className="flex justify-between mb-2">
              <span>Withdrawals</span>
              <span>Anytime</span>
            </div>
            <div className="flex justify-between">
              <span>Bridge Fees</span>
              <span>--</span>
            </div>
          </div>

          {/* Terms */}
          <div className="flex items-start gap-2">
            <div 
              className="w-5 h-5 mt-1 border rounded flex items-center justify-center cursor-pointer"
              onClick={() => setTermsAccepted(!termsAccepted)}
            >
              {termsAccepted && <Check size={16} />}
            </div>
            <p className="text-sm">
              By Clicking "<span className="font-bold">Confirm</span>", you agree to the Mega Vault Terms.
            </p>
          </div>

          {/* Connect Wallet Button */}
          <button
            className="w-full bg-white text-gray-900 py-4 rounded-lg font-bold"
            onClick={handleConnectWallet}
          >
            CONNECT WALLET
          </button>
        </div>
      </div>
    </div>
  );
};

// Helper component for the dropdown icon
const ChevronDown = ({ className, size }: { className?: string, size: number }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <polyline points="6 9 12 15 18 9"></polyline>
  </svg>
);

export default LiquidityVault;