import React, { useState } from 'react';

const PerformanceCard = () => {
  const [activeTab, setActiveTab] = useState('pnl'); // 'pnl' or 'account'
  const [activeMetric, setActiveMetric] = useState('account'); // 'account' or 'pnl'
  const [activeDuration, setActiveDuration] = useState('7D'); // '7D' or '30D'

  return (
    <div className="w-full bg-[#181C20] p-2 sm:p-4 md:p-6 rounded-lg shadow-md h-full overflow-hidden">
      {/* Tabs */}
      <div className="flex border-b border-gray-700 overflow-x-auto text-sm sm:text-base relative">
  {['pnl', 'account'].map((tab) => (
    <button
      key={tab}
      className={`relative px-2 sm:px-4 pb-2 sm:pb-3 whitespace-nowrap font-semibold transition-colors duration-300 ${
        activeTab === tab ? 'text-white' : 'text-[#BBBBBB]'
      }`}
      onClick={() => setActiveTab(tab)}
    >
      {tab === 'pnl' ? 'Total P&L' : 'Account Value'}

      {/* Animated Bottom Border */}
      <span
        className={`absolute left-0 bottom-0 h-0.5 bg-white transition-all duration-300 ${
          activeTab === tab ? 'w-full opacity-100' : 'w-0 opacity-0'
        }`}
      ></span>
    </button>
  ))}
</div>


      {/* Responsive Flex Container */}
      <div className="flex flex-col md:flex-row w-full gap-4 sm:gap-6 mt-4 sm:mt-6 h-full px-1 sm:px-2">
        {/* Performance Metrics (Left) */}
        <div className="w-full md:w-1/2 h-full px-1 sm:px-2">
          <h3 className="text-white font-semibold text-lg sm:text-2xl pb-1">Performance</h3>
          <div className="space-y-2 my-4 sm:my-6 text-sm sm:text-base">
            <div className="flex justify-between">
              <p className="text-[#BBBBBB]">PNL</p>
              <p className="text-[#14FFA2] font-semibold">$4,490,814.98</p>
            </div>
            <div className="flex justify-between">
              <p className="text-[#BBBBBB]">Max Drawdown</p>
              <p className="text-white">0.22%</p>
            </div>
            <div className="flex justify-between">
              <p className="text-[#BBBBBB]">Volume</p>
              <p className="text-white">$0.00</p>
            </div>
          </div>
        </div>

        {/* Graph (Right) */}
        <div className="w-full md:w-1/2 flex flex-col h-full justify-between pb-4 sm:pb-6">
          {/* Buttons */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 text-sm sm:text-base">
            <div className="flex gap-2">
              <button
                className={`px-3 sm:px-4 py-1 sm:py-2 rounded-full font-semibold ${
                  activeMetric === 'account'
                    ? 'bg-[#BBBBBB] text-black'
                    : 'border border-[#BBBBBB] text-[#D9D9D9]'
                }`}
                onClick={() => setActiveMetric('account')}
              >
                Account Value
              </button>
              <button
                className={`px-3 sm:px-4 py-1 sm:py-2 rounded-full font-semibold ${
                  activeMetric === 'pnl'
                    ? 'bg-[#BBBBBB] text-black'
                    : 'border border-[#BBBBBB] text-[#D9D9D9]'
                }`}
                onClick={() => setActiveMetric('pnl')}
              >
                PNL
              </button>
            </div>
            <div className="flex space-x-2">
              <button
                className={`px-2 sm:px-3 py-1 rounded-md text-xs font-semibold ${
                  activeDuration === '7D'
                    ? 'bg-[#BBBBBB] text-black'
                    : 'text-[#BBBBBB]'
                }`}
                onClick={() => setActiveDuration('7D')}
              >
                7D
              </button>
              <button
                className={`px-2 sm:px-3 py-1 rounded-md text-xs font-semibold ${
                  activeDuration === '30D'
                    ? 'bg-[#BBBBBB] text-black'
                    : 'text-[#BBBBBB]'
                }`}
                onClick={() => setActiveDuration('30D')}
              >
                30D
              </button>
            </div>
          </div>

          {/* Graph Section */}
          <div className="bg-gray-900 w-full h-36 sm:h-48 md:h-full my-4 sm:my-6 rounded-lg flex items-center justify-center overflow-hidden">
            <p className="text-gray-500 text-base sm:text-lg font-semibold">
              Graph - {activeMetric} | {activeDuration}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceCard;
