import { useState } from "react";

const VaultDepositWithdraw = () => {
  const [activeTab, setActiveTab] = useState("Deposit");
  const [amount, setAmount] = useState(0.0);
  const [checked, setChecked] = useState(false);
  const availableFunds = 100.65;

  const handleMax = () => {
    setAmount(availableFunds);
  };

  return (
    <div className="bg-[#181C20] py-4  sm:py-6 rounded-xl w-full h-full flex flex-col">
      {/* Tabs */}
      <div className="flex relative border-b border-[#333]">
        <button
          onClick={() => setActiveTab("Deposit")}
          className={`flex-1 text-base sm:text-lg pb-2 cursor-pointer transition-all ${
            activeTab === "Deposit" ? "text-white font-semibold" : "text-[#BBBBBB]"
          }`}
        >
          Deposit
        </button>
        <button
          onClick={() => setActiveTab("Withdraw")}
          className={`flex-1 text-base sm:text-lg pb-2 cursor-pointer transition-all ${
            activeTab === "Withdraw" ? "text-white font-semibold" : "text-[#BBBBBB]"
          }`}
        >
          Withdraw
        </button>
        <div
          className="absolute bottom-0 h-[2px] bg-white transition-all duration-300"
          style={{ width: "50%", left: activeTab === "Deposit" ? "0%" : "50%" }}
        />
      </div>

      {/* Content */}
      <div className="mt-4 flex-grow space-y-4 px-4">
        <div>
          <p className="text-[#BBBBBB] text-sm sm:text-base">Amount to {activeTab}</p>
          <div className="bg-[#121417] p-3 rounded-lg mt-2">
            <div className="flex items-center justify-between">
              <p className="text-white text-lg sm:text-xl">${amount.toFixed(2)}</p>
              <button
                onClick={handleMax}
                className="bg-[#214638] text-[#14FFA2] px-3 py-1 rounded-lg text-xs sm:text-sm"
              >
                MAX
              </button>
            </div>
            {activeTab === "Withdraw" && (
              <>
                <div className="border-t border-gray-700 my-2" />
                <div className="flex justify-between text-sm sm:text-base">
                  <p className="text-[#BBBBBB]">Available Funds</p>
                  <p className="text-white font-semibold">${availableFunds.toFixed(2)}</p>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Additional Info */}
        <div className="text-sm sm:text-base space-y-2">
          <div className="flex justify-between">
            <p className="text-[#BBBBBB]">Cross Free Collateral</p>
            <p className="text-white">-</p>
          </div>
          <div className="flex justify-between">
            <p className="text-[#BBBBBB]">Estimated Slippage</p>
            <p className="text-white">0.0%</p>
          </div>
          <div className="flex justify-between">
            <p className="text-[#BBBBBB]">Estimated Amount Received</p>
            <p className="text-white">$0.00</p>
          </div>
        </div>
      </div>

      {/* Checkbox & Button */}
      <div className="mt-4 space-y-4 px-4">
        <div className="flex items-start gap-2">
          <input
            type="checkbox"
            className="mt-1"
            checked={checked}
            onChange={() => setChecked(!checked)}
          />
          <p className="text-[#BBBBBB] text-sm">
            By clicking <span className="font-semibold text-white">"Confirm"</span>, you agree to the Mega Vault Terms.
          </p>
        </div>

        <button
          className={`w-full py-3 rounded-full text-sm sm:text-base transition ${
            checked
              ? "bg-white text-black cursor-pointer"
              : "bg-[#BBBBBB] text-[#141416] opacity-50 cursor-not-allowed"
          }`}
        >
          {activeTab === "Deposit" ? "DEPOSIT" : "WITHDRAW"}
        </button>
      </div>
    </div>
  );
};

export default VaultDepositWithdraw;
