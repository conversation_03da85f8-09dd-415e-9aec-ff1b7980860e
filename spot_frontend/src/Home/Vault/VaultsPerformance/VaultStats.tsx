import React from "react";

const StatCard = ({ title, value }) => {
  return (
    <div className="flex justify-center items-center bg-[#181C20] p-6 rounded-lg w-full h-full  ">
      <div className="text-center">
        <h3 className="text-[#BBBBBB] pb-2  text-lg">{title}</h3>
        <p className="text-white text-md">{value ?? "-"}</p>
      </div>
    </div>
  );
};

const ValueStats = () => {
  return (
    <div className="flex flex-col h-full sm:flex-row w-full justify-between gap-2">
      <div className="flex-1">
        <StatCard title="TVL" value="$10,000" />
      </div>
      <div className="flex-1">
        <StatCard title="Past Month Return" value="5%" />
      </div>
      <div className="flex-1">
        <StatCard title="Your Deposits" value="$2,500" />
      </div>
      <div className="flex-1">
        <StatCard title="All Time Earned" value="$500" />
      </div>
    </div>
  );
};

export default ValueStats;
