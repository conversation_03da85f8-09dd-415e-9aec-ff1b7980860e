
import VaultStats from "./VaultStats";
import VaultPerformance from "./VaultPerformance";
import VaultDepositWithdraw from "./VaultDepositWithdraw";
import VaultPositions from "./VaultPositions";
import VaultDescription from "./VaultDescription";

const Vaults = () => {
  return (
    <div className="bg-[#141416] min-h-screen flex flex-col  pt-6 text-white">
      {/* Header */}


      {/* Grid Layout */}
      <div className="flex flex-col md:grid md:grid-rows-8 md:grid-cols-12 gap-3 my-4 flex-grow">
        {/* Vault Stats - Full Width */}
        <div className="col-span-12 row-span-1">
          <VaultStats />
        </div>

        {/* Vault Performance */}
        <div className="col-span-12  md:row-span-4">
          <VaultPerformance />
        </div>

     

        {/* Vault Positions */}
        <div className="col-span-12   md:row-span-3">
          <VaultPositions />
        </div>

        {/* Vault Description */}
      
      </div>
    </div>
  );
};

export default Vaults;
