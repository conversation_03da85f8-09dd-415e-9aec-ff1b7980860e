import { FaDollarSign } from "react-icons/fa";

const VaultPositions = () => {
  const positions = [
    {
      market: "USDC",
      size: "$2,596,942",
      sizeDetailed: "2,596,941.9453 USDT",
      pnl: "$65.684",
      equity: "$2,596,942",
    },
    {
      market: "USDC",
      size: "$2,596,942",
      sizeDetailed: "2,596,941.9453 USDT",
      pnl: "$65.684",
      equity: "$2,596,942",
    },
  ];

  return (
    <div className="bg-[#181C20] w-full rounded-xl h-full overflow-y-auto">
      {/* Table Header - Only show on medium and above */}
      <div className="hidden md:flex justify-between py-5 px-8 border-b-2 border-[#1D2226] text-[#BBBBBB] text-md font-semibold">
        <p className="w-1/4 text-left">Market</p>
        <p className="w-1/4 text-left">Size</p>
        <p className="w-1/4 text-left">30d P&L</p>
        <p className="w-1/4 text-left">Equity</p>
      </div>

      {/* Positions Data */}
      <div className="mt-2">
        {positions.map((pos, index) => (
          <div
            key={index}
            className="flex flex-col md:flex-row justify-between px-4 md:px-8 py-4 border-b-2 border-[#1D2226] space-y-4 md:space-y-0"
          >
            {/* Market */}
            <div className="flex items-center md:items-start w-full md:w-1/4 space-x-3">
              <div className="bg-[#1D2226] p-2 rounded-full">
                <FaDollarSign className="text-[#BBBBBB] text-lg" />
              </div>
              <div>
                <p className="font-semibold text-white">{pos.market}</p>
                <p className="text-[#BBBBBB] text-sm">Long 1.0x</p>
              </div>
            </div>

            {/* Size */}
            <div className="w-full md:w-1/4 text-left">
              <p className="font-semibold text-white">{pos.size}</p>
              <p className="text-gray-500 text-xs">{pos.sizeDetailed}</p>
            </div>

            {/* P&L */}
            <div className="w-full md:w-1/4 text-left">
              <p className="text-[#14FFA2] font-semibold">{pos.pnl}</p>
              <p className="text-gray-500 text-xs md:hidden">30d P&L</p>
            </div>

            {/* Equity */}
            <div className="w-full md:w-1/4 text-left">
              <p className="text-white font-semibold">{pos.equity}</p>
              <p className="text-gray-500 text-xs md:hidden">Equity</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default VaultPositions;
