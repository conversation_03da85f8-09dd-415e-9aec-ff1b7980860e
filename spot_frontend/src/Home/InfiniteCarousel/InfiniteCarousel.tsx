import React, { useEffect, useState, useRef } from "react";
import { FaFire } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { useTrending } from "../../context/TrendingContext";
import useTokenSelection from "@/hooks/useTokenSelection";
// CSS for animation
const scrollingTickerStyles = {
  ticker: {
    whiteSpace: 'nowrap',
    display: 'inline-block',
    animation: 'marquee 60s linear infinite',
  },
  tickerItem: {
    display: 'inline-flex',
    alignItems: 'center',
    marginRight: '24px',
    background: '#181C20',
    padding: '10px 16px',
    borderRadius: '9999px',
    minWidth: '160px',
    justifyContent: 'center',
  },
  '@keyframes marquee': {
    '0%': { transform: 'translateX(0)' },
    '100%': { transform: 'translateX(-100%)' }
  }
};

const ScrollingTicker = () => {
  const tickerRef = useRef<HTMLDivElement | null>(null);
  const navigate = useNavigate();
  
  // Add fallback when the context is not available
  const trendingContext = useTrending();
  
  // Safely destructure the context or use fallback values
  const { 
    formattedTrendingData = [], 
    loading = false, 
    error = null 
  } = trendingContext || {};

  // Add CSS animation to document
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes marquee {
        0% { transform: translateX(0); }
        100% { transform: translateX(-50%); }
      }
      .ticker-wrapper {
        overflow: hidden;
        width: 100%;
      }
      .ticker-animation {
        display: flex;
        width: max-content;
        animation: marquee 60s linear infinite;
      }
      .ticker-container:hover .ticker-animation {
        animation-play-state: paused;
      }
      .ticker-item {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.5rem;
        margin: 0 0.75rem;
        background: #181C20;
        border-radius: 9999px;
        cursor: pointer;
        transition: background-color 0.2s;
      }
      .ticker-item:hover {
        background: #242830;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);
  const { isTokenSelected, handleTokenSelect,setSelectedTokens } = useTokenSelection();
  // Handle navigation to trade page with selected token
  const handleTokenClick =  async (e: any, token: any) => {
    // Store the selected token for the trade page
    await handleTokenSelect(e, token);
    localStorage.setItem("activeToken", JSON.stringify(token));
    console.log("Selected token:", token);
  };

  if (loading) {
    return (
      <div className="flex items-center h-full w-full bg-[#141416] overflow-hidden relative py-3">
        <div className="flex items-center px-4 text-orange-500 text-xl">
          <FaFire />
        </div>
        <div className="text-white">Loading trending tokens...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center h-full w-full bg-[#141416] overflow-hidden relative py-3">
        <div className="flex items-center px-4 text-orange-500 text-xl">
          <FaFire />
        </div>
        <div className="text-red-400">{error}</div>
      </div>
    );
  }

  // If no data is available
  if (!formattedTrendingData || formattedTrendingData.length === 0) {
    return (
      <div className="flex items-center h-full w-full bg-[#141416] overflow-hidden relative py-3">
        <div className="flex items-center px-4 text-orange-500 text-xl">
          <FaFire className="mr-2" />
          <span className="text-white font-medium">Trending</span>
        </div>
        <div className="text-gray-400">No trending tokens available</div>
      </div>
    );
  }

  return (
    <div className="flex items-center h-full w-full bg-[#141416] overflow-hidden relative">
      {/* 🔥 Trending Label with Fire Icon */}
      <div className="flex items-center px-4 text-orange-500 text-xl">
        <FaFire className="mr-2" />
        <span className="text-white font-medium">Trending</span>
      </div>

      {/* Moving Ticker */}
      <div className="ticker-wrapper">
        <div className="ticker-container">
          <div ref={tickerRef} className="ticker-animation">
            {/* First set of tokens */}
            {formattedTrendingData.map((coin, index) => (
              <div
                key={`first-${index}`}
                className="ticker-item"
                onClick={(e) => handleTokenClick(e,coin)}
              >
                <img src={coin.icon} alt={coin.name} className="w-6 h-6 rounded-full mr-2" />
                <span className="font-semibold mr-2 text-white">{coin.symbol}</span>
                <span className={`${coin.changeColor} font-bold`}>{coin.change}</span>
              </div>
            ))}
            
            {/* Duplicate set for continuous scrolling - must be identical to first set */}
            {formattedTrendingData.map((coin, index) => (
              <div
                key={`second-${index}`}
                className="ticker-item"
                onClick={(e) => handleTokenClick(e,coin)}
              >
                <img src={coin.icon} alt={coin.name} className="w-6 h-6 rounded-full mr-2" />
                <span className="font-semibold mr-2 text-white">{coin.symbol}</span>
                <span className={`${coin.changeColor} font-bold`}>{coin.change}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScrollingTicker;
