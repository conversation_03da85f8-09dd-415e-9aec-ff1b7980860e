import React from "react";
import { useState } from "react";
import { FiChevronUp,FiChevronDown } from "react-icons/fi";
import { FiInfo } from "react-icons/fi";
import Dashboard from "./Dashboard";
import PortfolioAllocation from "./PortfolioAllocation";

// Sample Data for Tokens
const tokenData = [
    { name: "USDT", price: "$0", change: "+14.47%", isPositive: true },
    { name: "USDT", price: "$0", change: "-14.47%", isPositive: false },
    { name: "USDT", price: "$0", change: "+14.47%", isPositive: true },
    { name: "USDT", price: "$0", change: "-14.47%", isPositive: false },
  ];

const sampleHoldings = [
  { id: 1, token: "BTC", price: "$42,300", balance: "0.005 BTC", unrealized: "+5.2%" },
  { id: 2, token: "ETH", price: "$3,200", balance: "0.75 ETH", unrealized: "-2.8%" },
  { id: 3, token: "SOL", price: "$140", balance: "10 SOL", unrealized: "+1.3%" },
  { id: 4, token: "DOGE", price: "$0.08", balance: "500 DOGE", unrealized: "-1.1%" },
  { id: 5, token: "MATIC", price: "$1.50", balance: "150 MATIC", unrealized: "+3.4%" }
];

const Account: React.FC =  () => {
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [activeTab, setActiveTab] = useState<string>("wallet"); 
  return (
    <div className="bg-[#181C20] h-full p-3 rounded-2xl w-full text-white  overflow-y-auto">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-sm font-bold text-[#BBBBBB]">{activeTab=="wallet"?"My Account":"Dashboard"}</h2>
        <div className="flex items-center space-x-3">
          <div 
            className={`p-2 rounded-sm cursor-pointer transition-all duration-200 ${
              activeTab === "wallet" ? "bg-[#1D2226] text-white" :""
            }`} 
            onClick={() => setActiveTab("wallet")}
          >
          <svg width="29" height="29" viewBox="0 0 29 29" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="29" height="29" rx="10" />
            <path d="M8 9.33333C8 10.1671 8.88978 10.9371 10.3333 11.354C11.7769 11.7709 13.5564 11.7709 15 11.354C16.4436 10.9371 17.3333 10.1671 17.3333 9.33333C17.3333 8.49956 16.4436 7.72956 15 7.31267C13.5564 6.89578 11.7769 6.89578 10.3333 7.31267C8.88978 7.72956 8 8.49956 8 9.33333Z" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M12.666 15.5556C12.666 16.8443 14.7551 17.8889 17.3327 17.8889C19.9102 17.8889 21.9993 16.8443 21.9993 15.5556C21.9993 14.2668 19.9102 13.2222 17.3327 13.2222C14.7551 13.2222 12.666 14.2668 12.666 15.5556Z" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8 9.33334V17.1111C8 17.8018 8.60044 18.2389 9.55556 18.6667" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8 13.2222C8 13.9129 8.60044 14.35 9.55556 14.7778" stroke="white" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          </div>

          <div 
            className={`p-2 rounded-sm cursor-pointer transition-all duration-200 ${
              activeTab === "history" ? "bg-[#1D2226] text-white" : ""
            }`} 
            onClick={() => setActiveTab("history")}
          >
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15.5 9.759C15.5 9.6927 15.4737 9.62911 15.4268 9.58222C15.3799 9.53534 15.3163 9.509 15.25 9.509H8.5V2.75C8.5 2.6837 8.47366 2.62011 8.42678 2.57322C8.37989 2.52634 8.3163 2.5 8.25 2.5C4.2525 2.5 1 5.7475 1 9.7395C1 13.8785 4.1215 17 8.2605 17C12.117 17 15.5 13.616 15.5 9.759ZM1.5 9.7395C1.5 6.1065 4.3935 3.1365 8 3.0045V9.759C8 9.897 8.112 10.009 8.25 10H14.995C14.8535 13.495 11.7675 16.5 8.2605 16.5C4.4065 16.5 1.5 13.594 1.5 9.7395Z" fill="white" stroke="white" strokeWidth="0.5"/>
              <path d="M9.75 8.5H16.7495C16.8875 8.509 17 8.388 17 8.25C17 4.2525 13.7475 1 9.75 1C9.6837 1 9.62011 1.02634 9.57322 1.07322C9.52634 1.12011 9.5 1.1837 9.5 1.25V8.259C9.5 8.397 9.612 8.509 9.75 8.5ZM10 1.5045C13.5315 1.634 16.3795 4.4895 16.497 8.009H10V1.5045Z" fill="white" stroke="white" strokeWidth="0.5"/>
            </svg>
          </div>
      </div>
      </div>

      {activeTab === "wallet" ? (
        <>
      <div className="flex justify-between pb-4 items-center text-[#BBBBBB] text-sm cursor-pointer" onClick={() => setIsOpen(!isOpen)}>
        <span className="flex items-center gap-1">
          Primary Tokens <FiInfo size={14} />
        </span>
        <div className="flex items-center space-x-2 text-white font-semibold">
          <span>$0</span>
          {isOpen ? <FiChevronUp /> : <FiChevronDown />}
        </div>
      </div>

      {isOpen && (
        <div className="mt-2">
          {tokenData.map((token, index) => (
            <div key={index} className="flex justify-between items-center py-2 text-white">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gray-500 rounded-full"></div>
                <div>
                  <p>{token.name}</p>
                  <p className={`text-sm ${token.isPositive ? "text-green-400" : "text-red-400"}`}>
                    {token.change}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <p className="text-white">{token.price}</p>
                <button className="w-6 h-6 flex items-center justify-center border border-gray-500 rounded-full text-gray-400">+</button>
              </div>
            </div>
          ))}
        </div>
      )}

      <hr className="border-white border-3 mb-4" />

      <p className="text-gray-400 text-sm mb-2">Holdings</p>

      <div className="grid grid-cols-3 text-[#BBBBBB] text-sm font-semibold py-2">
  <span className="text-left">Token/Price</span>
  <span className="text-center">Balance</span>
  <span className="text-right">Unrealized/%</span>
</div>

<div className="text-sm h-full overflow-y-auto">
  {sampleHoldings.map((holding) => (
    <div 
      key={holding.id} 
      className="grid grid-cols-3 text-white text-sm py-2 border-b border-gray-600"
    >
      <span className="text-left">
        {holding.token} <span className="text-gray-400">{holding.price}</span>
      </span>
      <span className="text-center">{holding.balance}</span>
      <span 
        className={`text-right ${holding.unrealized.startsWith("+") ? "text-green-400" : "text-red-400"}`}
      >
        {holding.unrealized}
      </span>
    </div>
  ))}
</div>

    </>
    ) : (
      <div className="h-full overflow-y-auto">
        <Dashboard/>
        <PortfolioAllocation/>
      </div>
    )}
    </div>
  );
};

export default Account;
