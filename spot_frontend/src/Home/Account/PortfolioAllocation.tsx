import React, { useState } from "react";
import { Pie<PERSON><PERSON>, Pie, Cell } from "recharts";

const portfolioData = [{ name: "Primary", value: 80 }, { name: "Other", value: 20 }];
const allocationData = [{ name: "Stocks", value: 60 }, { name: "<PERSON>pt<PERSON>", value: 30 }, { name: "Cash", value: 10 }];
const COLORS = ["#E6007A", "#707070", "#14FFA2"]; // Colors for different allocations

const PortfolioAllocation = () => {
  const [activeTab, setActiveTab] = useState<string>("Portfolio");

  return (
    <div className="bg-[#181C20] h-full py-3 rounded-2xl w-full max-w-md text-white relative overflow-y-auto">
      
      {/* Tabs Navigation */}
      <div className="flex space-x-6 pb-2 mb-4">
        {["Portfolio", "Allocation"].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`text-sm font-semibold ${
              activeTab === tab ? "text-white border-b-2 border-[#FF329B] pb-1" : "text-gray-400"
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Chart Section */}
      <div className="flex items-center space-x-6">
        <div className="relative">
          {/* Pie Chart */}
          <PieChart width={100} height={100}>
            <Pie
              data={activeTab === "Portfolio" ? portfolioData : allocationData}
              cx="50%"
              cy="50%"
              innerRadius={30}
              outerRadius={40}
              startAngle={90}
              endAngle={-270}
              dataKey="value"
              stroke="none"
            >
              {(activeTab === "Portfolio" ? portfolioData : allocationData).map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index]} />
              ))}
            </Pie>
          </PieChart>

          {/* Centered Text */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-sm font-semibold">
            $0
          </div>
        </div>

        {/* Legend */}
        <div className="flex flex-col space-y-2">
          {(activeTab === "Portfolio" ? portfolioData : allocationData).map((entry, index) => (
            <div key={index} className="flex items-center space-x-2">
              <span className="w-3 h-3 rounded-full" style={{ backgroundColor: COLORS[index] }}></span>
              <span className="text-gray-400">{entry.name} ({entry.value}%)</span>
            </div>
          ))}
        </div>
      </div>
      
    </div>
  );
};

export default PortfolioAllocation;
