import React, { useState } from "react";
import { Line } from "react-chartjs-2";
import { 
  Chart as ChartJS, 
  LineElement, 
  PointElement, 
  LinearScale, 
  CategoryScale, 
  Tooltip,
  TooltipItem,
  ChartOptions
} from "chart.js";

// Register required Chart.js components
ChartJS.register(LineElement, PointElement, LinearScale, CategoryScale, Tooltip);

// Define types for chart data
interface DataSets {
  [key: string]: number[]; // Index signature to allow string keys
}

interface TimeGraphProps {
  title: string;
  dataSets: DataSets;
}

const TimeGraph = ({ title, dataSets }: TimeGraphProps) => {
  const [activeRange, setActiveRange] = useState<string>("1W");

  // Function to Generate Future Time Labels
  const generateTimeLabels = (range: string): string[] => {
    const labels: string[] = [];
    let currentDate = new Date();
  
    // Move `currentDate` backward first
    if (range === "1D") currentDate.setHours(currentDate.getHours() - 24);
    if (range === "1W") currentDate.setDate(currentDate.getDate() - 7);
    if (range === "1M") currentDate.setDate(currentDate.getDate() - 30);
    if (range === "3M") currentDate.setDate(currentDate.getDate() - 90);
    if (range === "6M") currentDate.setDate(currentDate.getDate() - 180);
  
    // Generate 7 data points moving from past to present
    for (let i = 0; i < 7; i++) {
      labels.push(
        `${currentDate.getHours()}:${currentDate.getMinutes()}, ${currentDate.toLocaleString("default", {
          month: "short",
        })} ${currentDate.getDate()}`
      );
  
      // Move towards present
      if (range === "1D") currentDate.setHours(currentDate.getHours() + 4);
      if (range === "1W") currentDate.setDate(currentDate.getDate() + 1);
      if (range === "1M") currentDate.setDate(currentDate.getDate() + 4);
      if (range === "3M") currentDate.setDate(currentDate.getDate() + 10);
      if (range === "6M") currentDate.setDate(currentDate.getDate() + 20);
    }
  
    return labels;
  };

  // Chart Data
  const chartData = {
    labels: generateTimeLabels(activeRange),
    datasets: [
      {
        label: title,
        data: dataSets[activeRange] || [],
        borderColor: "#FFFFFF",
        backgroundColor: "rgba(168, 85, 247, 0.1)",
        borderWidth: 2,
        pointRadius: 3,
        tension: 0.4, // Smooth Line
      },
    ],
  };

  // Chart Options
  const chartOptions: ChartOptions<'line'> = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: { display: false },
      y: { display: false },
    },
    elements: {
      line: { borderWidth: 2 },
      point: { radius: 3 },
    },
    plugins: {
      tooltip: {
        enabled: true,
        callbacks: {
          title: function(tooltipItems) {
            return tooltipItems[0].label || '';
          },
          label: function(tooltipItem) {
            return `${title}: $${tooltipItem.raw}`;
          }
        },
      },
      legend: { display: false },
    },
  };

  return (
    <div className="bg-[#181C20] py-3 rounded-2xl w-full max-w-lg h-full text-white relative overflow-y-auto">
      
      {/* Title Section */}
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-md text-[#BBBBBB] font-semibold">{title}</h3>
        <div className="flex space-x-2">
          {["1D", "1W", "1M", "3M", "6M"].map((range) => (
            <button
              key={range}
              className={`px-3 py-1 rounded-md text-sm ${
                activeRange === range ? "bg-[#bbbbbb] text-black font-semibold" : "text-gray-400"
              }`}
              onClick={() => setActiveRange(range)}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Graph */}
      <div className="relative h-32">
        <Line data={chartData} options={chartOptions} />
      </div>
    </div>
  );
};

interface RangePickerProps {
  activeRange: string;
  setActiveRange: (range: string) => void;
}

const RangePicker = ({ activeRange, setActiveRange }: RangePickerProps) => {
  const handleSetRange = (range: string) => {
    setActiveRange(range);
  };

  return (
    <div className="flex items-center justify-center gap-3 mb-8">
      <button
        onClick={() => handleSetRange("1M")}
        className={`text-xs px-3 py-1 rounded-full ${
          activeRange === "1M"
            ? "bg-blue-500 text-white"
            : "bg-[#1C1C1E] text-[#D0D0D3]"
        }`}
      >
        1M
      </button>
      <button
        onClick={() => handleSetRange("1Y")}
        className={`text-xs px-3 py-1 rounded-full ${
          activeRange === "1Y"
            ? "bg-blue-500 text-white"
            : "bg-[#1C1C1E] text-[#D0D0D3]"
        }`}
      >
        1Y
      </button>
      <button
        onClick={() => handleSetRange("ALL")}
        className={`text-xs px-3 py-1 rounded-full ${
          activeRange === "ALL"
            ? "bg-blue-500 text-white"
            : "bg-[#1C1C1E] text-[#D0D0D3]"
        }`}
      >
        ALL
      </button>
    </div>
  );
};

const Dashboard = () => {
  const [activeRange, setActiveRange] = useState<string>("1M");

  // Different data for Balance and PnL
  const balanceData: DataSets = {
    "1D": [100, 120, 150, 170, 180, 200, 220],
    "1W": [80, 100, 130, 160, 190, 210, 250],
    "1M": [70, 95, 125, 155, 185, 205, 240],
    "3M": [50, 85, 115, 145, 175, 200, 230],
    "6M": [40, 75, 110, 140, 170, 195, 225],
  };

  const pnlData: DataSets = {
    "1D": [10, 15, 20, 18, 25, 30, 35],
    "1W": [5, 12, 18, 22, 27, 32, 38],
    "1M": [2, 8, 15, 20, 25, 30, 35],
    "3M": [1, 6, 12, 18, 24, 28, 32],
    "6M": [0, 5, 10, 15, 22, 26, 30],
  };

  return (
    <div className="flex flex-col h-full w-full text-white">
      <h1 className="text-xl font-semibold mb-4">Dashboard</h1>
      
      <div className="grid grid-cols-2 gap-4 mb-8">
        <div className="bg-[#1C1C1E] rounded-lg p-4">
          <h2 className="text-[#D0D0D3] text-sm font-medium mb-2">Current Balance</h2>
          <p className="text-2xl font-bold">$3,240.50</p>
        </div>
        
        <div className="bg-[#1C1C1E] rounded-lg p-4">
          <h2 className="text-[#D0D0D3] text-sm font-medium mb-2">Monthly Change</h2>
          <p className="text-2xl font-bold text-green-500">+$450.20</p>
        </div>
      </div>
      
      <RangePicker activeRange={activeRange} setActiveRange={setActiveRange} />
      
      <TimeGraph title="Net Worth Over Time" dataSets={balanceData} />
    </div>
  );
};

export default Dashboard;
