import React, { useEffect, useState } from "react";
import { homeAPI } from "@/utils/api";

interface TokenData {
  current_price?: number;
  price_change_percentage_24h?: number;
  total_volume?: number;
  volume_change_percentage_24h?: number;
  market_cap?: number;
  liquidity?: number | string;
  progress?: number;
}

interface TokenStatsProps {
  token: {
    id: string;
    network: string;
  };
}

const formatNumber = (num: number | string | undefined | null): string => {
  if (num === null || num === undefined || isNaN(Number(num))) return "N/A";

  const number = Number(num);
  if (number >= 1_000_000_000) {
    return (number / 1_000_000_000).toFixed(2) + "B";
  } else if (number >= 1_000_000) {
    return (number / 1_000_000).toFixed(2) + "M";
  } else if (number >= 1_000) {
    return (number / 1_000).toFixed(2) + "K";
  } else {
    return number.toFixed(2);
  }
};

const formatLowPrice = (price: number | undefined): JSX.Element => {
  if (price === undefined || price === null) return <span>N/A</span>;
  
  // For normal price range (0.01 and above)
  if (price >= 0.01) {
    return <span>${price.toFixed(4)}</span>;
  }
  
  // For scientific notation (e.g., 2.3847510365339614e-12)
  const priceStr = price.toString();
  
  if (priceStr.includes('e-')) {
    // Handle scientific notation
    const parts = priceStr.split('e-');
    const significantDigits = parts[0].replace('.', '');
    const exponent = parseInt(parts[1]);
    
    // For very small numbers like 2.3847510365339614e-12
    // We need to convert this to the format $0.0₁₁23847...
    const leadingZeros = exponent - 1;
    const significantPart = significantDigits.substring(0, 5);
    
    return (
      <span>
        $0.0<sub>{leadingZeros}</sub>{significantPart}
      </span>
    );
  }
  
  // For decimal notation (non-scientific)
  let leadingZeros = 0;
  let foundDecimal = false;
  
  for (let i = 0; i < priceStr.length; i++) {
    if (priceStr[i] === '.') {
      foundDecimal = true;
      continue;
    }
    
    if (foundDecimal && priceStr[i] === '0') {
      leadingZeros++;
    } else if (foundDecimal) {
      break;
    }
  }
  
  // Get the significant digits after all leading zeros
  const significantIndex = priceStr.indexOf('.') + leadingZeros + 1;
  const significantPart = priceStr.substring(significantIndex).substring(0, 5);
  
  return (
    <span>
      $0.0<sub>{leadingZeros}</sub>{significantPart}
    </span>
  );
};

const TokenStats: React.FC<TokenStatsProps> = ({ token }) => {
  const [tok, setToken] = useState<TokenData | null>(null);
  const [loadingToken, setLoadingToken] = useState<boolean>(false);
  
  // Memoize the token data to prevent unnecessary fetches
  const tokenKey = React.useMemo(() => {
    return `${token.id}-${token.network}`;
  }, [token.id, token.network]);

  useEffect(() => {
    let isMounted = true;
    let controller: AbortController | null = null;
    let timeoutId: NodeJS.Timeout;
    
    const fetchTokenData = async () => {
      try {
        // Set loading state only if we're still mounted
        if (isMounted) setLoadingToken(true);
        
        // Create abort controller for this request
        controller = new AbortController();
        const signal = controller.signal;
        
        const response = await homeAPI.fetchMarketInfo('market-data', {
          address: token.id,
          network: token.network,
          signal // Pass abort signal to allow cancellation
        });

        // Only process response if component is still mounted and not aborted
        if (!isMounted || signal.aborted) return;
        
        const data = response.data?.data;
        if (!data) return;

        setToken({
          current_price: data.price,
          price_change_percentage_24h: data.price_change_24h,
          total_volume: data.volume,
          market_cap: data.market_cap,
          volume_change_percentage_24h: data.volume_change_24h || 0.0,
          liquidity: data.liquidity,
          progress: Math.min(Math.max((data.price_change_24h ?? 0) / 2 + 50, 0), 100),
        });
      } catch (err) {
        // Only log error if not aborted and component is still mounted
        if (isMounted && !(err instanceof DOMException && err.name === 'AbortError')) {
          console.error("Failed to fetch token data", err);
        }
      } finally {
        // Set loading state to false if still mounted
        if (isMounted) setLoadingToken(false);
      }
    };

    // Debounce the API call to prevent too many requests
    timeoutId = setTimeout(fetchTokenData, 300);

    // Clean up function to abort ongoing requests when unmounting or when dependencies change
    return () => {
      isMounted = false;
      if (controller) controller.abort();
      clearTimeout(timeoutId);
    };
  }, [tokenKey]); // Only re-run when token ID or network changes

  return (
    <div className="w-full h-full overflow-x-auto scrollbar-hide">
      <div className="flex items-center justify-between px-4 gap-8 h-full rounded-xl text-white text-xs whitespace-nowrap">
        {loadingToken || !tok ? (
          // Skeleton UI
          <div className="flex items-center gap-8 animate-pulse w-full">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex flex-col space-y-1 w-28">
                <div className="h-4 bg-gray-600 rounded w-16"></div>
                <div className="h-5 bg-gray-700 rounded w-full"></div>
              </div>
            ))}
            <div className="flex flex-col space-y-1">
              <div className="h-4 bg-gray-600 rounded w-16"></div>
              <div className="flex items-center space-x-2">
                <div className="w-20 h-4 bg-gray-700 rounded-full"></div>
                <div className="w-6 h-4 bg-gray-600 rounded"></div>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Price */}
            <div className="flex flex-col">
              <span className="text-[#BBBBBB] text-sm">Price</span>
              <span className="text-sm font-semibold flex items-center">
                {formatLowPrice(tok.current_price)}
                <span
                  className={`ml-2 text-sm ${
                    (tok.price_change_percentage_24h ?? 0) >= 0 ? "text-green-400" : "text-[#FF329B]"
                  }`}
                >
                  {(tok.price_change_percentage_24h ?? 0) >= 0 ? "+" : ""}
                  {tok.price_change_percentage_24h?.toFixed(1)}%
                </span>
              </span>
            </div>
  
            {/* Volume */}
            <div className="flex flex-col">
              <span className="text-[#BBBBBB] text-sm">Volume</span>
              <span className="text-sm font-semibold">
                ${formatNumber(tok.total_volume!)}
                <span className="text-green-400 text-sm ml-1">
                  {(tok.volume_change_percentage_24h ?? 0) >= 0 ? "+" : ""}
                  {tok.volume_change_percentage_24h?.toFixed(1)}%
                </span>
              </span>
            </div>
  
            {/* Divider */}
            <div className="w-[1px] h-10 bg-[#BBBBBB] mx-4"></div>
  
            {/* MarketCap */}
            <div className="flex flex-col">
              <span className="text-[#BBBBBB] text-sm">MarketCap</span>
              <span className="text-sm font-semibold">
                ${formatNumber(tok.market_cap!)}
              </span>
            </div>
  
            {/* Liquidity */}
            <div className="flex flex-col">
              <span className="text-[#BBBBBB] text-sm">Liquidity</span>
              <span className="text-sm font-semibold">
                ${formatNumber(tok.liquidity as number)}
              </span>
            </div>
  
            {/* Progress */}
            <div className="flex flex-col">
              <span className="text-[#BBBBBB] text-sm">Progress</span>
              <div className="flex items-center">
                <div className="relative w-20 h-4 bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className="absolute left-0 h-full bg-white"
                    style={{ width: `${tok.progress}%` }}
                  ></div>
                </div>
                <span className="ml-2 text-sm">
                  {tok.progress?.toFixed(2)}%
                </span>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default TokenStats;