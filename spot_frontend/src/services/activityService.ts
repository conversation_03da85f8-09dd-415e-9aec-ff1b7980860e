import { api } from '@/utils/api';

interface ActivityResponse {
  message: string;
  status: string;
}

interface ActivityStats {
  activity: {
    totalSessions: number;
    activeSessions: number;
    pulsePageUsers: number;
    totalActiveUsers: number;
  };
  webSocket: {
    connected: boolean;
    activeUsers: number;
    lastUpdate: number | null;
  };
}

class ActivityService {
  private userId: string | null = null;
  private sessionId: string | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private isOnPulsePage = false;
  private isInitialized = false;

  constructor() {
    this.initializeSession();
    this.setupBeforeUnload();
  }

  /**
   * Initialize session with persistent user ID
   */
  private initializeSession(): void {
    if (this.isInitialized) return;

    // Try to get existing user ID from localStorage
    const existingUserId = localStorage.getItem('pulse_user_id');
    const existingSessionId = localStorage.getItem('pulse_session_id');

    if (existingUserId && existingSessionId) {
      this.userId = existingUserId;
      this.sessionId = existingSessionId;
      console.log('Restored existing session:', { userId: this.userId, sessionId: this.sessionId });
    } else {
      // Generate new session
      this.generateNewSession();
    }

    this.isInitialized = true;
  }

  /**
   * Generate a new session with unique IDs
   */
  private generateNewSession(): void {
    const timestamp = Date.now();
    this.userId = `user_${timestamp}_${Math.random().toString(36).substring(2, 11)}`;
    this.sessionId = `session_${timestamp}_${Math.random().toString(36).substring(2, 11)}`;

    // Store in localStorage for persistence
    localStorage.setItem('pulse_user_id', this.userId);
    localStorage.setItem('pulse_session_id', this.sessionId);

    console.log('Generated new session:', { userId: this.userId, sessionId: this.sessionId });
  }

  /**
   * Set the current user ID (for external user management)
   */
  public setUserId(userId: string): void {
    if (this.userId === userId) return; // Avoid unnecessary updates

    this.userId = userId;
    localStorage.setItem('pulse_user_id', userId);
    console.log('Updated user ID:', userId);
  }

  /**
   * Get or initialize user session
   */
  public getOrCreateSession(): { userId: string; sessionId: string } {
    if (!this.isInitialized) {
      this.initializeSession();
    }

    if (!this.userId || !this.sessionId) {
      this.generateNewSession();
    }

    return {
      userId: this.userId!,
      sessionId: this.sessionId!
    };
  }

  /**
   * Register user activity on pulse page
   */
  public async registerPulseActivity(): Promise<void> {
    // Prevent duplicate registrations
    if (this.isOnPulsePage) {
      console.log('User already registered on pulse page, skipping duplicate registration');
      return;
    }

    const session = this.getOrCreateSession();

    try {
      await api.post('/activity/pulse/register', {
        userId: session.userId,
        sessionId: session.sessionId
      });

      this.isOnPulsePage = true;
      this.startHeartbeat();

      console.log('Registered pulse activity for user:', session.userId);
    } catch (error) {
      console.error('Failed to register pulse activity:', error);
    }
  }

  /**
   * Unregister user activity from pulse page
   */
  public async unregisterPulseActivity(): Promise<void> {
    if (!this.isOnPulsePage) {
      console.log('User not registered on pulse page, skipping unregistration');
      return;
    }

    const session = this.getOrCreateSession();

    try {
      await api.post('/activity/pulse/unregister', {
        userId: session.userId,
        sessionId: session.sessionId
      });

      this.isOnPulsePage = false;
      this.stopHeartbeat();

      console.log('Unregistered pulse activity for user:', session.userId);
    } catch (error) {
      console.error('Failed to unregister pulse activity:', error);
      // Still mark as unregistered locally even if API call fails
      this.isOnPulsePage = false;
      this.stopHeartbeat();
    }
  }

  /**
   * Clear session data (for logout or session reset)
   */
  public clearSession(): void {
    this.unregisterPulseActivity();
    localStorage.removeItem('pulse_user_id');
    localStorage.removeItem('pulse_session_id');
    this.userId = null;
    this.sessionId = null;
    this.isInitialized = false;
    console.log('Session cleared');
  }

  /**
   * Send heartbeat to maintain activity
   */
  private async sendHeartbeat(): Promise<void> {
    if (!this.userId) {
      return;
    }

    try {
      await api.post('/activity/heartbeat', {
        userId: this.userId,
        sessionId: this.sessionId
      });
    } catch (error) {
      console.error('Failed to send heartbeat:', error);
    }
  }

  /**
   * Start heartbeat interval
   */
  private startHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    // Send heartbeat every 30 seconds
    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat();
    }, 30000);
  }

  /**
   * Stop heartbeat interval
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Get activity statistics
   */
  public async getActivityStats(): Promise<ActivityStats | null> {
    try {
      const response = await api.get('/activity/stats');
      return response.data.data;
    } catch (error) {
      console.error('Failed to get activity stats:', error);
      return null;
    }
  }

  /**
   * Get WebSocket status
   */
  public async getWebSocketStatus(): Promise<any> {
    try {
      const response = await api.get('/activity/websocket-status');
      return response.data.data;
    } catch (error) {
      console.error('Failed to get WebSocket status:', error);
      return null;
    }
  }

  /**
   * Setup beforeunload handler to clean up activity
   */
  private setupBeforeUnload(): void {
    window.addEventListener('beforeunload', () => {
      if (this.isOnPulsePage) {
        // Use sendBeacon for reliable cleanup on page unload
        const data = JSON.stringify({
          userId: this.userId,
          sessionId: this.sessionId
        });

        navigator.sendBeacon(`${import.meta.env.VITE_API_URL || 'http://localhost:5001'}/activity/pulse/unregister`, data);
      }
    });

    // Also handle visibility change
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.isOnPulsePage) {
        this.unregisterPulseActivity();
      } else if (!document.hidden && this.userId) {
        // Re-register if user comes back to visible pulse page
        const currentPath = window.location.pathname;
        if (currentPath.includes('/pulse') || currentPath === '/') {
          this.registerPulseActivity();
        }
      }
    });
  }

  /**
   * Check if user is currently on pulse page
   */
  public isUserOnPulsePage(): boolean {
    return this.isOnPulsePage;
  }

  /**
   * Get current session info
   */
  public getSessionInfo(): { userId: string | null; sessionId: string | null } {
    return {
      userId: this.userId,
      sessionId: this.sessionId
    };
  }
}

// Export singleton instance
export const activityService = new ActivityService();
