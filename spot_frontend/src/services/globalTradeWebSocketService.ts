// Types (matching backend)
export interface FormattedTrade {
  id: string;
  timestamp: number;
  type: 'buy' | 'sell';
  amount: string;
  usdAmount: string;
  mc: string;
  price: string;
  trader: string;
  age: string;
  txHash: string;
  marketCap: number;
  tokenAmount: number;
  tokenAmountUsd: number;
  actualTokenAmount: number;
  // Additional fields for live data
  pairData?: {
    price?: number;
    token0?: {
      address?: string;
      price?: number;
      marketCap?: number;
      totalSupply?: number;
      symbol?: string;
    };
    token1?: {
      address?: string;
      price?: number;
      marketCap?: number;
      totalSupply?: number;
      symbol?: string;
    };
    liquidity?: number;
    // Mobula volume metrics (directly from API)
    volume24h?: number;
    volume_1min?: number;
    volume_5min?: number;
    volume_1h?: number;
    volume_6h?: number;
    volume_12h?: number;
    // Mobula buy/sell volumes
    buy_volume_1min?: number;
    buy_volume_5min?: number;
    buy_volume_1h?: number;
    buy_volume_6h?: number;
    buy_volume_12h?: number;
    buy_volume_24h?: number;
    sell_volume_1min?: number;
    sell_volume_5min?: number;
    sell_volume_1h?: number;
    sell_volume_6h?: number;
    sell_volume_12h?: number;
    sell_volume_24h?: number;
    // Mobula transaction counts
    buyers_1min?: number;
    buyers_5min?: number;
    buyers_1h?: number;
    buyers_6h?: number;
    buyers_12h?: number;
    buyers_24h?: number;
    sellers_1min?: number;
    sellers_5min?: number;
    sellers_1h?: number;
    sellers_6h?: number;
    sellers_12h?: number;
    sellers_24h?: number;
  };
  token_price?: number;
  // Allow any additional fields from original trade
  [key: string]: any;
}

// Types for the global trade WebSocket service
interface TradeDataUpdate {
  poolAddress: string;
  trades: FormattedTrade[];
  latestRawTrade: any;
  timestamp: number;
}

interface PoolSubscription {
  poolAddress: string;
  subscribers: Set<string>;
  trades: FormattedTrade[];
  latestRawTrade: any | null;
  lastUpdate: number | null;
  isLoading: boolean;
  error: string | null;
}

interface TradeDataCallback {
  (update: TradeDataUpdate): void;
}

interface ConnectionStatusCallback {
  (isConnected: boolean, error?: string): void;
}

interface WebSocketMessage {
  type: 'trade' | 'error' | 'connected' | 'disconnected';
  data?: any;
  error?: string;
}

interface SubscriptionRequest {
  action: 'subscribe' | 'unsubscribe';
  poolAddress: string;
}

// Get WebSocket URL following the same proxy pattern as other services
const getWebSocketUrl = (): string => {
  const hostname = window.location.hostname;
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';

  // Production environment
  if (hostname.includes('crypfi.io')) {
    return 'wss://redfyn.crypfi.io/trading-panel-ws';
  }

  // Production server IP
  if (hostname === '*************') {
    return `${protocol}//*************:5003`;
  }

  // Development environment - use proxy pattern like HTTP APIs
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    // Use the same host and port as the frontend, let Vite proxy handle the routing
    const frontendPort = window.location.port || '4001';
    return `${protocol}//localhost:${frontendPort}/trading-panel-ws`;
  }

  // Fallback for other environments
  return `${protocol}//localhost:5003`;
};

/**
 * Global Trade WebSocket Service
 * Manages a single WebSocket connection shared across all components
 * Supports multiple pool subscriptions with automatic cleanup
 */
class GlobalTradeWebSocketService {
  private ws: WebSocket | null = null;
  private isConnected = false;
  private isConnecting = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 3000;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private maxTrades = 50;

  // Pool-based subscription management
  private poolSubscriptions = new Map<string, PoolSubscription>();
  
  // Callback management
  private tradeDataCallbacks = new Map<string, TradeDataCallback>();
  private connectionStatusCallbacks = new Set<ConnectionStatusCallback>();

  /**
   * Subscribe to trade data for a specific pool
   */
  public async subscribeToPool(
    poolAddress: string, 
    subscriberId: string, 
    callback: TradeDataCallback
  ): Promise<void> {
    console.log(`🔌 [GLOBAL] Subscribing to pool: ${poolAddress} (subscriber: ${subscriberId})`);

    // Store callback
    this.tradeDataCallbacks.set(subscriberId, callback);

    // Get or create pool subscription
    let poolSub = this.poolSubscriptions.get(poolAddress);
    if (!poolSub) {
      poolSub = {
        poolAddress,
        subscribers: new Set(),
        trades: [],
        latestRawTrade: null,
        lastUpdate: null,
        isLoading: true,
        error: null
      };
      this.poolSubscriptions.set(poolAddress, poolSub);
    }

    // Add subscriber to pool
    poolSub.subscribers.add(subscriberId);

    // Connect if not already connected
    if (!this.isConnected && !this.isConnecting) {
      await this.connect();
    }

    // Send subscription message if connected
    if (this.isConnected && this.ws) {
      this.sendSubscriptionMessage(poolAddress, 'subscribe');
    }

    // If we have existing data, immediately notify the subscriber
    if (poolSub.trades.length > 0 || poolSub.latestRawTrade) {
      callback({
        poolAddress,
        trades: poolSub.trades,
        latestRawTrade: poolSub.latestRawTrade,
        timestamp: poolSub.lastUpdate || Date.now()
      });
    }
  }

  /**
   * Unsubscribe from trade data for a specific pool
   */
  public unsubscribeFromPool(poolAddress: string, subscriberId: string): void {
    console.log(`🔌 [GLOBAL] Unsubscribing from pool: ${poolAddress} (subscriber: ${subscriberId})`);

    // Remove callback
    this.tradeDataCallbacks.delete(subscriberId);

    // Remove subscriber from pool
    const poolSub = this.poolSubscriptions.get(poolAddress);
    if (poolSub) {
      poolSub.subscribers.delete(subscriberId);

      // If no more subscribers for this pool, clean up
      if (poolSub.subscribers.size === 0) {
        console.log(`🧹 [GLOBAL] No more subscribers for pool ${poolAddress}, cleaning up`);
        this.poolSubscriptions.delete(poolAddress);

        // Send unsubscribe message if connected
        if (this.isConnected && this.ws) {
          this.sendSubscriptionMessage(poolAddress, 'unsubscribe');
        }
      }
    }

    // If no more pool subscriptions, disconnect after delay
    if (this.poolSubscriptions.size === 0) {
      console.log('🧹 [GLOBAL] No more pool subscriptions, scheduling disconnect');
      setTimeout(() => {
        if (this.poolSubscriptions.size === 0) {
          this.disconnect();
        }
      }, 5000); // 5 second delay to handle rapid re-subscriptions
    }
  }

  /**
   * Subscribe to connection status updates
   */
  public onConnectionStatus(callback: ConnectionStatusCallback): () => void {
    this.connectionStatusCallbacks.add(callback);
    
    // Immediately notify of current status
    callback(this.isConnected);

    // Return unsubscribe function
    return () => {
      this.connectionStatusCallbacks.delete(callback);
    };
  }

  /**
   * Get current data for a specific pool
   */
  public getPoolData(poolAddress: string): PoolSubscription | null {
    return this.poolSubscriptions.get(poolAddress) || null;
  }

  /**
   * Force reconnection
   */
  public async reconnect(): Promise<void> {
    console.log('🔄 [GLOBAL] Force reconnecting...');
    this.disconnect();
    await this.connect();
  }

  /**
   * Connect to WebSocket server
   */
  private async connect(): Promise<void> {
    if (this.isConnecting || this.isConnected) {
      return;
    }

    this.isConnecting = true;
    console.log('🔌 [GLOBAL] Connecting to trading WebSocket...');

    try {
      const wsUrl = getWebSocketUrl();
      console.log('🔗 [GLOBAL] WebSocket URL:', wsUrl);
      
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('✅ [GLOBAL] Connected to trading WebSocket server');
        this.isConnected = true;
        this.isConnecting = false;
        this.reconnectAttempts = 0;

        // Notify connection status callbacks
        this.connectionStatusCallbacks.forEach(callback => {
          callback(true);
        });

        // Re-subscribe to all active pools
        for (const poolAddress of this.poolSubscriptions.keys()) {
          this.sendSubscriptionMessage(poolAddress, 'subscribe');
        }
      };

      this.ws.onmessage = (event) => {
        this.handleMessage(event.data);
      };

      this.ws.onclose = (event) => {
        console.log(`🔌 [GLOBAL] Connection closed. Code: ${event.code}`);
        this.isConnected = false;
        this.isConnecting = false;

        // Notify connection status callbacks
        this.connectionStatusCallbacks.forEach(callback => {
          callback(false);
        });

        // Attempt reconnection if not intentional and we have active subscriptions
        if (event.code !== 1000 && this.poolSubscriptions.size > 0 && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      };

      this.ws.onerror = (error) => {
        console.error('❌ [GLOBAL] WebSocket error:', error);
        this.isConnected = false;
        this.isConnecting = false;

        // Notify connection status callbacks
        this.connectionStatusCallbacks.forEach(callback => {
          callback(false, 'Connection failed');
        });
      };

    } catch (error) {
      console.error('❌ [GLOBAL] Failed to create WebSocket connection:', error);
      this.isConnecting = false;
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  private disconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.ws) {
      try {
        // Send unsubscribe messages for all active pools
        for (const poolAddress of this.poolSubscriptions.keys()) {
          this.sendSubscriptionMessage(poolAddress, 'unsubscribe');
        }

        this.ws.close(1000, 'Intentional disconnect');
      } catch (error) {
        console.error('Error during disconnect:', error);
      }
      this.ws = null;
    }

    this.isConnected = false;
    this.isConnecting = false;

    // Notify connection status callbacks
    this.connectionStatusCallbacks.forEach(callback => {
      callback(false);
    });

    console.log('✅ [GLOBAL] Disconnected from trading WebSocket');
  }

  /**
   * Send subscription/unsubscription message
   */
  private sendSubscriptionMessage(poolAddress: string, action: 'subscribe' | 'unsubscribe'): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    const message: SubscriptionRequest = {
      action,
      poolAddress
    };

    try {
      this.ws.send(JSON.stringify(message));
      console.log(`✅ [GLOBAL] Sent ${action} message for pool: ${poolAddress}`);
    } catch (error) {
      console.error(`❌ [GLOBAL] Failed to send ${action} message:`, error);
    }
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(data: string): void {
    try {
      const message: WebSocketMessage = JSON.parse(data);

      switch (message.type) {
        case 'trade':
          this.handleTradeMessage(message.data);
          break;
        case 'error':
          console.error('❌ [GLOBAL] Server error:', message.error);
          break;
        case 'connected':
          console.log('✅ [GLOBAL] Server confirmed connection');
          break;
        case 'disconnected':
          console.log('🔌 [GLOBAL] Server confirmed disconnection');
          break;
        default:
          console.warn('⚠️ [GLOBAL] Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('❌ [GLOBAL] Failed to parse message:', error);
    }
  }

  /**
   * Handle trade data messages
   */
  private handleTradeMessage(tradeData: any): void {
    if (!tradeData || !tradeData.poolAddress) {
      console.warn('⚠️ [GLOBAL] Invalid trade data received:', tradeData);
      return;
    }

    const poolAddress = tradeData.poolAddress;
    const poolSub = this.poolSubscriptions.get(poolAddress);

    if (!poolSub) {
      console.warn(`⚠️ [GLOBAL] Received trade for unsubscribed pool: ${poolAddress}`);
      return;
    }

    console.log(`📊 [GLOBAL] Processing trade data for pool: ${poolAddress}`);

    // Add trade to pool's trade list
    poolSub.trades = [tradeData, ...poolSub.trades].slice(0, this.maxTrades);
    poolSub.latestRawTrade = tradeData;
    poolSub.lastUpdate = Date.now();
    poolSub.isLoading = false;
    poolSub.error = null;

    // Notify all subscribers for this pool
    const update: TradeDataUpdate = {
      poolAddress,
      trades: poolSub.trades,
      latestRawTrade: poolSub.latestRawTrade,
      timestamp: poolSub.lastUpdate
    };

    poolSub.subscribers.forEach(subscriberId => {
      const callback = this.tradeDataCallbacks.get(subscriberId);
      if (callback) {
        callback(update);
      }
    });
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    console.log(`🔄 [GLOBAL] Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);

    this.reconnectTimeout = setTimeout(() => {
      this.connect();
    }, delay);
  }
}

// Create and export singleton instance
export const globalTradeWebSocketService = new GlobalTradeWebSocketService();
