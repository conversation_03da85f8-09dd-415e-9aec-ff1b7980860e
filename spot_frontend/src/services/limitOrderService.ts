import axios from 'axios';

// Get the backend API URL
const getBackendApiUrl = () => {
  // Check if we have a specific backend URL in environment
  if (import.meta.env.VITE_BACKEND_API_URL) {
    return import.meta.env.VITE_BACKEND_API_URL;
  }

  // Dynamic fallback based on current hostname
  const hostname = window.location.hostname;
  
  // Production environments
  if (hostname.includes('crypfi.io')) {
    return 'https://redfyn.crypfi.io/spot-backend';
  }
  if (hostname.includes('lrbinfotech.com')) {
    return 'https://redfyn.lrbinfotech.com/spot-backend';
  }

  // Development environment
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return 'http://localhost:5001';
  }

  // Fallback for production
  return '/spot-backend';
};

const BACKEND_API_URL = getBackendApiUrl();

// TypeScript interfaces
export interface LimitOrder {
  id: string;
  user_id: string;
  token_address: string;
  token_name: string;
  token_symbol: string;
  token_image?: string;
  pool_address: string;
  dex_type: string;
  direction: 'buy' | 'sell';
  amount: number;
  target_price: number;
  current_price: number;
  current_market_cap?: number;
  target_market_cap?: number;
  slippage: number;
  wallet_address: string;
  wallet_id: string;
  status: 'pending' | 'executed' | 'cancelled' | 'expired';
  expires_at?: string;
  created_at: string;
  updated_at: string;
  executed_at?: string;
  execution_tx_hash?: string;
  error_message?: string;
}

export interface CreateLimitOrderRequest {
  user_id: string;
  token_address: string;
  token_name: string;
  token_symbol: string;
  token_image?: string;
  pool_address: string;
  dex_type: string;
  direction: 'buy' | 'sell';
  amount: number;
  target_price: number;
  current_price: number;
  current_market_cap?: number;
  target_market_cap?: number;
  slippage?: number;
  wallet_address: string;
  wallet_id: string;
  expires_at?: string;
}

export interface LimitOrderFilters {
  status?: 'pending' | 'executed' | 'cancelled' | 'expired';
  token_address?: string;
  direction?: 'buy' | 'sell';
  dex_type?: string;
  limit?: number;
  offset?: number;
  order_by?: 'created_at' | 'updated_at' | 'target_price';
  order_direction?: 'asc' | 'desc';
}

export interface LimitOrderStats {
  pending: number;
  executed: number;
  cancelled: number;
  expired: number;
  total: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  count?: number;
  error?: string;
  message?: string;
}

/**
 * Limit Order Service for frontend
 */
export class LimitOrderService {
  
  /**
   * Create a new limit order
   */
  async createLimitOrder(orderData: CreateLimitOrderRequest): Promise<ApiResponse<LimitOrder>> {
    try {
      const response = await axios.post(`${BACKEND_API_URL}/api/limit-orders`, orderData, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      });

      return response.data;
    } catch (error) {
      console.error('Error creating limit order:', error);
      
      if (axios.isAxiosError(error)) {
        return {
          success: false,
          error: error.response?.data?.error || error.message || 'Network error'
        };
      }

      return {
        success: false,
        error: 'Unknown error occurred'
      };
    }
  }

  /**
   * Get limit orders for a user
   */
  async getLimitOrders(userId: string, filters: LimitOrderFilters = {}): Promise<ApiResponse<LimitOrder[]>> {
    try {
      const params = new URLSearchParams({
        user_id: userId,
        ...Object.fromEntries(
          Object.entries(filters).map(([key, value]) => [key, String(value)])
        )
      });

      const response = await axios.get(`${BACKEND_API_URL}/api/limit-orders?${params}`, {
        timeout: 10000,
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching limit orders:', error);
      
      if (axios.isAxiosError(error)) {
        return {
          success: false,
          error: error.response?.data?.error || error.message || 'Network error'
        };
      }

      return {
        success: false,
        error: 'Unknown error occurred'
      };
    }
  }

  /**
   * Get a specific limit order by ID
   */
  async getLimitOrderById(userId: string, orderId: string): Promise<ApiResponse<LimitOrder>> {
    try {
      const response = await axios.get(`${BACKEND_API_URL}/api/limit-orders/${orderId}?user_id=${userId}`, {
        timeout: 10000,
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching limit order:', error);
      
      if (axios.isAxiosError(error)) {
        return {
          success: false,
          error: error.response?.data?.error || error.message || 'Network error'
        };
      }

      return {
        success: false,
        error: 'Unknown error occurred'
      };
    }
  }

  /**
   * Cancel a limit order
   */
  async cancelLimitOrder(userId: string, orderId: string): Promise<ApiResponse<LimitOrder>> {
    try {
      const response = await axios.delete(`${BACKEND_API_URL}/api/limit-orders/${orderId}/cancel?user_id=${userId}`, {
        timeout: 10000,
      });

      return response.data;
    } catch (error) {
      console.error('Error cancelling limit order:', error);

      if (axios.isAxiosError(error)) {
        return {
          success: false,
          error: error.response?.data?.error || error.message || 'Network error'
        };
      }

      return {
        success: false,
        error: 'Unknown error occurred'
      };
    }
  }

  /**
   * Permanently delete a limit order (only cancelled/expired orders)
   */
  async deleteLimitOrder(userId: string, orderId: string): Promise<ApiResponse<void>> {
    try {
      const response = await axios.delete(`${BACKEND_API_URL}/api/limit-orders/${orderId}?user_id=${userId}`, {
        timeout: 10000,
      });

      return response.data;
    } catch (error) {
      console.error('Error deleting limit order:', error);

      if (axios.isAxiosError(error)) {
        return {
          success: false,
          error: error.response?.data?.error || error.message || 'Network error'
        };
      }

      return {
        success: false,
        error: 'Unknown error occurred'
      };
    }
  }

  /**
   * Update a limit order
   */
  async updateLimitOrder(
    userId: string, 
    orderId: string, 
    updateData: { status?: 'pending' | 'cancelled'; error_message?: string }
  ): Promise<ApiResponse<LimitOrder>> {
    try {
      const response = await axios.put(`${BACKEND_API_URL}/api/limit-orders/${orderId}`, {
        user_id: userId,
        ...updateData
      }, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      });

      return response.data;
    } catch (error) {
      console.error('Error updating limit order:', error);
      
      if (axios.isAxiosError(error)) {
        return {
          success: false,
          error: error.response?.data?.error || error.message || 'Network error'
        };
      }

      return {
        success: false,
        error: 'Unknown error occurred'
      };
    }
  }

  /**
   * Get order statistics for a user
   */
  async getLimitOrderStats(userId: string): Promise<ApiResponse<LimitOrderStats>> {
    try {
      const response = await axios.get(`${BACKEND_API_URL}/api/limit-orders/stats?user_id=${userId}`, {
        timeout: 10000,
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching limit order stats:', error);
      
      if (axios.isAxiosError(error)) {
        return {
          success: false,
          error: error.response?.data?.error || error.message || 'Network error'
        };
      }

      return {
        success: false,
        error: 'Unknown error occurred'
      };
    }
  }

  /**
   * Helper method to validate order data before submission
   */
  validateOrderData(orderData: CreateLimitOrderRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required field validation
    const requiredFields: (keyof CreateLimitOrderRequest)[] = [
      'user_id', 'token_address', 'token_name', 'token_symbol',
      'pool_address', 'dex_type', 'direction', 'amount',
      'target_price', 'current_price', 'wallet_address', 'wallet_id'
    ];

    for (const field of requiredFields) {
      if (!orderData[field]) {
        errors.push(`Missing required field: ${field}`);
      }
    }

    // Numeric validations
    if (orderData.amount && orderData.amount <= 0) {
      errors.push('Amount must be greater than 0');
    }

    if (orderData.target_price && orderData.target_price <= 0) {
      errors.push('Target price must be greater than 0');
    }

    if (orderData.current_price && orderData.current_price <= 0) {
      errors.push('Current price must be greater than 0');
    }

    if (orderData.slippage && (orderData.slippage < 0 || orderData.slippage > 1)) {
      errors.push('Slippage must be between 0 and 1');
    }

    // Enum validations
    if (orderData.direction && !['buy', 'sell'].includes(orderData.direction)) {
      errors.push('Direction must be either "buy" or "sell"');
    }

    if (orderData.dex_type && !['pumpfun', 'pumpswap', 'launchlab'].includes(orderData.dex_type)) {
      errors.push('Invalid dex_type');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Export singleton instance
export const limitOrderService = new LimitOrderService();
