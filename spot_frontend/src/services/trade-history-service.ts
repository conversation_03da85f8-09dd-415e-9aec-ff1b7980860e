import { supabase } from '../utils/supabase-client';
import { Trade, Order, TradeHistory } from '../types/trade-history';

// Store a completed trade
export async function storeCompletedTrade(tradeData: Omit<Trade, 'id'>): Promise<{ success: boolean, data?: Trade, error?: any }> {
  try {
    const { data, error } = await supabase
      .from('trades')
      .insert([tradeData])
      .select();
    
    if (error) throw error;
    
    return { success: true, data: data?.[0] };
  } catch (error) {
    console.error('Error storing completed trade:', error);
    return { success: false, error };
  }
}

// Store a trade activity in history
export async function storeTradeHistory(historyData: Omit<TradeHistory, 'id'>): Promise<{ success: boolean, data?: TradeHistory, error?: any }> {
  try {
    // Ensure timestamp is set if not provided
    const dataWithTimestamp = {
      ...historyData,
      timestamp: historyData.timestamp || new Date().toISOString()
    };
    
    const { data, error } = await supabase
      .from('trade_history')
      .insert([dataWithTimestamp])
      .select();
    
    if (error) throw error;
    
    return { success: true, data: data?.[0] };
  } catch (error) {
    console.error('Error storing trade history:', error);
    return { success: false, error };
  }
}

// Store an order (market or limit)
export async function storeOrder(orderData: Omit<Order, 'id'>): Promise<{ success: boolean, data?: Order, error?: any }> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .insert([orderData])
      .select();
    
    if (error) throw error;
    
    return { success: true, data: data?.[0] };
  } catch (error) {
    console.error('Error storing order:', error);
    return { success: false, error };
  }
}

// Fetch a user's completed trades
export async function fetchCompletedTrades(walletAddress: string): Promise<Trade[]> {
  try {
    const { data, error } = await supabase
      .from('trades')
      .select('*')
      .eq('wallet_address', walletAddress)
      .order('timestamp', { ascending: false });
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    console.error('Error fetching completed trades:', error);
    return [];
  }
}

// Fetch a user's orders
export async function fetchOrders(walletAddress: string, status?: Order['status']): Promise<Order[]> {
  try {
    let query = supabase
      .from('orders')
      .select('*')
      .eq('wallet_address', walletAddress);
    
    // Add status filter if provided
    if (status) {
      query = query.eq('status', status);
    }
    
    const { data, error } = await query.order('timestamp', { ascending: false });
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    console.error('Error fetching orders:', error);
    return [];
  }
}

// Fetch a user's trade history
export async function fetchTradeHistory(walletAddress: string): Promise<TradeHistory[]> {
  try {
    const { data, error } = await supabase
      .from('trade_history')
      .select('*')
      .eq('wallet_address', walletAddress)
      .order('timestamp', { ascending: false });
    
    if (error) throw error;
    
    return data || [];
  } catch (error) {
    console.error('Error fetching trade history:', error);
    return [];
  }
}

// Update an order status (e.g., from Open to Filled or Canceled)
export async function updateOrderStatus(orderId: string, status: Order['status']): Promise<{ success: boolean, error?: any }> {
  try {
    const { error } = await supabase
      .from('orders')
      .update({ status })
      .eq('id', orderId);
    
    if (error) throw error;
    
    return { success: true };
  } catch (error) {
    console.error('Error updating order status:', error);
    return { success: false, error };
  }
}
