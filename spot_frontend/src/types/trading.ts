// Type definitions for trading related components

export interface TokenBase {
  address?: string;
  tokenAddress?: string;
  contract?: string;
  decimals?: number;
  symbol?: string;
  name?: string;
  chainId?: number | string;
  network?: string;
  balance?: string;
  chain?: string;
  price?: string | number;
  icon?: string;
}

export interface QuoteData {
  price?: string | number;
  pairAddress?: string;
  dex?: string;
  dexId?: string;
  chain?: string;
  name?: string;
  symbol?: string;
  address?: string;
  routerAddress?: string;
  path?: string[];
  executionPrice?: string | number;
  priceImpact?: string | number;
  platformFee?: {
    amount?: string | number;
    percentage?: string | number;
    token?: {
      symbol?: string;
    } | string | number;
  } | string | number | FeeData;
  feeData?: FeeData;  // Fee data from swap response
  tradingFee?: {
    amount?: string | number;
    percentage?: string | number;
    token?: string;
  } | string | number;
  amountOut?: string | number;
  amountIn?: string | number;
  tokenIn?: {
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
  tokenOut?: {
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
  quote?: {
    amountOut?: string | number;
    outputAmount?: string | number;
    priceImpact?: string | number;
    platformFee?: {
      recipient: string;
      amount: string;
      token: string;
      percentage: number;
    } | FeeData;
  };
  availableOptions?: string[];
  message?: string;
  data?: any;
}

export interface WalletBase {
  address?: string;
  connected?: boolean;
  getAddress?: () => string;
  sendTransaction?: (tx: any) => Promise<any>;
  provider?: any;
  ethersProvider?: any;
  embeddedWallet?: any;
  walletClient?: any;
  walletClientType?: string;
  connectedAt?: Date | string | number;
  id?: string;
  chain?: string;
  connector?: any;
  embedded?: boolean;
  type?: string;
  connectorType?: string;
  smartWalletType?: string;
  chainType?: string;
}

export interface FeeData {
  recipient: string;
  amount: string;
  token: string;
  percentage: number;
}

export interface PancakeswapQuoteData {
  baseToken: {
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
  quoteToken: {
    address: string;
    decimals: number;
    symbol: string;
    name: string;
  };
  pairAddress: string;
  feeData?: FeeData; // Fee data from swap response
  platformFee?: FeeData; // Fee data from quote response
}

export interface TradeOptions {
  slippageTolerance?: number | string;
  pairAddress?: string;
  gasLimit?: number;
}

export interface UseWalletsInterface {
  wallets: WalletBase[];
  connectWallet: (options?: any) => Promise<any>;
}

export interface TradingPanelProps {
  activeToken: TokenBase | null;
}

export interface WalletSelectorProps {
  onSelectWallet: (wallet: WalletBase) => void;
  selectedWallet: WalletBase | null;
}

export interface WalletApiResponse {
  success: boolean;
  balances?: {
    ethereum?: any[];
    bsc?: any[];
    solana?: any[];
    [key: string]: any[] | undefined;
  };
  message?: string;
}

export interface QuoteErrorResponse {
  code?: string;
  message?: string;
}

export interface SwapResponse {
  success: boolean;
  data?: any;
  message?: string;
  source?: string;
  error?: any;
  needsAllowance?: boolean;
}