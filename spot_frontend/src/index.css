@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  font-size: 14px;
}

/* Custom Scrollbar Styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(80, 80, 100, 0.5);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 100, 120, 0.7);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px; /* Adjust width */
}

::-webkit-scrollbar-thumb {
  background: #888; /* Thumb color */
  border-radius: 10px; /* Rounded edges */
}

::-webkit-scrollbar-thumb:hover {
  background: #555; /* Darker on hover */
}

::-webkit-scrollbar-track {
  background: transparent; /* Hides scrollbar track */
}

/* Add at the end of the file */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* Success popup visibility variables */
:root {
  --popup-visibility: visible;
  --popup-opacity: 1;
}

.popup-success-container {
  visibility: var(--popup-visibility);
  opacity: var(--popup-opacity);
  transition: opacity 0.3s, visibility 0.3s;
}

/* Force success popup visibility */
.popup-success-container {
  visibility: visible !important;
  opacity: 1 !important;
  display: flex !important;
  z-index: 9999 !important;
  position: fixed !important;
  inset: 0 !important;
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* Enhancement for pulsing animation */
@keyframes enhanced-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(20, 255, 162, 0.7);
  }
  
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(20, 255, 162, 0);
  }
  
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(20, 255, 162, 0);
  }
}

.animate-enhanced-pulse {
  animation: enhanced-pulse 2s infinite;
}
