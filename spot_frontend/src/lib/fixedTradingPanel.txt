// In handleTrade function, replace these lines:

        // Get transaction data
        const txHash = tradeResult.data?.transactionHash || tradeResult.data?.transaction?.transactionHash || tradeResult.data?.hash;
        
            // Open the success popup with transaction details
            setSuccessPopup({
              isOpen: true,
              txHash: txHash || '',
              tokenIn: tokenIn,
              tokenOut: tokenOut,
              amountIn: amount,
              amountOut: outputAmount
            });

// With this code:

        // Get transaction data
        const txHash = tradeResult.data?.transactionHash || tradeResult.data?.transaction?.transactionHash || tradeResult.data?.hash;
        console.log("Transaction success! Hash:", txHash);
        console.log("Transaction data:", tradeResult.data);
        
        // Open the success popup with transaction details
        console.log("Setting success popup with:", {
          isOpen: true,
          txHash: txHash || '',
          tokenIn: tokenIn?.symbol,
          tokenOut: tokenOut?.symbol,
          amountIn: amount,
          amountOut: outputAmount
        });
        
        setSuccessPopup({
          isOpen: true,
          txHash: txHash || '',
          tokenIn: tokenIn,
          tokenOut: tokenOut,
          amountIn: amount,
          amountOut: outputAmount
        }); 