// Simple API connectivity test
// Run this in the browser console to test API connectivity

async function testAPIConnectivity() {
  console.log('🧪 Testing API Connectivity...');
  
  const tests = [
    {
      name: 'Connect Coins',
      url: '/api/home/<USER>',
      method: 'GET'
    },
    {
      name: 'Network Highlights',
      url: '/api/home/<USER>',
      method: 'GET'
    },
    {
      name: 'All Balances (will fail without wallet)',
      url: '/api/wallet/all-balances',
      method: 'POST',
      data: { walletAddress: 'test' }
    }
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      console.log(`\n🔍 Testing: ${test.name}`);
      console.log(`📍 URL: ${test.url}`);
      
      const startTime = Date.now();
      
      const response = await fetch(test.url, {
        method: test.method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: test.data ? JSON.stringify(test.data) : undefined
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const data = await response.json();
      
      results.push({
        test: test.name,
        status: response.status,
        success: response.ok,
        duration: `${duration}ms`,
        dataReceived: !!data
      });
      
      if (response.ok) {
        console.log(`✅ ${test.name}: SUCCESS (${response.status}) - ${duration}ms`);
        console.log(`📊 Data keys:`, Object.keys(data));
      } else {
        console.log(`❌ ${test.name}: FAILED (${response.status}) - ${duration}ms`);
        console.log(`📊 Error:`, data);
      }
      
    } catch (error) {
      console.log(`💥 ${test.name}: ERROR - ${error.message}`);
      results.push({
        test: test.name,
        status: 'ERROR',
        success: false,
        duration: 'N/A',
        error: error.message
      });
    }
  }
  
  console.log('\n📋 Test Summary:');
  console.table(results);
  
  const successCount = results.filter(r => r.success).length;
  console.log(`\n🎯 Results: ${successCount}/${results.length} tests passed`);
  
  return results;
}

// Auto-run if in browser
if (typeof window !== 'undefined') {
  console.log('🚀 API Test Script Loaded. Run testAPIConnectivity() to test.');
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testAPIConnectivity };
}
