// Type declarations for @privy-io/react-auth/bsc
declare module '@privy-io/react-auth/bsc' {
  /**
   * Hook for interacting with Privy's smart account features on BSC
   * Returns a client that can be used to create and send UserOperations
   */
  export function usePrivySmartAccount(): {
    /**
     * Creates a bundled UserOperation from multiple operations
     * @param operations Array of operations to bundle
     */
    createBundledUserOperation?: (operations: any[]) => Promise<any>;
    
    /**
     * Creates a UserOperation
     * @param operations Array of operations
     */
    createUserOperation?: (operations: any[]) => Promise<any>;
    
    /**
     * Sends a UserOperation to the bundler
     * @param userOp UserOperation to send
     */
    sendUserOperation: (userOp: any) => Promise<string>;
    
    /**
     * The smart account address
     */
    address?: string;
    
    /**
     * Whether the smart account is ready
     */
    ready?: boolean;
  };
}
