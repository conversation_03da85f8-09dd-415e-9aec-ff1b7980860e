/**
 * BundlerPaymasterService
 * 
 * Centralized utilities for managing interactions with ERC-4337 bundlers and paymasters
 * Supports hybrid approach with NodeReal bundler and Pimlico paymaster
 */

// Configure correct metadata access for Vite
// This should be at the top of the file before imports

// Import only what's needed from paymasterClient, removing conflicting imports
import { getPimlicoRpcUrl, getPimlicoPaymasterUrl } from './paymasterClient';
import * as ethers from 'ethers';

// Define PlatformFee interface locally
interface PlatformFee {
  recipient: string;
  amount: string;
  token: string;
  percentage: number;
}

// Store transaction logs for debugging
let transactionLogs: any[] = [];
let nodeRealTransactionLog: NodeRealTransactionLog[] = [];

// Debug mode
const DEBUG_MODE = true;

// Add type declaration for import.meta
declare global {
  interface ImportMeta {
    env: Record<string, string>;
  }
}

// Keep only one NodeRealTransactionLog interface and export it
export interface NodeRealTransactionLog {
  timestamp?: number;
  action: string;
  status: 'pending' | 'success' | 'error' | 'warning';
  error?: string;
  params?: any;
  hash?: string;
  message?: string;
  data?: any;
}

// Add this utility function for BigInt-safe JSON stringify
function safeJsonStringify(obj: any, indent?: number): string {
  return JSON.stringify(obj, (key, value) => {
    // Convert BigInt to string representation
    if (typeof value === 'bigint') {
      return value.toString() + 'n'; // Add 'n' to identify it as BigInt
    }
    return value;
  }, indent);
}

/**
 * Add a new transaction log entry
 */
export function updateNodeRealTransactionLog(log: NodeRealTransactionLog): void {
  // Add timestamp if not provided
  if (!log.timestamp) {
    log.timestamp = Date.now();
  }
  
  // Add to the log array
  transactionLogs.push(log);
  
  // Also log to console for immediate visibility but handle BigInt serialization
  try {
    console.log(`NodeReal TX Log [${log.status}]: ${log.action}`, log);
  } catch (error) {
    // This is likely a BigInt serialization error
    if (error instanceof Error && error.message.includes('BigInt')) {
      console.log(`NodeReal TX Log [${log.status}]: ${log.action} - (BigInt values present, see safe log below)`);
      console.log('Safe serialized log:', safeJsonStringify(log, 2));
    } else {
      // Re-throw unexpected errors
      throw error;
    }
  }
  
  // Keep only the last 20 logs
  if (transactionLogs.length > 20) {
    transactionLogs = transactionLogs.slice(-20);
  }
}

/**
 * Get all transaction logs (for debugging purposes)
 */
export function getNodeRealTransactionLogs(): NodeRealTransactionLog[] {
  return [...transactionLogs];
}

/**
 * Gets the connected wallet address or throws if none is connected
 */
async function getConnectedWalletAddress(): Promise<string> {
  if (!window.ethereum) {
    throw new Error('No Ethereum provider found');
  }
  
  try {
    const accounts = await window.ethereum.request({ method: 'eth_accounts' });
    if (accounts && accounts.length > 0) {
      return accounts[0];
    }
    throw new Error('No connected wallet found');
  } catch (error) {
    console.error('Error getting connected wallet:', error);
    throw new Error('Failed to get connected wallet address');
  }
}

// Add this helper function to convert decimal chainId to hex format
function toHexChainId(chainId: number): string {
  return '0x' + chainId.toString(16);
}

/**
 * Send transaction using NodeReal bundler (forwards to hybrid implementation)
 * @deprecated Use sendHybridTransaction directly for clarity
 */
export async function sendNodeRealTransaction(
  to: string,
  data: string,
  value?: string,
  smartWalletClient?: any
): Promise<{ hash: string }> {
  logNodeRealDebug('sendNodeRealTransaction: Forwarding to sendHybridTransaction');
  return sendHybridTransaction(to, data, value || '0', smartWalletClient);
}

/**
 * Get the appropriate EntryPoint address based on the version needed
 * Pimlico on BSC is using v0.6, while our app is configured for v0.7
 */
function getEntryPointForVersion(version: 'v0.6' | 'v0.7', chainId?: number): string {
  // ERC-4337 EntryPoint addresses
  // v0.6 is used by Pimlico
  // v0.7 is used by latest Account Abstraction implementations
  const entryPoints = {
    'v0.6': '******************************************', // EntryPoint v0.6
    'v0.7': '******************************************'  // EntryPoint v0.7
  };
  
  return entryPoints[version];
}

/**
 * Directly estimates gas for a user operation using Pimlico API
 * This bypasses the normal routing mechanism to ensure we always use Pimlico
 * for gas estimation, since NodeReal doesn't support this method
 */
export async function estimateUserOperationGas(
  userOp: any,
  entryPoint: string,
  chainId: number = 56
): Promise<any> {
  // Always use Pimlico for gas estimation
  const pimlicoUrl = getPimlicoApiUrl(chainId);
  
  logNodeRealDebug('Estimating gas via Pimlico', { url: pimlicoUrl });
  
  try {
    // Format the user operation properly
    const formattedUserOp = { ...userOp };
    
    // Ensure we have required fields with defaults
    if (!formattedUserOp.callGasLimit) formattedUserOp.callGasLimit = '0x0';
    if (!formattedUserOp.verificationGasLimit) formattedUserOp.verificationGasLimit = '0x0';
    if (!formattedUserOp.preVerificationGas) formattedUserOp.preVerificationGas = '0x0';
    
    // Create a properly formatted JSON-RPC request
    const requestBody = {
      jsonrpc: '2.0',
      id: Date.now(),
      method: 'eth_estimateUserOperationGas',
      params: [formattedUserOp, entryPoint]
    };
    
    // Make the API call
    const response = await fetch(pimlicoUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody)
    });
    
    const result = await response.json();
    
    if (result.error) {
      logNodeRealDebug('Gas estimation error', { error: result.error });
      throw new Error(`Gas estimation failed: ${JSON.stringify(result.error)}`);
    }
    
    return result.result;
  } catch (error) {
    logNodeRealDebug('Gas estimation failed', { error });
    
    // Return default gas values on error
    return {
      callGasLimit: '0x' + (500000).toString(16),
      verificationGasLimit: '0x' + (500000).toString(16),
      preVerificationGas: '0x' + (500000).toString(16)
    };
  }
}

/**
 * Send a user operation through Pimlico's API
 * This is necessary because NodeReal's bundler doesn't support eth_sendUserOperation
 */
async function sendUserOperationPimlico(
  userOp: Record<string, any>,
  entryPoint: string,
  chainId: number = 56
): Promise<string> {
  try {
    logNodeRealDebug('Sending userOp through Pimlico API', {
      entryPoint,
      chainId
    });
    
    // Ensure we're using the v0.6 EntryPoint
    const v06EntryPoint = getEntryPointForVersion('v0.6', chainId);
    
    // Ensure userOp has all required fields for v0.6
    const cleanUserOp = {
      sender: userOp.sender,
      nonce: userOp.nonce,
      initCode: userOp.initCode || '0x',
      callData: userOp.callData,
      callGasLimit: userOp.callGasLimit,
      verificationGasLimit: userOp.verificationGasLimit,
      preVerificationGas: userOp.preVerificationGas,
      maxFeePerGas: userOp.maxFeePerGas || '0x' + (**********).toString(16), // 5 gwei default
      maxPriorityFeePerGas: userOp.maxPriorityFeePerGas || '0x' + (**********).toString(16), // 1.5 gwei default
      paymasterAndData: userOp.paymasterAndData || '0x',
      signature: userOp.signature || '0x'
    };
    
    // Remove any fields that don't belong in v0.6 format
    // These might be added by v0.7-compatible wallets
    delete (cleanUserOp as any).factory;
    delete (cleanUserOp as any).factoryData;
    
    // Log the cleaned user operation
    logNodeRealDebug('Cleaned userOp for Pimlico API', {
      sender: cleanUserOp.sender,
      nonce: cleanUserOp.nonce,
      entryPoint: v06EntryPoint,
      hasPaymaster: !!cleanUserOp.paymasterAndData && cleanUserOp.paymasterAndData !== '0x'
    });
    
    // Get the Pimlico RPC URL
    const pimlicoRpcUrl = getPimlicoRpcUrl(chainId);
    
    // Make the request to the Pimlico API
    const response = await fetch(pimlicoRpcUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'eth_sendUserOperation',
        params: [cleanUserOp, v06EntryPoint]
      })
    });
    
    const responseData = await response.json();
    
    // Check for errors in the response
    if (responseData.error) {
      logNodeRealDebug('Error sending userOp through Pimlico', responseData.error);
      throw new Error(`Pimlico sendUserOperation failed: ${
        typeof responseData.error === 'object' 
          ? (responseData.error.message || JSON.stringify(responseData.error))
          : responseData.error
      }`);
    }
    
    // Log successful submission
    logNodeRealDebug('UserOp sent successfully through Pimlico', responseData.result);
    
    // Return the userOpHash
    return responseData.result;
  } catch (error) {
    console.error('Error sending userOp through Pimlico:', error);
    logNodeRealDebug('Error sending userOp through Pimlico', {
      error: error instanceof Error ? error.message : String(error)
    });
    
    throw new Error(`Failed to send user operation: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Routes a specific ERC-4337 method to the appropriate provider
 * NodeReal doesn't support all ERC-4337 methods, so we need to route some to Pimlico
 */
async function routeErc4337Method(method: string, params: any[], chainId: number = 56): Promise<any> {
  // Get the appropriate URL based on method
  let url;
  if (method === 'eth_estimateUserOperationGas' || method === 'eth_sendUserOperation') {
    // Always use Pimlico for gas estimation and user operation submission
    url = getPimlicoApiUrl(chainId);
    logNodeRealDebug(`Routing ${method} to Pimlico`, { url });
  } else {
    // Use NodeReal for other methods
    url = getBundlerUrl(chainId);
    logNodeRealDebug(`Routing ${method} to NodeReal`, { url });
  }

  try {
    // Format the request properly
    const requestData = createPimlicoJsonRpcRequest(method, params);
    
    logNodeRealDebug(`Sending ${method} request`, { 
      url, 
      method, 
      params: params.length, 
      hasUserOp: method.includes('UserOperation'),
      hasEntryPoint: params.length > 1
    });

    // Make the request
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      throw new Error(`${method} failed with status: ${response.status}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(`${method} failed: ${JSON.stringify(data.error)}`);
    }

    return data.result;
  } catch (error) {
    logNodeRealDebug(`Error routing ${method}`, { error });
    throw error;
  }
}

// Override the window.smartWalletClient methods to use our routing function
export function patchSmartWalletClient(smartWalletClient: any, chainId: number = 56) {
  if (!smartWalletClient || typeof smartWalletClient !== 'object') return;

  // Save the original methods
  const originalSendUserOperation = smartWalletClient.sendUserOperation;
  const originalEstimateUserOperationGas = smartWalletClient.estimateUserOperationGas;

  // Override the methods
  smartWalletClient.sendUserOperation = async (...args: any[]) => {
    try {
      return await routeErc4337Method('eth_sendUserOperation', args, chainId);
    } catch (error) {
      if (originalSendUserOperation) {
        return originalSendUserOperation.apply(smartWalletClient, args);
      }
      throw error;
    }
  };

  smartWalletClient.estimateUserOperationGas = async (...args: any[]) => {
    try {
      return await routeErc4337Method('eth_estimateUserOperationGas', args, chainId);
    } catch (error) {
      if (originalEstimateUserOperationGas) {
        return originalEstimateUserOperationGas.apply(smartWalletClient, args);
      }
      throw error;
    }
  };

  return smartWalletClient;
}

/**
 * Gets the sender address from a wallet client
 * Handles different wallet client implementations
 */
function getSenderAddress(smartWalletClient: any): string {
  try {
    // Handle common wallet implementations
    
    // Check for smart wallet with account property
    if (smartWalletClient && smartWalletClient.account && smartWalletClient.account.address) {
      return smartWalletClient.account.address;
    }
    
    // Check for smart wallet with direct address property
    if (smartWalletClient && smartWalletClient.address) {
      return smartWalletClient.address;
    }
    
    // Check for a getSender method (common in ERC-4337 implementations)
    if (smartWalletClient && typeof smartWalletClient.getSender === 'function') {
      const sender = smartWalletClient.getSender();
      // If it returns a Promise, we can't use it in sync context
      if (typeof sender === 'string') {
        return sender;
      }
    }
    
    // Check for smart wallet client with inner client
    if (smartWalletClient && smartWalletClient.client) {
      // Try client.account.address
      if (smartWalletClient.client.account && smartWalletClient.client.account.address) {
        return smartWalletClient.client.account.address;
      }
      
      // Try client.address
      if (smartWalletClient.client.address) {
        return smartWalletClient.client.address;
      }
    }
    
    // Fallback - check for injected providers
    if (typeof window !== 'undefined' && window.ethereum) {
      if (window.ethereum.selectedAddress) {
        return window.ethereum.selectedAddress;
      }
    }
    
    throw new Error('Could not determine sender address from wallet client');
  } catch (error) {
    console.error('Error getting sender address:', error);
    throw new Error('Could not determine sender address from wallet client');
  }
}

/**
 * Handles a transaction without paymaster sponsorship
 * This is used as a fallback when Pimlico paymaster sponsorship is unavailable
 */
async function handleUnsponsored(
  to: string,
  data: string,
  value: string | bigint,
  smartWalletClient: any
): Promise<{ hash: string }> {
  logNodeRealDebug('Proceeding with unsponsored transaction (user pays gas)', { to, value });
  
  try {
    // If the wallet client has a sendUserOperation method, use it directly
    if (typeof smartWalletClient.sendUserOperation === 'function') {
      try {
        // For direct sendUserOperation calls, the format may vary by wallet implementation
        // Some wallets expect a UserOperation object, others expect an object with target/data/value
        const result = await smartWalletClient.sendUserOperation({
          target: to,
          data,
          value: value.toString(),
        });
        
        return { hash: result.hash || result };
      } catch (error) {
        logNodeRealDebug('Error using wallet sendUserOperation, will try alternative approach', { error });
        // Continue to alternative approach
      }
    }
    
    // Fall back to manual UserOperation creation and submission
    const sender = getSenderAddress(smartWalletClient);
    if (!sender) {
      throw new Error('No sender address found in wallet client');
    }
    
    // Get chainId - try multiple methods
    const chainId = getChainId(smartWalletClient);
    
    // Get the EntryPoint address for the chain
    const entryPoint = getEntryPointForVersion('v0.6', chainId);
    
    // Get proper fees for the chain
    const { maxFeePerGas, maxPriorityFeePerGas } = await getGasFees(chainId);
    
    // Get account nonce - this returns a string
    const nonceString = await getAccountNonce(sender, entryPoint, chainId);
    
    // Prepare a properly formatted UserOperation
    const userOp = {
      sender,
      nonce: nonceString,
      initCode: '0x',
      callData: encodeExecute(to, value, data),
      callGasLimit: '0x' + (500000).toString(16),
      verificationGasLimit: '0x' + (500000).toString(16),
      preVerificationGas: '0x' + (500000).toString(16),
      maxFeePerGas: '0x' + maxFeePerGas.toString(16),
      maxPriorityFeePerGas: '0x' + maxPriorityFeePerGas.toString(16),
      paymasterAndData: '0x',
      signature: '0xfffffffffffffffffffffffffffffff0000000000000000000000000000000007aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa1c'
    };
    
    // Send the UserOperation via eth_sendUserOperation
    const opHash = await routeErc4337Method('eth_sendUserOperation', [userOp, entryPoint], chainId);
    
    if (!opHash) {
      throw new Error('Failed to get operation hash from bundler');
    }
    
    logNodeRealDebug('Successfully sent unsponsored transaction', { 
      opHash, 
      userOp: {
        sender: userOp.sender,
        nonce: userOp.nonce,
        target: to,
        value: value.toString()
      }
    });
    
    // Just return the opHash - the actual tx hash will be available only after mining
    return { hash: opHash };
  } catch (error) {
    throw new Error(`Failed to send unsponsored transaction: ${error}`);
  }
}

/**
 * Formats error messages in a user-friendly way
 */
function getFormattedErrorMessage(error: any): string {
  const errorStr = String(error?.message || error);
  
  // Format error message for common issues
  if (errorStr.includes('Paymaster issues') || errorStr.includes('may not be sponsored') || 
      errorStr.includes('Transaction not eligible for sponsorship')) {
    return 'Account Abstraction error: Paymaster issues. Transaction may not be sponsored. You might need to pay gas fees for this transaction.';
  } 
  
  if (errorStr.includes('insufficient funds') || errorStr.includes('insufficient balance')) {
    return 'Insufficient funds to pay for gas. Please add funds to your wallet or try a smaller transaction.';
  }
  
  if (errorStr.includes('user rejected') || errorStr.includes('user denied')) {
    return 'Transaction was rejected by the user.';
  }
  
  if (errorStr.includes('nonce') && (errorStr.includes('too low') || errorStr.includes('invalid'))) {
    return 'Transaction error: Invalid account nonce. This usually happens when you have a pending transaction. Please wait for it to complete or try again later.';
  }
  
  if (errorStr.includes('gas') && (errorStr.includes('limit') || errorStr.includes('price'))) {
    return 'Transaction error: Gas estimation failed. The transaction might be too complex or the network might be congested.';
  }
  
  // Default error message
  return `Transaction failed: ${errorStr}`;
}

/**
 * Hybrid implementation that uses NodeReal's bundler with Pimlico's paymaster
 * This solves the compatibility issue where NodeReal doesn't support all ERC-4337 methods
 * but we still want to use sponsored transactions through Pimlico
 * 
 * IMPORTANT: This function supports multiple wallet client implementations with different
 * property structures:
 * 1. Some clients have client.account.address 
 * 2. Some have client.address directly
 * 3. Some have client.getAddress() method
 * 4. Some have address at the root level
 * 
 * The implementation handles these different structures robustly to prevent
 * "cannot read properties of undefined" errors.
 * 
 * If paymaster sponsorship is not available, the transaction will proceed
 * as an unsponsored transaction where the user pays gas.
 */
export async function sendHybridTransaction(
  to: string,
  data: string,
  value: string | bigint = '0',
  smartWalletClient: any
): Promise<{ hash: string }> {
  logNodeRealDebug('sendHybridTransaction start', { to, dataLength: data.length, value });
  
  try {
    // Get chain ID
    const chainId = getChainId(smartWalletClient);
    logNodeRealDebug(`Using chainId for transaction: ${chainId} (0x${chainId.toString(16)}) for ${getNetworkName(chainId)}`);
    
    // Get sender address from wallet client
    const sender = getSenderAddress(smartWalletClient);
    if (!sender) {
      throw new Error('Could not determine sender address from wallet client');
    }
    logNodeRealDebug('Using sender address', { sender });
    
    // Try to use the wallet's encodeCallData method if available
    let callData = data;
    if (typeof smartWalletClient.encodeCallData === 'function') {
      try {
        callData = await smartWalletClient.encodeCallData(to, data, value);
        logNodeRealDebug('Successfully encoded callData using wallet method', { callDataLength: callData.length });
      } catch (encodeError) {
        logNodeRealDebug('No encodeCallData method found, using raw calldata', { error: encodeError });
        callData = encodeExecute(to, value.toString(), data);
      }
    } else {
      logNodeRealDebug('No encodeCallData method found, using raw calldata');
      callData = encodeExecute(to, value.toString(), data);
    }
    
    // Get the Pimlico API URL
    const pimlicoUrl = getPimlicoApiUrl(chainId);
    logNodeRealDebug('Using Pimlico API URL:', { url: pimlicoUrl });
    
    // First step: Get the EntryPoint contract address
    const entryPoint = getEntryPointForVersion('v0.6', chainId);
    
    // Get the account nonce from EntryPoint (will try NodeReal first then Pimlico)
    const accountNonce = await getAccountNonce(sender, entryPoint, chainId);
    
    // Try to get paymaster data from Pimlico
    let paymasterAndData;
    try {
      paymasterAndData = await callPimlicoPaymasterApi(
        pimlicoUrl,
        sender,
        to,
        callData.length,
        value.toString(),
        chainId
      );
      
      logNodeRealDebug('Successfully got paymaster data', {
        paymasterAndDataLength: paymasterAndData.length
      });
    } catch (paymasterError) {
      logNodeRealDebug('Error getting paymaster data, proceeding with unsponsored transaction', {
        error: paymasterError
      });
      
      // If we can't get a paymaster, proceed with unsponsored transaction
      return handleUnsponsored(to, data, value, smartWalletClient);
    }
    
    // Ensure we have gas parameters
    const { maxFeePerGas, maxPriorityFeePerGas } = await getGasFees(chainId);
    
    // Try to estimate gas using our dedicated function
    let gasEstimate;
    try {
      // Create UserOperation for gas estimation
      const userOpForEstimation = {
        sender,
        nonce: accountNonce,
        initCode: '0x',
        callData,
        callGasLimit: '0x0',
        verificationGasLimit: '0x0',
        preVerificationGas: '0x0',
        maxFeePerGas: '0x' + maxFeePerGas.toString(16),
        maxPriorityFeePerGas: '0x' + maxPriorityFeePerGas.toString(16),
        paymasterAndData,
        signature: '0xfffffffffffffffffffffffffffffff0000000000000000000000000000000007aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa1c'
      };
      
      // Use our dedicated function to ensure Pimlico is used for estimation
      gasEstimate = await estimateUserOperationGas(
        userOpForEstimation,
        entryPoint,
        chainId
      );
      
      logNodeRealDebug('Gas estimation successful', { gasEstimate });
    } catch (estimateError) {
      logNodeRealDebug('Gas estimation failed, using defaults', { error: estimateError });
      // Use defaults if estimation fails
      gasEstimate = {
        callGasLimit: '0x' + (500000).toString(16),
        verificationGasLimit: '0x' + (500000).toString(16), 
        preVerificationGas: '0x' + (500000).toString(16)
      };
    }
    
    // Create the final UserOperation
    const userOp = {
      sender,
      nonce: accountNonce,
      initCode: '0x',
      callData,
      callGasLimit: gasEstimate.callGasLimit || '0x' + (500000).toString(16),
      verificationGasLimit: gasEstimate.verificationGasLimit || '0x' + (500000).toString(16),
      preVerificationGas: gasEstimate.preVerificationGas || '0x' + (500000).toString(16),
      maxFeePerGas: '0x' + maxFeePerGas.toString(16),
      maxPriorityFeePerGas: '0x' + maxPriorityFeePerGas.toString(16),
      paymasterAndData,
      signature: '0xfffffffffffffffffffffffffffffff0000000000000000000000000000000007aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa1c'
    };
    
    // Send the UserOperation via eth_sendUserOperation to Pimlico
    try {
      const opHash = await routeErc4337Method('eth_sendUserOperation', [userOp, entryPoint], chainId);
      
      if (!opHash) {
        throw new Error('Failed to get operation hash from bundler');
      }
      
      logNodeRealDebug('Transaction sent successfully', { opHash });
      
      // Return the operation hash - this isn't the transaction hash yet,
      // but the frontend can track it using eth_getUserOperationReceipt
      return { hash: opHash };
    } catch (sendError) {
      logNodeRealDebug('Error sending user operation, falling back to unsponsored', { error: sendError });
      
      // If sending with paymaster fails, try unsponsored
      return handleUnsponsored(to, data, value, smartWalletClient);
    }
  } catch (error) {
    // Handle any other errors
    logNodeRealDebug('Error in sendHybridTransaction', { error });
    throw error;
  }
}

/**
 * Enhanced transaction handling for ERC-4337 with support for different bundlers/paymasters
 */
export async function sendSwapTransaction(params: SendSwapParams): Promise<any> {
  try {
    updateNodeRealTransactionLog({
      action: 'swap',
      status: 'pending',
      params: {
        to: params.swapData.to,
        dataLength: params.swapData.data.length,
        value: params.swapData.value || '0',
        platformFee: safeJsonStringify(params.swapData.platformFee),
        useSmartWallet: params.useSmartWallet,
        hasSmartWalletClient: !!params.smartWalletClient
      }
    });
    
    logNodeRealDebug('Sending swap transaction with params:', {
      to: params.swapData.to,
      dataLength: params.swapData.data.length,
      value: params.swapData.value || '0',
      platformFee: safeJsonStringify(params.swapData.platformFee),
      useSmartWallet: params.useSmartWallet,
      hasSmartWalletClient: !!params.smartWalletClient
    });

    if (params.useSmartWallet && params.smartWalletClient) {
      logNodeRealDebug('Using hybrid approach (NodeReal bundler + Pimlico paymaster)');
      
      // Patch the smartWalletClient to ensure all ERC-4337 methods are properly routed
      const patchedClient = patchSmartWalletClient(params.smartWalletClient);
      
      const result = await sendHybridTransaction(
        params.swapData.to,
        params.swapData.data,
        params.swapData.value || '0',
        patchedClient
      );
      
      updateNodeRealTransactionLog({
        action: 'swap',
        status: 'success',
        hash: result.hash,
        params: {
          to: params.swapData.to,
          dataLength: params.swapData.data.length,
          value: params.swapData.value || '0'
        }
      });
      
      return result;
    } else if (params.sendTransactionFn) {
      // Use custom transaction function if provided
      const tx = await params.sendTransactionFn({
        to: params.swapData.to,
        data: params.swapData.data,
        value: params.swapData.value || '0'
      });
      
      // Handle both formats (hash string or {hash} object)
      const txHash = typeof tx === 'string' ? tx : tx.hash;
      
      // Update transaction log
      updateNodeRealTransactionLog({
        action: 'swap',
        status: 'success',
        hash: txHash,
        params: {
          to: params.swapData.to,
          dataLength: params.swapData.data.length,
          value: params.swapData.value || '0'
        }
      });
      
      return { hash: txHash };
    } else {
      // No appropriate transaction method available
      throw new Error('No transaction method available. Ensure wallet is connected.');
    }
  } catch (error: any) {
    // Enhance error messages for specific ERC-4337 errors
    let userFriendlyMessage = getFormattedErrorMessage(error);
    
    // Special handling for method not found errors
    if (error.message?.includes('eth_estimateUserOperationGas') || 
        error.message?.includes('method does not exist')) {
      userFriendlyMessage = 'Smart wallet transaction failed: Your wallet is using an incompatible bundler. Please try again or use a different wallet.';
      
      // Log detailed error for debugging
      logNodeRealDebug('ERC-4337 method routing error:', { 
        error: error.message,
        stack: error.stack,
        originalError: error
      });
    }
    
    // Special handling for paymaster errors
    if (error.message?.includes('paymaster') || 
        error.message?.includes('sponsor')) {
      userFriendlyMessage = 'Transaction sponsorship unavailable: The gas fee cannot be covered by the application at this time. Please try again later.';
    }
    
    updateNodeRealTransactionLog({
      action: 'swap',
      status: 'error',
      error: userFriendlyMessage,
      params: {
        to: params.swapData.to,
        dataLength: params.swapData.data?.length,
        value: params.swapData.value || '0'
      }
    });
    
    logNodeRealDebug('Error in sendSwapTransaction:', error);
    throw new Error(`Transaction failed: ${userFriendlyMessage}`);
  }
}

/**
 * Format bundler/paymaster errors for better readability
 * Use this for error messages shown to the user
 */
export function formatBundlerError(error: any): string {
  // Default error message
  let formattedError = 'Transaction failed';
  
  try {
    if (error instanceof Error) {
      const errorMessage = (error.message || '').toLowerCase();
      
      // Check for common ERC-4337 error codes
      if (errorMessage.includes('aa25') || errorMessage.includes('invalid account nonce')) {
        return 'Account Abstraction error: Invalid nonce. Please wait a moment and try again.';
      }
      
      if (errorMessage.includes('aa10') || errorMessage.includes('sender already constructed')) {
        return 'Account Abstraction error: This smart wallet is already deployed.';
      }
      
      if (errorMessage.includes('aa20') || errorMessage.includes('account not deployed')) {
        return 'Account Abstraction error: This smart wallet needs to be deployed first.';
      }
      
      if (errorMessage.includes('aa21') || errorMessage.includes('didn\'t pay prefund')) {
        return 'Account Abstraction error: Not enough balance for gas.';
      }
      
      if (errorMessage.includes('aa22') || errorMessage.includes('expired')) {
        return 'Account Abstraction error: Transaction expired. Please try again.';
      }
      
      if (errorMessage.includes('aa23') || errorMessage.includes('reverted')) {
        return 'Account Abstraction error: Transaction simulation failed. Please check your transaction parameters.';
      }
      
      if (errorMessage.includes('aa24') || errorMessage.includes('gas validation failed')) {
        return 'Account Abstraction error: Gas validation failed. Please try again.';
      }
      
      if (errorMessage.includes('aa30') || errorMessage.includes('paymaster')) {
        return 'Account Abstraction error: Paymaster issues. Transaction may not be sponsored.';
      }
      
      if (errorMessage.includes('aa31') || errorMessage.includes('paymaster deposit')) {
        return 'Account Abstraction error: Paymaster has insufficient deposit.';
      }
      
      if (errorMessage.includes('aa32') || errorMessage.includes('paymaster expired')) {
        return 'Account Abstraction error: Paymaster data expired.';
      }
      
      if (errorMessage.includes('aa33') || errorMessage.includes('paymaster validation')) {
        return 'Account Abstraction error: Paymaster validation failed.';
      }
      
      if (errorMessage.includes('insufficient funds') || errorMessage.includes('low balance')) {
        return 'Insufficient funds for transaction. Please ensure you have enough funds for gas.';
      }
      
      if (errorMessage.includes('gas') && errorMessage.includes('exceeded')) {
        return 'Gas limit exceeded. Please try a simpler transaction or increase gas limit.';
      }
      
      if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
        return 'Transaction timed out. Network might be congested, please try again later.';
      }
      
      if (errorMessage.includes('rejected') || errorMessage.includes('denied')) {
        return 'Transaction rejected. Please check your wallet and try again.';
      }
      
      if (errorMessage.includes('execution reverted')) {
        return 'Transaction execution reverted. Your transaction was not processed.';
      }
      
      if (errorMessage.includes('already known')) {
        return 'Transaction already submitted. Please check your wallet for pending transactions.';
      }
      
      // If we have a detailed error but none of the above patterns match
      if (errorMessage.length > 10) {
        // Clean up the error message for better readability
        let cleanError = errorMessage
          .replace(/error:/gi, '')
          .replace(/transaction failed:/gi, '')
          .replace(/userOperation reverted during simulation/gi, 'Transaction simulation failed')
          .replace(/with reason:/gi, ':');
          
        formattedError = `Transaction failed: ${cleanError.trim()}`;
      }
    } else if (typeof error === 'string') {
      formattedError = `Transaction failed: ${error}`;
    } else if (error && typeof error === 'object') {
      // Try to extract error message from object
      const objError = error.message || error.error || error.reason || JSON.stringify(error);
      formattedError = `Transaction failed: ${objError}`;
    }
  } catch (e) {
    // If error formatting itself fails, use a generic message
    console.error('Error while formatting bundler error:', e);
  }
  
  return formattedError;
}

/**
 * Get the Pimlico API key from various sources
 */
export function getBundlerApiKey(provider: 'nodereal' | 'pimlico'): string | null {
  if (provider === 'nodereal') {
    return import.meta.env.VITE_NODEREAL_API_KEY || null;
  } else if (provider === 'pimlico') {
    // Try to get from environment variables first
    const envApiKey = import.meta.env.VITE_PIMLICO_API_KEY;
    if (envApiKey) {
      return envApiKey;
    }
    
    // Try to get from window.env (might be set at runtime)
    if (window.env && window.env.PIMLICO_API_KEY) {
      return window.env.PIMLICO_API_KEY;
    }
    
    // Try to get from global PIMLICO_API_KEY
    if (window.PIMLICO_API_KEY) {
      return window.PIMLICO_API_KEY;
    }
  }
  
  return null;
}

/**
 * Get the bundler URL for a specific chain
 */
export function getBundlerUrl(chainId: number): string {
  // BSC mainnet
  if (chainId === 56) {
    const apiKey = getBundlerApiKey('nodereal');
    return `https://bsc-mainnet.nodereal.io/v1/${apiKey}`;
  }
  
  // Fallback
  return '';
}

/**
 * Get the paymaster URL for a specific chain
 */
export function getPaymasterUrl(chainId: number): string {
  // BSC mainnet
  if (chainId === 56) {
    const apiKey = getBundlerApiKey('pimlico');
    return `https://api.pimlico.io/v2/${chainId}/rpc?apikey=${apiKey}`;
  }
  
  // Fallback
  return '';
}

/**
 * Get the estimation bundler URL for ERC-4337 operations not supported by NodeReal
 */
export function getEstimationBundlerUrl(chainId: number): string {
  // Always use Pimlico for gas estimation
  const pimlicoApiKey = import.meta.env.VITE_PIMLICO_API_KEY || '';
  if (chainId === 56) {
    return `https://api.pimlico.io/v2/${chainId}/rpc?apikey=${pimlicoApiKey}`;
  }
  
  // Fallback to the standard URL
  return getBundlerUrl(chainId);
}

/**
 * Get method routing configuration for a specific chain
 * This helps route specific ERC-4337 methods to the right provider
 */
export function getMethodRouting(chainId: number): Record<string, string> {
  // BSC mainnet
  if (chainId === 56) {
    return {
      // Route these to Pimlico instead of NodeReal
      eth_estimateUserOperationGas: 'estimationBundler',
      eth_supportedEntryPoints: 'estimationBundler',
      eth_chainId: 'estimationBundler'
    };
  }
  
  // Fallback - empty routing
  return {};
}

/**
 * Get the chain ID for a transaction, defaulting to BSC (56) if not specified
 */
async function getChainIdForTransaction(smartWalletClient: any): Promise<number> {
  try {
    // First try to get the chain ID from the client
    if (smartWalletClient?.chain?.id) {
      return smartWalletClient.chain.id;
    }
    
    // If that fails, try to get it from the account
    if (smartWalletClient?.account?.client?.chain?.id) {
      return smartWalletClient.account.client.chain.id;
    }
    
    // Default to BSC mainnet (56)
    return 56;
  } catch (error) {
    logNodeRealDebug('Error getting chain ID, defaulting to BSC (56)', {
      error: error instanceof Error ? error.message : String(error)
    });
    return 56;
  }
}

/**
 * Get the network name for a chain ID
 */
function getNetworkName(chainId: number): string {
  const networks: Record<string, string> = {
    '1': 'Ethereum Mainnet',
    '56': 'BSC Mainnet',
    '97': 'BSC Testnet',
    '137': 'Polygon Mainnet',
    '43114': 'Avalanche C-Chain',
    '42161': 'Arbitrum One'
  };
  
  return networks[chainId.toString()] || `Chain ${chainId}`;
}

/**
 * Get the Pimlico API URL for a specific chain
 */
function getPimlicoApiUrl(chainId: number = 56): string {
  // For BSC mainnet (56), always use the Pimlico endpoint
  const pimlicoApiKey = getBundlerApiKey('pimlico') || '';
  
  // Always include debug logs for important operations
  logNodeRealDebug('Using Pimlico API URL for chain', { 
    chainId,
    apiKey: pimlicoApiKey ? 'Present' : 'Missing' 
  });
  
  // BSC Mainnet
  if (chainId === 56) {
    return `https://api.pimlico.io/v2/56/rpc?apikey=${pimlicoApiKey}`;
  }
  
  // BSC Testnet
  if (chainId === 97) {
    return `https://api.pimlico.io/v2/97/rpc?apikey=${pimlicoApiKey}`;
  }
  
  // Default to BSC mainnet
  return `https://api.pimlico.io/v2/56/rpc?apikey=${pimlicoApiKey}`;
}

/**
 * PaymasterResponse type definition
 */
interface PaymasterResponse {
  type: 'v0.6' | 'v0.7';
  paymasterData: string;
  paymasterAndData?: string;
  nonce?: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
}

/**
 * Get paymaster data from Pimlico
 */
async function getPimlicoPaymasterData(params: {
  url: string;
  sender: string;
  target: string;
  callDataLength: number;
  value: string;
  chain: number;
}): Promise<PaymasterResponse> {
  try {
    // Implement paymaster client logic here
    // This is where you'd call the Pimlico API to get paymaster data
    
    // For now, we'll mock it with the existing function
    const paymasterData = await callPimlicoPaymasterApi(params.url, params.sender, params.target, params.callDataLength, params.value, params.chain);
    
    // Parse the response and return in the expected format
    if (paymasterData.includes('0x777777777777')) {
      // This is a v0.7 paymasterAndData
      return {
        type: 'v0.7',
        paymasterData: paymasterData,
        paymasterAndData: paymasterData
      };
    } else {
      // This is a v0.6 paymasterData
      return {
        type: 'v0.6',
        paymasterData: paymasterData,
        paymasterAndData: paymasterData
      };
    }
  } catch (error) {
    logNodeRealDebug('Error getting paymaster data from Pimlico', { error: String(error) });
    throw error;
  }
}

/**
 * Helper function to create properly formatted JSON-RPC requests for Pimlico API
 * This ensures consistent format with proper typing for all API calls
 */
function createPimlicoJsonRpcRequest(method: string, params: any): any {
  // Make sure params is an array
  let formattedParams;
  
  if (Array.isArray(params)) {
    formattedParams = params;
  } else if (method === 'pm_sponsorUserOperation' || method === 'pm_getPaymasterAndData') {
    // Format for paymaster API calls
    const {
      sender,
      target,
      callDataLength,
      value,
      entryPoint,
      chainId,
      nonce
    } = params;
    
    // Create a minimal user operation object
    const userOp = {
      sender,
      nonce,
      initCode: '0x',
      callData: `0x${'0'.repeat(callDataLength * 2)}`, // Simplified callData
      callGasLimit: '0x' + (300000).toString(16),
      verificationGasLimit: '0x' + (1500000).toString(16),
      preVerificationGas: '0x' + (1500000).toString(16),
      maxFeePerGas: '0x' + (150000000).toString(16), // 1.5 Gwei
      maxPriorityFeePerGas: '0x' + (150000000).toString(16), // 1.5 Gwei
      paymasterAndData: '0x',
      signature: '0xfffffffffffffffffffffffffffffff0000000000000000000000000000000007aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa1c'
    };
    
    // Format the parameters as an array with EntryPoint address
    formattedParams = [userOp, entryPoint];
  } else {
    // For other methods, convert object to array
    formattedParams = [params];
  }
  
  // For eth_estimateUserOperationGas and eth_sendUserOperation, ensure we have
  // proper EntryPoint address as second parameter if not already present
  if ((method === 'eth_estimateUserOperationGas' || method === 'eth_sendUserOperation') && 
      formattedParams.length === 1) {
    // Add default EntryPoint address for BSC
    const entryPointAddress = '******************************************';
    formattedParams.push(entryPointAddress);
  }
  
  // Ensure proper userOp format for eth_estimateUserOperationGas
  if (method === 'eth_estimateUserOperationGas' && formattedParams.length > 0) {
    // Check if the first param has target, data, value format instead of proper userOp format
    const firstParam = formattedParams[0];
    if (firstParam && typeof firstParam === 'object' && 
        (firstParam.target || firstParam.to) && !firstParam.callData) {
      // Convert to proper format
      const target = firstParam.target || firstParam.to;
      const data = firstParam.data;
      const value = firstParam.value || '0x0';
      
      // Create properly formatted userOp
      formattedParams[0] = {
        sender: firstParam.sender || '',
        nonce: firstParam.nonce || '0x0',
        initCode: firstParam.initCode || '0x',
        callData: data,
        callGasLimit: firstParam.callGasLimit || '0x0',
        verificationGasLimit: firstParam.verificationGasLimit || '0x0',
        preVerificationGas: firstParam.preVerificationGas || '0x0',
        maxFeePerGas: firstParam.maxFeePerGas || '0x0',
        maxPriorityFeePerGas: firstParam.maxPriorityFeePerGas || '0x0',
        paymasterAndData: firstParam.paymasterAndData || '0x',
        signature: firstParam.signature || '0xfffffffffffffffffffffffffffffff0000000000000000000000000000000007aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa1c'
      };
    }
  }
  
  // Ensure we have valid JSON-RPC format
  return {
    jsonrpc: '2.0',
    id: Date.now(), // Use numeric ID
    method,
    params: formattedParams
  };
}

/**
 * Internal function to call Pimlico paymaster API
 */
async function callPimlicoPaymasterApi(
  url: string,
  sender: string, 
  target: string, 
  callDataLength: number, 
  value: string, 
  chainId: number
): Promise<string> {
  const maxAttempts = 3;
  let attempt = 0;
  let lastError: Error | null = null;
  let backoffDelay = 1000;
  
  // Get the EntryPoint address for v0.6 format (used by Pimlico)
  const entryPoint = getEntryPointForVersion('v0.6', chainId);
  
  // List of supported methods to try in order
  const supportedMethods = [
    'pm_sponsorUserOperation',
    'pm_getPaymasterAndData'
  ];
  
  while (attempt < maxAttempts) {
    attempt++;
    try {
      // Get the account nonce
      let nonce = await getAccountNonce(sender, entryPoint, chainId);
      
      // For each supported method, try calling it
      for (const method of supportedMethods) {
        try {
          logNodeRealDebug(`Trying Pimlico method: ${method}`, { attempt });
          
          // Create a properly formatted request
          const requestData = createPimlicoJsonRpcRequest(method, {
            sender,
            target,
            callDataLength,
            value,
            entryPoint,
            chainId: toHexChainId(chainId),
            nonce
          });
          
          // Make the API call
          const response = await fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestData)
          });
          
          // Check for HTTP errors
          if (!response.ok) {
            throw new Error(`Pimlico API returned ${response.status}: ${response.statusText}`);
          }
          
          // Parse the response
          const result = await response.json();
          
          // Check for API errors
          if (result.error) {
            throw new Error(`Pimlico API error: ${JSON.stringify(result.error)}`);
          }
          
          // Extract the paymaster data
          const paymasterData = result.result;
          if (!paymasterData || typeof paymasterData !== 'string' || !paymasterData.startsWith('0x')) {
            throw new Error(`Invalid paymaster data: ${paymasterData}`);
          }
          
          return paymasterData;
        } catch (methodError) {
          logNodeRealDebug(`Method ${method} failed, trying next if available`, { error: methodError });
          // Continue to the next method
          continue;
        }
      }
      
      // If we get here, all methods failed
      throw new Error('All paymaster methods failed');
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      logNodeRealDebug('Error calling Pimlico paymaster API', { error: lastError.message, attempt });
      
      // If we still have attempts left, wait with exponential backoff
      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, backoffDelay));
        backoffDelay *= 2; // Exponential backoff
      }
    }
  }
  
  // If all attempts failed, throw the last error
  if (lastError) {
    throw lastError;
  }
  
  // This should never happen due to the loop and error handling above
  throw new Error('Failed to get paymaster data after multiple attempts');
}

// Add a direct ethers nonce getter as fallback
async function getEthersNonce(address: string): Promise<string> {
  try {
    // Use ethers to get the nonce directly from chain
    const bscRpcUrl = 'https://bsc-dataseed.binance.org/';
    const provider = new ethers.providers.JsonRpcProvider(bscRpcUrl);
    
    // Get transaction count (nonce)
    const count = await provider.getTransactionCount(address);
    return `0x${count.toString(16)}`;
  } catch (error) {
    console.error('Error getting nonce via ethers:', error);
    throw new Error(`Failed to get nonce via ethers: ${error}`);
  }
}

/**
 * Helper function to log NodeReal-related information
 */
export function logNodeRealDebug(message: string, data?: any) {
  try {
    // Format timestamp
    const timestamp = new Date().toISOString();
    
    // Format data object for cleaner logging
    let dataString = '';
    
    if (data) {
      // Handle error objects specifically
      if (data.error !== undefined) {
        const errorData = typeof data.error === 'string' 
          ? data.error
          : data.error instanceof Error 
            ? data.error.message 
            : String(data.error);
        
        dataString = JSON.stringify({ ...data, error: errorData }, null, 2);
      } else {
        dataString = JSON.stringify(data, null, 2);
      }
    }
    
    // Log to console with consistent format
    console.log(`[NodeReal ${timestamp}] ${message}${dataString ? '\n' + dataString : ''}`);
    
    // Store in transaction logs for debugging
    nodeRealTransactionLog.push({
      timestamp: Date.now(),
      action: 'debug',
      status: 'pending',
      message,
      data
    });
  } catch (err) {
    // Fallback in case of logging error
    console.error('Error in logNodeRealDebug:', err);
  }
}

/**
 * Interface for parameters used in sendSwapTransaction
 */
export interface SendSwapParams {
  swapData: {
    to: string;
    data: string;
    value?: string;
    platformFee?: {
      recipient: string;
      amount: string;
      token: string;
      percentage: number;
    } | null;
  };
  useSmartWallet?: boolean;
  smartWalletClient?: any;
  sendTransactionFn?: (tx: any) => Promise<string | { hash: string }>;
}

/**
 * Gets the nonce for a specific account from the EntryPoint
 */
async function getAccountNonce(
  sender: string,
  entryPoint: string,
  chainId: number = 56
): Promise<string> {
  try {
    logNodeRealDebug('Fetching account nonce from EntryPoint', { sender, entryPoint });
    
    // Create a properly formatted request
    const url = getBundlerUrl(chainId); // Try the bundler first
    
    try {
      // First try with NodeReal
      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: Date.now(),
          method: 'eth_call',
          params: [
            {
              to: entryPoint,
              data: `0x9e5d4c49000000000000000000000000${sender.slice(2).toLowerCase()}`
            },
            'latest'
          ]
        })
      });
      
      const data = await response.json();
      
      if (data.error || !data.result) {
        throw new Error('No result from NodeReal');
      }
      
      logNodeRealDebug('Successfully fetched account nonce', { sender, nonce: data.result });
      return data.result;
    } catch (nodeRealError) {
      // If NodeReal fails, try Pimlico
      logNodeRealDebug('Error with NodeReal, trying Pimlico for nonce', { error: nodeRealError });
      
      const pimlicoUrl = getPimlicoApiUrl(chainId);
      
      const pimlicoResponse = await fetch(pimlicoUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: Date.now(),
          method: 'eth_call',
          params: [
            {
              to: entryPoint,
              data: `0x9e5d4c49000000000000000000000000${sender.slice(2).toLowerCase()}`
            },
            'latest'
          ]
        })
      });
      
      const pimlicoData = await pimlicoResponse.json();
      
      if (pimlicoData.error || !pimlicoData.result) {
        throw new Error('No result from Pimlico either');
      }
      
      logNodeRealDebug('Successfully fetched account nonce from Pimlico', { 
        sender, 
        nonce: pimlicoData.result 
      });
      return pimlicoData.result;
    }
  } catch (error) {
    logNodeRealDebug('Error fetching nonce, using default', { error });
    return '0x00'; // Default to 0 if we can't get the nonce
  }
}

/**
 * Encodes a transaction into an execute function call for smart contract wallets
 * This encodes: execute(address to, uint256 value, bytes data)
 */
function encodeExecute(to: string, value: string | bigint, data: string): string {
  try {
    // Function signature for execute(address,uint256,bytes)
    const funcSig = '0xb61d27f6';
    
    // Ensure to has 0x prefix and is lowercase
    const cleanedTo = to.toLowerCase();
    
    // Encode address parameter (remove 0x prefix, pad to 32 bytes/64 chars)
    const encodedAddress = cleanedTo.startsWith('0x') 
      ? cleanedTo.slice(2).padStart(64, '0') 
      : cleanedTo.padStart(64, '0');
    
    // Encode value parameter (remove 0x prefix if present, convert to hex, pad to 32 bytes/64 chars)
    let valueHex = '0';
    
    if (typeof value === 'bigint') {
      valueHex = value.toString(16);
    } else if (typeof value === 'string') {
      if (value.startsWith('0x')) {
        valueHex = value.slice(2);
      } else {
        // Try to parse as number and convert to hex
        try {
          const bigValue = BigInt(value);
          valueHex = bigValue.toString(16);
        } catch (e) {
          // If parsing fails, default to 0
          valueHex = '0';
        }
      }
    }
    
    const encodedValue = valueHex.padStart(64, '0');
    
    // Encode data
    const dataHex = data.startsWith('0x') ? data.slice(2) : data;
    
    // Calculate offset to bytes parameter (fixed at 96 bytes = 3 words)
    const dataOffset = '0000000000000000000000000000000000000000000000000000000000000060';
    
    // Calculate length of bytes as number of bytes
    const dataLength = Math.ceil(dataHex.length / 2);
    const encodedDataLength = dataLength.toString(16).padStart(64, '0');
    
    // Combine all parts
    const callData = `${funcSig}${encodedAddress}${encodedValue}${dataOffset}${encodedDataLength}${dataHex}`;
    
    // Ensure 0x prefix
    return callData.startsWith('0x') ? callData : '0x' + callData;
  } catch (error) {
    logNodeRealDebug('Error encoding execute call', { error, to, value, dataLength: data.length });
    
    // Fallback to simple data in case of error
    return data;
  }
}

function logNodeRealSuccess(action: string, params: any, result: any) {
  // Log to console
  logNodeRealDebug(`${action} completed successfully`, { params, result });

  // Add to transaction log
  nodeRealTransactionLog.push({
    timestamp: Date.now(),
    action,
    status: 'success',
    params,
    data: result // Use data instead of result to match interface
  });
}

/**
 * Gets the chain ID from a wallet client, trying multiple access methods
 */
function getChainId(walletClient: any): number {
  try {
    // Try multiple access patterns since client implementations vary
    if (walletClient.chain && walletClient.chain.id) {
      return walletClient.chain.id;
    }
    
    if (walletClient.chainId) {
      return typeof walletClient.chainId === 'function' 
        ? walletClient.chainId() 
        : walletClient.chainId;
    }
    
    if (walletClient.getChainId) {
      return walletClient.getChainId();
    }
    
    // Try the client property if it exists
    if (walletClient.client) {
      if (walletClient.client.chain && walletClient.client.chain.id) {
        return walletClient.client.chain.id;
      }
      
      if (walletClient.client.chainId) {
        return typeof walletClient.client.chainId === 'function' 
          ? walletClient.client.chainId() 
          : walletClient.client.chainId;
      }
    }
    
    // Default to BSC mainnet
    return 56;
  } catch (error) {
    logNodeRealDebug('Error getting chainId, falling back to BSC mainnet', { error });
    return 56; // Default to BSC mainnet
  }
}

/**
 * Gets gas fee estimates for the current network
 */
async function getGasFees(chainId: number): Promise<{ maxFeePerGas: number, maxPriorityFeePerGas: number }> {
  try {
    // For BSC, we use fixed gas prices
    if (chainId === 56) {
      return {
        maxFeePerGas: 150000000, // 0.15 gwei
        maxPriorityFeePerGas: 150000000 // 0.15 gwei
      };
    }
    
    // For other chains we could implement dynamic gas estimation
    // but for now we use a safe default
    return {
      maxFeePerGas: 150000000,
      maxPriorityFeePerGas: 150000000
    };
  } catch (error) {
    logNodeRealDebug('Error estimating gas fees, using defaults', { error });
    return {
      maxFeePerGas: 150000000,
      maxPriorityFeePerGas: 150000000
    };
  }
}

/**
 * Main entry point for sending hybrid transactions
 * Attempts to use Pimlico paymaster first, falls back to unsponsored if needed
 * This is what should be called by external code
 */
export async function sendErc4337Transaction(
  to: string,
  data: string,
  value: string | bigint = '0',
  options: any = {}
): Promise<{ hash: string }> {
  const { useSmartWallet = true, smartWalletClient = null, platformFee = null } = options;
  
  // Log the transaction attempt
  logNodeRealTransaction('pending', {
    action: 'swap',
    status: 'pending',
    params: {
      to,
      dataLength: data.length,
      value,
      platformFee,
      useSmartWallet,
      hasSmartWalletClient: !!smartWalletClient
    },
    timestamp: Date.now()
  });
  
  try {
    if (useSmartWallet && smartWalletClient) {
      // Log that we're using the smart wallet
      console.log('Using smart wallet client for transaction');
      
      // Send the transaction using our hybrid approach
      return await sendHybridTransaction(to, data, value, smartWalletClient);
    } else {
      // If no smart wallet is available, use the standard approach
      // You need to implement this based on your needs
      throw new Error('Non-smart wallet transactions not implemented');
    }
  } catch (error) {
    logNodeRealTransaction('failed', {
      action: 'swap',
      status: 'failed',
      params: {
        to,
        dataLength: data.length,
        value,
        error: String(error)
      },
      timestamp: Date.now()
    });
    
    throw error;
  }
}

/**
 * Internal helper function to log transaction events to the nodeRealTransactionLog
 */
function logNodeRealTransaction(status: 'pending' | 'success' | 'failed', data: any) {
  try {
    // Create a transaction log entry
    const logEntry: NodeRealTransactionLog = {
      action: data.action || 'transaction',
      status: status === 'pending' ? 'pending' : 
              status === 'success' ? 'success' : 'error',
      params: data.params || {},
      hash: data.hash,
      message: data.message,
      data: data.data,
      timestamp: Date.now()
    };
    
    // Add to the global transaction log
    updateNodeRealTransactionLog(logEntry);
    
  } catch (error) {
    console.error('Error logging transaction:', error);
    // Don't throw, this is just for logging
  }
} 