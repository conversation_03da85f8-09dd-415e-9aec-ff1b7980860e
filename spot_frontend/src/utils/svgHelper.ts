import React from 'react';

// Function to convert kebab-case attributes to camelCase
export function convertSvgAttributes(attributes: Record<string, any>) {
  const converted: Record<string, any> = {};
  for (const [key, value] of Object.entries(attributes)) {
    if (key.includes('-')) {
      // Convert kebab-case to camelCase
      const camelKey = key.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
      converted[camelKey] = value;
    } else {
      converted[key] = value;
    }
  }
  return converted;
}
