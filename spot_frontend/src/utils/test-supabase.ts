import { supabase } from './supabase-client';

// Define interfaces for better type safety
interface TableStatus {
  exists: boolean;
  error?: string;
  rowCount?: number;
  sample?: any;
}

interface SupabaseTestResult {
  success: boolean;
  message?: string;
  error?: string;
  tables?: Record<string, TableStatus>;
}

/**
 * Tests the Supabase connection and checks for table existence
 */
export async function testSupabaseConnection(): Promise<SupabaseTestResult> {
  try {
    // Test basic connection with a simple query
    const { error: connectionError } = await supabase.from('trade_history').select('count', { count: 'exact', head: true });
    
    if (connectionError) {
      console.error('❌ Failed to connect to Supabase:', connectionError.message);
      return { success: false, error: connectionError.message };
    }
    
    console.log('✅ Connected to Supabase successfully!');
    
    // Test if our tables exist
    const tables = ['trades', 'orders', 'trade_history'];
    const tableResults: Record<string, TableStatus> = {};
    
    for (const table of tables) {
      // Try to get just one row from each table to verify it exists
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        if (error.code === '42P01') { // PostgreSQL code for undefined_table
          console.error(`❌ Table "${table}" doesn't exist`);
          tableResults[table] = { exists: false, error: 'Table does not exist' };
        } else {
          console.error(`❌ Error querying "${table}" table:`, error.message);
          tableResults[table] = { exists: false, error: error.message };
        }
      } else {
        console.log(`✅ Table "${table}" exists and is accessible`);
        tableResults[table] = { 
          exists: true, 
          rowCount: data.length,
          sample: data.length > 0 ? data[0] : null
        };
      }
    }
    
    return { 
      success: true, 
      message: 'Connection test completed',
      tables: tableResults
    };
    
  } catch (error: any) { // Type assertion for better error handling
    console.error('❌ Unexpected error testing Supabase connection:', error);
    return { 
      success: false, 
      error: error.message || 'Unknown error occurred'
    };
  }
}
