/**
 * Smart Wallet Utilities
 * These functions help detect if a real smart wallet client is available
 * and validate transaction hashes
 */

/**
 * Checks if the provided client is a real smart wallet client or just a fallback implementation
 * @param smartWalletClient The client to check
 * @returns true if a real smart wallet client is available, false if it's a fallback
 */
export function isRealSmartWallet(smartWalletClient: any): boolean {
  // Check if client exists
  if (!smartWalletClient) return false;
  
  // Check if it's an object
  if (typeof smartWalletClient !== 'object') return false;
  
  // Check for required methods (can be either sendUserOperation or sendTransaction)
  const hasSendUserOp = typeof smartWalletClient.sendUserOperation === 'function';
  const hasSendTx = typeof smartWalletClient.sendTransaction === 'function';
  
  if (!hasSendUserOp && !hasSendTx) return false;
  
  // Check for account property
  const hasAccount = smartWalletClient.account && 
                     typeof smartWalletClient.account === 'object' && 
                     typeof smartWalletClient.account.address === 'string' &&
                     smartWalletClient.account.address.startsWith('0x');
  
  // Check for direct address property
  const hasDirectAddress = typeof smartWalletClient.address === 'string' && 
                           smartWalletClient.address.startsWith('0x');
  
  // Need either account.address or direct address
  return hasAccount || hasDirectAddress;
}

/**
 * Validates a transaction hash to ensure it's real and from the blockchain
 * @param txHash Transaction hash to validate
 * @returns true if the hash is valid, false otherwise
 */
export function isValidTransactionHash(txHash: string | undefined | null): boolean {
  return Boolean(
    txHash && 
    typeof txHash === 'string' && 
    txHash.length >= 64 &&
    txHash.startsWith('0x') &&
    !txHash.includes('fallback') &&
    !txHash.includes('pending')
  );
}

/**
 * Utility functions for working with Privy Smart Wallets
 */

/**
 * Gets the address from a smart wallet client safely
 * 
 * @param smartWalletClient The smart wallet client
 * @returns The address or undefined if not available
 */
export function getSmartWalletAddress(smartWalletClient: any): string | undefined {
  if (!smartWalletClient) return undefined;
  
  // Check if it has an account object with address
  if (smartWalletClient.account && 
      typeof smartWalletClient.account === 'object' && 
      typeof smartWalletClient.account.address === 'string') {
    return smartWalletClient.account.address;
  }
  
  // Check if it has a direct address property
  if (typeof smartWalletClient.address === 'string') {
    return smartWalletClient.address;
  }
  
  return undefined;
}
