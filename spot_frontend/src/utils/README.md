# Token Balance Fetching Utilities

This directory contains utilities for fetching token balances from connected wallets on both EVM and Solana chains.

## Files

### tokenBalances.js

Provides core functionality for fetching token balances from EVM and Solana chains:

- `fetchEVMTokenBalances(address, chainId)`: Fetches all ERC-20 token balances for an EVM wallet using Alchemy's API
- `fetchSolanaTokenBalances(address)`: Fetches all SPL token balances for a Solana wallet
- `getTokenBalances(address, chainType, chainId)`: Unified function that handles both EVM and Solana addresses

### tokenBalancesHelper.js

Provides helper functions for working with Privy wallet integration:

- `fetchWalletTokenBalances(wallets, activeChainId)`: Fetches token balances for multiple wallets from Privy

## Usage

```javascript
// For direct token balance fetching
import { getTokenBalances } from './utils/tokenBalances';

// Example: Fetch EVM token balances
const evmTokens = await getTokenBalances('0x123...', 'evm', 1); // Ethereum mainnet

// Example: Fetch Solana token balances
const solTokens = await getTokenBalances('ABC123...', 'solana');

// For Privy integration
import { fetchWalletTokenBalances } from './utils/tokenBalancesHelper';
import { useWallets } from '@privy-io/react-auth';

const { wallets } = useWallets();
const tokenBalances = await fetchWalletTokenBalances(wallets, activeChainId);
```

## Configuration

- Alchemy API key should be moved to environment variables in production
- Solana RPC URL can be configured in tokenBalances.js

## Return Format

Both utilities return token balances in a consistent format:

```javascript
[
  {
    // For EVM chains
    contractAddress: "0x123...", // or "native" for native tokens
    symbol: "ETH",
    name: "Ethereum",
    balance: "1.234",
    decimals: 18,
    chain: "Ethereum",
    chainId: 1
  },
  // For Solana
  {
    mintAddress: "ABC123...", // or "native" for SOL
    symbol: "SOL",
    name: "Solana",
    balance: "2.345",
    decimals: 9,
    chain: "Solana",
    chainId: "solana"
  }
]
```