import axios from 'axios';
import { TokenBase, SwapResponse } from '../types/trading';
import { detectTokenChain } from './chainUtils';
import { execSwap } from './swap';
import { quoteFourMeme } from './quote';

// Make sure we use the same TokenBase and SwapResponse types everywhere
type LocalTokenBase = TokenBase;
type LocalSwapResponse = SwapResponse;

// Dynamic API base URL based on the current hostname
function getLiquidityApiBaseUrl() {
  const hostname = window.location.hostname;
  if (hostname.includes('crypfi.io')) {
    return 'https://api.crypfi.io/api';
  } else if (hostname.includes('redfyn.xyz')) {
    return 'https://api.redfyn.xyz/api';
  } else {
    return '/api'; // Default to relative path for local development
  }
}

// Define API base URL
const API_BASE_URL = getLiquidityApiBaseUrl();

/**
 * Function to prepare a swap transaction on FourMeme (BSC)
 */
export const execFourMeme = async (
  tokenIn: LocalTokenBase, 
  tokenOut: LocalTokenBase, 
  amount: string, 
  recipient: string, 
  slippageTolerance: number = 0.5
): Promise<LocalSwapResponse> => {
  try {
    console.log(`Preparing FourMeme swap: ${amount} ${tokenIn.symbol} to ${tokenOut.symbol}`);
    
    // Validate requirements
    if (!tokenIn.address || !tokenOut.address) {
      console.error('Missing token address(es) for FourMeme swap');
      return {
        success: false,
        message: 'Missing token address(es)',
        data: null
      };
    }
    
    // Verify this is for BSC chain
    const tokenChain = detectTokenChain(tokenIn);
    if (tokenChain !== 'bsc') {
      console.error('FourMeme is only available for BSC tokens');
      return {
        success: false,
        message: 'FourMeme is only available for BSC tokens',
        data: null
      };
    }
    
    // Normalize amount to a decimal string
    const normalizedAmount = amount.replace(/,/g, '');
    
    // Prepare API request
    const requestBody = {
      tokenIn: {
        address: tokenIn.address,
        decimals: tokenIn.decimals || 18,
        symbol: tokenIn.symbol || 'Unknown',
        name: tokenIn.name || tokenIn.symbol || 'Unknown Token'
      },
      tokenOut: {
        address: tokenOut.address,
        decimals: tokenOut.decimals || 18,
        symbol: tokenOut.symbol || 'Unknown',
        name: tokenOut.name || tokenOut.symbol || 'Unknown Token'
      },
      amount: normalizedAmount,
      recipient: recipient,
      slippageTolerance: slippageTolerance
    };
    
    // Make API call to prepare the swap
    const response = await axios.post(`${API_BASE_URL}/fourmeme/prepare-swap`, requestBody);
    
    if (response.data && response.data.success) {
      console.log('FourMeme swap prepared successfully:', response.data);
      return {
        success: true,
        data: response.data.data,
        message: 'Swap prepared successfully',
        source: 'fourmeme'
      };
    } else {
      console.warn('Failed to prepare FourMeme swap:', response.data?.message || 'Unknown error');
      return {
        success: false,
        message: response.data?.message || 'Failed to prepare swap',
        data: null
      };
    }
  } catch (error) {
    console.error('Error preparing FourMeme swap:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to prepare swap',
      data: null
    };
  }
};

/**
 * Complete a trade on FourMeme (BSC)
 */
export const completeTradeFourMeme = async (
  baseToken: LocalTokenBase, 
  quoteToken: LocalTokenBase, 
  amount: string, 
  recipient: string, 
  privySendTransaction: (tx: any) => Promise<{ hash: string }>,
  options: Record<string, any> = {}
): Promise<LocalSwapResponse> => {
  console.log(`Completing FourMeme trade: ${amount} ${quoteToken.symbol} for ${baseToken.symbol}`);
  try {
    // First, get a quote to validate the swap is possible
    const quoteResponse = await quoteFourMeme(quoteToken, baseToken, amount);
    
    if (!quoteResponse.success) {
      return {
        success: false,
        message: `Failed to get FourMeme quote: ${quoteResponse.message}`,
        data: null
      };
    }
    
    // Prepare the swap transaction
    const preparedSwapResponse = await execFourMeme(
      quoteToken, 
      baseToken, 
      amount,
      recipient,
      options.slippageTolerance || 0.5
    );
    
    if (!preparedSwapResponse.success || !preparedSwapResponse.data) {
      return {
        success: false,
        message: `Failed to prepare FourMeme swap: ${preparedSwapResponse.message}`,
        data: null
      };
    }
    
    // Add metadata for transaction recording
    const preparedSwapData = {
      ...preparedSwapResponse.data,
      tokenIn: quoteToken,
      tokenOut: baseToken,
      amountIn: amount,
      dex: 'fourmeme'
    };
    
    // Since FourMeme is on BSC, we can use the regular execSwap function
    // The chainId should be handled internally by the smart wallet client
    return await execSwap(preparedSwapData, privySendTransaction, { useSmartWalletClient: true });
  } catch (error) {
    console.error('Error completing FourMeme trade:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to complete FourMeme trade',
      data: null
    };
  }
};
