import { ethers } from 'ethers';

interface PlatformFee {
  recipient: string;
  amount: string;
  token: string;
  percentage: number;
}

/**
 * Verifies a transaction to confirm that platform fees were included
 * @param txHash Transaction hash to check
 * @param platformFee Platform fee details to verify
 * @param rpcUrl Optional RPC URL to use (defaults to BSC)
 * @returns Object with verification results
 */
export const verifyTransactionFees = async (
  txHash: string,
  platformFee?: PlatformFee,
  rpcUrl: string = 'https://bsc-dataseed.binance.org/'
) => {
  if (!txHash || !txHash.startsWith('0x')) {
    return { verified: false, reason: 'Invalid transaction hash' };
  }
  
  if (!platformFee) {
    return { verified: true, reason: 'No platform fee to verify' };
  }
  
  try {
    // Create provider for the chain
    const provider = new ethers.providers.JsonRpcProvider(rpcUrl);
    
    // Get transaction details
    const txDetails = await provider.getTransaction(txHash);
    console.log('[FEE VERIFICATION] Transaction details:', {
      hash: txDetails.hash.substring(0, 10) + '...',
      from: txDetails.from, 
      to: txDetails.to,
      data: txDetails.data.substring(0, 20) + '...'
    });
    
    // Get transaction receipt to check transfers
    const txReceipt = await provider.getTransactionReceipt(txHash);
    console.log('[FEE VERIFICATION] Transaction receipt logs count:', txReceipt.logs.length);
    
    // Look for token transfer events
    // ERC-20 Transfer event signature
    const transferEvent = ethers.utils.id('Transfer(address,address,uint256)');
    
    // Find logs that look like token transfers to the platform fee recipient
    const platformFeeTransferLogs = txReceipt.logs.filter(log => {
      // Check if this is a Transfer event
      const isTransferEvent = log.topics[0] === transferEvent;
      
      // Convert recipient address format to match the log format (checksummed & padded)
      const feeRecipientAddress = platformFee.recipient.toLowerCase();
      
      // Check if the "to" address in the Transfer event matches our recipient
      // The "to" address is the second topic (index 1) in the Transfer event
      const hasMatchingRecipient = log.topics[2] && 
                                  log.topics[2].toLowerCase().includes(feeRecipientAddress.slice(2));  // slice(2) to remove '0x'
      
      return isTransferEvent && hasMatchingRecipient;
    });
    
    console.log(`[FEE VERIFICATION] Found ${platformFeeTransferLogs.length} potential platform fee transfer logs`);
    
    // If we found any matching logs, we can consider the fee included
    if (platformFeeTransferLogs.length > 0) {
      console.log('[FEE VERIFICATION] ✅ PLATFORM FEE CONFIRMED!');
      console.log(`[FEE VERIFICATION] Fee recipient: ${platformFee.recipient}`);
      console.log(`[FEE VERIFICATION] Fee amount: ${platformFee.amount} ${platformFee.token}`);
      
      return { 
        verified: true, 
        reason: 'Platform fee transfer found in logs', 
        logs: platformFeeTransferLogs 
      };
    }
    
    // If we didn't find any matching logs, also check for internal transactions
    // by looking for native token transfers to the recipient
    const nativeTransfersToRecipient = txReceipt.logs.filter(log => {
      return log.address === platformFee.recipient;
    });
    
    if (nativeTransfersToRecipient.length > 0) {
      console.log('[FEE VERIFICATION] ✅ PLATFORM FEE CONFIRMED (native token)!');
      return {
        verified: true,
        reason: 'Native token transfer to platform fee recipient found',
        logs: nativeTransfersToRecipient
      };
    }
    
    console.log('[FEE VERIFICATION] ❌ PLATFORM FEE NOT FOUND!');
    console.log(`[FEE VERIFICATION] Expected fee: ${platformFee.amount} ${platformFee.token} to ${platformFee.recipient}`);
    
    return { verified: false, reason: 'No platform fee transfer found in transaction logs' };
  } catch (error) {
    console.error('[FEE VERIFICATION] Error verifying platform fee:', error);
    return { 
      verified: false, 
      reason: `Error verifying platform fee: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
};

/**
 * Checks a transaction for platform fee inclusion
 * @param txHash Transaction hash to check
 * @param platformFee Platform fee details
 */
export const checkTransactionForFees = async (
  txHash: string, 
  platformFee?: PlatformFee
): Promise<void> => {
  if (!txHash || !platformFee) {
    console.log('[FEE VERIFICATION] No transaction hash or platform fee to verify');
    return;
  }
  
  try {
    console.log(`[FEE VERIFICATION] Verifying platform fee in transaction ${txHash}`);
    console.log(`[FEE VERIFICATION] Expected fee: ${platformFee.amount} ${platformFee.token} to ${platformFee.recipient}`);
    
    // Wait a few seconds to allow the transaction to be indexed
    console.log('[FEE VERIFICATION] Waiting for transaction to be indexed...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Verify the transaction
    const result = await verifyTransactionFees(txHash, platformFee);
    
    if (result.verified) {
      console.log('[FEE VERIFICATION] ✅ Platform fee verification successful!');
      console.log(`[FEE VERIFICATION] Reason: ${result.reason}`);
      
      // Display a clear confirmation message
      console.log(`
        ==============================================
        ✅ PLATFORM FEE SUCCESSFULLY INCLUDED!
        ----------------------------------------------
        • Recipient: ${platformFee.recipient}
        • Amount: ${platformFee.amount} ${platformFee.token}
        • Transaction: ${txHash.substring(0, 10)}...
        ==============================================
      `);
    } else {
      console.log('[FEE VERIFICATION] ❌ Platform fee verification failed!');
      console.log(`[FEE VERIFICATION] Reason: ${result.reason}`);
      
      // Display a clear failure message
      console.log(`
        ==============================================
        ❌ PLATFORM FEE NOT FOUND IN TRANSACTION!
        ----------------------------------------------
        • Expected Recipient: ${platformFee.recipient}
        • Expected Amount: ${platformFee.amount} ${platformFee.token}
        • Transaction: ${txHash.substring(0, 10)}...
        
        This may indicate that:
        1. The transaction is still processing
        2. The platform fee was not included in the transaction
        3. The fee was sent in a separate transaction
        ==============================================
      `);
    }
  } catch (error) {
    console.error('[FEE VERIFICATION] Error in fee verification:', error);
  }
}; 