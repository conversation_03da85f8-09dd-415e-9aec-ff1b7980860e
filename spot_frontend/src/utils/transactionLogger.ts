import { ethers } from 'ethers';

// Define transaction statuses
export enum TransactionStatus {
  INITIATED = 'INITIATED',
  PREPARING = 'PREPARING',
  APPROVAL_REQUESTED = 'APPROVAL_REQUESTED',
  APPROVAL_COMPLETED = 'APPROVAL_COMPLETED',
  SWAP_REQUESTED = 'SWAP_REQUESTED',
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  UNKNOWN = 'UNKNOWN'
}

// Define log severity levels
export enum LogSeverity {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR'
}

// Define transaction log entry interface
export interface TransactionLogEntry {
  id: string;
  timestamp: number;
  status: TransactionStatus;
  severity: LogSeverity;
  message: string;
  data?: any;
  tokenIn?: {
    symbol: string;
    address: string;
    amount: string;
  };
  tokenOut?: {
    symbol: string;
    address: string;
    amount: string;
  };
  walletAddress?: string;
  txHash?: string;
  error?: Error | string;
  dex?: string;
  chainId?: number | string;
}

// Transaction metadata
export interface TransactionMetadata {
  dex?: string;
  chainId?: number | string;
  walletAddress?: string;
  walletType?: string;
  slippage?: number;
  method?: 'bundled' | 'traditional' | 'solana' | string;
  platformFee?: any;
}

class TransactionLogger {
  private logs: Map<string, TransactionLogEntry[]> = new Map();
  private notificationCallback?: (message: string, severity: LogSeverity) => void;
  private isEnabled: boolean = true;
  private maxLogSize: number = 100;

  // Generate a unique transaction ID
  public generateTransactionId(): string {
    return `tx-${Date.now()}-${Math.floor(Math.random() * 1000000)}`;
  }

  // Set a notification callback for important log events
  public setNotificationCallback(callback: (message: string, severity: LogSeverity) => void): void {
    this.notificationCallback = callback;
  }

  // Enable or disable logging
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  // Add a log entry for a transaction
  public addLog(transactionId: string, status: TransactionStatus, message: string, severity: LogSeverity = LogSeverity.INFO, data?: any): void {
    if (!this.isEnabled) return;

    // Get or create the transaction log array
    if (!this.logs.has(transactionId)) {
      this.logs.set(transactionId, []);
    }

    const transaction = this.logs.get(transactionId);
    if (!transaction) return;

    // Create a new log entry
    const logEntry: TransactionLogEntry = {
      id: transactionId,
      timestamp: Date.now(),
      status,
      severity,
      message,
      data
    };

    // Add the log entry
    transaction.push(logEntry);

    // Trim logs if they exceed max size
    if (transaction.length > this.maxLogSize) {
      transaction.shift();
    }

    // Log to console
    this.logToConsole(logEntry);

    // Send notification for warnings and errors
    if ((severity === LogSeverity.WARNING || severity === LogSeverity.ERROR) && this.notificationCallback) {
      this.notificationCallback(message, severity);
    }
  }

  // Initialize a new transaction with metadata
  public initTransaction(
    transactionId: string,
    tokenIn: { symbol: string; address: string; amount: string },
    tokenOut: { symbol: string; address: string; amount: string },
    metadata?: TransactionMetadata
  ): void {
    if (!this.isEnabled) return;

    // Create initial log entry
    const logEntry: TransactionLogEntry = {
      id: transactionId,
      timestamp: Date.now(),
      status: TransactionStatus.INITIATED,
      severity: LogSeverity.INFO,
      message: `Initiating ${tokenIn.symbol} → ${tokenOut.symbol} transaction`,
      tokenIn,
      tokenOut,
      walletAddress: metadata?.walletAddress,
      dex: metadata?.dex,
      chainId: metadata?.chainId,
      data: metadata
    };

    // Create and initialize the transaction log
    this.logs.set(transactionId, [logEntry]);

    // Log to console
    this.logToConsole(logEntry);
  }

  // Update transaction status
  public updateStatus(transactionId: string, status: TransactionStatus, message: string, data?: any): void {
    this.addLog(transactionId, status, message, LogSeverity.INFO, data);
  }

  // Update transaction with error
  public logError(transactionId: string, error: Error | string, message?: string): void {
    const errorMessage = message || (error instanceof Error ? error.message : error);
    const logEntry: TransactionLogEntry = {
      id: transactionId,
      timestamp: Date.now(),
      status: TransactionStatus.FAILED,
      severity: LogSeverity.ERROR,
      message: `Transaction failed: ${errorMessage}`,
      error
    };

    // Get transaction log array
    if (!this.logs.has(transactionId)) {
      this.logs.set(transactionId, []);
    }

    const transaction = this.logs.get(transactionId);
    if (!transaction) return;

    // Add error log
    transaction.push(logEntry);

    // Log to console
    this.logToConsole(logEntry);

    // Send notification
    if (this.notificationCallback) {
      // Format a user-friendly error message
      const userMessage = this.formatErrorForUser(errorMessage);
      this.notificationCallback(userMessage, LogSeverity.ERROR);
    }
  }

  // Record transaction hash when available
  public setTransactionHash(transactionId: string, txHash: string): void {
    // Get transaction log array
    if (!this.logs.has(transactionId)) {
      return;
    }

    const transactions = this.logs.get(transactionId);
    if (!transactions) return;

    // Update all log entries with this transaction ID
    for (const entry of transactions) {
      entry.txHash = txHash;
    }

    // Add a specific log for the hash
    this.addLog(
      transactionId,
      TransactionStatus.PENDING,
      `Transaction submitted with hash: ${txHash}`,
      LogSeverity.INFO,
      { txHash }
    );
  }

  // Mark a transaction as completed
  public completeTransaction(transactionId: string, txHash?: string, data?: any): void {
    // Add completion log
    this.addLog(
      transactionId,
      TransactionStatus.COMPLETED,
      `Transaction completed${txHash ? ` with hash: ${txHash}` : ''}`,
      LogSeverity.INFO,
      data
    );

    // If hash was provided, make sure it's set on all entries
    if (txHash) {
      this.setTransactionHash(transactionId, txHash);
    }
  }

  // Get logs for a specific transaction
  public getTransactionLogs(transactionId: string): TransactionLogEntry[] {
    return this.logs.get(transactionId) || [];
  }

  // Get all transaction logs
  public getAllLogs(): Map<string, TransactionLogEntry[]> {
    return this.logs;
  }

  // Clear logs for a specific transaction
  public clearTransaction(transactionId: string): void {
    this.logs.delete(transactionId);
  }

  // Clear all logs
  public clearAllLogs(): void {
    this.logs.clear();
  }

  // Export logs to JSON
  public exportToJson(): string {
    const logsObject: Record<string, TransactionLogEntry[]> = {};
    this.logs.forEach((value, key) => {
      logsObject[key] = value;
    });
    return JSON.stringify(logsObject, null, 2);
  }

  // Export logs to CSV
  public exportToCsv(): string {
    let csv = 'Transaction ID,Timestamp,Status,Severity,Message,Token In,Token Out,Wallet,TxHash\n';
    
    this.logs.forEach((logs, _) => {
      logs.forEach(log => {
        const timestamp = new Date(log.timestamp).toISOString();
        const tokenIn = log.tokenIn ? `${log.tokenIn.symbol} (${log.tokenIn.amount})` : '';
        const tokenOut = log.tokenOut ? `${log.tokenOut.symbol} (${log.tokenOut.amount})` : '';
        
        csv += `${log.id},${timestamp},${log.status},${log.severity},"${log.message.replace(/"/g, '""')}","${tokenIn}","${tokenOut}",${log.walletAddress || ''},${log.txHash || ''}\n`;
      });
    });
    
    return csv;
  }

  // Helper methods for common transaction types

  // Log an approval transaction
  public logApproval(
    transactionId: string, 
    token: { symbol: string, address: string },
    spender: string,
    amount: string,
    walletAddress: string
  ): void {
    this.addLog(
      transactionId,
      TransactionStatus.APPROVAL_REQUESTED,
      `Requesting approval for ${token.symbol}`,
      LogSeverity.INFO,
      {
        token,
        spender,
        amount,
        walletAddress
      }
    );
  }

  // Log a swap transaction
  public logSwap(
    transactionId: string,
    tokenIn: { symbol: string, address: string, amount: string },
    tokenOut: { symbol: string, address: string, amount: string },
    dex: string,
    walletAddress: string,
    slippage: number
  ): void {
    this.addLog(
      transactionId,
      TransactionStatus.SWAP_REQUESTED,
      `Requesting swap: ${tokenIn.amount} ${tokenIn.symbol} → ${tokenOut.amount} ${tokenOut.symbol} on ${dex}`,
      LogSeverity.INFO,
      {
        tokenIn,
        tokenOut,
        dex,
        walletAddress,
        slippage
      }
    );
  }

  // Log a bundled transaction
  public logBundledTransaction(
    transactionId: string,
    operations: { to: string, data: string, value: string }[],
    walletAddress: string,
    metadata?: TransactionMetadata
  ): void {
    this.addLog(
      transactionId,
      TransactionStatus.PREPARING,
      `Preparing bundled transaction with ${operations.length} operations`,
      LogSeverity.INFO,
      {
        operations,
        walletAddress,
        ...metadata
      }
    );
  }

  // Private helper methods

  // Log to console with formatting
  private logToConsole(logEntry: TransactionLogEntry): void {
    const timestamp = new Date(logEntry.timestamp).toISOString();
    const prefix = `[TX ${logEntry.id.slice(0, 8)}][${logEntry.status}][${logEntry.severity}]`;
    
    switch (logEntry.severity) {
      case LogSeverity.DEBUG:
        console.log(`${prefix} ${timestamp} - ${logEntry.message}`, logEntry.data || '');
        break;
      case LogSeverity.INFO:
        console.log(`${prefix} ${timestamp} - ${logEntry.message}`, logEntry.data || '');
        break;
      case LogSeverity.WARNING:
        console.warn(`${prefix} ${timestamp} - ${logEntry.message}`, logEntry.data || '');
        break;
      case LogSeverity.ERROR:
        console.error(`${prefix} ${timestamp} - ${logEntry.message}`, logEntry.error || logEntry.data || '');
        break;
    }
  }

  // Format error messages to be more user-friendly
  private formatErrorForUser(errorMessage: string): string {
    // Remove technical details like contract addresses
    const cleanedMessage = errorMessage
      .replace(/0x[a-fA-F0-9]{40}/g, '[address]')
      .replace(/0x[a-fA-F0-9]{64}/g, '[hash]');
    
    // Common error translations
    if (cleanedMessage.includes('user rejected') || cleanedMessage.includes('rejected by user')) {
      return 'Transaction was rejected. Please try again if you wish to proceed.';
    }
    
    if (cleanedMessage.includes('insufficient funds')) {
      return 'Transaction failed: Insufficient funds to complete this transaction.';
    }
    
    if (cleanedMessage.includes('gas required exceeds allowance')) {
      return 'Transaction failed: Gas required exceeds your wallet balance.';
    }
    
    if (cleanedMessage.includes('cannot estimate gas')) {
      return 'Transaction failed: Unable to estimate gas. The token may not be tradeable.';
    }

    if (cleanedMessage.includes('router not found') || cleanedMessage.includes('router address')) {
      return 'Transaction failed: Swap router not found. Please try another DEX.';
    }

    if (cleanedMessage.includes('no transaction hash')) {
      return 'Transaction may have been submitted, but we couldn\'t confirm it. Please check your wallet transactions.';
    }

    // Default user-friendly message
    return 'Transaction failed: ' + (cleanedMessage.length > 100 
      ? cleanedMessage.substring(0, 100) + '...' 
      : cleanedMessage);
  }
}

// Create and export a singleton instance
export const transactionLogger = new TransactionLogger();

// Default export for convenience
export default transactionLogger; 