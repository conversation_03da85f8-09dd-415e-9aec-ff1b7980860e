import { PublicKey } from '@solana/web3.js';
import { TokenBase } from '../types/trading';

export type ChainType = 'solana' | 'bsc' | 'ethereum' | 'polygon' | 'avalanche' | 'unknown';

/**
 * Checks if an address is in Solana format
 * @param address The address to check
 * @returns boolean indicating if address is a valid Solana address
 */
export const isSolanaAddress = (address?: string): boolean => {
  if (!address) return false;
  
  try {
    // Attempt to create a PublicKey - will throw if invalid
    new PublicKey(address);
    
    // Additional checks for Solana address format
    // Solana addresses are 32-44 characters long and typically base58 encoded
    return address.length >= 32 && address.length <= 44;
  } catch (error) {
    return false;
  }
};

/**
 * Checks if an address is in EVM format (Ethereum, BSC, etc.)
 * @param address The address to check
 * @returns boolean indicating if address is a valid EVM address
 */
export const isEvmAddress = (address?: string): boolean => {
  if (!address) return false;
  
  // EVM addresses are 42 characters long (with 0x prefix) and hexadecimal
  const evmRegex = /^0x[a-fA-F0-9]{40}$/;
  return evmRegex.test(address);
};

/**
 * Determines the chain type based on a token's address format
 * @param token The token object containing address information
 * @returns The detected chain type
 */
export const detectTokenChain = (token?: TokenBase): ChainType => {
  if (!token) return 'unknown';
  
  // Try to get the token address from various possible properties
  const address = token.address || token.tokenAddress || token.contract;
  
  // Check if a network/chain property is explicitly provided
  if (token.network) {
    const network = token.network.toLowerCase();
    if (network.includes('solana')) return 'solana';
    if (network.includes('bsc') || network.includes('binance')) return 'bsc';
    if (network.includes('ethereum')) return 'ethereum';
    if (network.includes('polygon')) return 'polygon';
    if (network.includes('avalanche')) return 'avalanche';
  }
  
  // Use chainId to determine network if available
  if (token.chainId) {
    const chainId = typeof token.chainId === 'string' 
      ? parseInt(token.chainId, 10) 
      : token.chainId;
      
    switch(chainId) {
      case 1: return 'ethereum';
      case 56: return 'bsc';
      case 137: return 'polygon';
      case 43114: return 'avalanche';
      case 101: return 'solana';  // Solana mainnet
      case 103: return 'solana';  // Solana devnet
    }
  }
  
  // If no explicit network indicators, try to detect from address format
  if (address) {
    // Specific detection for Solana Wrapped SOL
    if (address === 'So11111111111111111111111111111111111111112') {
      return 'solana';
    }
    
    if (isSolanaAddress(address)) {
      return 'solana';
    }
    
    if (isEvmAddress(address)) {
      // Without additional context, we can't distinguish between different EVM chains
      // If the symbol is BNB or address matches known BSC tokens, assume BSC
      if (token.symbol && ['BNB', 'BUSD', 'CAKE'].includes(token.symbol.toUpperCase())) {
        return 'bsc';
      }
      
      // Default to Ethereum for EVM addresses if we can't determine specifically
      return 'ethereum';
    }
  }
  
  return 'unknown';
};

/**
 * Helper function to get appropriate RPC URL for a chain
 * @param chainType The detected chain type
 * @returns An RPC URL for the chain
 */
export const getChainRpcUrl = (chainType: ChainType): string => {
  switch (chainType) {
    case 'solana':
      return import.meta.env.VITE_SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com';
    case 'bsc':
      return import.meta.env.VITE_BSC_RPC_URL || 'https://bsc-dataseed.binance.org';
    case 'ethereum':
      return import.meta.env.VITE_ETH_RPC_URL || 'https://mainnet.infura.io/v3/your-infura-key';
    case 'polygon':
      return import.meta.env.VITE_POLYGON_RPC_URL || 'https://polygon-rpc.com';
    case 'avalanche':
      return import.meta.env.VITE_AVALANCHE_RPC_URL || 'https://api.avax.network/ext/bc/C/rpc';
    default:
      return import.meta.env.VITE_DEFAULT_RPC_URL || 'https://bsc-dataseed.binance.org';
  }
};

/**
 * Helper function to get chain ID for a chain type
 * @param chainType The detected chain type
 * @returns The chain ID as a number
 */
export const getChainId = (chainType: ChainType): number => {
  switch (chainType) {
    case 'solana':
      return 101; // Solana mainnet
    case 'bsc':
      return 56;
    case 'ethereum':
      return 1;
    case 'polygon':
      return 137;
    case 'avalanche':
      return 43114;
    default:
      return 56; // Default to BSC
  }
};
