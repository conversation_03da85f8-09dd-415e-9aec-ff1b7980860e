import { TokenBase, SwapResponse } from '../types/trading';
import axios from 'axios';
import qs from 'qs';
import { detectTokenChain } from './chainUtils';

// Use SwapResponse as our QuoteResponse type
type QuoteResponse = SwapResponse;

// Utility function to get the base URL for the liquidity API
function getLiquidityApiBaseUrl() {
  const hostname = window.location.hostname;
  if (hostname.includes('crypfi.io')) {
    return 'https://redfyn.crypfi.io/liquidity-api';
  }
  // Add /api to the URL if it's not already included
  const baseUrl = import.meta.env.VITE_API_LIQUIDITY_URL || 'https://redfyn.lrbinfotech.com/liquidity-api';
  return baseUrl.endsWith('/api') ? baseUrl : `${baseUrl}/api`;
}

// Define API base URL
const API_BASE_URL = getLiquidityApiBaseUrl();

// Function to get quotes from PancakeSwap directly
export const quotePancakeswap = async (
  baseToken: TokenBase, 
  quoteToken: TokenBase, 
  amount: string, 
  pairAddress?: string
): Promise<QuoteResponse> => {
  try {
    console.log(`Getting PancakeSwap quote for pair: ${quoteToken.symbol}/${baseToken.symbol}`);
    
    // Ensure tokens have all required fields
    if (!quoteToken.address && !quoteToken.tokenAddress && !quoteToken.contract) {
      console.error("Missing address for quote token:", quoteToken);
      return {
        success: false,
        message: "Quote token must have an address property",
        data: null
      };
    }
    
    if (!baseToken.address && !baseToken.tokenAddress && !baseToken.contract) {
      console.error("Missing address for base token:", baseToken);
      return {
        success: false,
        message: "Base token must have an address property",
        data: null
      };
    }
    
    // Prepare the request body with complete token objects
    const requestBody = {
      tokenIn: {
        address: quoteToken.address || quoteToken.tokenAddress || quoteToken.contract,
        decimals: quoteToken.decimals || 18,
        symbol: quoteToken.symbol || "Unknown",
        name: quoteToken.name || quoteToken.symbol || "Unknown Token"
      },
      tokenOut: {
        address: baseToken.address || baseToken.tokenAddress || baseToken.contract,
        decimals: baseToken.decimals || 18,
        symbol: baseToken.symbol || "Unknown",
        name: baseToken.name || baseToken.symbol || "Unknown Token"
      },
      amount: amount || "1",
      pairAddress: pairAddress
    };
    
    console.log('PancakeSwap quote request:', requestBody);
    // Make the request to the PancakeSwap API
    const response = await axios.post(`${API_BASE_URL}/pancakeswap/quote`, requestBody);
    
    // Check if we got a valid response
    if (response.data && response.data.success && response.data.data) {
      console.log('PancakeSwap quote response:', response.data);
      
      // Ensure platformFee is included if present
      const data = response.data.data;
      if (data.platformFee) {
        console.log('Platform fee data found in API response:', data.platformFee);
      } else {
        console.log('No platform fee found in API response');
      }
      
      return {
        success: true,
        data: response.data.data,
        source: 'pancakeswap'
      };
    } else {
      console.warn('Invalid PancakeSwap quote response:', response.data);
      return {
        success: false,
        message: response.data?.message || 'Failed to get quote from PancakeSwap',
        data: null
      };
    }
  } catch (error: unknown) {
    console.error('Error getting PancakeSwap quote:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to get quote',
      error: error instanceof Error && 'response' in error ? (error as any).response?.data : error,
      data: null
    };
  }
};

// Function to get quotes from Uniswap directly
export const quoteUniswap = async (
  baseToken: TokenBase, 
  quoteToken: TokenBase, 
  amount: string
): Promise<QuoteResponse> => {
  try {
    console.log(`Getting Uniswap quote for pair: ${quoteToken.symbol}/${baseToken.symbol}`);
    
    // Ensure tokens have all required fields
    if (!quoteToken.address && !quoteToken.tokenAddress && !quoteToken.contract) {
      console.error("Missing address for quote token:", quoteToken);
      return {
        success: false,
        message: "Quote token must have an address property",
        data: null
      };
    }
    
    if (!baseToken.address && !baseToken.tokenAddress && !baseToken.contract) {
      console.error("Missing address for base token:", baseToken);
      return {
        success: false,
        message: "Base token must have an address property",
        data: null
      };
    }
    
    // Prepare the request body with complete token objects
    const requestBody = {
      tokenIn: {
        address: quoteToken.address || quoteToken.tokenAddress || quoteToken.contract,
        decimals: quoteToken.decimals || 18,
        symbol: quoteToken.symbol || "Unknown",
        name: quoteToken.name || quoteToken.symbol || "Unknown Token"
      },
      tokenOut: {
        address: baseToken.address || baseToken.tokenAddress || baseToken.contract,
        decimals: baseToken.decimals || 18,
        symbol: baseToken.symbol || "Unknown",
        name: baseToken.name || baseToken.symbol || "Unknown Token"
      },
      amount: amount || "1",
      protocol: "auto"
    };
    
    console.log('Uniswap quote request:', requestBody);
    
    // Make the request to the Uniswap API through our proxy
    const response = await axios.post(`${API_BASE_URL}/uniswap/quote`, requestBody);
    
    // Check if we got a valid response
    if (response.data && response.data.success && response.data.data) {
      console.log('Uniswap quote response:', response.data);
      
      // Ensure platformFee is included if present
      const data = response.data.data;
      if (data.platformFee) {
        console.log('Platform fee data found in API response:', data.platformFee);
      } else {
        console.log('No platform fee found in API response');
      }
      
      return {
        success: true,
        data: response.data.data,
        source: 'uniswap'
      };
    } else {
      console.warn('Invalid Uniswap quote response:', response.data);
      return {
        success: false,
        message: response.data?.message || 'Failed to get quote from Uniswap',
        data: null
      };
    }
  } catch (error: unknown) {
    console.error('Error getting Uniswap quote:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to get quote',
      error: error instanceof Error && 'response' in error ? (error as any).response?.data : error,
      data: null
    };
  }
};

// Function to get quotes from SushiSwap directly
export const quoteSushiSwap = async (
  baseToken: TokenBase, 
  quoteToken: TokenBase, 
  amount: string
): Promise<QuoteResponse> => {
  try {
    console.log(`Getting SushiSwap quote for pair: ${quoteToken.symbol}/${baseToken.symbol}`);
    
    // Ensure tokens have all required fields
    if (!quoteToken.address && !quoteToken.tokenAddress && !quoteToken.contract) {
      console.error("Missing address for quote token:", quoteToken);
      return {
        success: false,
        message: "Quote token must have an address property",
        data: null
      };
    }
    
    if (!baseToken.address && !baseToken.tokenAddress && !baseToken.contract) {
      console.error("Missing address for base token:", baseToken);
      return {
        success: false,
        message: "Base token must have an address property",
        data: null
      };
    }
    
    // Prepare the request body with complete token objects
    const requestBody = {
      tokenIn: {
        address: quoteToken.address || quoteToken.tokenAddress || quoteToken.contract,
        decimals: quoteToken.decimals || 18,
        symbol: quoteToken.symbol || "Unknown",
        name: quoteToken.name || quoteToken.symbol || "Unknown Token"
      },
      tokenOut: {
        address: baseToken.address || baseToken.tokenAddress || baseToken.contract,
        decimals: baseToken.decimals || 18,
        symbol: baseToken.symbol || "Unknown",
        name: baseToken.name || baseToken.symbol || "Unknown Token"
      },
      amount: amount || "1"
    };
    
    console.log('SushiSwap quote request:', requestBody);
    
    // Make the request to the SushiSwap API through our proxy
    const response = await axios.post(`${API_BASE_URL}/sushiswap/quote`, requestBody);
    
    // Check if we got a valid response
    if (response.data && response.data.success && response.data.data) {
      console.log('SushiSwap quote response:', response.data);
      
      // Ensure platformFee is included if present
      const data = response.data.data;
      if (data.platformFee) {
        console.log('Platform fee data found in API response:', data.platformFee);
      } else {
        console.log('No platform fee found in API response');
      }
      
      return {
        success: true,
        data: response.data.data,
        source: 'sushiswap'
      };
    } else {
      console.warn('Invalid SushiSwap quote response:', response.data);
      return {
        success: false,
        message: response.data?.message || 'Failed to get quote from SushiSwap',
        data: null
      };
    }
  } catch (error: unknown) {
    console.error('Error getting SushiSwap quote:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to get quote',
      error: error instanceof Error && 'response' in error ? (error as any).response?.data : error,
      data: null
    };
  }
};

// Function to get quotes from Raydium directly
export const quoteRaydium = async (
  tokenIn: TokenBase, 
  tokenOut: TokenBase, 
  amount: string
): Promise<QuoteResponse> => {
  try {
    console.log(`Getting Raydium quote for pair: ${tokenIn.symbol}/${tokenOut.symbol}`);
    console.log(`Getting Raydium quote for pair: ${tokenIn.address}/${tokenOut.address}`);
    
    // Ensure tokens have all required fields
    if (!tokenIn.address && !tokenIn.tokenAddress && !tokenIn.contract) {
      console.error("Missing address for input token:", tokenIn);
      return {
        success: false,
        message: "Input token must have an address property",
        data: null
      };
    }
    
    if (!tokenOut.address && !tokenOut.tokenAddress && !tokenOut.contract) {
      console.error("Missing address for output token:", tokenOut);
      return {
        success: false,
        message: "Output token must have an address property",
        data: null
      };
    }
    
    // Verify this is for Solana chain
    const tokenChain = detectTokenChain(tokenIn);
    if (tokenChain !== 'solana') {
      console.error('Raydium is only available for Solana tokens');
      return {
        success: false,
        message: 'Raydium is only available for Solana tokens',
        data: null
      };
    }
    
    // Normalize amount to a decimal string
    const normalizedAmount = amount.replace(/,/g, '');
    
    // Prepare the request body with complete token objects
    const requestBody = {
      tokenIn: {
        address: tokenIn.address || tokenIn.tokenAddress || tokenIn.contract,
        decimals: tokenIn.decimals || 9,
        symbol: tokenIn.symbol || "Unknown",
        name: tokenIn.name || tokenIn.symbol || "Unknown Token"
      },
      tokenOut: {
        address: tokenOut.address || tokenOut.tokenAddress || tokenOut.contract,
        decimals: tokenOut.decimals || 9,
        symbol: tokenOut.symbol || "Unknown",
        name: tokenOut.name || tokenOut.symbol || "Unknown Token"
      },
      amount: normalizedAmount || "1",
      slippageTolerance: 0.5
    };
    
    console.log('Raydium quote request:', requestBody);
    
    // Make the request to the Raydium API through our proxy
    const response = await axios.post(`${API_BASE_URL}/raydium/quote`, requestBody);
    
    // Check if we got a valid response
    if (response.data && response.data.success && response.data.data) {
      console.log('Raydium quote response:', response.data);
      
      // Ensure platformFee is included if present
      const data = response.data.data;
      if (data.platformFee) {
        console.log('Platform fee data found in API response:', data.platformFee);
      } else {
        console.log('No platform fee found in API response');
      }
      
      return {
        success: true,
        data: response.data.data,
        source: 'raydium'
      };
    } else {
      console.warn('Invalid Raydium quote response:', response.data);
      return {
        success: false,
        message: response.data?.message || 'Failed to get quote from Raydium',
        data: null
      };
    }
  } catch (error: unknown) {
    console.error('Error getting Raydium quote:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to get quote',
      error: error instanceof Error && 'response' in error ? (error as any).response?.data : error,
      data: null
    };
  }
};

// Function to get quotes from Meteora directly
export const quoteMeteora = async (
  tokenIn: TokenBase, 
  tokenOut: TokenBase, 
  amount: string
): Promise<QuoteResponse> => {
  try {
    console.log(`Getting Meteora quote for pair: ${tokenIn.symbol}/${tokenOut.symbol}`);
    
    // Ensure tokens have all required fields
    if (!tokenIn.address && !tokenIn.tokenAddress && !tokenIn.contract) {
      console.error("Missing address for input token:", tokenIn);
      return {
        success: false,
        message: "Input token must have an address property",
        data: null
      };
    }
    
    if (!tokenOut.address && !tokenOut.tokenAddress && !tokenOut.contract) {
      console.error("Missing address for output token:", tokenOut);
      return {
        success: false,
        message: "Output token must have an address property",
        data: null
      };
    }
    
    // Verify this is for Solana chain
    const tokenChain = detectTokenChain(tokenIn);
    if (tokenChain !== 'solana') {
      console.error('Meteora is only available for Solana tokens');
      return {
        success: false,
        message: 'Meteora is only available for Solana tokens',
        data: null
      };
    }
    
    // Normalize amount to a decimal string
    const normalizedAmount = amount.replace(/,/g, '');
    
    // Prepare the request body with complete token objects
    const requestBody = {
      tokenIn: {
        address: tokenIn.address || tokenIn.tokenAddress || tokenIn.contract,
        decimals: tokenIn.decimals || 9,
        symbol: tokenIn.symbol || "Unknown",
        name: tokenIn.name || tokenIn.symbol || "Unknown Token"
      },
      tokenOut: {
        address: tokenOut.address || tokenOut.tokenAddress || tokenOut.contract,
        decimals: tokenOut.decimals || 9,
        symbol: tokenOut.symbol || "Unknown",
        name: tokenOut.name || tokenOut.symbol || "Unknown Token"
      },
      amount: amount || "1",
      slippageTolerance: 0.5
    };
    
    console.log('Meteora quote request:', requestBody);
    
    // Make the request to the Meteora API through our proxy
    const response = await axios.post(`${API_BASE_URL}/meteora/quote`, requestBody);
    
    // Check if we got a valid response
    if (response.data && response.data.success && response.data.data) {
      console.log('Meteora quote response:', response.data);
      
      // Ensure platformFee is included if present
      const data = response.data.data;
      if (data.platformFee) {
        console.log('Platform fee data found in API response:', data.platformFee);
      } else {
        console.log('No platform fee found in API response');
      }
      
      return {
        success: true,
        data: response.data.data,
        source: 'meteora'
      };
    } else {
      console.warn('Invalid Meteora quote response:', response.data);
      return {
        success: false,
        message: response.data?.message || 'Failed to get quote from Meteora',
        data: null
      };
    }
  } catch (error: unknown) {
    console.error('Error getting Meteora quote:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to get quote',
      error: error instanceof Error && 'response' in error ? (error as any).response?.data : error,
      data: null
    };
  }
};

// Function to get quotes from FourMeme directly
export const quoteFourMeme = async (
  tokenIn: TokenBase, 
  tokenOut: TokenBase, 
  amount: string
): Promise<QuoteResponse> => {
  try {
    console.log(`Getting FourMeme quote for pair: ${tokenIn.symbol}/${tokenOut.symbol}`);
    
    // Ensure tokens have all required fields
    if (!tokenIn.address && !tokenIn.tokenAddress && !tokenIn.contract) {
      console.error("Missing address for input token:", tokenIn);
      return {
        success: false,
        message: "Input token must have an address property",
        data: null
      };
    }
    
    if (!tokenOut.address && !tokenOut.tokenAddress && !tokenOut.contract) {
      console.error("Missing address for output token:", tokenOut);
      return {
        success: false,
        message: "Output token must have an address property",
        data: null
      };
    }
    
    // Verify this is for BSC chain
    const tokenChain = detectTokenChain(tokenIn);
    if (tokenChain !== 'bsc') {
      console.error('FourMeme is only available for BSC tokens');
      return {
        success: false,
        message: 'FourMeme is only available for BSC tokens',
        data: null
      };
    }
    
    // Normalize amount to a decimal string
    const normalizedAmount = amount.replace(/,/g, '');
    
    // Prepare the request body with complete token objects
    const requestBody = {
      tokenIn: {
        address: tokenIn.address || tokenIn.tokenAddress || tokenIn.contract,
        decimals: tokenIn.decimals || 18,
        symbol: tokenIn.symbol || "Unknown",
        name: tokenIn.name || tokenIn.symbol || "Unknown Token"
      },
      tokenOut: {
        address: tokenOut.address || tokenOut.tokenAddress || tokenOut.contract,
        decimals: tokenOut.decimals || 18,
        symbol: tokenOut.symbol || "Unknown",
        name: tokenOut.name || tokenOut.symbol || "Unknown Token"
      },
      amount: amount || "1",
      slippageTolerance: 0.5
    };
    
    console.log('FourMeme quote request:', requestBody);
    
    // Make the request to the FourMeme API through our proxy
    const response = await axios.post(`${API_BASE_URL}/fourmeme/quote`, requestBody);
    
    // Check if we got a valid response
    if (response.data && response.data.success && response.data.data) {
      console.log('FourMeme quote response:', response.data);
      
      // Ensure platformFee is included if present
      const data = response.data.data;
      if (data.platformFee) {
        console.log('Platform fee data found in API response:', data.platformFee);
      } else {
        console.log('No platform fee found in API response');
      }
      
      return {
        success: true,
        data: response.data.data,
        source: 'fourmeme'
      };
    } else {
      console.warn('Invalid FourMeme quote response:', response.data);
      return {
        success: false,
        message: response.data?.message || 'Failed to get quote from FourMeme',
        data: null
      };
    }
  } catch (error: unknown) {
    console.error('Error getting FourMeme quote:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to get quote',
      error: error instanceof Error && 'response' in error ? (error as any).response?.data : error,
      data: null
    };
  }
};