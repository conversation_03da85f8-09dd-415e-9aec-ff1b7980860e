/**
 * Utility functions for wallet management
 * Provides consistent wallet access across components using defaultWallets localStorage
 */

import { getCachedWalletId, getPrivySolanaWalletInfo } from '../api/privy_api';

/**
 * Get the default Solana wallet address from localStorage
 * Falls back to Privy hooks if no default wallet is set
 * 
 * @param authenticated - Whether user is authenticated
 * @param solanaWallets - Array of Solana wallets from Privy hooks
 * @returns Solana wallet address or null
 */
export const getDefaultSolanaWalletAddress = (
  authenticated: boolean,
  solanaWallets: any[]
): string | null => {
  if (!authenticated) {
    console.log('User not authenticated');
    return null;
  }

  // First try to get from defaultWallets localStorage
  try {
    const storedDefaults = localStorage.getItem('defaultWallets');
    if (storedDefaults) {
      const defaultWallets = JSON.parse(storedDefaults);
      if (defaultWallets.solana) {

        return defaultWallets.solana;
      }
    }
  } catch (error) {
    console.error('Error reading defaultWallets from localStorage:', error);
  }

  // Fallback to Privy hooks if no default wallet set
  if (solanaWallets.length === 0) {
    console.log('No Solana wallets connected');
    return null;
  }

  const solanaWallet = solanaWallets[0];
  if (!solanaWallet.address) {
    console.log('Solana wallet found but no address available');
    return null;
  }

  console.log('Using fallback Solana wallet from Privy:', solanaWallet.address);
  return solanaWallet.address;
};

/**
 * Get Solana wallet info (address and ID) for swap operations
 * Uses defaultWallets localStorage for consistent wallet selection
 * 
 * @param authenticated - Whether user is authenticated
 * @param userId - User ID from Privy
 * @param solanaWallets - Array of Solana wallets from Privy hooks
 * @returns Promise<{address: string, id: string} | null>
 */
export const getDefaultSolanaWalletInfo = async (
  authenticated: boolean,
  userId: string | undefined,
  solanaWallets: any[]
): Promise<{ address: string; id: string } | null> => {
  if (!authenticated || !userId) {
    console.log('User not authenticated or user ID not available');
    return null;
  }

  // First try to get wallet address from defaultWallets localStorage
  let walletAddress: string | null = null;
  try {
    const storedDefaults = localStorage.getItem('defaultWallets');
    if (storedDefaults) {
      const defaultWallets = JSON.parse(storedDefaults);
      if (defaultWallets.solana) {
        walletAddress = defaultWallets.solana;
        console.log('Using default Solana wallet from localStorage for swap:', walletAddress);
      }
    }
  } catch (error) {
    console.error('Error reading defaultWallets from localStorage:', error);
  }

  // Fallback to Privy hooks if no default wallet set
  if (!walletAddress) {
    if (solanaWallets.length === 0) {
      console.log('No Solana wallets connected');
      return null;
    }

    const solanaWallet = solanaWallets[0];
    if (!solanaWallet.address) {
      console.log('Solana wallet found but no address available');
      return null;
    }

    walletAddress = solanaWallet.address;
    console.log('Using fallback Solana wallet from Privy for swap:', walletAddress);
  }

  console.log('Getting wallet info for swap:', {
    address: walletAddress,
    source: walletAddress === solanaWallets[0]?.address ? 'privy' : 'localStorage'
  });

  // 1. Try to get cached wallet ID first (fastest)
  const cachedWalletId = getCachedWalletId(userId, walletAddress);
  if (cachedWalletId) {
    console.log('Using cached wallet ID:', cachedWalletId);
    return {
      address: walletAddress,
      id: cachedWalletId
    };
  }

  // 2. If no cache, try Privy API (should be rare if preloading works)
  try {
    console.log('Cache miss, fetching from Privy API...');
    const privyResponse = await getPrivySolanaWalletInfo(userId);

    if (privyResponse.success && privyResponse.data) {
      // Check if the connected wallet matches the Privy wallet
      if (privyResponse.data.address === walletAddress) {
        console.log('Found matching wallet ID from Privy API:', privyResponse.data.walletId);
        return {
          address: walletAddress,
          id: privyResponse.data.walletId
        };
      } else {
        // Try to find matching wallet in all Solana wallets
        const matchingWallet = privyResponse.data.allSolanaWallets.find(
          wallet => wallet.address === walletAddress
        );

        if (matchingWallet) {
          console.log('Found matching wallet ID from Privy API (multiple wallets):', matchingWallet.id);
          return {
            address: walletAddress,
            id: matchingWallet.id
          };
        }
      }
    }

    console.warn('Could not find wallet ID from Privy API, using fallback');
  } catch (error) {
    console.error('Error fetching wallet ID from Privy API:', error);
  }

  // 3. Fallback to generated ID if everything fails
  const fallbackId = `solana_${walletAddress.slice(0, 8)}_${walletAddress.slice(-8)}`;
  console.log('Using fallback wallet ID:', fallbackId);

  return {
    address: walletAddress,
    id: fallbackId
  };
};

/**
 * Interface for default wallets stored in localStorage
 */
export interface DefaultWallets {
  ethereum?: string;
  solana?: string;
}

/**
 * Get all default wallets from localStorage
 * 
 * @returns DefaultWallets object or empty object if none found
 */
export const getDefaultWallets = (): DefaultWallets => {
  try {
    const storedDefaults = localStorage.getItem('defaultWallets');
    if (storedDefaults) {
      return JSON.parse(storedDefaults);
    }
  } catch (error) {
    console.error('Error reading defaultWallets from localStorage:', error);
  }
  return {};
};

/**
 * Set default wallets in localStorage
 * 
 * @param wallets - DefaultWallets object to store
 */
export const setDefaultWallets = (wallets: DefaultWallets): void => {
  try {
    localStorage.setItem('defaultWallets', JSON.stringify(wallets));
    console.log('Default wallets updated:', wallets);
  } catch (error) {
    console.error('Error storing defaultWallets to localStorage:', error);
  }
};
