import axios from 'axios';
import { TokenBase, SwapResponse } from '../types/trading';
import { detectTokenChain } from './chainUtils';
import { execSolanaSwap } from './swap';
import { quoteMeteora } from './quote';

// Make sure we use the same TokenBase and SwapResponse types everywhere
type LocalTokenBase = TokenBase;
type LocalSwapResponse = SwapResponse;

// Dynamic API base URL based on the current hostname
function getLiquidityApiBaseUrl() {
  const hostname = window.location.hostname;
  if (hostname.includes('crypfi.io')) {
    return 'https://api.crypfi.io/api';
  } else if (hostname.includes('redfyn.xyz')) {
    return 'https://api.redfyn.xyz/api';
  } else {
    return '/api'; // Default to relative path for local development
  }
}

// Define API base URL
const API_BASE_URL = getLiquidityApiBaseUrl();

/**
 * Function to prepare a swap transaction on Meteora (Solana)
 */
export const execMeteora = async (
  tokenIn: LocalTokenBase, 
  tokenOut: LocalTokenBase, 
  amount: string, 
  recipient: string, 
  slippageTolerance: number = 0.5
): Promise<LocalSwapResponse> => {
  try {
    console.log(`Preparing Meteora swap: ${amount} ${tokenIn.symbol} to ${tokenOut.symbol}`);
    
    // Validate requirements
    if (!tokenIn.address || !tokenOut.address) {
      console.error('Missing token address(es) for Meteora swap');
      return {
        success: false,
        message: 'Missing token address(es)',
        data: null
      };
    }
    
    // Verify this is for Solana chain
    const tokenChain = detectTokenChain(tokenIn);
    if (tokenChain !== 'solana') {
      console.error('Meteora is only available for Solana tokens');
      return {
        success: false,
        message: 'Meteora is only available for Solana tokens',
        data: null
      };
    }
    
    // Normalize amount to a decimal string
    const normalizedAmount = amount.replace(/,/g, '');
    
    // Prepare API request
    const requestBody = {
      tokenIn: {
        address: tokenIn.address,
        decimals: tokenIn.decimals || 9,
        symbol: tokenIn.symbol || 'Unknown',
        name: tokenIn.name || tokenIn.symbol || 'Unknown Token'
      },
      tokenOut: {
        address: tokenOut.address,
        decimals: tokenOut.decimals || 9,
        symbol: tokenOut.symbol || 'Unknown',
        name: tokenOut.name || tokenOut.symbol || 'Unknown Token'
      },
      amount: normalizedAmount,
      recipient: recipient, // Recipient Solana address
      slippageTolerance: slippageTolerance
    };
    
    // Make API call to prepare the swap
    const response = await axios.post(`${API_BASE_URL}/meteora/prepare-swap`, requestBody);
    
    if (response.data && response.data.success) {
      console.log('Meteora swap prepared successfully:', response.data);
      return {
        success: true,
        data: response.data.data,
        message: 'Swap prepared successfully',
        source: 'meteora'
      };
    } else {
      console.warn('Failed to prepare Meteora swap:', response.data?.message || 'Unknown error');
      return {
        success: false,
        message: response.data?.message || 'Failed to prepare swap',
        data: null
      };
    }
  } catch (error) {
    console.error('Error preparing Meteora swap:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to prepare swap',
      data: null
    };
  }
};

/**
 * Complete a trade on Meteora (Solana)
 */
export const completeTradeMeteora = async (
  baseToken: LocalTokenBase, 
  quoteToken: LocalTokenBase, 
  amount: string, 
  recipient: string, 
  privySendTransaction: (tx: any) => Promise<{ hash: string }>,
  options: Record<string, any> = {}
): Promise<LocalSwapResponse> => {
  console.log(`Completing Meteora trade: ${amount} ${quoteToken.symbol} for ${baseToken.symbol}`);
  try {
    // First, get a quote to validate the swap is possible
    const quoteResponse = await quoteMeteora(quoteToken, baseToken, amount);
    
    if (!quoteResponse.success) {
      return {
        success: false,
        message: `Failed to get Meteora quote: ${quoteResponse.message}`,
        data: null
      };
    }
    
    // Prepare the swap transaction
    const preparedSwapResponse = await execMeteora(
      quoteToken, 
      baseToken, 
      amount,
      recipient,
      options.slippageTolerance || 0.5
    );
    
    if (!preparedSwapResponse.success || !preparedSwapResponse.data) {
      return {
        success: false,
        message: `Failed to prepare Meteora swap: ${preparedSwapResponse.message}`,
        data: null
      };
    }
    
    // Add metadata for transaction recording
    const preparedSwapData = {
      ...preparedSwapResponse.data,
      tokenIn: quoteToken,
      tokenOut: baseToken,
      amountIn: amount,
      dex: 'meteora'
    };
    
    // Execute the Solana swap (different from EVM swaps)
    return await execSolanaSwap(preparedSwapData, privySendTransaction);
  } catch (error) {
    console.error('Error completing Meteora trade:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to complete Meteora trade',
      data: null
    };
  }
};
