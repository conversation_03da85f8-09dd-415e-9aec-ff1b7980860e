/**
 * Pimlico Client Utilities
 * 
 * This file provides utilities for interacting with Pimlico's ERC-4337 services
 * for both bundling and paymaster (gas sponsorship) operations.
 */

// Import the getBundlerApiKey function from bundlerPaymasterService
import { getBundlerApiKey } from './bundlerPaymasterService';

// Cached Pimlico API key to avoid constant environment lookups
let pimlicoApiKey: string | null = null;

/**
 * Get the Pimlico API key from various possible sources
 */
export function getPimlicoApiKey(): string {
  // Use the getBundlerApiKey function from bundlerPaymasterService
  const apiKey = getBundlerApiKey('pimlico');
  if (apiKey) {
    return apiKey;
  }
  
  // Fallback for backwards compatibility
  return 'MISSING_API_KEY'; // This should never be reached now
}

/**
 * Get the Pimlico paymaster URL for a specific chain
 */
export function getPimlicoPaymasterUrl(chainId: number | string = 56): string {
  const apiKey = getPimlicoApiKey();
  return `https://api.pimlico.io/v2/${chainId}/rpc?apikey=${apiKey}`;
}

/**
 * Get paymaster data from Pimlico for a specific transaction
 * This overloaded version takes explicit transaction parameters instead of a userOp object
 *
 * @param sender - The sender address (smart wallet address)
 * @param target - The target address for the transaction
 * @param callData - The call data for the transaction
 * @param value - The value amount for the transaction
 * @param chainId - Optional chainId parameter (defaults to 56 for BSC)
 * @returns Promise<string> - The paymaster data hex string
 */
export async function getPimlicoPaymasterData(
  sender: string,
  target: string,
  callData: string,
  value: string,
  chainId?: number
): Promise<string>;

/**
 * Get paymaster data from Pimlico for a user operation
 * Format the request according to Pimlico's API v2 format
 *
 * @param userOp - The user operation to send
 * @returns Promise<string> - The paymaster data hex string
 */
export async function getPimlicoPaymasterData(
  userOpOrSender: any | string,
  target?: string,
  callData?: string,
  value?: string,
  chainId?: number
): Promise<string> {
  try {
    // Use BSC (56) as default chain ID
    const userOpChainId = chainId || 56;
    console.log('Pimlico using chainId:', userOpChainId);
    
    let apiUrl = getPimlicoPaymasterUrl(userOpChainId);
    let userOp: any;
    
    // Check if the first parameter is already a UserOperation or we need to build one
    if (typeof userOpOrSender === 'object' && userOpOrSender !== null) {
      // Clone the object to avoid modifying the original
      userOp = { ...userOpOrSender };
      
      // Remove chainId from the userOp copy - it's not part of the standard format
      if ('chainId' in userOp) {
        delete userOp.chainId;
      }
      
      console.log('Calling Pimlico paymaster API', {
        url: apiUrl,
        userOp: {
          sender: userOp.sender,
          nonce: userOp.nonce || '0x0',
          callData: userOp.callData ? userOp.callData.substring(0, 20) + '...' : '0x',
          chainId: userOpChainId // Log the chainId for debugging only
        }
      });
    } else if (typeof userOpOrSender === 'string') {
      // Handle case when individual parameters are passed
      console.log('Calling Pimlico paymaster API with converted parameters', {
        url: apiUrl,
        sender: userOpOrSender,
        target,
        callDataLength: callData ? callData.length : 0,
        value: value || '0',
        chainId: userOpChainId // Log the chainId for debugging only
      });
      
      // Create a userOp object from individual parameters
      userOp = {
        sender: userOpOrSender,
        callData: callData || '0x',
        nonce: '0x' + Math.floor(Math.random() * 1000000000).toString(16),
        maxFeePerGas: '0x12a05f200',  // 5 Gwei in hex for BSC
        maxPriorityFeePerGas: '0x12a05f200',  // 5 Gwei in hex for BSC
        signature: '0xfffffffffffffffffffffffffffffff0000000000000000000000000000000007aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa1c'
      };
    } else {
      throw new Error('Invalid parameters passed to getPimlicoPaymasterData');
    }
    
    // Prepare the userOp object with correct formatting for ERC-4337 v0.7
    // According to the Pimlico docs, v0.7 uses different field names than v0.6
    // See: https://docs.pimlico.io/references/bundler/endpoints/eth_sendUserOperation
    const formattedUserOp: Record<string, any> = {
      sender: userOp.sender,
      nonce: userOp.nonce || '0x0',
      // For deployed smart accounts, we only need these fields
      callData: userOp.callData || '0x',
      callGasLimit: userOp.callGasLimit || '0x0',
      verificationGasLimit: userOp.verificationGasLimit || '0x0', 
      preVerificationGas: userOp.preVerificationGas || '0x0',
      signature: userOp.signature || '0xfffffffffffffffffffffffffffffff0000000000000000000000000000000007aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa1c'
      // IMPORTANT: Do NOT include chainId or other non-standard fields
    };
    
    // Set BSC-specific context values
    setChainContext(formattedUserOp);
    
    // Remove any fields that are 'null' or undefined
    Object.keys(formattedUserOp).forEach(key => {
      if (formattedUserOp[key] === null || formattedUserOp[key] === undefined) {
        delete formattedUserOp[key];
      }
    });
    
    // Log the cleaned UserOperation
    console.log('Cleaned UserOperation for v0.7:', {
      sender: formattedUserOp.sender,
      nonce: formattedUserOp.nonce,
      callDataLength: userOp.callData ? userOp.callData.length : 0
    });

    // This is the correct EntryPoint v0.7 address across all chains
    // Reference: https://docs.pimlico.io/reference/contract-addresses
    const entryPointAddress = '******************************************';
    
    // Format request payload according to Pimlico API v2 docs
    const requestPayload = {
      method: 'pm_getPaymasterData',
      params: [
        formattedUserOp,
        entryPointAddress,
        // Add proper chainId in hex format for BSC (0x38 = 56 in decimal)
        '0x38'  // Previously was "0x" which caused the chainId mismatch error
      ],
      id: 1,
      jsonrpc: '2.0'
    };
    
    // DEFENSIVE CHECK: Ensure no chainId exists in the params object
    // This prevents the "Unrecognized key(s) in object: 'chainId'" error
    if (requestPayload.params[0] && 
        typeof requestPayload.params[0] === 'object' && 
        'chainId' in (requestPayload.params[0] as object)) {
      console.warn('Found unexpected chainId in request payload, removing it');
      delete (requestPayload.params[0] as any).chainId;
    }
    
    // Call the Pimlico API
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestPayload),
    });
    
    // Parse the response
    const responseData = await response.json();
    
    // Log response for debugging
    console.log('Pimlico paymaster API response:', responseData);
    
    if (!response.ok || responseData.error) {
      const errorMessage = responseData.error?.message || 
        responseData.error?.data?.message ||
        `Pimlico API error: ${response.statusText}`;
      
      console.error('Pimlico API error details:', {
        status: response.status,
        method: requestPayload.method,
        error: responseData.error,
        url: apiUrl
      });
      
      throw new Error(errorMessage);
    }
    
    // V0.7 compatibility: format the result based on Pimlico's response format
    // Check if the result is in v0.7 format (an object with paymaster fields)
    if (responseData.result && typeof responseData.result === 'object') {
      console.log('Detected v0.7 paymaster response format:', responseData.result);
      
      // Format for v0.7: build paymasterAndData from individual fields
      // This makes it compatible with both v0.6 and v0.7
      const paymaster = responseData.result.paymaster || '';
      const paymasterData = responseData.result.paymasterData || '';
      
      // Validate paymaster address - must be a valid Ethereum address
      if (paymaster && paymaster.startsWith('0x') && paymaster.length === 42) {
        // Combine into the paymasterAndData format expected by sendHybridTransaction
        // Only if paymaster is a valid address
        return paymaster + (paymasterData.startsWith('0x') ? paymasterData.substring(2) : paymasterData);
      } else {
        console.warn('Invalid paymaster address from Pimlico:', paymaster);
        return ''; // Return empty string if paymaster is invalid
      }
    }
    
    // For standard v0.6 response format
    return responseData.result;
  } catch (error) {
    console.error('Error getting paymaster data from Pimlico:', error);
    throw error;
  }
}

/**
 * Get the Pimlico RPC URL with API key
 * 
 * @param chainId - Optional chain ID (defaults to 56 for BSC)
 * @returns The Pimlico RPC URL with API key
 */
export function getPimlicoRpcUrl(chainId: number = 56): string {
  const apiKey = getPimlicoApiKey();
  return `https://api.pimlico.io/v2/${chainId}/rpc?apikey=${apiKey}`;
}

/**
 * Set chain context for the Pimlico API
 * Ensures that the user operation has the correct implicit chain context
 */
function setChainContext(formattedUserOp: Record<string, any>): void {
  // Set BSC-specific gas values - these implicitly indicate a BSC transaction
  // without needing to explicitly specify chainId
  formattedUserOp.maxFeePerGas = '0x12a05f200'; // 5 Gwei in hex for BSC
  formattedUserOp.maxPriorityFeePerGas = '0x12a05f200'; // 5 Gwei in hex for BSC
  
  // Remove any chain ID if it somehow got included
  if ('chainId' in formattedUserOp) {
    delete formattedUserOp.chainId;
  }
}

/**
 * Initialize global window properties if they don't exist
 * This ensures our client can store and access the Pimlico API key
 */
export function initializePaymasterClient(): void {
  // Make sure window.env exists
  if (!window.env) {
    window.env = {};
  }
  
  // Set the API key if available in environment
  if (import.meta.env.VITE_PIMLICO_API_KEY && !window.env.PIMLICO_API_KEY) {
    window.env.PIMLICO_API_KEY = import.meta.env.VITE_PIMLICO_API_KEY;
  }
  
  console.log('Pimlico client initialized for paymaster services, sponsorship is ' + 
    (getPimlicoApiKey() ? 'available' : 'not available'));
} 