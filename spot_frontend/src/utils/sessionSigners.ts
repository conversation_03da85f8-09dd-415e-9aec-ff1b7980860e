import { useHeadlessDelegatedActions } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import { useSmartWallets } from '@privy-io/react-auth/smart-wallets';
import { useWallets } from '@privy-io/react-auth';
import { usePrivy } from '@privy-io/react-auth';

/**
 * Hook for delegating wallet access to session signers
 * This utility automatically delegates all wallets to session signers
 * without requiring user interaction
 */
export const useSessionSigners = () => {
  const { delegateWallet } = useHeadlessDelegatedActions();
  const { wallets: solanaWallets } = useSolanaWallets();
  const { client: smartWalletClient } = useSmartWallets();
  const { wallets } = useWallets();
  const { user } = usePrivy();

  /**
   * Validate if an address belongs to the current user
   * @param address The wallet address to validate
   * @param chainType The chain type ('solana' or 'ethereum')
   */
  const validateUserOwnsAddress = (address: string, chainType: 'solana' | 'ethereum'): boolean => {
    if (!user || !address) {
      console.warn('Cannot validate address: missing user or address');
      return false;
    }

    // Check linked accounts for the address
    const linkedAccounts = user.linkedAccounts || [];
    const matchingAccount = linkedAccounts.find(account =>
      account.address?.toLowerCase() === address.toLowerCase() &&
      (chainType === 'solana' ? account.type === 'wallet' && account.chainType === 'solana' :
       chainType === 'ethereum' && (account.type === 'wallet' || account.type === 'smart_wallet'))
    );

    if (matchingAccount) {
      console.log(`✅ Address ${address} validated as belonging to user`);
      return true;
    }

    // Also check smart wallet if it's an ethereum address
    if (chainType === 'ethereum' && user.smartWallet?.address?.toLowerCase() === address.toLowerCase()) {
      console.log(`✅ Smart wallet address ${address} validated as belonging to user`);
      return true;
    }

    console.warn(`❌ Address ${address} does not belong to current user`);
    return false;
  };

  /**
   * Delegate a Solana wallet to session signers
   * @param address The Solana wallet address to delegate
   */
  const delegateSolanaWallet = async (address: string): Promise<boolean> => {
    try {
      // Validate that the address belongs to the current user
      if (!validateUserOwnsAddress(address, 'solana')) {
        console.error(`Cannot delegate Solana wallet ${address}: address not associated with current user`);
        return false;
      }

      console.log(`Delegating Solana wallet ${address} to session signers`);
      await delegateWallet({
        address,
        chainType: 'solana'
      });
      console.log('✅ Solana wallet delegation successful');
      return true;
    } catch (error) {
      console.error('❌ Error delegating Solana wallet:', error);
      return false;
    }
  };
  
  /**
   * Delegate an EVM wallet to session signers
   * @param address The EVM wallet address to delegate
   */
  const delegateEvmWallet = async (address: string): Promise<boolean> => {
    try {
      // Validate that the address belongs to the current user
      if (!validateUserOwnsAddress(address, 'ethereum')) {
        console.error(`Cannot delegate EVM wallet ${address}: address not associated with current user`);
        return false;
      }

      console.log(`Delegating EVM wallet ${address} to session signers`);
      await delegateWallet({
        address,
        chainType: 'ethereum'
      });
      console.log('✅ EVM wallet delegation successful');
      return true;
    } catch (error) {
      console.error('❌ Error delegating EVM wallet:', error);
      return false;
    }
  };
  
  /**
   * Automatically delegate all wallets to session signers
   * This function will delegate all Solana and EVM wallets that belong to the current user
   */
  const delegateAllWallets = async (): Promise<void> => {
    if (!user) {
      console.warn('Cannot delegate wallets: no authenticated user');
      return;
    }

    console.log('🔄 Starting wallet delegation process for user:', user.id);

    // Track delegated addresses to avoid duplicates
    const delegatedAddresses = new Set<string>();
    const successfulDelegations: string[] = [];
    const failedDelegations: string[] = [];

    // Delegate Solana wallets
    if (solanaWallets && solanaWallets.length > 0) {
      console.log(`📱 Found ${solanaWallets.length} Solana wallets to delegate`);

      for (const wallet of solanaWallets) {
        if (wallet.address && !delegatedAddresses.has(wallet.address)) {
          const success = await delegateSolanaWallet(wallet.address);
          delegatedAddresses.add(wallet.address);

          if (success) {
            successfulDelegations.push(`SOL:${wallet.address}`);
          } else {
            failedDelegations.push(`SOL:${wallet.address}`);
          }
        }
      }
    }

    // Delegate Smart Wallet if available
    if (smartWalletClient && smartWalletClient.account?.address) {
      const address = smartWalletClient.account.address;
      if (!delegatedAddresses.has(address)) {
        const success = await delegateEvmWallet(address);
        delegatedAddresses.add(address);

        if (success) {
          successfulDelegations.push(`SW:${address}`);
        } else {
          failedDelegations.push(`SW:${address}`);
        }
      }
    }

    // Delegate any other EVM wallets
    if (wallets && wallets.length > 0) {
      console.log(`💼 Found ${wallets.length} additional wallets to process`);

      for (const wallet of wallets) {
        // Only process EVM wallets (addresses starting with 0x)
        if (wallet.address &&
            wallet.address.startsWith('0x') &&
            !delegatedAddresses.has(wallet.address)) {
          const success = await delegateEvmWallet(wallet.address);
          delegatedAddresses.add(wallet.address);

          if (success) {
            successfulDelegations.push(`EVM:${wallet.address}`);
          } else {
            failedDelegations.push(`EVM:${wallet.address}`);
          }
        }
      }
    }

    // Log delegation summary
    console.log(`✅ Delegation process complete:`, {
      total: delegatedAddresses.size,
      successful: successfulDelegations.length,
      failed: failedDelegations.length
    });

    if (successfulDelegations.length > 0) {
      console.log('✅ Successfully delegated:', successfulDelegations);
    }

    if (failedDelegations.length > 0) {
      console.warn('❌ Failed to delegate:', failedDelegations);
    }
  };
  
  /**
   * Clear any cached wallet data that might be causing delegation issues
   */
  const clearWalletCache = (): void => {
    try {
      // Clear Privy-related localStorage items
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (
          key.startsWith('privy:') ||
          key.includes('wallet') ||
          key.includes('session') ||
          key.includes('delegation')
        )) {
          keysToRemove.push(key);
        }
      }

      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        console.log(`🧹 Cleared cached data: ${key}`);
      });

      console.log(`🧹 Cleared ${keysToRemove.length} cached wallet items`);
    } catch (error) {
      console.error('Error clearing wallet cache:', error);
    }
  };

  /**
   * Reset delegation state and clear cache
   * Use this when delegation errors occur
   */
  const resetDelegationState = (): void => {
    console.log('🔄 Resetting delegation state...');
    clearWalletCache();

    // Dispatch event to notify components that delegation state was reset
    window.dispatchEvent(new CustomEvent('delegation-state-reset', {
      detail: { timestamp: Date.now() }
    }));
  };

  return {
    delegateSolanaWallet,
    delegateEvmWallet,
    delegateAllWallets,
    clearWalletCache,
    resetDelegationState,
    validateUserOwnsAddress
  };
};
