import * as React from 'react';

// Define interface for the props
interface SvgIconProps {
  className?: string;
  width?: string | number;
  height?: string | number;
  [key: string]: any;
}

// Create SVG components with correct React props
export const createSvgIcon = (
  SvgComponent: React.ComponentType<React.SVGProps<SVGSVGElement>>,
  props?: SvgIconProps
) => {
  // Return React element with proper props
  return (
    <div className={props?.className} style={{ width: props?.width, height: props?.height }}>
      <SvgComponent {...props} />
    </div>
  );
};