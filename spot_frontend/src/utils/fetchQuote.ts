import { TokenBase, QuoteData, PancakeswapQuoteData, FeeData } from '../types/trading';
import { getBestDEXQuote, getPairList } from './trading-api';
import { walletAPI } from './api';

// Define interface for quote updates received from streaming API
interface QuoteUpdate {
  success: boolean;
  data: QuoteData[];
  bestQuote?: QuoteData;
  allQuotes?: QuoteData[];
  platformFee?: FeeData | null;
}

interface FetchQuoteParams {
  amount: string;
  selectedCoin: any;
  activeToken: any;
  setIsLoadingQuote: (loading: boolean) => void;
  setDexQuotes: (quotes: any[]) => void;
  setQuoteData: (data: QuoteData | ((prevData: QuoteData | null) => QuoteData) | null) => void;
  setPancakeswapQuoteData: (data: any) => void;
  setIsTokenTradeable: (tradeable: boolean) => void;
  setOutputAmount: (amount: string) => void;
  setExpectedPrice: (price: string) => void;
  setStatusMessage: (message: string) => void;
  isBuyAction: boolean;
}

/**
 * Fetches quotes from multiple DEXes and updates the UI as soon as quotes arrive
 */
export const fetchQuote = async (params: FetchQuoteParams): Promise<void> => {
  const {
    amount,
    selectedCoin,
    activeToken,
    setIsLoadingQuote,
    setDexQuotes,
    setQuoteData,
    setPancakeswapQuoteData,
    setIsTokenTradeable,
    setOutputAmount,
    setExpectedPrice,
    setStatusMessage,
    isBuyAction
  } = params;
  
  // Validate required inputs
  if (!selectedCoin || !activeToken) {
    console.log('Missing token information, cannot fetch quote');
    setStatusMessage("Missing token information");
    return;
  }
  
  try {
    console.log(`Fetching quote for ${amount} ${selectedCoin.symbol} to ${activeToken.symbol} (${isBuyAction ? 'BUY' : 'SELL'} action)`);
    setIsLoadingQuote(true);
    
    // Initialize with empty data
    setDexQuotes([]);
    setQuoteData(null);
    
    // First check if the token pair is tradeable
    if (selectedCoin.address && 
        activeToken.address && 
        selectedCoin.address.toLowerCase() === activeToken.address.toLowerCase()) {
      setIsTokenTradeable(false);
      setStatusMessage("Cannot trade a token with itself");
      setIsLoadingQuote(false);
      return;
    }

    // Check for any available pairs to determine if the token is tradeable
    try {
      const pairlistResponse = await getPairList(selectedCoin, activeToken);
      if (!pairlistResponse.success || 
          !pairlistResponse.matchedPair || 
          !pairlistResponse.pairs || 
          pairlistResponse.pairs.length === 0) {
        setIsTokenTradeable(false);
        setStatusMessage("No liquidity pools available for this pair");
        setIsLoadingQuote(false);
        return;
      }
      setIsTokenTradeable(true);
    } catch (error) {
      console.error("Error checking pair availability:", error);
      setIsTokenTradeable(false);
      setStatusMessage("Error checking pair availability");
      setIsLoadingQuote(false);
      return;
    }
    
    // Use getBestDEXQuote with streaming updates
    getBestDEXQuote(
      activeToken, 
      selectedCoin, 
      amount, 
      (quoteUpdate: QuoteUpdate) => {
        if (quoteUpdate.success && quoteUpdate.data && quoteUpdate.data.length > 0) {
          // Update all quotes as they come in
          setDexQuotes(quoteUpdate.data);
          
          // Get the best quote so far
          if (quoteUpdate.bestQuote) {
            const bestQuote = quoteUpdate.bestQuote;
            
            // Update quote data with latest best quote
            setQuoteData(bestQuote);
            
            // Check for platform fee
            const platformFee = bestQuote.platformFee || quoteUpdate.platformFee || null;
            
            // Save PancakeSwap data if it's from PancakeSwap
            if (bestQuote.dex === 'pancakeswap' || bestQuote.dexId === 'pancakeswap') {
              setPancakeswapQuoteData(bestQuote);
            }
            
            // Update output amount
            const outputAmount = bestQuote.amountOut || 
              (bestQuote.quote && (bestQuote.quote.amountOut || bestQuote.quote.outputAmount)) || 'N/A';
            setOutputAmount(outputAmount.toString());
            
            // Update expected price
            const price = bestQuote.executionPrice || bestQuote.price || 
              (bestQuote.quote ? (bestQuote.quote.amountOut ? 
                Number(bestQuote.quote.amountOut) / Number(amount) : 
                undefined) : 
              undefined) || 'N/A';
            setExpectedPrice(typeof price === 'string' || typeof price === 'number' ? price.toString() : 'N/A');
            
            // Clear any error messages
            setStatusMessage("");
          }
        }
      }
    ).then(finalResponse => {
      // Handle the final response
      setIsLoadingQuote(false);
      
      if (!finalResponse.success) {
        setStatusMessage(finalResponse.message || "Failed to get quotes");
      }
      
      // Get available options if needed
      if (finalResponse.availableTokens && finalResponse.availableTokens.length > 0) {
        setQuoteData((prevData: QuoteData | null) => {
          const newData: QuoteData = {
            ...(prevData || {}),
            availableOptions: finalResponse.availableTokens
          };
          return newData;
        });
      }
    }).catch(error => {
      setIsLoadingQuote(false);
      console.error("Error fetching quotes:", error);
      setStatusMessage(`Error fetching quotes: ${error instanceof Error ? error.message : "Unknown error"}`);
    });
    
  } catch (error) {
    setIsLoadingQuote(false);
    console.error("Error in fetchQuote:", error);
    setStatusMessage(`Error: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
};
