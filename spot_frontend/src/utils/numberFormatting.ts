/**
 * Utility functions for formatting numbers in trading interfaces
 */

/**
 * Format small numbers with proper scientific notation
 * Examples:
 * 0.0000005 -> "5.0e-7"
 * 0.000123 -> "0.000123"
 * 0.5 -> "0.5"
 * 123.45 -> "123.45"
 */
export const formatSmallNumber = (num: number, maxDecimals: number = 6): string => {
  if (num === 0) return '0';
  
  const absNum = Math.abs(num);
  
  // For very small numbers (less than 0.000001), use scientific notation
  if (absNum < 0.000001) {
    const exp = Math.floor(Math.log10(absNum));
    const mantissa = num / Math.pow(10, exp);
    return `${mantissa.toFixed(1)}e${exp}`;
  }
  
  // For small numbers, show appropriate decimal places
  if (absNum < 0.001) {
    return num.toFixed(8).replace(/\.?0+$/, '');
  }
  
  if (absNum < 1) {
    return num.toFixed(maxDecimals).replace(/\.?0+$/, '');
  }
  
  // For larger numbers, use standard formatting
  return num.toFixed(4).replace(/\.?0+$/, '');
};

/**
 * Format price with appropriate precision
 */
export const formatPrice = (price: number): string => {
  if (price === 0) return '0';
  
  const absPrice = Math.abs(price);
  
  // Very small prices
  if (absPrice < 0.000001) {
    const exp = Math.floor(Math.log10(absPrice));
    const mantissa = price / Math.pow(10, exp);
    return `${mantissa.toFixed(1)}e${exp}`;
  }
  
  // Small prices
  if (absPrice < 0.001) {
    return price.toExponential(3);
  }
  
  if (absPrice < 1) {
    return price.toFixed(6);
  }
  
  // Regular prices
  return price.toFixed(4);
};

/**
 * Format large numbers with K, M, B suffixes
 */
export const formatLargeNumber = (num: number): string => {
  if (num === 0) return '0';
  
  const absNum = Math.abs(num);
  
  if (absNum >= 1e9) {
    return (num / 1e9).toFixed(2) + 'B';
  } else if (absNum >= 1e6) {
    return (num / 1e6).toFixed(2) + 'M';
  } else if (absNum >= 1e3) {
    return (num / 1e3).toFixed(2) + 'K';
  } else {
    return num.toFixed(2);
  }
};

/**
 * Format currency with proper USD formatting
 */
export const formatCurrency = (amount: number): string => {
  if (amount === 0) return '$0';
  
  const absAmount = Math.abs(amount);
  
  // For very small amounts, show more precision
  if (absAmount < 0.01) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4,
      maximumFractionDigits: 8
    }).format(amount);
  }
  
  // For small amounts, show cents
  if (absAmount < 1) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 4
    }).format(amount);
  }
  
  // Standard currency formatting
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

/**
 * Format time ago string
 */
export const formatTimeAgo = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - (timestamp * 1000); // Convert to milliseconds
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}d`;
  } else if (hours > 0) {
    return `${hours}h`;
  } else if (minutes > 0) {
    return `${minutes}m`;
  } else {
    return `${Math.max(1, seconds)}s`;
  }
};

/**
 * Format wallet address for display
 */
export const formatWalletAddress = (wallet: string): string => {
  if (!wallet) return 'Unknown';
  if (wallet.length <= 8) return wallet;
  return wallet.slice(0, 4) + '...' + wallet.slice(-4);
};

/**
 * Get priority level based on USD amount
 */
export const getPriorityLevel = (amount: number): 'High' | 'Medium' | 'Low' => {
  if (amount > 10000) return 'High';
  if (amount > 1000) return 'Medium';
  return 'Low';
};

/**
 * Format percentage with proper sign and precision
 */
export const formatPercentage = (value: number): string => {
  const sign = value >= 0 ? '+' : '';
  return `${sign}${value.toFixed(2)}%`;
};

/**
 * Format market cap estimate
 */
export const formatMarketCap = (price: number, supply: number = 1000000): string => {
  const marketCap = price * supply;
  return formatCurrency(marketCap);
};

/**
 * Format SOL amount with appropriate precision
 */
export const formatSOL = (amount: number): string => {
  if (amount === 0) return '0 SOL';
  
  const absAmount = Math.abs(amount);
  
  if (absAmount < 0.001) {
    return `${formatSmallNumber(amount)} SOL`;
  }
  
  if (absAmount < 1) {
    return `${amount.toFixed(4)} SOL`;
  }
  
  return `${amount.toFixed(2)} SOL`;
};

/**
 * Format transaction hash for display
 */
export const formatTxHash = (txHash: string): string => {
  if (!txHash) return 'N/A';
  if (txHash.length <= 16) return txHash;
  return txHash.slice(0, 8) + '...';
};
