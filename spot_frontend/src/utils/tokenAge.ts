/**
 * Token Age Utility Functions
 * Handles live countdown timers and static age display for tokens
 */

export interface TokenAgeInfo {
  displayText: string;
  isLive: boolean; // Whether this should update every second
  ageInSeconds: number;
  ageInMinutes: number;
  ageInHours: number;
  ageInDays: number;
}

/**
 * Calculate token age information from createdAt timestamp
 */
export function calculateTokenAge(createdAt: string | null | undefined): TokenAgeInfo {
  if (!createdAt) {
    return {
      displayText: '',
      isLive: false,
      ageInSeconds: 0,
      ageInMinutes: 0,
      ageInHours: 0,
      ageInDays: 0
    };
  }

  const now = Date.now();
  const createdTime = new Date(createdAt).getTime();
  
  // Handle invalid dates
  if (isNaN(createdTime)) {
    return {
      displayText: '',
      isLive: false,
      ageInSeconds: 0,
      ageInMinutes: 0,
      ageInHours: 0,
      ageInDays: 0
    };
  }

  const ageInMs = now - createdTime;
  const ageInSeconds = Math.max(0, Math.floor(ageInMs / 1000)); // Ensure never negative
  const ageInMinutes = Math.max(0, Math.floor(ageInSeconds / 60));
  const ageInHours = Math.max(0, Math.floor(ageInMinutes / 60));
  const ageInDays = Math.max(0, Math.floor(ageInHours / 24));

  // For tokens less than 1 minute old, show live countdown
  if (ageInSeconds < 60) {
    return {
      displayText: `${ageInSeconds}s`,
      isLive: true,
      ageInSeconds,
      ageInMinutes,
      ageInHours,
      ageInDays
    };
  }

  // For tokens 1-59 minutes old
  if (ageInMinutes < 60) {
    return {
      displayText: `${ageInMinutes}m`,
      isLive: false,
      ageInSeconds,
      ageInMinutes,
      ageInHours,
      ageInDays
    };
  }

  // For tokens 1-23 hours old
  if (ageInHours < 24) {
    return {
      displayText: `${ageInHours}h`,
      isLive: false,
      ageInSeconds,
      ageInMinutes,
      ageInHours,
      ageInDays
    };
  }

  // For tokens 1+ days old
  return {
    displayText: `${ageInDays}d`,
    isLive: false,
    ageInSeconds,
    ageInMinutes,
    ageInHours,
    ageInDays
  };
}

/**
 * Format age display text with appropriate styling classes
 */
export function getAgeDisplayClasses(ageInfo: TokenAgeInfo): string {
  if (!ageInfo.displayText) return '';

  // Live countdown (< 1 minute) - bright green with pulse animation
  if (ageInfo.isLive) {
    return 'text-green-400 font-semibold animate-pulse';
  }

  // Recent tokens (< 5 minutes) - green
  if (ageInfo.ageInMinutes < 5) {
    return 'text-green-400 font-semibold';
  }

  // Moderately new tokens (< 30 minutes) - yellow-green
  if (ageInfo.ageInMinutes < 30) {
    return 'text-yellow-400 font-medium';
  }

  // Older tokens (< 2 hours) - yellow
  if (ageInfo.ageInHours < 2) {
    return 'text-yellow-300 font-medium';
  }

  // Old tokens - gray
  return 'text-gray-400 font-normal';
}

/**
 * Check if a token should have live updates
 */
export function shouldUpdateLive(createdAt: string | null | undefined): boolean {
  if (!createdAt) return false;
  
  const now = Date.now();
  const createdTime = new Date(createdAt).getTime();
  
  if (isNaN(createdTime)) return false;
  
  const ageInMs = now - createdTime;
  const ageInSeconds = Math.max(0, Math.floor(ageInMs / 1000)); // Ensure never negative

  return ageInSeconds < 60; // Only update live for tokens less than 1 minute old
}
