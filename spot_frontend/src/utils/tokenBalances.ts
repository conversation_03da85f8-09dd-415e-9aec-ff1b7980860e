/**
 * Token balance utility functions
 */

export interface TokenBalance {
  symbol: string;
  name: string;
  balance: string;
  decimals: number;
  contractAddress?: string; // For EVM tokens
  mintAddress?: string;     // For Solana tokens
  logo?: string;
}

// Default token logos for fallbacks
const DEFAULT_TOKEN_LOGOS = {
  ETH: 'https://cryptologos.cc/logos/ethereum-eth-logo.svg',
  SOL: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png',
  USDC: 'https://cryptologos.cc/logos/usd-coin-usdc-logo.svg',
};

/**
 * Get real token balances for a given address
 * Currently only supports Solana chains using the getSolanaTokenBalances API
 * 
 * @param address - Wallet address
 * @param chainType - 'evm' or 'solana'
 * @param chainId - Chain ID for EVM chains (e.g., '1' for Ethereum)
 * @returns Promise<TokenBalance[]>
 */
export const getTokenBalances = async (
  address: string,
  chainType: 'evm' | 'solana',
  chainId?: string
): Promise<TokenBalance[]> => {
  if (!address) {
    console.error('No address provided to getTokenBalances');
    return [];
  }
  
  console.log(`Fetching token balances for ${address}, chain type: ${chainType}`);
  
  // For EVM chains (This is a placeholder - real API should be implemented)
  if (chainType === 'evm') {
    console.log(`No real API for EVM token balances yet for ${address} on chain ${chainId}`);
    // Return empty array until EVM API is implemented
    return [];
  } else if (chainType === 'solana') {
    console.log(`For Solana wallets, use the getSolanaTokenBalances API directly in Navbar component`);
    // The Solana token balances are handled directly in the Navbar component
    // using the getSolanaTokenBalances function from getSolToken.ts
    return [];
  } else {
    console.error('Invalid chain type provided to getTokenBalances');
    return [];
  }
};

/**
 * Format token balance with appropriate decimals
 * 
 * @param balance - Raw balance string
 * @param decimals - Token decimals
 * @returns Formatted balance string
 */
export const formatTokenBalance = (balance: string, decimals: number): string => {
  if (!balance) return '0';
  
  try {
    // For mock data, we're assuming the balance is already in human-readable format
    // so we don't need to apply decimals conversion
    const value = parseFloat(balance);
    
    // Format based on size
    if (value < 0.0001 && value > 0) {
      return '< 0.0001';
    } else if (value > 1000000) {
      return `${(value / 1000000).toFixed(2)}M`;
    } else if (value > 1000) {
      return `${(value / 1000).toFixed(2)}K`;
    } else {
      return value.toFixed(4);
    }
  } catch (error) {
    console.error('Error formatting token balance:', error);
    return '0';
  }
};

/**
 * Get chain name from chain ID
 * 
 * @param chainId - Chain ID string
 * @returns Chain name
 */
export const getChainName = (chainId: string): string => {
  const chains: Record<string, string> = {
    '1': 'Ethereum',
    '137': 'Polygon',
    '56': 'BSC',
    '10': 'Optimism',
    '42161': 'Arbitrum',
    '8453': 'Base',
    'solana': 'Solana'
  };
  
  return chains[chainId] || 'Unknown';
};

