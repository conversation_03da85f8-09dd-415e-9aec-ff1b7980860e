import { globalTradeWebSocketService } from '@/services/globalTradeWebSocketService';

/**
 * Development utility functions for debugging WebSocket connections
 * These functions should only be used during development
 */

/**
 * Log current connection state to console
 */
export const logConnectionState = (context: string = 'Debug') => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }
  
  globalTradeWebSocketService.logConnectionState(context);
};

/**
 * Get connection statistics
 */
export const getConnectionStats = () => {
  return globalTradeWebSocketService.getConnectionStats();
};

/**
 * Detect connection leaks
 */
export const detectConnectionLeaks = () => {
  return globalTradeWebSocketService.detectLeaks();
};

/**
 * Force cleanup all connections
 */
export const forceCleanupConnections = () => {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('Force cleanup is only available in development mode');
    return;
  }
  
  globalTradeWebSocketService.forceCleanup();
};

/**
 * Add connection debugging to window object for browser console access
 */
export const addConnectionDebugToWindow = () => {
  if (process.env.NODE_ENV !== 'development' || typeof window === 'undefined') {
    return;
  }

  // Add debug functions to window object
  (window as any).connectionDebug = {
    logState: logConnectionState,
    getStats: getConnectionStats,
    detectLeaks: detectConnectionLeaks,
    forceCleanup: forceCleanupConnections,
    
    // Convenience functions
    status: () => {
      const stats = getConnectionStats();
      const leaks = detectConnectionLeaks();
      
      console.log('📊 Connection Status:', {
        connected: stats.isConnected,
        pools: stats.poolCount,
        subscribers: stats.subscriberCount,
        callbacks: stats.callbackCount,
        hasLeaks: leaks.hasLeaks,
        issues: leaks.issues
      });
      
      return { stats, leaks };
    },
    
    monitor: () => {
      console.log('🔍 Starting connection monitoring...');
      const interval = setInterval(() => {
        const stats = getConnectionStats();
        const leaks = detectConnectionLeaks();
        
        console.log('📊 [MONITOR]', {
          time: new Date().toLocaleTimeString(),
          connected: stats.isConnected,
          pools: stats.poolCount,
          subscribers: stats.subscriberCount,
          hasLeaks: leaks.hasLeaks
        });
        
        if (leaks.hasLeaks) {
          console.warn('⚠️ [MONITOR] Leaks detected:', leaks.issues);
        }
      }, 5000);
      
      console.log('🔍 Monitoring started. Call connectionDebug.stopMonitor() to stop.');
      (window as any).connectionDebug._monitorInterval = interval;
    },
    
    stopMonitor: () => {
      const interval = (window as any).connectionDebug._monitorInterval;
      if (interval) {
        clearInterval(interval);
        delete (window as any).connectionDebug._monitorInterval;
        console.log('🔍 Connection monitoring stopped.');
      }
    },
    
    help: () => {
      console.log(`
🔍 Connection Debug Commands:
  
  connectionDebug.status()        - Show current connection status
  connectionDebug.logState()      - Log detailed connection state
  connectionDebug.getStats()      - Get connection statistics
  connectionDebug.detectLeaks()   - Check for connection leaks
  connectionDebug.forceCleanup()  - Force cleanup all connections
  connectionDebug.monitor()       - Start monitoring (logs every 5s)
  connectionDebug.stopMonitor()   - Stop monitoring
  connectionDebug.help()          - Show this help
      `);
    }
  };

  console.log('🔍 Connection debug tools added to window.connectionDebug');
  console.log('🔍 Type connectionDebug.help() for available commands');
};

/**
 * Monitor WebSocket connections in the browser
 * Logs when new connections are created or closed
 */
export const monitorBrowserWebSockets = () => {
  if (process.env.NODE_ENV !== 'development' || typeof window === 'undefined') {
    return;
  }

  const originalWebSocket = window.WebSocket;
  let connectionCount = 0;

  window.WebSocket = class extends originalWebSocket {
    constructor(url: string | URL, protocols?: string | string[]) {
      super(url, protocols);
      connectionCount++;
      
      console.log(`🔌 [WS MONITOR] New connection #${connectionCount}: ${url}`);
      console.log(`🔌 [WS MONITOR] Total active connections: ${connectionCount}`);
      
      if (connectionCount > 1) {
        console.warn('⚠️ [WS MONITOR] Multiple WebSocket connections detected!');
        console.warn('⚠️ [WS MONITOR] This may indicate a connection pooling issue');
      }

      this.addEventListener('close', () => {
        connectionCount--;
        console.log(`🔌 [WS MONITOR] Connection closed: ${url}`);
        console.log(`🔌 [WS MONITOR] Total active connections: ${connectionCount}`);
      });

      this.addEventListener('error', (error) => {
        console.error(`❌ [WS MONITOR] Connection error: ${url}`, error);
      });
    }
  };

  console.log('🔌 [WS MONITOR] Browser WebSocket monitoring enabled');
  
  // Return cleanup function
  return () => {
    window.WebSocket = originalWebSocket;
    console.log('🔌 [WS MONITOR] Browser WebSocket monitoring disabled');
  };
};

/**
 * Initialize all connection debugging tools
 * Call this once in your app's entry point during development
 */
export const initConnectionDebug = () => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  console.log('🔍 Initializing connection debugging tools...');
  
  // Add debug functions to window
  addConnectionDebugToWindow();
  
  // Monitor browser WebSockets
  const cleanupWebSocketMonitor = monitorBrowserWebSockets();
  
  // Log initial state
  setTimeout(() => {
    logConnectionState('Initial state');
  }, 1000);

  return {
    cleanup: () => {
      if (cleanupWebSocketMonitor) {
        cleanupWebSocketMonitor();
      }
      
      // Clean up window debug object
      if (typeof window !== 'undefined') {
        delete (window as any).connectionDebug;
      }
    }
  };
};

// Auto-initialize in development
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initConnectionDebug);
  } else {
    initConnectionDebug();
  }
}
