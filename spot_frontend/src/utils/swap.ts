import axios from 'axios';
import { quotePancakeswap, quoteRaydium, quoteMeteora, quoteFourMeme } from './quote';
import { ethers } from 'ethers';
import { useSendTransaction } from "@privy-io/react-auth";
import { detectToken<PERSON>hain } from './chainUtils';
import { 
  sendHybridTransaction, 
  sendNodeRealTransaction, 
  sendSwapTransaction,
  logNodeRealDebug,
  updateNodeRealTransactionLog,
  NodeRealTransactionLog,
  SendSwapParams,
  getNodeRealTransactionLogs
} from './bundlerPaymasterService';

// Import Solana dependencies
import { PublicKey, Transaction, VersionedTransaction } from '@solana/web3.js';

// Import the paymaster client
import { getPimlicoPaymasterData } from './paymasterClient';

// Extend the global Window interface to include our properties
declare global {
  interface Window {
    ethereum?: any;
    privy?: {
      recordTransaction?: (txData: {
        hash: string;
        chainId?: number | undefined;
        description?: string | undefined;
        metadata?: any;
      }) => void;
      exportSolana?: (options: any) => any;
    };
    PIMLICO_API_KEY?: string;
    env?: {
      PIMLICO_API_KEY?: string;
      [key: string]: any;
    };
    smartWalletClient?: any; // Add smartWalletClient for NodeReal compatibility
    nodeRealTxLog?: Array<{
      timestamp: string;
      title: string;
      data?: any;
    }>;
  }
}

// Basic token interface
interface TokenBase {
  address?: string;
  tokenAddress?: string;
  contract?: string;
  decimals?: number;
  symbol?: string;
  name?: string;
  chainId?: number | string;
  network?: string;
}

// Response format for swap operations
export interface SwapResponse {
  success: boolean;
  data?: any;
  message?: string;
  source?: string;
  error?: any;
  needsAllowance?: boolean;
}

// Minimal wallet interface
interface WalletBase {
  address?: string;
  connected?: boolean;
  getAddress?: () => string;
  sendTransaction?: (tx: any) => Promise<any>;
  provider?: any;
  ethersProvider?: any;
  embeddedWallet?: any;
  walletClient?: any;
  walletClientType?: string;
  send?: (method: string, params: any[]) => Promise<any>;
  // Add Privy transaction recording capability
  recordTransaction?: (txData: {
    hash: string;
    description: string;
    metadata?: any;
  }) => Promise<void>;
}

// Prepared swap data structure
interface PreparedSwap {
  to: string;
  data: string;
  value?: string;
  gasLimit?: string;
  // Add fields for Privy transaction tracking
  tokenIn?: {
    symbol?: string;
    address?: string;
  };
  tokenOut?: {
    symbol?: string;
    address?: string;
  };
  amountIn?: string;
  amountOut?: string;
  dex?: string;
}

// Token contracts mapping for common tokens on different networks
const TOKEN_CONTRACTS: Record<string, Record<string, string>> = {
  ethereum: {
    'WETH': '******************************************',
    'USDC': '******************************************',
    'USDT': '******************************************'
  },
  bsc: {
    'WBNB': '******************************************',
    'BUSD': '******************************************',
    'USDT': '******************************************'
  },
  // Add other networks as needed
};

// Helper function to get network name from chain ID
const getNetworkName = (chainId: number | string): string => {
  if (chainId === 1 || chainId === '0x1') return 'ethereum';
  if (chainId === 56 || chainId === '0x38') return 'bsc';
  if (chainId === 97 || chainId === '0x61') return 'bsc-testnet';
  if (chainId === 137 || chainId === '0x89') return 'polygon';
  if (chainId === 80001 || chainId === '0x13881') return 'polygon-mumbai';
  if (chainId === 43114 || chainId === '0xa86a') return 'avalanche';
  if (chainId === 10 || chainId === '0xa') return 'optimism';
  if (chainId === 42161 || chainId === '0xa4b1') return 'arbitrum';
  return 'unknown';
};

// Normalize token address based on chain
export const normalizeTokenAddress = (token: TokenBase | string, chainId?: number | string): string | null => {
  let address = '';
  
  if (typeof token === 'string') {
    address = token;
  } else {
    // Try multiple properties where address might be stored
    address = token.address || token.tokenAddress || token.contract || '';
  }
  
  if (!address) return null;
  
  // Solana addresses don't start with 0x
  if (!address.startsWith('0x')) {
    return address; // Return as-is for Solana
  }
  
  // For EVM chains, convert to lowercase
  return address.toLowerCase();
};

// Get the base API URL for liquidity services
function getLiquidityApiBaseUrl() {
  // Use environment variable if set
  const envApiUrl = import.meta.env.VITE_LIQUIDITY_API_URL;
  if (envApiUrl) {
    console.log(`Using liquid API URL from environment: ${envApiUrl}`);
    return envApiUrl;
  }
  
  // Determine based on hostname
  const hostname = window.location.hostname;
  if (hostname.includes('crypfi.io')) {
    return 'https://api.crypfi.io/api';
  } else if (hostname.includes('redfyn.xyz')) {
    return 'https://api.redfyn.xyz/api';
  } else {
    // Default to localhost for development
    return 'http://localhost:3047/api';
  }
}

// Define API base URL for the liquidity pool
const API_BASE_URL = getLiquidityApiBaseUrl();

// Log transaction information to the console and global array
function logTransaction(type: string, hash: string, status: 'success' | 'pending' | 'error', details?: any) {
  const timestamp = new Date().toISOString();
  
  // Log to console
  console.log(`[TX ${timestamp}] ${type} (${status}):`, hash, details || '');
  
  // Add to window transaction log if it exists
  if (!window.txLog) {
    window.txLog = [];
  }
  
  window.txLog.push({
    timestamp,
    type,
    hash,
    status,
    details
  });
}

// Add to Window interface
declare global {
  interface Window {
    txLog?: Array<{
      timestamp: string;
      type: string;
      hash: string;
      status: string;
      details?: any;
    }>;
  }
}

// Export PancakeSwap execution function
export const execPancakeswap = async (
  tokenIn: TokenBase, 
  tokenOut: TokenBase, 
  amount: string, 
  recipient: string, 
  slippageTolerance: number = 0.5,
  platformFee?: {
    recipient: string;
    amount: string;
    token: string;
    percentage: number;
  } | null
): Promise<SwapResponse> => {
  try {
    console.log(`Preparing PancakeSwap swap: ${amount} ${tokenIn.symbol} to ${tokenOut.symbol}, slippage: ${slippageTolerance}%, with platform fee: ${platformFee ? 'yes' : 'no'}`);
    
    // Validate requirements
    if (!tokenIn.address && !tokenIn.tokenAddress) {
      console.error('Missing input token address for PancakeSwap swap');
      return {
        success: false,
        message: 'Missing input token address',
        data: null
      };
    }
    
    if (!tokenOut.address && !tokenOut.tokenAddress) {
      console.error('Missing output token address for PancakeSwap swap');
      return {
        success: false,
        message: 'Missing output token address',
        data: null
      };
    }
    
    // Format token addresses and create token objects
    const tokenInAddress = tokenIn.address || tokenIn.tokenAddress || '';
    const tokenOutAddress = tokenOut.address || tokenOut.tokenAddress || '';
    
    // Prepare the request body
    const requestBody = {
      tokenIn: {
        address: tokenInAddress,
        decimals: tokenIn.decimals,
        symbol: tokenIn.symbol
      },
      tokenOut: {
        address: tokenOutAddress,
        decimals: tokenOut.decimals,
        symbol: tokenOut.symbol
      },
      amount,
      recipient,
      slippageTolerance,
      deadline: Math.floor(Date.now() / 1000) + 60 * 20 // 20 minutes from now
    };

    // Add platform fee to the request if provided
    if (platformFee) {
      console.log('PLATFORM FEE DEBUG: Adding fee to request:', platformFee);
      (requestBody as any).platformFee = platformFee;
      
      // Debug what's in the final request after fee added
      console.log('PLATFORM FEE DEBUG: Full request with fee:', JSON.stringify(requestBody, null, 2));
    }

    console.log('PancakeSwap swap request:', requestBody);
    
    // Add detailed platform fee debugging
    if (platformFee) {
      console.log('PLATFORM FEE DEBUG: Including fee in PancakeSwap request:', {
        recipient: platformFee.recipient,
        amount: platformFee.amount,
        token: platformFee.token,
        percentage: platformFee.percentage
      });
    } else {
      console.log('PLATFORM FEE DEBUG: No platform fee included in request');
    }
    
    // Get API URL
    const apiUrl = getLiquidityApiBaseUrl();
    console.log('Using API URL:', apiUrl);
    
    try {
      // Make API call to prepare the swap
      const response = await axios.post(`${apiUrl}/pancakeswap/prepare-swap`, requestBody);
      
      if (response.data && response.data.success) {
        console.log('PancakeSwap swap prepared successfully:', response.data);
        
        // Add platform fee validation logging - check multiple possible field names
        if (platformFee) {
          const responseFee = response.data.data?.platformFee || response.data.data?.feeData || response.data.data?.fee;
          if (responseFee) {
            console.log('PLATFORM FEE DEBUG: Fee confirmed in API response:', responseFee);
          } else {
            console.warn('PLATFORM FEE DEBUG: Fee was sent but not found in API response!');
            // Add platform fee to the response data if not present
            if (response.data.data) {
              response.data.data.platformFee = platformFee;
            }
          }
        }
        
        return {
          success: true,
          data: response.data.data,
          message: 'Swap prepared successfully',
          source: 'pancakeswap'
        };
      } else {
        console.error('Failed to prepare PancakeSwap swap:', response.data);
        return {
          success: false,
          message: response.data.message || 'Failed to prepare swap',
          source: 'pancakeswap',
          data: null
        };
      }
    } catch (apiError: any) {
      // Special handling for allowance errors
      const errorMessage = apiError instanceof Error ? apiError.message : String(apiError);
      const responseData = apiError?.response?.data;
      
      console.error(`API error (${apiError?.response?.status || 'unknown status'})`, 
        responseData || errorMessage);
      
      if (responseData?.code === 'INSUFFICIENT_ALLOWANCE' || 
          errorMessage.includes('allowance') || 
          errorMessage.includes('approve')) {
        console.log('Detected allowance error:', errorMessage);
        
        return {
          success: false,
          message: responseData?.message || 'Insufficient token allowance',
          needsAllowance: true,
          data: {
            tokenAddress: tokenInAddress,
            spenderAddress: responseData?.data?.spender || '',
            requiredAllowance: responseData?.data?.requiredAllowance || amount
          }
        };
      }
      
      // Check for validation errors
      if (apiError?.response?.status === 400 && responseData?.message) {
        return {
          success: false,
          message: responseData.message,
          source: 'pancakeswap',
          data: null
        };
      }
      
      // General API error
      return {
        success: false,
        message: responseData?.message || `API error preparing swap: ${errorMessage}`,
        source: 'pancakeswap',
        data: null,
        error: apiError
      };
    }
  } catch (error) {
    console.error('Error executing PancakeSwap swap:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error executing swap',
      source: 'pancakeswap',
      data: null
    };
  }
};

// Function to prepare a swap transaction on Uniswap
export const execUniswap = async (
  tokenIn: TokenBase, 
  tokenOut: TokenBase, 
  amount: string, 
  recipient: string, 
  slippageTolerance: number = 0.5,
  deadline: number = 1800 // Default 30 minutes
): Promise<SwapResponse> => {
  try {
    console.log(`Preparing Uniswap swap: ${amount} ${tokenIn.symbol} to ${tokenOut.symbol}`);
    
    // Get chain ID from tokenIn or tokenOut - Uniswap is on Ethereum
    const chainId = tokenIn.chainId || tokenIn.network || tokenOut.chainId || tokenOut.network || 1; // Default to Ethereum
    
    // Normalize token addresses using our helper function
    const tokenInAddress = normalizeTokenAddress(tokenIn, chainId);
    const tokenOutAddress = normalizeTokenAddress(tokenOut, chainId);
    
    // Validate required parameters
    if (!tokenInAddress) {
      console.error("Missing or invalid tokenIn address:", tokenIn);
      return {
        success: false,
        message: "TokenIn must have a valid address",
        data: null
      };
    }
    
    if (!tokenOutAddress) {
      console.error("Missing or invalid tokenOut address:", tokenOut);
      return {
        success: false,
        message: "TokenOut must have a valid address",
        data: null
      };
    }
    
    if (!amount || parseFloat(amount) <= 0) {
      console.error("Invalid amount:", amount);
      return {
        success: false,
        message: "Amount must be a positive number",
        data: null
      };
    }
    
    if (!recipient) {
      console.error("Missing recipient address");
      return {
        success: false,
        message: "Recipient address is required",
        data: null
      };
    }
    
    // Prepare the request body
    const requestBody = {
      tokenIn: {
        address: tokenInAddress,
        decimals: tokenIn.decimals || 18,
        symbol: tokenIn.symbol || "Unknown",
        name: tokenIn.name || tokenIn.symbol || "Unknown Token"
      },
      tokenOut: {
        address: tokenOutAddress,
        decimals: tokenOut.decimals || 18,
        symbol: tokenOut.symbol || "Unknown",
        name: tokenOut.name || tokenOut.symbol || "Unknown Token"
      },
      amount: amount,
      recipient: recipient,
      slippageTolerance: slippageTolerance,
      deadline: deadline,
      protocol: "auto" // Let Uniswap choose the best protocol
    };
    
    console.log('Uniswap prepare-swap request:', requestBody);
    
    // Make the request to the Uniswap API
    const response = await axios.post(`${API_BASE_URL}/uniswap/prepare-swap`, requestBody);
    
    // Check if we got a valid response
    if (response.data && response.data.success && response.data.data) {
      console.log('Uniswap prepare-swap response:', response.data);
      
      // Extract the transaction data from the response
      const swapData = response.data.data.transactions || response.data.data.swapData;
      
      // Return the data needed for transaction signing
      return {
        success: true,
        data: {
          swapData: swapData,
          quote: response.data.data.quote,
          feeData: response.data.data.feeData || response.data.data.platformFee,
          source: 'uniswap'
        }
      };
    } else {
      console.warn('Invalid Uniswap prepare-swap response:', response.data);
      return {
        success: false,
        message: response.data?.message || 'Failed to prepare swap on Uniswap',
        data: null
      };
    }
  } catch (error: unknown) {
    console.error('Error preparing Uniswap swap:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to prepare swap',
      error: error instanceof Error && 'response' in error ? (error as any).response?.data : error,
      data: null
    };
  }
};

// Function to prepare a swap transaction on SushiSwap
export const execSushiSwap = async (
  tokenIn: TokenBase, 
  tokenOut: TokenBase, 
  amount: string, 
  recipient: string, 
  slippageTolerance: number = 0.5
): Promise<SwapResponse> => {
  try {
    console.log(`Preparing SushiSwap swap: ${amount} ${tokenIn.symbol} to ${tokenOut.symbol}`);
    
    // Get chain ID from tokenIn or tokenOut - SushiSwap is on Ethereum primarily
    const chainId = tokenIn.chainId || tokenIn.network || tokenOut.chainId || tokenOut.network || 1; // Default to Ethereum
    
    // Normalize token addresses using our helper function
    const tokenInAddress = normalizeTokenAddress(tokenIn, chainId);
    const tokenOutAddress = normalizeTokenAddress(tokenOut, chainId);
    
    // Validate required parameters
    if (!tokenInAddress) {
      console.error("Missing or invalid tokenIn address:", tokenIn);
      return {
        success: false,
        message: "TokenIn must have a valid address",
        data: null
      };
    }
    
    if (!tokenOutAddress) {
      console.error("Missing or invalid tokenOut address:", tokenOut);
      return {
        success: false,
        message: "TokenOut must have a valid address",
        data: null
      };
    }
    
    if (!amount || parseFloat(amount) <= 0) {
      console.error("Invalid amount:", amount);
      return {
        success: false,
        message: "Amount must be a positive number",
        data: null
      };
    }
    
    if (!recipient) {
      console.error("Missing recipient address");
      return {
        success: false,
        message: "Recipient address is required",
        data: null
      };
    }
    
    // Prepare the request body
    const requestBody = {
      tokenIn: {
        address: tokenInAddress,
        decimals: tokenIn.decimals || 18,
        symbol: tokenIn.symbol || "Unknown",
        name: tokenIn.name || tokenIn.symbol || "Unknown Token"
      },
      tokenOut: {
        address: tokenOutAddress,
        decimals: tokenOut.decimals || 18,
        symbol: tokenOut.symbol || "Unknown",
        name: tokenOut.name || tokenOut.symbol || "Unknown Token"
      },
      amount: amount,
      recipient: recipient,
      slippageTolerance: slippageTolerance
    };
    
    console.log('SushiSwap prepare-swap request:', requestBody);
    
    // Make the request to the SushiSwap API
    const response = await axios.post(`${API_BASE_URL}/sushiswap/prepare-swap`, requestBody);
    
    // Check if we got a valid response
    if (response.data && response.data.success && response.data.data) {
      console.log('SushiSwap prepare-swap response:', response.data);
      
      // Extract the transaction data from the response
      const swapData = response.data.data.swapData;
      
      // Return the data needed for transaction signing
      return {
        success: true,
        data: {
          swapData: swapData,
          quote: response.data.data.quote,
          approvalData: response.data.data.approvalData,
          feeData: response.data.data.feeData || response.data.data.fee,
          source: 'sushiswap'
        }
      };
    } else {
      console.warn('Invalid SushiSwap prepare-swap response:', response.data);
      return {
        success: false,
        message: response.data?.message || 'Failed to prepare swap on SushiSwap',
        data: null
      };
    }
  } catch (error: unknown) {
    console.error('Error preparing SushiSwap swap:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to prepare swap',
      error: error instanceof Error && 'response' in error ? (error as any).response?.data : error,
      data: null
    };
  }
};

// Update the showForceSuccessPopup to use the logger
export function showForceSuccessPopup(txHash: string, tokenIn: any, tokenOut: any, amountIn: string, amountOut: string) {
  // Log the transaction
  logTransaction('Swap', txHash, 'success', {
    tokenIn: tokenIn?.symbol || 'Unknown',
    tokenOut: tokenOut?.symbol || 'Unknown',
    amountIn,
    amountOut
  });
  
  // Remove any existing forced popups
  const existingPopup = document.getElementById('forced-success-popup');
  if (existingPopup) {
    document.body.removeChild(existingPopup);
  }

  // Create container
  const popupContainer = document.createElement('div');
  popupContainer.id = 'forced-success-popup';
  popupContainer.className = 'fixed inset-0 flex items-center justify-center z-[9999] bg-black bg-opacity-80 popup-success-container';
  popupContainer.style.cssText = 'visibility: visible !important; opacity: 1 !important; display: flex !important;';

  // Create popup content
  const explorerUrl = txHash ? `https://bscscan.com/tx/${txHash}` : '#';
  popupContainer.innerHTML = `
    <div class="bg-[#101114] border-2 border-[#14FFA2] rounded-lg p-6 max-w-md w-full mx-4 shadow-2xl animate-fadeIn" style="animation-duration: 0.5s; animation-fill-mode: both;">
      <div class="flex flex-col items-center">
        <div class="w-20 h-20 flex items-center justify-center rounded-full bg-[#214638] mb-4 animate-enhanced-pulse">
          <svg xmlns="http://www.w3.org/2000/svg" class="text-[#14FFA2] w-12 h-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </div>
        
        <h3 class="text-xl font-bold text-white mb-2">Trade Successful!</h3>
        
        <div class="w-full p-4 bg-[#1B1D21] rounded-lg mb-4">
          <div class="flex justify-between items-center mb-2">
            <span class="text-gray-400">You paid</span>
            <span class="text-white font-medium">${amountIn} ${tokenIn?.symbol || ''}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-400">You received</span>
            <span class="text-white font-medium">${amountOut} ${tokenOut?.symbol || ''}</span>
          </div>
        </div>
        
        <a href="${explorerUrl}" target="_blank" rel="noopener noreferrer" class="flex items-center text-[#14FFA2] hover:underline mb-4">
          View transaction 
          <svg class="ml-1 w-3 h-3" viewBox="0 0 20 20" fill="currentColor">
            <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"></path>
            <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5z"></path>
          </svg>
        </a>
        
        <button id="force-close-popup-btn" class="py-3 px-6 bg-[#214638] text-[#14FFA2] rounded-lg font-medium hover:bg-[#2a5b48] transition-colors duration-200 w-full">
          Close
        </button>
      </div>
    </div>
  `;

  // Add to DOM
  document.body.appendChild(popupContainer);

  // Add close handler with delay to allow for rendering
  setTimeout(() => {
    const closeButton = document.getElementById('force-close-popup-btn');
    if (closeButton) {
      closeButton.addEventListener('click', () => {
        const popup = document.getElementById('forced-success-popup');
        if (popup) {
          document.body.removeChild(popup);
        }
      });
    }
  }, 100);

  // Auto-close after 15 seconds
  setTimeout(() => {
    const popup = document.getElementById('forced-success-popup');
    if (popup) {
      document.body.removeChild(popup);
    }
  }, 15000);
}

// Modify execSwap to pass platformFee to sendSwapTransaction
export const execSwap = async (
    preparedSwap: PreparedSwap, 
    privySendTransaction: (tx: any) => Promise<{ hash: string }>, 
    options: { 
      useSmartWalletClient?: boolean;
      platformFee?: {
        recipient: string;
        amount: string;
        token: string;
        percentage: number;
      } | null;
    } = {}
): Promise<SwapResponse> => {
  console.log("Executing swap with prepared data:", preparedSwap);

  if (!preparedSwap || !preparedSwap.to || !preparedSwap.data) {
    console.error("Invalid preparedSwap data");
    return { success: false, message: "Invalid swap data provided." };
  }

  // Ensure privySendTransaction is a function
  if (typeof privySendTransaction !== 'function') {
      console.error("privySendTransaction function is not valid.");
      return { success: false, message: "Wallet transaction function unavailable." };
  }

  try {
    // Construct the transaction object needed by Privy's sendTransaction hook
    const tx = {
        to: preparedSwap.to,
        data: preparedSwap.data,
        value: preparedSwap.value || '0x0', // Ensure value is hex string or 0x0
        gasLimit: preparedSwap.gasLimit, // Optional: Use if provided
    };

    // Remove undefined fields
    if (tx.gasLimit === undefined) delete tx.gasLimit;

    console.log("Sending swap transaction with params:", tx);
    
    // Check if we should use the smart wallet client approach
    if (options.useSmartWalletClient && window.smartWalletClient) {
      // Use sendSwapTransaction for smart wallet clients, which handles NodeReal compatibility
      console.log("Using sendSwapTransaction with smart wallet client");
      
      // Include platformFee in the swapData we pass to sendSwapTransaction
      const swapParams = {
        swapData: {
          ...tx, 
          platformFee: options.platformFee
        },
        smartWalletClient: window.smartWalletClient,
        useSmartWallet: true
      };
      
      try {
        // Call sendSwapTransaction directly
        const txResult = await sendSwapTransaction(swapParams);
        console.log("Smart wallet transaction result:", txResult);
        
        // Return in the format expected by the caller
        return {
          success: true,
          message: "Transaction submitted successfully",
          data: { 
            transactionHash: txResult.hash,
            hash: txResult.hash,
            platformFee: txResult.platformFee || options.platformFee
          },
          source: preparedSwap.dex || 'unknown'
        };
      } catch (error) {
        console.error("Error with smart wallet transaction:", error);
        throw error;
      }
    } else {
      // Execute the swap using wrapped sendTransaction
      const wrappedSendTransaction = async (tx: any): Promise<{ hash: string }> => {
        const result = await privySendTransaction(tx);
        // Normalize the result to { hash: string } format
        if (typeof result === 'string') {
          return { hash: result };
        }
        return result;
      };
      
      // Use the wrapped function
      const txResponse = await execSwap(
        preparedSwap,
        wrappedSendTransaction,
        {
          useSmartWalletClient: false, // We're in the fallback path
          platformFee: options.platformFee
        }
      );
      
      console.log('Transaction sent, response:', txResponse);
      
      // Add detailed platform fee logging
      console.log('PLATFORM FEE DEBUG: Transaction details:', {
        useSmartWallet: options.useSmartWalletClient,
        platformFeeIncluded: !!options.platformFee,
        platformFeeDetails: options.platformFee ? JSON.stringify(options.platformFee) : 'None'
      });
      
      if (!txResponse) {
        return {
          success: false,
          message: 'Transaction was sent but no response was returned',
          needsAllowance: false
        };
      }
      
      // Extract the transaction hash from various possible formats and ensure consistent format
      let transactionHash = '';
      
      if (typeof txResponse === 'string') {
        // If response is just a string hash
        transactionHash = txResponse;
        console.log('Transaction hash is string:', transactionHash);
      } else {
        // Try all possible hash property names with proper type handling
        const txObj = txResponse as any; // Use type assertion for safety
        transactionHash = txObj.hash || txObj.txHash || txObj.transactionHash || '';
        console.log('Transaction hash from object:', transactionHash);
      }
      
      if (!transactionHash) {
        return {
          success: false,
          message: 'Transaction was sent but no transaction hash was returned',
          needsAllowance: false
        };
      }
      
      // Force show success popup with transaction details
      if (transactionHash && preparedSwap.tokenIn && preparedSwap.tokenOut) {
        console.log('Showing forced success popup for transaction:', transactionHash);
        try {
          showForceSuccessPopup(
            transactionHash,
            preparedSwap.tokenIn,
            preparedSwap.tokenOut,
            preparedSwap.amountIn || '0',
            preparedSwap.amountOut || '0'
          );
        } catch (popupError) {
          console.error('Failed to show forced success popup:', popupError);
        }
      }
      
      // Record the transaction for tracking if window.privy is available
      try {
        if (window.privy?.recordTransaction) {
          await window.privy.recordTransaction({
            hash: transactionHash,
            description: `Swap ${preparedSwap.amountIn} ${preparedSwap.tokenIn?.symbol} for ${preparedSwap.tokenOut?.symbol} on ${preparedSwap.dex}`,
            metadata: {
              tokenIn: preparedSwap.tokenIn,
              tokenOut: preparedSwap.tokenOut,
              amountIn: preparedSwap.amountIn,
              dex: preparedSwap.dex
            }
          });
        }
      } catch (recordError) {
        console.warn('Failed to record transaction:', recordError);
        // Continue as the swap was successful
      }
      
      // Return success with transaction details
      return {
        success: true,
        message: "Transaction submitted successfully",
        data: { 
          transactionHash,
          platformFee: options.platformFee 
        },
        source: preparedSwap.dex || 'unknown'
      };
    }
  } catch (error: unknown) {
    console.error("Error sending swap transaction via Privy:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown transaction error";
    
    // Check for user rejection
    if (errorMessage.includes('rejected') || errorMessage.includes('denied')) {
      return { success: false, message: "Transaction rejected by user." };
    }
    
    // Return error details
    return {
      success: false,
      message: errorMessage,
      error: error,
      source: preparedSwap.dex || 'unknown'
    };
  }
};

// Update getRouteForSwap to handle ethereum type safely
async function getRouteForSwap(
  tokenIn: any,
  tokenOut: any,
  amount: string | number,
  slippageTolerance?: number
): Promise<{
  routerAddress: string;
  data: string;
  value: string;
  amountOut?: string;
} | null> {
  try {
    // Prepare the swap data from PancakeSwap API
    const slippage = slippageTolerance || 0.5;
    console.log(`Preparing swap with ${slippage}% slippage tolerance`);
    
    // Get selected address safely
    let recipient = '';
    try {
      if (window.ethereum && typeof window.ethereum.selectedAddress === 'string') {
        recipient = window.ethereum.selectedAddress;
      }
    } catch (e) {
      console.warn('Could not read ethereum.selectedAddress, using empty string', e);
    }
    
    // Use execPancakeswap to get the swap route
    const preparedSwap = await execPancakeswap(
      tokenIn, 
      tokenOut, 
      amount.toString(),
      recipient,
      slippage
    );
    
    // Check if preparation was successful
    if (!preparedSwap.success) {
      console.error('PancakeSwap preparation failed:', preparedSwap.message);
      return null;
    }
    
    // Extract the swap route from the prepared swap
    const swapData = preparedSwap.data || {};
    return {
      routerAddress: swapData.to || '******************************************', // PancakeSwap router
      data: swapData.data || '',
      value: swapData.value || '0',
      amountOut: swapData.amountOut
    };
  } catch (error) {
    console.error('Error getting swap route:', error);
    return null;
  }
}

// Update the TradeResponse interface to include hash and other fields
interface TradeResponse {
  success: boolean;
  transactionHash?: string;
  amountOut?: string;
  error?: string;
  message?: string; // Keep for backward compatibility
  data?: any;
  needsAllowance?: boolean;
}

// Update the completeTradePancakeswap function to work with our hybrid transaction approach
export async function completeTradePancakeswap(
  tokenIn: any,
  tokenOut: any,
  amount: string | number,
  recipient: string,
  sendTransaction: (tx: any) => Promise<string | { hash: string }>,
  options: {
    slippageTolerance?: number,
    pairAddress?: string,
    gasLimit?: number,
    useSmartWalletClient?: boolean,
    smartWalletClient?: any,
    platformFee?: {
      recipient: string;
      amount: string;
      token: string;
      percentage: number;
    } | null;
  } = {}
): Promise<TradeResponse> {
  console.log('completeTradePancakeswap: Using smart wallet client:', !!options.smartWalletClient);
  
  try {
    // Get the optimal route for the swap
    const route = await getRouteForSwap(
      tokenIn,
      tokenOut,
      amount,
      options.slippageTolerance
    );

    if (!route) {
      console.error('No route found for swap');
      return {
        success: false,
        error: "No route found for the requested swap"
      };
    }

    // Log route details
    console.log('PancakeSwap route found:', { 
      routerAddress: route.routerAddress,
      dataLength: route.data?.length || 0,
      value: route.value || '0', 
      amountOut: route.amountOut 
    });

    // Prepare the transaction data
    const txData = {
      to: route.routerAddress,
      data: route.data,
      value: route.value,
      platformFee: options.platformFee
    };

    let txHash;
    
    // Execute the transaction based on wallet type
    if (options.useSmartWalletClient && options.smartWalletClient) {
      console.log('Using smart wallet client for transaction');
      
      try {
        // Use our enhanced sendSwapTransaction function with full hybrid support
        const result = await sendSwapTransaction({
          swapData: txData,
          useSmartWallet: true,
          smartWalletClient: options.smartWalletClient,
          sendTransactionFn: sendTransaction
        });
        
        txHash = result.hash || result;
      } catch (txError: any) {
        console.error('Error in sendSwapTransaction:', txError);
        return {
          success: false,
          error: txError.message || "Transaction failed: " + String(txError)
        };
      }
    } else {
      console.log('Using regular wallet for transaction');
      
      try {
        // Regular wallet - use provided sendTransaction function
        const result = await sendTransaction(txData);
        txHash = typeof result === 'string' ? result : result.hash;
      } catch (txError: any) {
        console.error('Error in regular wallet transaction:', txError);
        return {
          success: false,
          error: txError.message || "Transaction failed: " + String(txError)
        };
      }
    }

    if (!txHash) {
      console.error('No transaction hash returned');
      return {
        success: false,
        error: "Transaction failed: no hash returned"
      };
    }

    console.log('Swap transaction successful:', txHash);
    
    // Record transaction in Privy if available
    if (window.privy?.recordTransaction) {
      try {
        window.privy.recordTransaction({
          hash: txHash,
          chainId: 56, // BSC mainnet
          description: `Swap ${amount} ${tokenIn.symbol} for ${route.amountOut || '?'} ${tokenOut.symbol}`,
          metadata: {
            type: 'swap',
            tokenIn: tokenIn.symbol,
            tokenOut: tokenOut.symbol,
            amountIn: amount.toString(),
            amountOut: route.amountOut || 'unknown'
          }
        });
      } catch (privyError) {
        console.error('Error recording transaction in Privy:', privyError);
        // Non-critical, continue
      }
    }

    return {
      success: true,
      transactionHash: txHash,
      amountOut: route.amountOut
    };
  } catch (error: any) {
    console.error('Error in completeTradePancakeswap:', error);
    return {
      success: false,
      error: error.message || "An unexpected error occurred"
    };
  }
}

/**
 * Complete a trade on Uniswap
 */
export const completeTradeUniswap = async (
  baseToken: TokenBase, 
  quoteToken: TokenBase, 
  amount: string, 
  recipient: string, 
  privySendTransaction: (tx: any) => Promise<{ hash: string }>, // Accept hook function
  // Remove wallet: WalletBase, 
  options: Record<string, any> = {}
): Promise<SwapResponse> => {
   console.log(`Completing Uniswap trade: ${amount} ${quoteToken.symbol} for ${baseToken.symbol}`);
   try {
    // Prepare the swap using execUniswap
    const preparedSwapResponse = await execUniswap(
      quoteToken, 
      baseToken,  
      amount,
      recipient,
      options.slippageTolerance || 0.5,
      options.deadline
    );
    if (!preparedSwapResponse.success || !preparedSwapResponse.data) {
      return { success: false, message: `Failed to prepare Uniswap swap: ${preparedSwapResponse.message}` };
    }

    const preparedSwapData: PreparedSwap = { 
        ...preparedSwapResponse.data, 
        tokenIn: quoteToken,
        tokenOut: baseToken,
        amountIn: amount, 
        dex: 'uniswap' 
    };
    if (options.gasLimit) preparedSwapData.gasLimit = options.gasLimit.toString();
    const chainId = baseToken.chainId || quoteToken.chainId || 1; // Default to Ethereum

    // Execute the swap using privySendTransaction
    const tx = {
        to: preparedSwapData.to,
        data: preparedSwapData.data,
        value: preparedSwapData.value || '0x0',
        gasLimit: preparedSwapData.gasLimit,
        chainId: chainId
    };

    const txResponse = await privySendTransaction(tx);

    // Handle both formats: either txResponse is the hash string itself or an object with hash property
    let txHash: string | undefined;
    
    // Type guards for transaction response handling
    if (typeof txResponse === 'string') {
        txHash = txResponse; // Direct string hash
        console.log("Received direct hash from transaction:", txHash);
    } else if (txResponse && typeof txResponse === 'object') {
        const hash = (txResponse as any).hash;
        if (typeof hash === 'string') {
            txHash = hash;
            console.log("Received object with hash from transaction:", hash);
        } else {
            console.log("Received object without valid hash property");
        }
    } else {
        console.log("Received unexpected response type:", typeof txResponse);
    }
    
    if (!txHash) {
        console.error("Transaction sent but no valid hash received:", txResponse);
        throw new Error("Transaction sent, but hash was missing.");
    }
    
    console.log("Transaction sent, hash:", txHash);
    
    // Record the transaction for tracking if window.privy is available
    try {
      if (window.privy?.recordTransaction) {
        await window.privy.recordTransaction({
          hash: txHash,
          description: `Swap ${amount} ${quoteToken.symbol} for ${baseToken.symbol} on Uniswap`,
          metadata: {
            tokenIn: quoteToken,
            tokenOut: baseToken,
            amountIn: amount,
            dex: 'uniswap'
          }
        });
      }
    } catch (recordError) {
      console.warn('Failed to record transaction:', recordError);
      // Continue as the swap was successful
    }
    
    // Return success with transaction details
    return {
      success: true,
      data: {
        transactionHash: txHash,
        tokenIn: preparedSwapData.tokenIn,
        tokenOut: preparedSwapData.tokenOut,
        amountIn: preparedSwapData.amountIn,
        amountOut: preparedSwapData.amountOut,
        dex: preparedSwapData.dex
      },
      message: "Transaction submitted successfully"
    };

  } catch (error: unknown) {
    console.error("Error completing Uniswap trade:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to complete Uniswap trade",
      data: null
    };
  }
};

/**
 * Complete a trade on SushiSwap
 */
export const completeTradeSushiSwap = async (
  baseToken: TokenBase, 
  quoteToken: TokenBase, 
  amount: string, 
  recipient: string, 
  privySendTransaction: (tx: any) => Promise<{ hash: string }>, // Accept hook function
  // Remove wallet: WalletBase, 
  options: Record<string, any> = {}
): Promise<SwapResponse> => {
  console.log(`Completing SushiSwap trade: ${amount} ${quoteToken.symbol} for ${baseToken.symbol}`);
   try {
    // Prepare swap using execSushiSwap
    const preparedSwapResponse = await execSushiSwap(
      quoteToken, 
      baseToken, 
      amount,
      recipient,
      options.slippageTolerance || 0.5
    );
    if (!preparedSwapResponse.success || !preparedSwapResponse.data) {
       return { success: false, message: `Failed to prepare SushiSwap swap: ${preparedSwapResponse.message}` };
    }

     const preparedSwapData: PreparedSwap = { 
        ...preparedSwapResponse.data, 
        tokenIn: quoteToken,
        tokenOut: baseToken,
        amountIn: amount,
        dex: 'sushiswap' 
    };
     if (options.gasLimit) preparedSwapData.gasLimit = options.gasLimit.toString();
     const chainId = baseToken.chainId || quoteToken.chainId || 1; // Default to Ethereum

    // Execute swap using privySendTransaction
    const tx = {
        to: preparedSwapData.to,
        data: preparedSwapData.data,
        value: preparedSwapData.value || '0x0',
        gasLimit: preparedSwapData.gasLimit,
        chainId: chainId
    };

    const txResponse = await privySendTransaction(tx);

    // Handle both formats: either txResponse is the hash string itself or an object with hash property
    let txHash: string | undefined;
    
    // Type guards for transaction response handling
    if (typeof txResponse === 'string') {
        txHash = txResponse; // Direct string hash
        console.log("Received direct hash from transaction:", txHash);
    } else if (txResponse && typeof txResponse === 'object') {
        const hash = (txResponse as any).hash;
        if (typeof hash === 'string') {
            txHash = hash;
            console.log("Received object with hash from transaction:", hash);
        } else {
            console.log("Received object without valid hash property");
        }
    } else {
        console.log("Received unexpected response type:", typeof txResponse);
    }
    
    if (!txHash) {
        console.error("Transaction sent but no valid hash received:", txResponse);
        throw new Error("Transaction sent, but hash was missing.");
    }
    
    console.log("Transaction sent, hash:", txHash);
    
    // Record the transaction for tracking if window.privy is available
    try {
      if (window.privy?.recordTransaction) {
        await window.privy.recordTransaction({
          hash: txHash,
          description: `Swap ${amount} ${quoteToken.symbol} for ${baseToken.symbol} on SushiSwap`,
          metadata: {
            tokenIn: quoteToken,
            tokenOut: baseToken,
            amountIn: amount,
            dex: 'sushiswap'
          }
        });
      }
    } catch (recordError) {
      console.warn('Failed to record transaction:', recordError);
      // Continue as the swap was successful
    }
    
    // Show the success popup with transaction details
    if (txHash) {
      console.log('Showing success popup for completeTradeSushiSwap:', txHash);
      try {
        showForceSuccessPopup(
          txHash,
          quoteToken,
          baseToken,
          amount,
          preparedSwapResponse.data.amountOut?.toString() || '0'
        );
      } catch (popupError) {
        console.error('Failed to show success popup in completeTradeSushiSwap:', popupError);
      }
    }
    
    return {
      success: true,
      data: {
        transactionHash: txHash,
        tokenIn: preparedSwapData.tokenIn,
        tokenOut: preparedSwapData.tokenOut,
        amountIn: preparedSwapData.amountIn,
        amountOut: preparedSwapData.amountOut,
        dex: preparedSwapData.dex
      },
      message: "Transaction submitted successfully"
    };

  } catch (error: unknown) {
    console.error("Error completing SushiSwap trade:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to complete SushiSwap trade",
      data: null
    };
  }
}; 

/**
 * Function to prepare a swap transaction on Raydium (Solana)
 */
export const execRaydium = async (
  tokenIn: TokenBase, 
  tokenOut: TokenBase, 
  amount: string, 
  recipient: string, 
  slippageTolerance: number = 0.5
): Promise<SwapResponse> => {
  try {
    console.log(`Preparing Raydium swap: ${amount} ${tokenIn.symbol} to ${tokenOut.symbol}`);
    
    // Validate requirements
    if (!tokenIn.address || !tokenOut.address) {
      console.error('Missing token address(es) for Raydium swap');
      return {
        success: false,
        message: 'Missing token address(es)',
        data: null
      };
    }
    
    // Verify this is for Solana chain
    const tokenChain = detectTokenChain(tokenIn);
    if (tokenChain !== 'solana') {
      console.error('Raydium is only available for Solana tokens');
      return {
        success: false,
        message: 'Raydium is only available for Solana tokens',
        data: null
      };
    }
    
    // Normalize amount to a decimal string
    const normalizedAmount = amount.replace(/,/g, '');
    
    // Prepare API request
    const requestBody = {
      tokenIn: {
        address: tokenIn.address,
        decimals: tokenIn.decimals || 9,
        symbol: tokenIn.symbol || 'Unknown',
        name: tokenIn.name || tokenIn.symbol || 'Unknown Token'
      },
      tokenOut: {
        address: tokenOut.address,
        decimals: tokenOut.decimals || 9,
        symbol: tokenOut.symbol || 'Unknown',
        name: tokenOut.name || tokenOut.symbol || 'Unknown Token'
      },
      amount: normalizedAmount,
      recipient: recipient, // Recipient Solana address
      slippageTolerance: slippageTolerance
    };
    
    // Make API call to prepare the swap
    const response = await axios.post(`${API_BASE_URL}/raydium/prepare-swap`, requestBody);
    
    if (response.data && response.data.success) {
      console.log('Raydium swap prepared successfully:', response.data);
      return {
        success: true,
        data: response.data.data,
        message: 'Swap prepared successfully',
        source: 'raydium'
      };
    } else {
      console.warn('Failed to prepare Raydium swap:', response.data?.message || 'Unknown error');
      return {
        success: false,
        message: response.data?.message || 'Failed to prepare swap',
        data: null
      };
    }
  } catch (error) {
    console.error('Error preparing Raydium swap:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to prepare swap',
      data: null
    };
  }
};

/**
 * Execute a Solana transaction for Raydium/Meteora with Privy
 */
export const execSolanaSwap = async (
  preparedSwap: any,
  privySendTransaction: (tx: any) => Promise<{ hash: string }>,
  options: { chainId?: number | string } = {}
): Promise<SwapResponse> => {
  try {
    console.log('Executing Solana swap transaction...');
    
    // Extract transaction from the prepared swap
    const { transaction, signers = [] } = preparedSwap;
    
    // Check if we have a transaction
    if (!transaction) {
      return {
        success: false,
        message: 'Invalid transaction data',
        data: null
      };
    }
    
    // For Solana, privySendTransaction works a bit differently
    // It expects either a base64 encoded transaction or a Transaction object
    let txToSend;
    
    // We could receive different formats from backend
    if (typeof transaction === 'string') {
      // If it's a base64 string, we can send it directly
      txToSend = { transaction };
    } else if (transaction instanceof Transaction || transaction instanceof VersionedTransaction) {
      // If it's already a Transaction object
      txToSend = { transaction };
    } else if (transaction.data) {
      // If it's a transaction data object
      txToSend = { transaction: transaction.data };
    } else {
      return {
        success: false,
        message: 'Invalid transaction format',
        data: null
      };
    }
    
    // Send the transaction using Privy's sendTransaction
    const { hash } = await privySendTransaction(txToSend);
    
    // Record the transaction for tracking if available
    try {
      if (window.privy?.recordTransaction) {
        await window.privy.recordTransaction({
          hash,
          description: `Swap ${preparedSwap.amountIn} ${preparedSwap.tokenIn?.symbol} for ${preparedSwap.tokenOut?.symbol} on ${preparedSwap.dex}`,
          metadata: {
            tokenIn: preparedSwap.tokenIn,
            tokenOut: preparedSwap.tokenOut,
            amountIn: preparedSwap.amountIn,
            dex: preparedSwap.dex
          }
        });
      }
    } catch (recordError) {
      console.warn('Failed to record transaction:', recordError);
      // Continue anyway as the swap is completed
    }
    
    return {
      success: true,
      data: { hash },
      message: 'Transaction submitted successfully'
    };
  } catch (error) {
    console.error('Error executing Solana swap:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to execute swap',
      data: null
    };
  }
};

/**
 * Complete a trade on Raydium (Solana)
 */
export const completeTradeRaydium = async (
  baseToken: TokenBase, 
  quoteToken: TokenBase, 
  amount: string, 
  recipient: string, 
  privySendTransaction: (tx: any) => Promise<{ hash: string }>,
  options: Record<string, any> = {}
): Promise<SwapResponse> => {
  console.log(`Completing Raydium trade: ${amount} ${quoteToken.symbol} for ${baseToken.symbol}`);
  try {
    // First, get a quote to validate the swap is possible
    const quoteResponse = await quoteRaydium(quoteToken, baseToken, amount);
    
    if (!quoteResponse.success) {
      return {
        success: false,
        message: `Failed to get Raydium quote: ${quoteResponse.message}`,
        data: null
      };
    }
    
    // Prepare the swap transaction
    const preparedSwapResponse = await execRaydium(
      quoteToken, 
      baseToken, 
      amount,
      recipient,
      options.slippageTolerance || 0.5
    );
    
    if (!preparedSwapResponse.success || !preparedSwapResponse.data) {
      return {
        success: false,
        message: `Failed to prepare Raydium swap: ${preparedSwapResponse.message}`,
        data: null
      };
    }
    
    // Add metadata for transaction recording
    const preparedSwapData = {
      ...preparedSwapResponse.data,
      tokenIn: quoteToken,
      tokenOut: baseToken,
      amountIn: amount,
      dex: 'raydium'
    };
    
    // Execute the Solana swap (different from EVM swaps)
    return await execSolanaSwap(preparedSwapData, privySendTransaction);
  } catch (error) {
    console.error('Error completing Raydium trade:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to complete Raydium trade',
      data: null
    };
  }
};

// Update extractTransactionHash to also log platform fee information
function extractTransactionHash(data: any): string {
  if (!data) return '';
  
  // Check all possible locations for the hash
  if (typeof data === 'string') return data;
  
  // Log platform fee information for debugging - check multiple field names
  const feesInfo = data.platformFee || data.feeData || data.fee;
  if (feesInfo) {
    console.log('PLATFORM FEE DEBUG: Fee found in response data:', feesInfo);
  } else {
    console.log('PLATFORM FEE DEBUG: No fee found in response data');
    // Log the full response structure for debugging
    console.log('PLATFORM FEE DEBUG: Response structure:', Object.keys(data));
  }
  
  if (data.hash) return data.hash;
  if (data.txHash) return data.txHash; 
  if (data.transactionHash) return data.transactionHash;
  if (data.transaction?.hash) return data.transaction.hash;
  if (data.transaction?.transactionHash) return data.transaction.transactionHash;
  
  // Return empty string if no hash found
  return '';
}