import React, { useState } from "react";
import Spot from "./Spot";
import Overview from "./Overview";

const Tabs = () => {
  const [activeTab, setActiveTab] = useState<"Overview" | "Spot" | "Perpetuals">("Spot");

  const renderContent = () => {
    switch (activeTab) {
      case "Overview":
        return <Overview />;
      case "Spot":
        return <Spot />;
      case "Perpetuals":
        return <div className="mt-4 text-white">Perpetuals content goes here</div>;
      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col bg-[#141416] p-4">
      <div className="flex space-x-6 pb-2">
        {["Overview", "Spot", "Perpetuals"].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab as "Overview" | "Spot" | "Perpetuals")}
            className={`text-lg font-medium pb-1 transition-colors ${
              activeTab === tab
                ? "text-white"
                : "text-[#BBBBBB] hover:text-white"
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      <div className="flex-1 h-full overflow-auto">{renderContent()}</div>

    </div>
  );
};

export default Tabs;
