import React from "react";
import SparklineChart from "./Sparkline";

const gainers = [
  {
    name: "AERO-USD",
    price: 0.5911,
    change: "+28.06%",
    data: [0.3, 0.4, 0.35, 0.5, 0.6, 0.59],
  },
  // Add more as needed
];

const losers = [
  {
    name: "XYZ-USD",
    price: 0.4123,
    change: "-13.56%",
    data: [0.5, 0.47, 0.45, 0.44, 0.42, 0.41],
  },
  // Add more as needed
];

const Overview = () => {
  return (
    <div className="h-[50rem] flex flex-col p-4 bg-[#141416] text-white">
    <div className="grid grid-cols-1 xl:grid-cols-8 gap-4 flex-1 ">
      {/* Portfolio Overview Panel */}
      <div className="xl:col-span-6 bg-[#181C20] rounded-lg p-6 flex flex-col justify-between">
        <div className="mb-6">
          <h2 className="text-xl font-semibold">Portfolio Overview</h2>
          <div className="mt-2">
            <div className="text-sm text-gray-400">Equity</div>
            <div className="flex justify-between items-center w-full mt-1">
              <div className="text-3xl font-bold">
                0.00 <span className="text-green-500 text-sm">+0.00%</span>
              </div>
              <div className="flex gap-3 items-center text-sm font-medium">
                {["24H", "7D", "30D", "ALL"].map((r, i) => (
                  <button
                    key={r}
                    className={`px-2 py-1 rounded ${
                      i === 0
                        ? "bg-[#BBBBBB] text-[#181C20] font-semibold"
                        : "hover:bg-gray-700"
                    }`}
                  >
                    {r}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="flex-grow flex items-center justify-center">
          <div className="text-center text-gray-500">
            <div className="text-6xl mb-2">👻</div>
            <div className="text-lg">No Data Available</div>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-6 gap-4 mt-6">
          {["Available Margin", "Live PnL", "Realized PnL", "Profit Factor", "Win Rate", "Win Rate"].map((label) => (
            <div
              key={label}
              className="bg-[#1D2226] rounded-lg p-3 text-center h-[6rem]"
            >
              <div className="text-sm text-[#BBBBBB]">{label}</div>
              <div className="text-lg font-medium">---</div>
            </div>
          ))}
        </div>
      </div>

      {/* Watchlist Panel */}
      <div className="xl:col-span-2 bg-[#181C20] rounded-lg p-4 space-y-6">
        <div>
          <h2 className="text-lg font-semibold mb-1">Watchlist</h2>
          <div className="text-gray-500 h-[5rem] text-center items-center">-</div>
        </div>

        {/* Top Gainers */}
        <div>
  <h3 className="text-md font-semibold mb-2">Top Gainers</h3>
  <div className="space-y-3">
    {gainers.map((coin, i) => (
      <div
        key={i}
        className="flex justify-between items-center gap-4"
      >
        {/* Left: Coin info */}
        <div className="flex items-center gap-2 min-w-[100px]">
          <div className="w-6 h-6 bg-gray-500 rounded-full" />
          <div className="text-sm">{coin.name}</div>
        </div>

        {/* Center: Sparkline */}
        <div className="flex-grow flex justify-center">
          <div className="w-24 h-8">
            <SparklineChart dataPoints={coin.data} color="#14FFA2" />
          </div>
        </div>

        {/* Right: Price info */}
        <div className="text-right min-w-[80px]">
          <div className="text-sm">${coin.price.toFixed(4)}</div>
          <div className="text-[#14FFA2] text-xs">{coin.change}</div>
        </div>
      </div>
    ))}
  </div>
</div>


        {/* Top Losers */}
        <div>
  <h3 className="text-md font-semibold mb-2">Top Gainers</h3>
  <div className="space-y-3">
    {losers.map((coin, i) => (
      <div
        key={i}
        className="flex justify-between items-center gap-4"
      >
        {/* Left: Coin info */}
        <div className="flex items-center gap-2 min-w-[100px]">
          <div className="w-6 h-6 bg-gray-500 rounded-full" />
          <div className="text-sm">{coin.name}</div>
        </div>

        {/* Center: Sparkline */}
        <div className="flex-grow flex justify-center">
          <div className="w-24 h-8">
            <SparklineChart dataPoints={coin.data} color="#FF329B" />
          </div>
        </div>

        {/* Right: Price info */}
        <div className="text-right min-w-[80px]">
          <div className="text-sm">${coin.price.toFixed(4)}</div>
          <div className="text-[#FF329B] text-xs">{coin.change}</div>
        </div>
      </div>
    ))}
  </div>
</div>

      </div>
    </div>
    </div>
  );
};

export default Overview;
