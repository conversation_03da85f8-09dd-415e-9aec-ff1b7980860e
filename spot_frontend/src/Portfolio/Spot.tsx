import React from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  LineElement,
  CategoryScale,
  LinearScale,
  PointElement,
} from "chart.js";

ChartJS.register(LineElement, CategoryScale, LinearScale, PointElement);

const dummyChartData = {
  labels: Array(10).fill(""),
  datasets: [
    {
      data: Array(10).fill(0),
      borderColor: "#7FFFD4",
      borderWidth: 1.5,
      tension: 0.4,
      pointRadius: 0,
    },
  ],
};

const PortfolioDashboard = () => {
  return (
    <div className="h-[60rem] bg-[#141416] text-white p-4 ">
      {/* Top Row: Balance and PNL */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-2">

        <div className="bg-[#181C20] rounded-lg p-4 col-span-1">
          <h2 className="text-md text-white pb-6">Balance</h2>
          <div className="text-xs text-[#BBBBBB]">Total Value</div>
          <div className="text-2xl font-semibold">$0</div>
          <div className="text-xs text-[#BBBBBB] mt-4">Unrealized PNL</div>
          <div className="text-xl font-medium">$0</div>
          <hr className="my-3 border-gray-600" />
          <div className="text-xs text-[#BBBBBB]">Available Balance</div>
          <div className="text-lg font-semibold">$0</div>
        </div>

        {/* PNL Chart */}
        <div className="bg-[#181C20] rounded-lg p-4 col-span-2">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-md text-white">PNL</h2>
            <div className="flex gap-2 text-xs">
              {["24H", "7D", "30D", "ALL"].map((label, idx) => (
                <button
                  key={label}
                  className={`px-2 py-1 rounded ${
                    idx === 0
                      ? "bg-gray-300 text-black"
                      : "bg-[#181818] border border-gray-600"
                  }`}
                >
                  {label}
                </button>
              ))}
            </div>
          </div>
          <Line
            data={dummyChartData}
            options={{
              responsive: true,
              plugins: { legend: { display: false } },
              scales: { x: { display: false }, y: { display: false } },
            }}
            height={100}
          />
        </div>
      </div>

      {/* Bottom Row: Left Side Cards + Asset Table */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-2 mt-2 lg:mt-2">

        <div className="space-y-2 col-span-1">
          {/* Performance History */}
          <div className="bg-[#181C20] rounded-lg p-4">
            <h3 className="text-md text-white mb-4">PERFORMANCE HISTORY</h3>
            <div className="flex justify-between text-sm text-gray-400">
            <div className="text-xs text-[#BBBBBB]">Volume</div>
            <div className="mb-2">-</div>
            </div>
            <div className="flex justify-between text-sm text-gray-400">
            <div className="text-xs text-[#BBBBBB]">Realized P&L</div>
            <div className="mb-2">-</div>
            </div>
            <div className="flex justify-between text-sm text-gray-400">
            <div className="text-xs text-[#BBBBBB]">Realized ROI</div>
            <div>-</div>
            </div>
          </div>

          {/* Filtered Totals */}
          <div className="bg-[#181C20] rounded-lg p-4">
  <h3 className="text-md text-white mb-4">FILTERED TOTALS</h3>

  <div className="flex justify-between text-sm text-gray-400">
    <span>Value</span>
    <span className="text-white">$0</span>
  </div>
  <div className="flex justify-between text-sm text-gray-400">
    <span>Cost</span>
    <span className="text-white">$0</span>
  </div>
  <div className="flex justify-between text-sm text-gray-400">
    <span>Unrealized P&L</span>
    <span className="text-white">0</span>
  </div>
  <div className="flex justify-between text-sm text-gray-400">
    <span>Unrealized ROI</span>
    <span className="text-white">0%</span>
  </div>
</div>

        </div>

        {/* Asset Table */}
        <div className="bg-[#181C20] rounded-lg p-4 col-span-2">
          <div className="flex justify-between items-center mb-2">
            <div className="flex gap-1 text-xs font-medium">
              <button className="bg-gray-300 text-black px-3 py-1 rounded">
                Assets
              </button>
              <button className="bg-[#181818] border border-gray-600 px-3 py-1 rounded text-gray-400">
                Trades
              </button>
            </div>
            <div className="flex gap-2">
              <button className="border border-gray-600 px-3 py-1 rounded text-xs">
                Filters
              </button>
              <button className="border border-gray-600 px-3 py-1 rounded text-xs">
                All Networks
              </button>
            </div>
          </div>
          <div className="grid grid-cols-5 text-gray-400 text-sm border-t border-gray-700 pt-2">
            <div>Coin</div>
            <div>Price 24h</div>
            <div>Cost</div>
            <div>Value</div>
            <div>Unrealized P&L</div>
          </div>
          <div className="text-center text-[#BBBBBB] py-4">No Assets</div>
        </div>
      </div>
    </div>
  );
};

export default PortfolioDashboard;
