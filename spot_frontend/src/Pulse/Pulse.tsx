import { useState, useEffect } from 'react';
import PulseDash from './Pulse/Pulse';
import CryptoDashboard from './Pulse/Tab';
import Navbar from '@/Home/Navbar/Navbar';
import { activityService } from '@/services/activityService';
import { Search, SlidersHorizontal } from 'lucide-react';
import { ChevronDown } from 'lucide-react';
import Display from './Pulse/Options/Display';
import QuickBuy from './Pulse/Options/QuickBuy';
import TradingSettingsModal from './Pulse/TradingSettiingsModal';
const tabs = ["Pulse", "New", "Trending", "Pre-Launch"];
type SettingsType = {
  greyButtons: boolean;
  circleImages: boolean;
  progressBar: boolean;
  compactTables: boolean;
  // add any other keys that you have here too
};
type CustomRows ={
  marketCap: boolean;
  volume: boolean;
  tx: boolean;
  socials: boolean;
  holders: boolean;
  proTraders: boolean;
  devMigrations: boolean;
}
const Pulse = () => {
  const [activeTab, setActiveTab] = useState("Pulse");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMetric, setSelectedMetric] = useState("MarketCap");
  const [selectedTime, setSelectedTime] = useState("24h");
  const [isOpen2, setIsOpen2] = useState(false);
  
  const metrics = ["MarketCap", "Volume", "Tx"]; // Example metrics
  const timeRanges = ["24h", "7d", "30d"]; // Example time options
  useEffect(() => {
    const session = activityService.getOrCreateSession();
    console.log('Main Pulse component mounted with session:', session);
    activityService.registerPulseActivity();
    return () => {
      activityService.unregisterPulseActivity();
    };
  }, []);
// Display + QuickBuy settings
const [showDropdown, setShowDropdown] = useState(false);
// const [metricsSize, setMetricsSize] = useState('small');
// const [quickBuySize, setQuickBuySize] = useState('small');
const handlePresetClick = () => setModalOpen(true);
const handleAmountChange = (value: string) => setAmount(value);

const [settings, setSettings] = useState<SettingsType>(() => {
  const saved = localStorage.getItem('display_settings');
  return saved ? JSON.parse(saved) : {
    greyButtons: false,
    circleImages: false,
    progressBar: false,
    compactTables: false,
  };
});

const [customRows, setCustomRows] = useState<CustomRows>(() => {
  const saved = localStorage.getItem('custom_rows');
  return saved ? JSON.parse(saved) : {
    marketCap: true,
    volume: true,
    tx: true,
    socials: true,
    holders: true,
    proTraders: true,
    devMigrations: true,
  };
});

const [metricsSize, setMetricsSize] = useState(() => {
  return localStorage.getItem('metrics_size') || 'large';
});

const [quickBuySize, setQuickBuySize] = useState(() => {
  return localStorage.getItem('quick_buy_size') || 'small';
});


const [amount, setAmount] = useState(""); // Entered amount
const [modalOpen, setModalOpen] = useState(false);
const [preset, setPreset] = useState(3);

  return (
    <div className="h-screen overflow-auto bg-[#141416]">
      <Navbar />

      {/* Tabs + Conditional Search UI */}
      <div className="flex justify-between items-center bg-[#141416] text-white font-medium text-lg pt-6 px-4 mb-4">
        <div className="flex gap-6">
          {tabs.map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`relative pb-1 hover:text-white transition ${
                activeTab === tab ? "text-white" : "text-gray-400"
              }`}
            >
              {tab}
              {activeTab === tab && (
                <span className="absolute left-0 bottom-0 w-full h-[2px] bg-white rounded" />
              )}
            </button>
          ))}
        </div>

        {/* Only show on "New" or "Pre-Launch" */}
        {(activeTab === "New" || activeTab === "Pre-Launch") && (
          <div className="flex gap-4 items-center p-3">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={16} className="bg-[#181C20] text-[#BBBBBB]" />
              </div>
              <input
                type="text"
                placeholder="Filter by name..."
                className="bg-[#181C20] text-white rounded-full pl-10 pr-4 py-2 text-sm w-60 focus:outline-none border-2 border-[#BBBBBB]"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <button className="p-2 rounded-lg bg-[#1E1E20] text-gray-300">
              <SlidersHorizontal size={18} />
            </button>
          </div>
        )}
        {activeTab === "Trending" && (
  <div className="flex justify-end items-center  p-3 text-white rounded-xl">
    <div className="flex space-x-4 items-center">
      {/* Metric Toggle Buttons */}
      <div className="flex space-x-3">
        {metrics.map((metric) => (
          <button
            key={metric}
            onClick={() => setSelectedMetric(metric)}
            className={`px-4 py-2 border-2 rounded-lg text-sm transition ${
              selectedMetric === metric
                ? 'bg-white text-[#141416] border-white'
                : 'bg-transparent border-[#BBBBBB4D] text-white'
            }`}
          >
            {metric}
          </button>
        ))}
      </div>

      {/* Time Range Dropdown */}
      <div className="relative w-28">
        <button
          onClick={() => setIsOpen2(!isOpen2)}
          className="flex items-center justify-between bg-[#181C20] w-full text-gray-300 rounded-lg px-4 py-2 text-sm border-2 border-[#BBBBBB]"
        >
          <span>{selectedTime}</span>
          <ChevronDown size={16} />
        </button>

        {isOpen2 && (
          <div className="absolute top-full mt-2 w-full bg-gray-800 text-white rounded-xl shadow-lg z-50">
            {timeRanges.map((time) => (
              <div
                key={time}
                onClick={() => {
                  setSelectedTime(time);
                  setIsOpen2(false);
                }}
                className={`px-4 py-2 cursor-pointer hover:bg-gray-700 rounded-lg ${
                  selectedTime === time ? 'bg-gray-700' : ''
                }`}
              >
                {time}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Settings Icon */}
      <button className="bg-transparent hover:bg-[#1E1F25] p-2 rounded-lg">
        <SlidersHorizontal className="w-5 h-5 text-white" />
      </button>
    </div>
  </div>
)}
{activeTab === "Pulse" && (
  <div className="flex items-center justify-end gap-4 p-3">

    <Display
      metricsSize={metricsSize}
      setMetricsSize={setMetricsSize}
      showDropdown={showDropdown}
      setShowDropdown={setShowDropdown}
      quickBuySize={quickBuySize}
      setQuickBuySize={setQuickBuySize}
      settings={settings}
      setSettings={setSettings}
      customRows={customRows}
      setCustomRows={setCustomRows}
    />

    <div>
      <QuickBuy
        onAmountChange={handleAmountChange}
        selectedPreset={preset}
        onPresetClick={handlePresetClick}
      />
      {modalOpen && (
        <TradingSettingsModal
          visible={modalOpen}
          onClose={() => setModalOpen(false)}
          selectedPreset={preset}
          onPresetChange={setPreset}
        />
      )}
    </div>
  </div>
)}


        
      </div>

      {activeTab === "Pulse" ? (
      <PulseDash
      settings={settings}
      customRows={customRows}
      metricsSize={metricsSize}
      quickBuySize={quickBuySize}
      amount={amount}
      preset={preset}
      setPreset={setPreset}
      onPresetClick={handlePresetClick}
    />
    
      ) : (
        <CryptoDashboard activeTab={activeTab} searchTerm={searchTerm} />
      )}
    </div>
  );
};

export default Pulse;
