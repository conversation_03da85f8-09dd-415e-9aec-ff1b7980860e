

type FooterProps = {
  preset: number;
  setPreset: (preset: number) => void;
  onPresetClick: () => void;
};

export default function Footer({ preset, setPreset: _setPreset, onPresetClick }: FooterProps) {
  
  return (
    <div className="bg-[#0d0e10] text-gray-300 w-full h-10 px-4 flex items-center justify-between border-t border-gray-800">
      {/* Left Section */}
      <div className="flex items-center space-x-4">
        {/* Preset */}
        <div className="flex items-center space-x-1">
          <button  onClick={onPresetClick} className="bg-indigo-600/80 hover:bg-indigo-600 text-xs font-medium py-1 px-2 rounded flex items-center">
            <svg viewBox="0 0 24 24" width="14" height="14" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
              <path d="M12 20h9"></path>
              <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
            </svg>
            <span>PRESET {preset}</span>
          </button>
        </div>
        
        {/* PnL Tracker */}
        <div className="flex items-center space-x-1 border-l border-gray-700 pl-4">
          <svg viewBox="0 0 24 24" width="14" height="14" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round">
            <line x1="12" y1="20" x2="12" y2="10"></line>
            <line x1="18" y1="20" x2="18" y2="4"></line>
            <line x1="6" y1="20" x2="6" y2="16"></line>
          </svg>
          <span className="text-xs font-medium">PnL Tracker</span>
        </div>
      </div>
      
      {/* Right Section */}
      <div className="flex items-center space-x-4">
        {/* Twitter */}
        <button className="text-gray-400 hover:text-gray-200 transition-colors">
          <svg viewBox="0 0 24 24" width="18" height="18" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round">
            <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
          </svg>
        </button>
        
        {/* Discord */}
        <button className="text-gray-400 hover:text-gray-200 transition-colors">
          <svg viewBox="0 0 24 24" width="18" height="18" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <path d="M18.9327 4.93652C17.6012 4.32861 16.1645 3.8784 14.6525 3.60607C14.4361 4.01117 14.1803 4.56384 14.0033 5.00034C12.4018 4.74679 10.81 4.74679 9.23726 5.00034C9.06023 4.56384 8.79967 4.01117 8.58182 3.60607C7.06833 3.8784 5.63015 4.32957 4.29871 4.93749C1.84336 8.84131 1.12766 12.6423 1.48732 16.3841C3.25335 17.7144 4.96877 18.5571 6.65731 19.1037C7.07316 18.5239 7.44268 17.9057 7.7607 17.2508C7.1909 17.0305 6.65007 16.7618 6.14373 16.4428C6.32065 16.3132 6.49272 16.1779 6.65875 16.0378C9.89021 17.5565 13.3739 17.5565 16.5638 16.0378C16.7308 16.1779 16.9029 16.3132 17.0788 16.4428C16.5715 16.7628 16.0297 17.0315 15.4589 17.2518C15.7769 17.9057 16.1454 18.5248 16.5623 19.1046C18.2518 18.5581 19.9682 17.7154 21.7342 16.3841C22.1583 12.0534 21.0021 8.28622 18.9327 4.93652ZM8.20167 14.0009C7.16252 14.0009 6.31379 13.0534 6.31379 11.9015C6.31379 10.7495 7.14287 9.80101 8.20167 9.80101C9.26047 9.80101 10.1092 10.7485 10.0896 11.9015C10.0906 13.0534 9.26047 14.0009 8.20167 14.0009ZM15.0199 14.0009C13.9807 14.0009 13.132 13.0534 13.132 11.9015C13.132 10.7495 13.9611 9.80101 15.0199 9.80101C16.0787 9.80101 16.9274 10.7485 16.9078 11.9015C16.9078 13.0534 16.0787 14.0009 15.0199 14.0009Z" />
          </svg>
        </button>
      </div>
    </div>
  );
}