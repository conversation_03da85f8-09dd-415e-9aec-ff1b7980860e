import { useState, useEffect } from 'react';
import { LockIcon, X } from 'lucide-react';

type TradingSettingsModalProps = {
  selectedPreset?: number;
  onPresetChange?: (preset: number, settings: SettingsObject) => void;
  visible: boolean;
  onClose: () => void;
  initialAutoFee?: boolean;
  initialLockPreset?: boolean;
};

type SettingsObject = {
  slippage: string;
  priority: string;
  bribe: string;
};

type PresetSettings = {
  [key: number]: SettingsObject;
};

export default function TradingSettingsModal({
  selectedPreset = 3,
  onPresetChange,
  visible,
  onClose,
  initialAutoFee = false,
  initialLockPreset = true,
}: TradingSettingsModalProps) {
  // Default settings for each preset
  const defaultPresetSettings: PresetSettings = {
    1: { slippage: '10', priority: '0.0005', bribe: '0.0005' },
    2: { slippage: '15', priority: '0.0008', bribe: '0.0008' },
    3: { slippage: '20', priority: '0.001', bribe: '0.001' }
  };

  const [preset, setPreset] = useState(selectedPreset);
  const [buySettings, setBuySettings] = useState(true);
  const [autoFee, setAutoFee] = useState(initialAutoFee);
  const [lockPreset, setLockPreset] = useState(initialLockPreset);
  const [mevReduction, setMevReduction] = useState(false);
  const [rpcUrl, setRpcUrl] = useState('');

  // State for storing settings for each preset
  const [presetSettings, setPresetSettings] = useState<PresetSettings>({
    1: { ...defaultPresetSettings[1] },
    2: { ...defaultPresetSettings[2] },
    3: { ...defaultPresetSettings[3] }
  });

  // Current settings based on selected preset
  const [currentSettings, setCurrentSettings] = useState(presetSettings[preset]);

  useEffect(() => {
    setPreset(selectedPreset);
    setCurrentSettings(presetSettings[selectedPreset]);
  }, [selectedPreset, presetSettings]);

  if (!visible) return null;

  // Handle preset selection
  const handlePresetClick = (value: number) => {
    setPreset(value);
    setCurrentSettings(presetSettings[value]);
    onPresetChange?.(value, presetSettings[value]);
  };

  // Handle settings change
  const handleSettingChange = (field: keyof SettingsObject, value: string) => {
    const updatedSettings = { ...currentSettings, [field]: value };
    setCurrentSettings(updatedSettings);
    
    // Update the preset settings object
    setPresetSettings({
      ...presetSettings,
      [preset]: updatedSettings
    });
    
    // Notify parent component if needed
    onPresetChange?.(preset, updatedSettings);
  };

  // Handle form submission
  const handleSubmit = () => {
    console.log('Selected Preset:', preset);
    console.log('Settings:', presetSettings[preset]);
    onClose();
  };

  return (
    <div className="fixed inset-0 z-500 flex items-center justify-center bg-black/50">
      <div className="bg-[#111215] text-white rounded-xl p-5 w-full max-w-md shadow-lg relative">
        {/* Close Icon */}
        <button onClick={onClose} className="absolute top-4 right-4 text-gray-400 hover:text-white">
          <X size={18} />
        </button>

        <h2 className="text-lg font-semibold mb-4">Trading Settings</h2>

        {/* Preset Buttons */}
        <div className="flex justify-between bg-[#1a1b1f] rounded-md overflow-hidden mb-4">
          {[1, 2, 3].map((p) => (
            <button
              key={p}
              className={`flex-1 py-2 text-sm font-semibold transition-colors ${
                preset === p ? 'bg-blue-600 text-white' : 'bg-transparent text-gray-300 hover:bg-[#2a2b30]'
              }`}
              onClick={() => handlePresetClick(p)}
            >
              PRESET {p}
            </button>
          ))}
        </div>

        {/* Buy/Sell Toggle */}
        <div className="flex justify-between mb-4 bg-[#1a1b1f] rounded-md overflow-hidden">
          <button
            className={`flex-1 py-2 text-sm font-medium ${
              buySettings ? 'text-green-400 bg-[#1f2a21]' : 'text-gray-300'
            }`}
            onClick={() => setBuySettings(true)}
          >
            Buy Settings
          </button>
          <button
            className={`flex-1 py-2 text-sm font-medium ${
              !buySettings ? 'text-red-400 bg-[#2a1f1f]' : 'text-gray-300'
            }`}
            onClick={() => setBuySettings(false)}
          >
            Sell Settings
          </button>
        </div>

        {/* Input Fields */}
        <div className="grid grid-cols-3 gap-2 mb-4 text-xs">
          {[
            { label: 'SLIPPAGE', key: 'slippage', unit: '%' },
            { label: 'PRIORITY', key: 'priority', unit: 'ETH' },
            { label: 'BRIBE', key: 'bribe', unit: 'ETH' },
          ].map((field) => (
            <div key={field.label} className="bg-[#1a1b1f] rounded-md py-2 px-2 text-center">
              <input
                type="text"
                value={currentSettings[field.key as keyof SettingsObject]}
                onChange={(e) => handleSettingChange(field.key as keyof SettingsObject, e.target.value)}
                className="w-full bg-[#242529] border-none rounded text-center text-sm py-1 text-gray-100 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              <p className="text-gray-400 mt-1 text-[10px]">{field.label} {field.unit}</p>
            </div>
          ))}
        </div>

        {/* RPC and MEV */}
        <div className="flex items-center space-x-2 mb-4">
          <input
            className="flex-1 text-sm bg-[#1a1b1f] border-none rounded-md px-3 py-2 text-gray-300 placeholder-gray-500 focus:outline-none"
            placeholder="RPC https://a...e.com"
            value={rpcUrl}
            onChange={(e) => setRpcUrl(e.target.value)}
          />
          <label className="flex items-center space-x-1 text-xs">
            <input
              type="checkbox"
              className="form-checkbox accent-blue-500"
              checked={mevReduction}
              onChange={(e) => setMevReduction(e.target.checked)}
            />
            <span className="text-gray-300">MEV reduction</span>
          </label>
        </div>

        {/* Auto Fee and Lock Preset */}
        <div className="space-y-2 mb-4">
          <label className="flex items-center space-x-2 text-sm">
            <input
              type="checkbox"
              className="form-checkbox accent-blue-500"
              checked={autoFee}
              onChange={(e) => setAutoFee(e.target.checked)}
            />
            <span className="text-gray-300">Auto Fee (beta)</span>
          </label>
          <label className="flex items-center space-x-2 text-sm">
            <input
              type="checkbox"
              className="form-checkbox accent-blue-500"
              checked={lockPreset}
              onChange={(e) => setLockPreset(e.target.checked)}
            />
            <span className="flex items-center text-blue-400">
              <LockIcon size={14} className="mr-1" />
              Lock Preset for Pulse
            </span>
          </label>
        </div>

        {/* Continue Button */}
        <button 
          className="w-full py-2 text-sm font-semibold bg-blue-600 rounded-full hover:bg-blue-700 transition"
          onClick={handleSubmit}
        >
          Continue
        </button>
      </div>
    </div>
  );
}