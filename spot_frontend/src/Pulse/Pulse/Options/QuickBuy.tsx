import { useState, useEffect, useCallback } from "react";
import { usePrivy } from "@privy-io/react-auth";
import { useSolanaWallets } from "@privy-io/react-auth/solana";
import { getTokenBalance, BalanceRequest, BalanceResponse } from "../../../api/solana_api";

interface QuickBuyProps {
  onAmountChange?: (amount: string) => void;
  selectedPreset: number;
  onPresetClick: () => void;
}

const QuickBuy = ({ onAmountChange, selectedPreset, onPresetClick }: QuickBuyProps) => {
  const [amount, setAmount] = useState("");
  const [balanceData, setBalanceData] = useState<BalanceResponse | null>(null);
  const [isLoadingBalance, setIsLoadingBalance] = useState<boolean>(false);
  
  // Privy hooks for wallet access
  const { authenticated, user } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;

    // Validate input - only allow positive numbers
    if (val === '' || (!isNaN(parseFloat(val)) && parseFloat(val) >= 0)) {
      setAmount(val);
      onAmountChange?.(val);
    }
  };

  // Validate amount against balance
  const validateAmount = (inputAmount: string): { isValid: boolean; error?: string } => {
    if (!inputAmount || inputAmount === '') {
      return { isValid: true };
    }

    const numAmount = parseFloat(inputAmount);
    if (isNaN(numAmount) || numAmount <= 0) {
      return { isValid: false, error: 'Please enter a valid amount' };
    }

    if (balanceData?.success) {
      const solBalance = parseFloat(balanceData.data.solBalance);
      if (numAmount > solBalance) {
        return {
          isValid: false,
          error: `Insufficient balance. Available: ${solBalance.toFixed(4)} SOL`
        };
      }
    }

    return { isValid: true };
  };

  const validation = validateAmount(amount);

  // Get Solana wallet address using defaultWallets
  const getSolanaWalletAddress = useCallback(() => {
    if (!authenticated || solanaWallets.length === 0) {
      return null;
    }
    
    // Get the first available Solana wallet
    const solanaWallet = solanaWallets[0];
    return solanaWallet?.address || null;
  }, [authenticated, solanaWallets]);

  // Fetch balance function
  const fetchBalance = useCallback(async () => {
    // Check if user is authenticated
    if (!authenticated) {
      console.log('User not authenticated, cannot fetch balance');
      setBalanceData(null);
      return;
    }

    // Get wallet address
    const walletAddress = getSolanaWalletAddress();
    if (!walletAddress) {
      console.log('No Solana wallet connected, cannot fetch balance');
      setBalanceData(null);
      return;
    }

    // Validate Solana address format
    const isSolanaAddress = (address: string): boolean => {
      const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
      return base58Regex.test(address);
    };

    if (!isSolanaAddress(walletAddress)) {
      console.error('Invalid Solana wallet address format:', walletAddress);
      setBalanceData(null);
      return;
    }

    try {
      setIsLoadingBalance(true);
      
      // For SOL balance we can just send wallet address - the backend is now configured to handle this
      const balanceRequest: BalanceRequest = {
        walletAddress: walletAddress
        // Not including tokenAddress will make the API return only SOL balance
      };

      console.log('Fetching SOL balance for wallet:', walletAddress);
      const response = await getTokenBalance(balanceRequest);
      setBalanceData(response);

      if (!response.success) {
        console.error('Balance fetch failed:', response.error);
      }

    } catch (error) {
      console.error('Error fetching balance:', error);
      setBalanceData(null);
    } finally {
      setIsLoadingBalance(false);
    }
  }, [authenticated, getSolanaWalletAddress]);

  // Get display balance
  const getDisplayBalance = useCallback(() => {
    // Check if user is authenticated and has wallet connected
    if (!authenticated) {
      return '0';
    }

    if (!getSolanaWalletAddress()) {
      return '0';
    }

    // Check if balance data is available and successful
    if (!balanceData?.success) {
      return '0';
    }

    // Show SOL balance
    const solBalance = parseFloat(balanceData.data.solBalance);
    return solBalance.toFixed(4);
  }, [authenticated, balanceData, getSolanaWalletAddress]);

  // Fetch balance when component mounts or wallet changes
  useEffect(() => {
    fetchBalance();

    // Set up listener for wallet changes
    const handleStorageChange = () => {
      fetchBalance();
    };

    // Set up listener for successful quick buy transactions
    const handleQuickBuySuccess = (event: CustomEvent) => {
      console.log('QuickBuy: Received quickBuySuccess event, refreshing balance', event.detail);
      // Refresh balance after successful transaction
      setTimeout(() => {
        fetchBalance();
      }, 1000); // Additional 1 second delay for blockchain confirmation
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('quickBuySuccess', handleQuickBuySuccess as EventListener);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('quickBuySuccess', handleQuickBuySuccess as EventListener);
    };
  }, [fetchBalance]);

  // Set up periodic balance refresh when user is authenticated and has wallet
  useEffect(() => {
    if (!authenticated || !getSolanaWalletAddress()) {
      return;
    }

    // Refresh balance every 30 seconds when user is active
    const intervalId = setInterval(() => {
      console.log('QuickBuy: Periodic balance refresh');
      fetchBalance();
    }, 30000); // 30 seconds

    return () => {
      clearInterval(intervalId);
    };
  }, [authenticated, getSolanaWalletAddress, fetchBalance]);

  return (
    <div className="relative">
      <div className={`flex items-center bg-[#1A1A1A] border rounded-full px-4 py-1 text-white text-sm space-x-3 ${
        !validation.isValid ? 'border-red-500' : 'border-gray-700'
      }`}>
        <span className="opacity-80">Quick Buy</span>

        {/* Input field */}
        <input
          type="number"
          placeholder="Amount"
          value={amount}
          onChange={handleInputChange}
          className={`bg-transparent placeholder-gray-400 border-none outline-none text-white px-2 py-1 rounded-full w-24
                     appearance-none [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none ${
                       !validation.isValid ? 'text-red-400' : ''
                     }`}
        />

        {/* SOL Balance indicator with left border - non-clickable */}
        <div
          className="flex items-center space-x-1 px-2 py-1 rounded-full border-l border-gray-700 pl-3"
        >
          <div className="flex items-center space-x-1">
            <img src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png" alt="Solana" className="w-4 h-4" />
            <span className="text-white text-xs">
              {isLoadingBalance ? (
                <div className="animate-pulse">...</div>
              ) : (
                `${getDisplayBalance()} SOL`
              )}
            </span>
          </div>
        </div>
      </div>

      {/* Error message */}
      {!validation.isValid && validation.error && (
        <div className="absolute top-full mt-1 left-0 text-red-400 text-xs bg-[#1A1A1A] border border-red-500 rounded px-2 py-1 z-10">
          {validation.error}
        </div>
      )}
    </div>
  );
};

export default QuickBuy;

