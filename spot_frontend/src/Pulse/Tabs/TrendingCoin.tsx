import React, { useState, useEffect } from 'react';
import { ChevronUp, ChevronDown, ExternalLink } from 'lucide-react';

// Updated interface to match your actual data structure
interface TokenData {
  baseimage: string;
  bonding: number;
  createdAt: string;
  current_price: number;
  exchange_logo: string;
  exchange_name: string;
  holders_count: number;
  id: string;
  image: string;
  liquidity: number;
  market_cap: number;
  name: string;
  network: string;
  pool_address: string;
  price_change_percentage_24h: number;
  supply: number;
  symbol: string;
  total_volume: number;
  trades_24h: number;
}

// Mapped interface for display
interface CoinData {
  id: string;
  name: string;
  symbol: string;
  description: string;
  image: string;
  priceChange: number;
  volume: string;
  volumeChange: number;
  marketCap: string;
  currentLiquidity: string;
  swaps: string;
  swapsRatio: string;
  makers: string;
  security: string;
  age: string;
}

type SortField = 'priceChange' | 'volume' | 'volumeChange' | 'marketCap' | 'currentLiquidity' | 'swaps' | 'makers' | 'age';

interface TrendingCoinProps {
  tokens: TokenData[];
}

// Helper function to format numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `$${(num / 1000000).toFixed(2)}M`;
  } else if (num >= 1000) {
    return `$${(num / 1000).toFixed(2)}K`;
  }
  return `$${num.toFixed(2)}`;
};

// Helper function to calculate age from createdAt
const calculateAge = (createdAt: string): string => {
  const now = new Date();
  const created = new Date(createdAt);
  const diffMs = now.getTime() - created.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffDays > 0) {
    return `${diffDays}d`;
  } else if (diffHours > 0) {
    return `${diffHours}h`;
  } else {
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    return `${diffMinutes}m`;
  }
};

// Helper function to determine security status
const getSecurityStatus = (priceChange: number, volume: number): string => {
  if (priceChange > 10 || volume < 1000) return 'High Risk';
  if (priceChange > 5 || volume < 5000) return 'Medium';
  return 'Safe';
};

// Transform token data to display format
const transformTokenData = (tokens: TokenData[]): CoinData[] => {
  return tokens.map(token => {
    // Simulate volume change (you can replace this with real data when available)
    const volumeChange = (Math.random() - 0.5) * 100; // Random -50% to +50%
    
    return {
      id: token.id,
      name: token.name,
      symbol: token.symbol,
      description: token.symbol,
      image: token.image || token.baseimage,
      priceChange: token.price_change_percentage_24h,
      volume: formatNumber(token.total_volume),
      volumeChange: Math.round(volumeChange * 100) / 100,
      marketCap: formatNumber(token.market_cap),
      currentLiquidity: formatNumber(token.liquidity),
      swaps: token.trades_24h.toString(),
      swapsRatio: `${Math.round((token.trades_24h / token.holders_count) * 100) / 100}`, // Trades per holder ratio
      makers: token.holders_count.toString(),
      security: getSecurityStatus(token.price_change_percentage_24h, token.total_volume),
      age: calculateAge(token.createdAt)
    };
  });
};

const TrendingCoin: React.FC<TrendingCoinProps> = ({ tokens }) => {
  const [selectedMetric, setSelectedMetric] = useState<string>('Volume');
  const [data, setData] = useState<CoinData[]>([]);
  const [sortField, setSortField] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Transform tokens data when component mounts or tokens change
  useEffect(() => {
    const transformedData = transformTokenData(tokens);
    setData(transformedData);
  }, [tokens]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }

    // Sort data according to field and direction
    const sortedData = [...data].sort((a, b) => {
      let aValue: any = a[field];
      let bValue: any = b[field];

      // Handle different data types
      if (field === 'age') {
        // Convert age to minutes for comparison
        const convertAgeToMinutes = (ageStr: string): number => {
          if (ageStr.includes('d')) {
            return parseFloat(ageStr.replace('d', '')) * 24 * 60;
          } else if (ageStr.includes('h')) {
            return parseFloat(ageStr.replace('h', '')) * 60;
          } else if (ageStr.includes('m')) {
            return parseFloat(ageStr.replace('m', ''));
          }
          return 0;
        };
        aValue = convertAgeToMinutes(aValue);
        bValue = convertAgeToMinutes(bValue);
      } else if (typeof aValue === 'string' && aValue.includes('$')) {
        // Handle monetary values
        aValue = parseFloat(aValue.replace(/[$,KM]/g, '')) * (aValue.includes('M') ? 1000000 : (aValue.includes('K') ? 1000 : 1));
        bValue = parseFloat((bValue as string).replace(/[$,KM]/g, '')) * ((bValue as string).includes('M') ? 1000000 : ((bValue as string).includes('K') ? 1000 : 1));
      } else if (typeof aValue === 'string' && !isNaN(parseFloat(aValue))) {
        // Handle numeric strings
        aValue = parseFloat(aValue);
        bValue = parseFloat(bValue as string);
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setData(sortedData);
  };

  const renderSortIcon = (field: SortField) => {
    const isActive = sortField === field;
  
    return (
      <div className="flex flex-col items-center ml-1">
        <ChevronUp
          size={14}
          className={isActive && sortDirection === 'asc' ? 'text-white' : 'text-gray-500'}
        />
        <ChevronDown
          size={14}
          className={isActive && sortDirection === 'desc' ? 'text-white' : 'text-gray-500'}
        />
      </div>
    );
  };

  const timeRanges = ['1h', '7h', '7d'];
  const [selectedTime, setSelectedTime] = useState<string>('1h');
  const [isOpen2, setIsOpen2] = useState<boolean>(false);

  return (
    <div className="px-4 py-2 h-screen overflow-auto">
      <div className="flex justify-between text-white items-center mb-4 bg-[#181C20] p-3">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-800 text-sm text-[#BBBBBB]">
              <th className="text-left py-3 px-4">Coins</th>
              <th 
                className="cursor-pointer text-left py-3" 
                onClick={() => handleSort('priceChange')}
              >
                <div className="flex items-center space-x-1">
                  <span>Price Change</span>
                  {renderSortIcon('priceChange')}
                </div>
              </th>
              <th 
                className="cursor-pointer text-left py-3" 
                onClick={() => handleSort('volume')}
              >
                <div className="flex items-center space-x-1">
                  <span>Volume</span>
                  {renderSortIcon('volume')}
                </div>
              </th>
              <th 
                className="cursor-pointer text-left py-3" 
                onClick={() => handleSort('volumeChange')}
              >
                <div className="flex items-center space-x-1">
                  <span>Volume Change</span>
                  {renderSortIcon('volumeChange')}
                </div>
              </th>
              <th 
                className="cursor-pointer text-left py-3" 
                onClick={() => handleSort('marketCap')}
              >
                <div className="flex items-center space-x-1">
                  <span>MarketCap</span>
                  {renderSortIcon('marketCap')}
                </div>
              </th>
              <th 
                className="cursor-pointer text-left py-3" 
                onClick={() => handleSort('currentLiquidity')}
              >
                <div className="flex items-center space-x-1">
                  <span>Current Liquidity</span>
                  {renderSortIcon('currentLiquidity')}
                </div>
              </th>
              <th 
                className="cursor-pointer text-left py-3" 
                onClick={() => handleSort('swaps')}
              >
                <div className="flex items-center space-x-1">
                  <span>Swaps</span>
                  {renderSortIcon('swaps')}
                </div>
              </th>
              <th 
                className="cursor-pointer text-left py-3" 
                onClick={() => handleSort('makers')}
              >
                <div className="flex items-center space-x-1">
                  <span>Makers</span>
                  {renderSortIcon('makers')}
                </div>
              </th>
              <th className="text-left py-3">Security</th>
              <th 
                className="cursor-pointer text-left py-3" 
                onClick={() => handleSort('age')}
              >
                <div className="flex items-center space-x-1">
                  <span>Age</span>
                  {renderSortIcon('age')}
                </div>
              </th>
              <th className="text-left py-3"></th>
            </tr>
          </thead>
          <tbody>
            {data.map((coin) => (
              <tr key={coin.id} className="border-b border-gray-800 hover:bg-gray-800">
                <td className="py-4 pl-2">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gray-600 rounded-full mr-3 overflow-hidden">
                      {coin.image ? (
                        <img src={coin.image} alt={coin.name} className="w-full h-full object-cover" />
                      ) : (
                        <div className="w-full h-full bg-gray-600" />
                      )}
                    </div>
                    <div>
                      <div className="flex items-center">
                        <span className="font-medium">{coin.name}</span>
                        <ExternalLink size={14} className="ml-1 text-gray-500" />
                      </div>
                      <div className="text-sm text-gray-400">{coin.symbol}</div>
                    </div>
                  </div>
                </td>
                <td className="text-left py-3">
                  <span className={coin.priceChange >= 0 ? 'text-[#14FFA2]' : 'text-[#FF329B]'}>
                    {coin.priceChange > 0 ? '+' : ''}{coin.priceChange.toFixed(2)}%
                  </span>
                </td>
                <td className="text-left py-3">{coin.volume}</td>
                <td className="text-left py-3">
                  <span className={coin.volumeChange >= 0 ? 'text-[#14FFA2]' : 'text-[#FF329B]'}>
                    {coin.volumeChange > 0 ? '+' : ''}{coin.volumeChange}%
                  </span>
                </td>
                <td className="text-left py-3">{coin.marketCap}</td>
                <td className="text-left py-3">{coin.currentLiquidity}</td>
                <td className="text-left py-3">
                  <div>
                    <div>{coin.swaps}</div>
                    <div className="text-sm text-gray-400">{coin.swapsRatio}</div>
                  </div>
                </td>
                <td className="text-left py-3">{coin.makers}</td>
                <td className="text-left py-3">
                  <span className={`text-xs px-2 py-1 rounded ${
                    coin.security === 'Safe' ? 'bg-green-900 text-green-300' :
                    coin.security === 'Medium' ? 'bg-yellow-900 text-yellow-300' :
                    'bg-red-900 text-red-300'
                  }`}>
                    {coin.security}
                  </span>
                </td>
                <td className="text-left py-3">{coin.age}</td>
                <td className="text-left py-3 pr-2">
                  <button className="bg-[#214638] hover:bg-green-600 text-white px-4 py-1 rounded text-sm transition-colors">
                    Buy
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TrendingCoin;