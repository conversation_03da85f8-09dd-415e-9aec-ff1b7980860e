import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

// Updated interface to match your actual data structure
interface TokenData {
  baseimage: string;
  bonding: number;
  createdAt: string;
  current_price: number;
  exchange_logo: string;
  exchange_name: string;
  holders_count: number;
  id: string;
  image: string;
  liquidity: number;
  market_cap: number;
  name: string;
  network: string;
  pool_address: string;
  price_change_percentage_24h: number;
  supply: number;
  symbol: string;
  total_volume: number;
  trades_24h: number;
}

// Mapped interface for display
interface CoinData {
  id: string;
  name: string;
  symbol: string;
  description: string;
  image: string;
  age: string;
  currentLiquidity: string;
  liquidityChange: string;
  initialLiquidity: string; // Not available in current data
  marketCap: string;
  swaps: string;
  swapsRatio: string; // Not available in current data
  makers: string; // Not available in current data
  volume: string;
  price: number;
  priceChange: number;
}

interface SortConfig {
  key: keyof CoinData | null;
  direction: 'asc' | 'desc';
}

interface NewCoinProps {
  tokens: TokenData[];
  searchTerm: string;
}

// Helper function to format numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `$${(num / 1000000).toFixed(2)}M`;
  } else if (num >= 1000) {
    return `$${(num / 1000).toFixed(2)}K`;
  }
  return `$${num.toFixed(2)}`;
};

// Helper function to calculate age from createdAt
const calculateAge = (createdAt: string): string => {
  const now = new Date();
  const created = new Date(createdAt);
  const diffMs = now.getTime() - created.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffDays > 0) {
    return `${diffDays}d`;
  } else if (diffHours > 0) {
    return `${diffHours}h`;
  } else {
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    return `${diffMinutes}m`;
  }
};

// Helper function to strip dollar and K/M for sorting
const stripDollarAndK = (value: string): number => {
  const cleaned = value.replace('$', '').replace(',', '');
  if (cleaned.includes('K')) {
    return parseFloat(cleaned.replace('K', '')) * 1000;
  } else if (cleaned.includes('M')) {
    return parseFloat(cleaned.replace('M', '')) * 1000000;
  }
  return parseFloat(cleaned) || 0;
};

// Transform token data to display format
const transformTokenData = (tokens: TokenData[]): CoinData[] => {
  console.log('Transforming token data:', tokens);
  return tokens.map(token => ({
    id: token.id,
    name: token.name,
    symbol: token.symbol,
    description: token.symbol, // Using symbol as description for now
    image: token.image || token.baseimage,
    age: calculateAge(token.createdAt),
    currentLiquidity: formatNumber(token.liquidity),
    liquidityChange: `${token.price_change_percentage_24h > 0 ? '+' : ''}${token.price_change_percentage_24h.toFixed(2)}%`,
    initialLiquidity: 'N/A', // Not available in current data
    marketCap: formatNumber(token.market_cap),
    swaps: token.trades_24h.toString(),
    swapsRatio: 'N/A', // Not available in current data
    makers: token.holders_count.toString() || '0', // Using holders_count as makers
    volume: formatNumber(token.total_volume),
    price: token.current_price,
    priceChange: token.price_change_percentage_24h
  }));
};

const NewCoin: React.FC<NewCoinProps> = ({ tokens, searchTerm }) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: 'asc' });

  // Transform the tokens data
  const transformedData = transformTokenData(tokens);

  const handleSort = (key: keyof CoinData) => {
    setSortConfig(prev => {
      const isSameKey = prev.key === key;
      const direction = isSameKey && prev.direction === 'asc' ? 'desc' : 'asc';
      return { key, direction };
    });
  };

  const sortedData = [...transformedData].sort((a, b) => {
    const { key, direction } = sortConfig;
    if (!key) return 0;

    let aVal = a[key];
    let bVal = b[key];

    if (typeof aVal === 'number' && typeof bVal === 'number') {
      return direction === 'asc' ? aVal - bVal : bVal - aVal;
    } else if (typeof aVal === 'string' && typeof bVal === 'string') {
      const isMoney = aVal.startsWith('$');
      const aParsed = isMoney ? stripDollarAndK(aVal) : aVal;
      const bParsed = isMoney ? stripDollarAndK(bVal) : bVal;

      if (typeof aParsed === 'number' && typeof bParsed === 'number') {
        return direction === 'asc' ? aParsed - bParsed : bParsed - aParsed;
      }

      return direction === 'asc'
        ? String(aParsed).localeCompare(String(bParsed))
        : String(bParsed).localeCompare(String(aParsed));
    }

    return 0;
  });

  const filteredData = sortedData.filter(
    (coin) =>
      coin.name?.toLowerCase().includes(searchTerm?.toLowerCase()) ||
      coin.description?.toLowerCase().includes(searchTerm?.toLowerCase()) ||
      coin.symbol?.toLowerCase().includes(searchTerm?.toLowerCase())
  );

  const getIcon = (key: keyof CoinData) => {
    if (sortConfig.key !== key) return <ChevronDown size={14} />;
    return sortConfig.direction === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />;
  };

  // Column configuration with visibility flags
  const columns = [
    { label: "Age", key: "age" as keyof CoinData, visible: true },
    { label: "Current Liquidity", key: "currentLiquidity" as keyof CoinData, visible: true },
    { label: "Initial Liquidity", key: "initialLiquidity" as keyof CoinData, visible: false }, // Hidden until API provides data
    { label: "MarketCap", key: "marketCap" as keyof CoinData, visible: true },
    { label: "Swaps", key: "swaps" as keyof CoinData, visible: true },
    { label: "Makers", key: "makers" as keyof CoinData, visible: true },
    { label: "Volume", key: "volume" as keyof CoinData, visible: true },
  ];

  return (
    <div className="px-4 py-2">
      <div className="overflow-x-auto bg-[#181C20] text-white">
        <table className="w-full">
          <thead>
            <tr className="text-[#BBBBBB] text-sm border-b border-gray-800">
              <th className="text-left py-3 px-4">Coins</th>
              {columns.filter(col => col.visible).map(({ label, key }) => (
                <th key={key} className="text-left py-3 cursor-pointer" onClick={() => handleSort(key)}>
                  <div className="flex items-center gap-1">
                    {label} {getIcon(key)}
                  </div>
                </th>
              ))}
              <th className="text-left py-3">Security</th>
              <th className="text-left py-3"></th>
            </tr>
          </thead>
          <tbody>
            {filteredData.map((coin) => (
              <tr key={coin.id} className="border-b border-gray-800 hover:bg-gray-900/30 text-white">
                <td className="py-4 px-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-white overflow-hidden">
                      {coin.image ? (
                        <img src={coin.image} alt={coin.name} className="w-full h-full object-cover" />
                      ) : (
                        <div className="w-full h-full bg-gray-600" />
                      )}
                    </div>
                    <div>
                      <div className="flex items-center gap-1">
                        <div className="text-white font-medium">{coin.name}</div>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-gray-400">
                          <path d="M7 17l9.2-9.2M17 17V7H7" />
                        </svg>
                      </div>
                      <div className="text-gray-400 text-sm">{coin.symbol}</div>
                    </div>
                  </div>
                </td>
                
                {/* Age */}
                <td className="py-4">{coin.age}</td>
                
                {/* Current Liquidity */}
                <td className="py-4">
                  <div>
                    <div>{coin.currentLiquidity}</div>
                    <div className={`text-sm ${coin.priceChange >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                      {coin.liquidityChange}
                    </div>
                  </div>
                </td>
                
                {/* Initial Liquidity - Hidden for now */}
                {/* <td className="py-4">{coin.initialLiquidity}</td> */}
                
                {/* MarketCap */}
                <td className="py-4">{coin.marketCap}</td>
                
                {/* Swaps */}
                <td className="py-4">
                  <div>
                    <div>{coin.swaps}</div>
                    {coin.swapsRatio !== 'N/A' && (
                      <div className="text-sm text-gray-400">{coin.swapsRatio}</div>
                    )}
                  </div>
                </td>
                
                {/* Makers */}
                <td className="py-4">{coin.makers}</td>
                
                {/* Volume */}
                <td className="py-4">{coin.volume}</td>
                
                {/* Security */}
                <td className="py-4">
                  <div className="flex items-center gap-1">
                    <div className={`w-2 h-2 rounded-full ${coin.priceChange >= 0 ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span className="text-xs text-gray-400">
                      {coin.priceChange >= 0 ? 'Safe' : 'Risk'}
                    </span>
                  </div>
                </td>
                
                {/* Action */}
                <td className="py-4">
                  <button className="bg-[#214638] text-green-400 px-4 py-1 rounded text-sm font-medium hover:bg-[#2a5a42] transition-colors">
                    Buy
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default NewCoin;