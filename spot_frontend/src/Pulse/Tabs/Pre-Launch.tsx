import React, { useState } from 'react';
import { ChevronUp, ChevronDown, Twitter } from 'lucide-react';

// Updated interface to match your actual data structure
interface TokenData {
  baseimage: string;
  bonding: number;
  createdAt: string;
  current_price: number;
  exchange_logo: string;
  exchange_name: string;
  holders_count: number;
  id: string;
  image: string;
  liquidity: number;
  market_cap: number;
  name: string;
  network: string;
  pool_address: string;
  price_change_percentage_24h: number;
  supply: number;
  symbol: string;
  total_volume: number;
  trades_24h: number;
}

// Mapped interface for display
interface CryptoData {
  id: string;
  name: string;
  symbol: string;
  project: string;
  image: string;
  age: string;
  marketCap: string;
  progress: number;
  volume: string;
  swaps: number;
  holders:string;
  dev: number; // Not available in current data
  sniped: string; // Not available in current data
  socials: boolean; // Not available in current data
}

type SortField = 'age' | 'marketCap' | 'progress' | 'volume' | 'swaps';
type SortDirection = 'asc' | 'desc';

interface PreLaunchProps {
  tokens: TokenData[];
  searchTerm: string;
}

// Helper function to format numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `$${(num / 1000000).toFixed(2)}M`;
  } else if (num >= 1000) {
    return `$${(num / 1000).toFixed(2)}K`;
  }
  return `$${num.toFixed(2)}`;
};

// Helper function to calculate age from createdAt
const calculateAge = (createdAt: string): string => {
  const now = new Date();
  const created = new Date(createdAt);
  const diffMs = now.getTime() - created.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffDays > 0) {
    return `${diffDays}d`;
  } else if (diffHours > 0) {
    return `${diffHours}h`;
  } else {
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    return `${diffMinutes}m`;
  }
};

// Helper function to calculate progress based on market cap (simulated)
const calculateProgress = (marketCap: number): number => {
  // Simulate progress based on market cap - you can adjust this logic
  const maxCap = 100000; // Assume $100K is 100% progress
  const progress = Math.min((marketCap / maxCap) * 100, 100);
  return Math.round(progress);
};

// Helper function to simulate holder distribution


// Transform token data to display format
const transformTokenData = (tokens: TokenData[]): CryptoData[] => {
  return tokens.map(token => {

    const progress = calculateProgress(token.market_cap);
    
    return {
      id: token.id,
      name: token.name,
      symbol: token.symbol,
      project: token.symbol, // Using symbol as project name for now
      image: token.image || token.baseimage,
      age: calculateAge(token.createdAt),
      marketCap: formatNumber(token.market_cap),
      progress,
      volume: formatNumber(token.total_volume),
      swaps: token.trades_24h,
      holders:token.holders_count.toString() || '0',
 
      dev: Math.floor(Math.random() * 10) + 5, // Simulated dev percentage (5-15%)
      sniped: 'N/A', // Not available in current data
      socials: Math.random() > 0.5 // Random social presence
    };
  });
};

const PreLaunch = ({ tokens, searchTerm }: PreLaunchProps) => {
  const [sortField, setSortField] = useState<SortField>('marketCap');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  // Transform the tokens data
  const transformedData = transformTokenData(tokens);

  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const renderSortIcon = (field: SortField) => {
    const isActive = sortField === field;
    return (
      <div className="flex flex-col items-center ml-1">
        <ChevronUp
          size={14}
          className={isActive && sortDirection === 'asc' ? 'text-white' : 'text-gray-500'}
        />
        <ChevronDown
          size={14}
          className={isActive && sortDirection === 'desc' ? 'text-white' : 'text-gray-500'}
        />
      </div>
    );
  };

  const sortedData = [...transformedData].sort((a, b) => {
    const getValue = (item: CryptoData, field: SortField): number => {
      switch (field) {
        case 'age':
          // Convert age string to hours for sorting
          const ageStr = item.age;
          if (ageStr.includes('d')) {
            return parseFloat(ageStr.replace('d', '')) * 24;
          } else if (ageStr.includes('h')) {
            return parseFloat(ageStr.replace('h', ''));
          } else if (ageStr.includes('m')) {
            return parseFloat(ageStr.replace('m', '')) / 60;
          }
          return 0;
        case 'marketCap':
          return parseFloat(item.marketCap.replace(/[$,KM]/g, '')) * (item.marketCap.includes('M') ? 1000000 : (item.marketCap.includes('K') ? 1000 : 1));
        case 'progress':
          return item.progress;
        case 'volume':
          return parseFloat(item.volume.replace(/[$,KM]/g, '')) * (item.volume.includes('M') ? 1000000 : (item.volume.includes('K') ? 1000 : 1));
        case 'swaps':
          return item.swaps;
        default:
          return 0;
      }
    };

    const aVal = getValue(a, sortField);
    const bVal = getValue(b, sortField);
    return sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
  });

  const filteredData = sortedData.filter(token =>
    token.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    token.symbol.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Column configuration with visibility flags
  const columns = [
    { label: "Age", key: "age" as SortField, visible: true },
    { label: "MarketCap", key: "marketCap" as SortField, visible: true },
    { label: "Progress", key: "progress" as SortField, visible: true },
    { label: "Volume", key: "volume" as SortField, visible: true },
    { label: "Swaps", key: "swaps" as SortField, visible: true },
  ];

  return (
    <div className="px-4 py-2">
      <div className="flex justify-between text-white items-center mb-4 bg-[#181C20] p-3">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-800 text-sm text-gray-400">
              <th className="px-4 py-3 text-left">Coins</th>
              {columns.filter(col => col.visible).map(({ label, key }) => (
                <th key={key} className="px-4 py-3 text-left cursor-pointer" onClick={() => handleSort(key)}>
                  <div className="flex items-center">{label} {renderSortIcon(key)}</div>
                </th>
              ))}
              <th className="px-4 py-3 text-left">Holders</th>
              {/* <th className="px-4 py-3 text-left">Top 10</th> */}
              <th className="px-4 py-3 text-left">Dev</th>
              <th className="px-4 py-3 text-left">Sniped</th>
              <th className="px-4 py-3 text-left">Socials</th>
              <th className="px-4 py-3"></th>
            </tr>
          </thead>
          <tbody>
            {filteredData.map((crypto) => (
              <tr key={crypto.id} className="border-b border-gray-800 hover:bg-gray-800">
                <td className="px-4 py-4">
                  <div className="flex items-center">
                    <div className="w-8 h-8 mr-3 bg-gray-600 rounded-full overflow-hidden">
                      {crypto.image ? (
                        <img src={crypto.image} alt={crypto.name} className="w-full h-full object-cover" />
                      ) : (
                        <div className="w-full h-full bg-gray-600" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium flex items-center">
                        {crypto.name}
                        <svg className="ml-1 w-4 h-4" viewBox="0 0 24 24" fill="none">
                          <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                      </div>
                      <div className="text-sm text-gray-400">{crypto.symbol}</div>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-4">{crypto.age}</td>
                <td className="px-4 py-4">{crypto.marketCap}</td>
                <td className="px-4 py-4">
                  <div className="flex items-center">
                    <div className="w-24 bg-gray-700 rounded-full h-2 mr-2">
                      <div
                        className="bg-white h-2 rounded-full"
                        style={{ width: `${crypto.progress}%` }}
                      ></div>
                    </div>
                    <span>{crypto.progress}%</span>
                  </div>
                </td>
                <td className="px-4 py-4">{crypto.volume}</td>
                <td className="px-4 py-4">
                  <div>
                    <div className="font-medium">{crypto.swaps.toLocaleString()}</div>
                    
                  </div>
                </td>
                <td className="px-4 py-4">{crypto.holders}</td>
                 {/*<td className="px-4 py-4">{crypto.percentActive}%</td> */}
                <td className="px-4 py-4">{crypto.dev}%</td>
                <td className="px-4 py-4">
                  <span className="text-gray-400">{crypto.sniped}</span>
                </td>
                <td className="px-4 py-4">
                  <div className="flex space-x-2">
                    {crypto.socials && (
                      <Twitter size={20} className="text-blue-400 hover:text-blue-300 cursor-pointer" />
                    )}
                    {!crypto.socials && (
                      <span className="text-gray-500 text-sm">None</span>
                    )}
                  </div>
                </td>
                <td className="px-4 py-4">
                  <button className="bg-[#214638] hover:bg-green-700 text-green-100 px-4 py-1 rounded text-sm transition-colors">
                    Buy
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PreLaunch;