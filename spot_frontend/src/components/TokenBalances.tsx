import React, { useState, useEffect } from 'react';
import { usePrivy, useWallets } from '@privy-io/react-auth';
import { getTokenBalances } from '../utils/tokenBalances';

/**
 * TokenBalances component that fetches and displays token balances for connected wallets
 * Supports both EVM and Solana chains
 */
const TokenBalances = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<null>(null);
  const [evmTokens, setEvmTokens] = useState<any[]>([]);
  const [solanaTokens, setSolanaTokens] = useState<any[]>([]);
  const [selectedChain, setSelectedChain] = useState<string>('1'); // Default to Ethereum mainnet
  
  const { authenticated } = usePrivy();
  const { wallets } = useWallets();
  
  // Chain options for the dropdown
  const chainOptions = [
    { id: '1', name: 'Ethereum' },
    { id: '137', name: 'Polygon' },
    { id: '10', name: 'Optimism' },
    { id: '42161', name: 'Arbitrum' },
    { id: '8453', name: 'Base' },
    { id: 'solana', name: 'Solana' }
  ];
  
  // Fetch token balances when wallets or selected chain changes
  useEffect(() => {
    const fetchBalances = async () => {
      if (!authenticated || !wallets || wallets.length === 0) {
        console.log('Not authenticated or no wallets connected');
        return;
      }
      
      setLoading(true);
      setError(null);
      
      try {
        // Find EVM wallets (addresses starting with 0x)
        const evmWallets = wallets.filter(wallet => 
          wallet.address && wallet.address.startsWith('0x')
        );
        
        // Find Solana wallets
        const solanaWallets = wallets.filter(wallet => 
          wallet.walletClientType === 'phantom' || 
          wallet.walletClientType === 'solana' ||
          (wallet.chains && wallet.chains.includes('solana'))
        );
        
        console.log(`Found ${evmWallets.length} EVM wallets and ${solanaWallets.length} Solana wallets`);
        
        // If the selected chain is Solana, fetch Solana token balances
        if (selectedChain === 'solana' && solanaWallets.length > 0) {
          const solanaWallet = solanaWallets[0]; // Use the first Solana wallet
          const solanaTokenBalances = await getTokenBalances(
            solanaWallet.address,
            'solana'
          );
          setSolanaTokens(solanaTokenBalances);
          setEvmTokens([]); // Clear EVM tokens when viewing Solana
        } 
        // Otherwise fetch EVM token balances for the selected chain
        else if (selectedChain !== 'solana' && evmWallets.length > 0) {
          const evmWallet = evmWallets[0]; // Use the first EVM wallet
          const evmTokenBalances = await getTokenBalances(
            evmWallet.address,
            'evm',
            selectedChain
          );
          setEvmTokens(evmTokenBalances);
          setSolanaTokens([]); // Clear Solana tokens when viewing EVM chains
        } else {
          setError('No compatible wallet found for the selected chain');
        }
      } catch (err) {
        console.error('Error fetching token balances:', err);
        setError(`Failed to fetch token balances: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };
    
    fetchBalances();
  }, [authenticated, wallets, selectedChain]);
  
  // Handle chain selection change
  const handleChainChange = (e: React.MouseEvent) => {
    setSelectedChain(e.target.value);
  };
  
  // Determine which tokens to display based on selected chain
  const tokensToDisplay = selectedChain === 'solana' ? solanaTokens : evmTokens;
  
  return (
    <div className="p-4 bg-[#181C20] rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-white">Token Balances</h2>
        
        {/* Chain selector dropdown */}
        <select 
          value={selectedChain}
          onChange={handleChainChange}
          className="bg-[#1D2226] text-white p-2 rounded-lg outline-none"
          disabled={loading || !authenticated}
        >
          {chainOptions.map(chain => (
            <option key={chain.id} value={chain.id}>
              {chain.name}
            </option>
          ))}
        </select>
      </div>
      
      {/* Connect wallet message if not authenticated */}
      {!authenticated && (
        <div className="text-center py-8">
          <p className="text-gray-400 mb-4">Connect your wallet to view token balances</p>
        </div>
      )}
      
      {/* Loading indicator */}
      {loading && (
        <div className="flex justify-center py-8">
          <div className="animate-pulse text-[#14FFA2]">Loading token balances...</div>
        </div>
      )}
      
      {/* Error message */}
      {error && (
        <div className="bg-red-900/30 text-red-400 p-3 rounded-lg mb-4">
          {error}
        </div>
      )}
      
      {/* Token list */}
      {authenticated && !loading && !error && tokensToDisplay.length > 0 && (
        <div className="overflow-hidden rounded-lg border border-[#2D3035]">
          <table className="min-w-full divide-y divide-[#2D3035]">
            <thead className="bg-[#1D2226]">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Token</th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">Balance</th>
              </tr>
            </thead>
            <tbody className="bg-[#181C20] divide-y divide-[#2D3035]">
              {tokensToDisplay.map((token, index) => (
                <tr key={index}>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="ml-4">
                        <div className="text-sm font-medium text-white">
                          {token.symbol || 'Unknown'}
                        </div>
                        <div className="text-xs text-gray-400">
                          {selectedChain === 'solana' ? token.mintAddress : token.contractAddress}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-right">
                    <div className="text-sm text-white">{parseFloat(token.balance).toFixed(6)}</div>
                    <div className="text-xs text-gray-400">{token.decimals} decimals</div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {/* No tokens message */}
      {authenticated && !loading && !error && tokensToDisplay.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-400">No tokens found for this chain</p>
        </div>
      )}
      
      {/* Refresh button */}
      {authenticated && (
        <div className="mt-4 flex justify-center">
          <button
            onClick={() => {
              setLoading(true);
              // Force re-fetch by toggling the selected chain and back
              const currentChain = selectedChain;
              setSelectedChain(currentChain === '1' ? '137' : '1');
              setTimeout(() => setSelectedChain(currentChain), 100);
            }}
            disabled={loading}
            className={`px-4 py-2 rounded-lg ${loading ? 'bg-gray-700 cursor-not-allowed' : 'bg-[#14FFA2] hover:bg-[#0DD589] text-black'}`}
          >
            Refresh Balances
          </button>
        </div>
      )}
    </div>
  );
};

export default TokenBalances;