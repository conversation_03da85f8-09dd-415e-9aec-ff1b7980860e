import React, { useState, useEffect } from 'react';
import { 
  transactionLogger, 
  TransactionLogEntry, 
  TransactionStatus, 
  LogSeverity 
} from '../utils/transactionLogger';
import { FaDownload, FaSearch, FaFilter, FaTimes, FaExternalLinkAlt, FaChevronDown } from 'react-icons/fa';

interface TransactionMonitorProps {
  showMonitor: boolean;
  onClose: () => void;
}

const TransactionMonitor: React.FC<TransactionMonitorProps> = ({ showMonitor, onClose }) => {
  const [transactions, setTransactions] = useState<Map<string, TransactionLogEntry[]>>(new Map());
  const [filter, setFilter] = useState<string>('all'); // 'all', 'completed', 'pending', 'failed'
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [expandedTx, setExpandedTx] = useState<string | null>(null);

  // Load transactions when component mounts or when visibility changes
  useEffect(() => {
    if (showMonitor) {
      updateTransactions();
      // Set up interval to refresh transactions periodically
      const interval = setInterval(updateTransactions, 2000);
      return () => clearInterval(interval);
    }
  }, [showMonitor]);

  // Update the transactions state from the logger
  const updateTransactions = () => {
    const logs = transactionLogger.getAllLogs();
    setTransactions(new Map(logs));
  };

  // Export logs as JSON
  const handleExportJSON = () => {
    const json = transactionLogger.exportToJson();
    downloadFile(json, 'transaction-logs.json', 'application/json');
  };

  // Export logs as CSV
  const handleExportCSV = () => {
    const csv = transactionLogger.exportToCsv();
    downloadFile(csv, 'transaction-logs.csv', 'text/csv');
  };

  // Helper function to download a file
  const downloadFile = (content: string, fileName: string, contentType: string) => {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Clear all logs
  const handleClearLogs = () => {
    if (window.confirm('Are you sure you want to clear all transaction logs?')) {
      transactionLogger.clearAllLogs();
      updateTransactions();
    }
  };

  // Expand/collapse a transaction to show details
  const toggleExpand = (txId: string) => {
    setExpandedTx(expandedTx === txId ? null : txId);
  };

  // Get transaction status color
  const getStatusColor = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.COMPLETED:
        return 'text-green-400';
      case TransactionStatus.FAILED:
        return 'text-red-400';
      case TransactionStatus.PENDING:
        return 'text-yellow-400';
      default:
        return 'text-blue-400';
    }
  };

  // Get severity color
  const getSeverityColor = (severity: LogSeverity) => {
    switch (severity) {
      case LogSeverity.ERROR:
        return 'text-red-400';
      case LogSeverity.WARNING:
        return 'text-yellow-400';
      case LogSeverity.INFO:
        return 'text-blue-400';
      case LogSeverity.DEBUG:
        return 'text-gray-400';
      default:
        return 'text-white';
    }
  };

  // Get filtered transactions
  const getFilteredTransactions = () => {
    const txArray: [string, TransactionLogEntry[]][] = Array.from(transactions.entries());
    
    // Apply status filter
    const statusFiltered = txArray.filter(([_, logs]) => {
      if (filter === 'all') return true;
      
      // Get the latest status
      const latestLog = logs[logs.length - 1];
      if (!latestLog) return false;
      
      if (filter === 'completed' && latestLog.status === TransactionStatus.COMPLETED) return true;
      if (filter === 'pending' && 
          (latestLog.status === TransactionStatus.PENDING || 
           latestLog.status === TransactionStatus.PREPARING || 
           latestLog.status === TransactionStatus.APPROVAL_REQUESTED || 
           latestLog.status === TransactionStatus.SWAP_REQUESTED)) return true;
      if (filter === 'failed' && latestLog.status === TransactionStatus.FAILED) return true;
      
      return false;
    });
    
    // Apply search filter
    if (!searchTerm) return statusFiltered;
    
    return statusFiltered.filter(([_, logs]) => {
      const searchTermLower = searchTerm.toLowerCase();
      // Search through messages and token symbols
      return logs.some(log => 
        log.message.toLowerCase().includes(searchTermLower) || 
        log.tokenIn?.symbol.toLowerCase().includes(searchTermLower) ||
        log.tokenOut?.symbol.toLowerCase().includes(searchTermLower) ||
        log.txHash?.toLowerCase().includes(searchTermLower)
      );
    });
  };

  // Get latest status of a transaction
  const getLatestStatus = (logs: TransactionLogEntry[]) => {
    if (!logs || logs.length === 0) return TransactionStatus.UNKNOWN;
    return logs[logs.length - 1].status;
  };

  // Format relative time
  const formatRelativeTime = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) return 'just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return `${Math.floor(diff / 86400000)}d ago`;
  };

  // Get transaction summary
  const getTransactionSummary = (logs: TransactionLogEntry[]): string => {
    if (!logs || logs.length === 0) return 'Unknown transaction';
    
    const initLog = logs.find(log => log.tokenIn && log.tokenOut);
    if (initLog && initLog.tokenIn && initLog.tokenOut) {
      return `${initLog.tokenIn.symbol} → ${initLog.tokenOut.symbol}`;
    }
    
    return logs[0].message;
  };

  // Get explorer URL for a transaction
  const getExplorerUrl = (txHash: string | undefined, chainId: number | string | undefined): string => {
    if (!txHash) return '';
    
    // Default to BSCScan
    let explorerBase = 'https://bscscan.com/tx/';
    
    // Set explorer base URL based on chain ID
    if (chainId) {
      switch (chainId.toString()) {
        case '1':
          explorerBase = 'https://etherscan.io/tx/';
          break;
        case '56':
          explorerBase = 'https://bscscan.com/tx/';
          break;
        case '137':
          explorerBase = 'https://polygonscan.com/tx/';
          break;
        case 'solana':
          explorerBase = 'https://explorer.solana.com/tx/';
          break;
        default:
          explorerBase = 'https://bscscan.com/tx/';
      }
    }
    
    return `${explorerBase}${txHash}`;
  };

  // Build transaction list items
  const buildTransactionList = () => {
    const filteredTransactions = getFilteredTransactions();
    
    // Sort by timestamp (newest first)
    filteredTransactions.sort(([_, logsA], [__, logsB]) => {
      const timestampA = logsA.length > 0 ? logsA[logsA.length - 1].timestamp : 0;
      const timestampB = logsB.length > 0 ? logsB[logsB.length - 1].timestamp : 0;
      return timestampB - timestampA;
    });
    
    if (filteredTransactions.length === 0) {
      return (
        <div className="text-gray-400 text-center py-8">
          No transactions found
        </div>
      );
    }
    
    return filteredTransactions.map(([txId, logs]) => {
      const status = getLatestStatus(logs);
      const summary = getTransactionSummary(logs);
      const latestLog = logs[logs.length - 1];
      const txHash = latestLog.txHash;
      const chainId = latestLog.chainId;
      const isExpanded = expandedTx === txId;
      
      return (
        <div 
          key={txId}
          className={`mb-2 rounded-lg overflow-hidden border ${
            status === TransactionStatus.COMPLETED ? 'border-green-800/50 bg-green-950/20' : 
            status === TransactionStatus.FAILED ? 'border-red-800/50 bg-red-950/20' : 
            'border-gray-700 bg-gray-800/50'
          }`}
        >
          {/* Transaction Header */}
          <div 
            className="p-3 flex items-center justify-between cursor-pointer"
            onClick={() => toggleExpand(txId)}
          >
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <span className={`font-medium ${getStatusColor(status)}`}>
                  {status}
                </span>
                <span className="text-gray-400 text-xs">
                  {formatRelativeTime(latestLog.timestamp)}
                </span>
              </div>
              <div className="text-white mt-1">{summary}</div>
            </div>
            <div className="flex items-center space-x-3">
              {txHash && (
                <a 
                  href={getExplorerUrl(txHash, chainId)} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  onClick={(e) => e.stopPropagation()}
                  className="text-blue-400 hover:text-blue-300 transition-colors"
                >
                  <FaExternalLinkAlt />
                </a>
              )}
              <div 
                className={`transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}
              >
                <FaChevronDown className="text-gray-400" />
              </div>
            </div>
          </div>
          
          {/* Transaction Details */}
          {isExpanded && (
            <div className="px-3 pb-3 border-t border-gray-700">
              <div className="max-h-96 overflow-y-auto">
                {logs.map((log, index) => (
                  <div 
                    key={index} 
                    className={`py-1.5 ${index !== logs.length - 1 ? 'border-b border-gray-700/50' : ''}`}
                  >
                    <div className="flex justify-between text-xs text-gray-400">
                      <span className={getSeverityColor(log.severity)}>
                        {log.severity}
                      </span>
                      <span>
                        {new Date(log.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="text-sm mt-1 text-white">
                      {log.message}
                    </div>
                    {log.data && (
                      <details className="mt-1">
                        <summary className="text-xs text-gray-400 cursor-pointer">Details</summary>
                        <pre className="text-xs text-gray-400 mt-1 whitespace-pre-wrap bg-black/30 p-2 rounded">
                          {JSON.stringify(log.data, null, 2)}
                        </pre>
                      </details>
                    )}
                    {log.error && (
                      <div className="mt-1 text-xs text-red-400">
                        Error: {typeof log.error === 'string' ? log.error : log.error.message}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      );
    });
  };

  if (!showMonitor) return null;

  return (
    <div className="fixed inset-x-0 bottom-0 bg-[#121212] border-t border-gray-700 z-50 max-h-[50vh] flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-2 border-b border-gray-700">
        <div className="flex items-center space-x-4">
          <h2 className="text-white font-medium">Transaction Monitor</h2>
          <div className="flex items-center space-x-2">
            <button 
              className={`px-3 py-1 rounded-full text-xs font-medium ${filter === 'all' ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300'}`}
              onClick={() => setFilter('all')}
            >
              All
            </button>
            <button 
              className={`px-3 py-1 rounded-full text-xs font-medium ${filter === 'completed' ? 'bg-green-600 text-white' : 'bg-gray-700 text-gray-300'}`}
              onClick={() => setFilter('completed')}
            >
              Completed
            </button>
            <button 
              className={`px-3 py-1 rounded-full text-xs font-medium ${filter === 'pending' ? 'bg-yellow-600 text-white' : 'bg-gray-700 text-gray-300'}`}
              onClick={() => setFilter('pending')}
            >
              Pending
            </button>
            <button 
              className={`px-3 py-1 rounded-full text-xs font-medium ${filter === 'failed' ? 'bg-red-600 text-white' : 'bg-gray-700 text-gray-300'}`}
              onClick={() => setFilter('failed')}
            >
              Failed
            </button>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Search */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="bg-gray-800 text-white px-3 py-1 rounded-lg text-sm pl-8 w-48"
            />
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs" />
          </div>
          
          {/* Export Buttons */}
          <button 
            onClick={handleExportJSON}
            className="bg-gray-700 hover:bg-gray-600 text-white rounded p-1.5 text-sm"
            title="Export as JSON"
          >
            <FaDownload />
          </button>
          <button 
            onClick={handleExportCSV}
            className="bg-gray-700 hover:bg-gray-600 text-white rounded p-1.5 text-sm"
            title="Export as CSV"
          >
            <FaDownload />
          </button>
          
          {/* Clear Button */}
          <button 
            onClick={handleClearLogs}
            className="bg-red-700 hover:bg-red-600 text-white rounded p-1.5 text-sm"
            title="Clear logs"
          >
            <FaTimes />
          </button>
          
          {/* Close Button */}
          <button 
            onClick={onClose}
            className="bg-gray-700 hover:bg-gray-600 text-white rounded p-1.5 text-sm"
            title="Close monitor"
          >
            <FaTimes />
          </button>
        </div>
      </div>
      
      {/* Transaction List */}
      <div className="flex-1 overflow-y-auto p-2">
        {buildTransactionList()}
      </div>
    </div>
  );
};

export default TransactionMonitor; 