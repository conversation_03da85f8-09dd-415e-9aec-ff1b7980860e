import React, { useState, useEffect } from 'react';
import { globalTradeWebSocketService } from '@/services/globalTradeWebSocketService';

interface ConnectionStats {
  isConnected: boolean;
  poolCount: number;
  subscriberCount: number;
  callbackCount: number;
  connectionStatusCallbackCount: number;
  reconnectAttempts: number;
}

interface LeakDetection {
  hasLeaks: boolean;
  issues: string[];
}

/**
 * Connection Monitor Component
 * Provides real-time monitoring of WebSocket connections and leak detection
 * Useful for debugging connection pooling and ensuring proper cleanup
 */
const ConnectionMonitor: React.FC = () => {
  const [stats, setStats] = useState<ConnectionStats>({
    isConnected: false,
    poolCount: 0,
    subscriberCount: 0,
    callbackCount: 0,
    connectionStatusCallbackCount: 0,
    reconnectAttempts: 0
  });
  
  const [leakDetection, setLeakDetection] = useState<LeakDetection>({
    hasLeaks: false,
    issues: []
  });
  
  const [isVisible, setIsVisible] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Update stats periodically
  useEffect(() => {
    const updateStats = () => {
      const currentStats = globalTradeWebSocketService.getConnectionStats();
      const currentLeaks = globalTradeWebSocketService.detectLeaks();
      
      setStats(currentStats);
      setLeakDetection(currentLeaks);
    };

    // Initial update
    updateStats();

    // Set up interval for auto-refresh
    let interval: NodeJS.Timeout | null = null;
    if (autoRefresh) {
      interval = setInterval(updateStats, 2000); // Update every 2 seconds
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [autoRefresh]);

  // Manual refresh
  const handleRefresh = () => {
    const currentStats = globalTradeWebSocketService.getConnectionStats();
    const currentLeaks = globalTradeWebSocketService.detectLeaks();
    
    setStats(currentStats);
    setLeakDetection(currentLeaks);
    
    // Log detailed state
    globalTradeWebSocketService.logConnectionState('Manual refresh');
  };

  // Force cleanup
  const handleForceCleanup = () => {
    if (window.confirm('Are you sure you want to force cleanup all connections? This will disconnect all active subscriptions.')) {
      globalTradeWebSocketService.forceCleanup();
      handleRefresh();
    }
  };

  // Force reconnect
  const handleForceReconnect = async () => {
    try {
      await globalTradeWebSocketService.reconnect();
      handleRefresh();
    } catch (error) {
      console.error('Failed to reconnect:', error);
    }
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsVisible(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg shadow-lg text-sm font-medium transition-colors"
          title="Show Connection Monitor"
        >
          📊 Monitor
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-gray-900 text-white rounded-lg shadow-xl border border-gray-700 w-96 max-h-96 overflow-y-auto">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700">
        <h3 className="text-sm font-semibold">WebSocket Connection Monitor</h3>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`text-xs px-2 py-1 rounded ${
              autoRefresh ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-600 hover:bg-gray-700'
            } transition-colors`}
            title={autoRefresh ? 'Disable auto-refresh' : 'Enable auto-refresh'}
          >
            {autoRefresh ? '🔄 Auto' : '⏸️ Manual'}
          </button>
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-400 hover:text-white transition-colors"
            title="Hide monitor"
          >
            ✕
          </button>
        </div>
      </div>

      {/* Connection Status */}
      <div className="p-3 border-b border-gray-700">
        <div className="flex items-center gap-2 mb-2">
          <div className={`w-3 h-3 rounded-full ${stats.isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
          <span className="text-sm font-medium">
            {stats.isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
        
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div>
            <span className="text-gray-400">Pools:</span>
            <span className="ml-1 font-mono">{stats.poolCount}</span>
          </div>
          <div>
            <span className="text-gray-400">Subscribers:</span>
            <span className="ml-1 font-mono">{stats.subscriberCount}</span>
          </div>
          <div>
            <span className="text-gray-400">Callbacks:</span>
            <span className="ml-1 font-mono">{stats.callbackCount}</span>
          </div>
          <div>
            <span className="text-gray-400">Status CBs:</span>
            <span className="ml-1 font-mono">{stats.connectionStatusCallbackCount}</span>
          </div>
          <div className="col-span-2">
            <span className="text-gray-400">Reconnect Attempts:</span>
            <span className="ml-1 font-mono">{stats.reconnectAttempts}</span>
          </div>
        </div>
      </div>

      {/* Leak Detection */}
      <div className="p-3 border-b border-gray-700">
        <div className="flex items-center gap-2 mb-2">
          <div className={`w-3 h-3 rounded-full ${leakDetection.hasLeaks ? 'bg-red-400' : 'bg-green-400'}`}></div>
          <span className="text-sm font-medium">
            {leakDetection.hasLeaks ? 'Leaks Detected' : 'No Leaks'}
          </span>
        </div>
        
        {leakDetection.hasLeaks && (
          <div className="space-y-1">
            {leakDetection.issues.map((issue, index) => (
              <div key={index} className="text-xs text-red-400 bg-red-900/20 p-2 rounded">
                ⚠️ {issue}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="p-3 space-y-2">
        <div className="flex gap-2">
          <button
            onClick={handleRefresh}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors"
          >
            🔄 Refresh
          </button>
          <button
            onClick={handleForceReconnect}
            className="flex-1 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors"
            disabled={stats.isConnected}
            title={stats.isConnected ? 'Already connected' : 'Force reconnect'}
          >
            🔌 Reconnect
          </button>
        </div>
        
        <button
          onClick={handleForceCleanup}
          className="w-full bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors"
        >
          🧹 Force Cleanup
        </button>
        
        <div className="text-xs text-gray-400 text-center">
          Expected: 1 connection, {stats.poolCount} pool{stats.poolCount !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Debug Info */}
      <div className="p-3 bg-gray-800 rounded-b-lg">
        <details className="text-xs">
          <summary className="cursor-pointer text-gray-400 hover:text-white">
            Debug Info
          </summary>
          <div className="mt-2 space-y-1 font-mono text-gray-300">
            <div>Connection: {stats.isConnected ? '✅' : '❌'}</div>
            <div>Pool Count: {stats.poolCount}</div>
            <div>Total Subscribers: {stats.subscriberCount}</div>
            <div>Trade Callbacks: {stats.callbackCount}</div>
            <div>Status Callbacks: {stats.connectionStatusCallbackCount}</div>
            <div>Reconnect Attempts: {stats.reconnectAttempts}</div>
            <div>Has Leaks: {leakDetection.hasLeaks ? '❌' : '✅'}</div>
            <div>Auto Refresh: {autoRefresh ? '✅' : '❌'}</div>
          </div>
        </details>
      </div>
    </div>
  );
};

export default ConnectionMonitor;
