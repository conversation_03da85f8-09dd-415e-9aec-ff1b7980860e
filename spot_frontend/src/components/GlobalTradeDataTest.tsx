import React, { useState } from 'react';
import { useGlobalTradeData } from '@/hooks/useGlobalTradeData';
import { useConnectionMonitor, useBrowserWebSocketMonitor } from '@/hooks/useConnectionMonitor';
import ConnectionMonitor from './ConnectionMonitor';

/**
 * Test component to verify the global trade data connection pooling
 * This component can be used to test multiple instances and verify only one WebSocket connection is created
 */
const GlobalTradeDataTest: React.FC = () => {
  const [poolAddress, setPoolAddress] = useState<string>('66Vnm942tWCgeGXpfQDjFASewQfjogpVHDS55PHypump');
  const [showSecondInstance, setShowSecondInstance] = useState(false);

  // Connection monitoring hooks
  const connectionMonitor = useConnectionMonitor({
    enabled: true,
    logInterval: 5000, // Log every 5 seconds
    alertOnLeaks: true,
    autoCleanupOnLeaks: false
  });

  const browserMonitor = useBrowserWebSocketMonitor();

  return (
    <div className="p-6 bg-gray-900 text-white min-h-screen">
      <h1 className="text-2xl font-bold mb-6">Global Trade Data Connection Test</h1>
      
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">Pool Address:</label>
        <input
          type="text"
          value={poolAddress}
          onChange={(e) => setPoolAddress(e.target.value)}
          className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
          placeholder="Enter pool address"
        />
      </div>

      <div className="mb-4">
        <button
          onClick={() => setShowSecondInstance(!showSecondInstance)}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded"
        >
          {showSecondInstance ? 'Hide' : 'Show'} Second Instance
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* First Instance */}
        <TradeDataInstance 
          title="Instance 1" 
          poolAddress={poolAddress} 
          instanceId="1"
        />

        {/* Second Instance (conditional) */}
        {showSecondInstance && (
          <TradeDataInstance 
            title="Instance 2" 
            poolAddress={poolAddress} 
            instanceId="2"
          />
        )}
      </div>

      <div className="mt-6 p-4 bg-gray-800 rounded">
        <h3 className="text-lg font-semibold mb-2">Testing Instructions:</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Open browser DevTools → Network tab → Filter by "WS" (WebSocket)</li>
          <li>Refresh the page and observe WebSocket connections</li>
          <li>With global connection pooling, you should see only ONE "trading-panel-ws" connection</li>
          <li>Click "Show Second Instance" - still only ONE connection should exist</li>
          <li>Change the pool address - both instances should update with the same data</li>
          <li>Check console logs for connection management details</li>
        </ol>
      </div>

      <div className="mt-6 p-4 bg-gray-800 rounded">
        <h3 className="text-lg font-semibold mb-2">Connection Monitoring:</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium mb-2">Global Service Stats:</h4>
            <div className="space-y-1">
              <div>Connected: {connectionMonitor.stats.isConnected ? '✅' : '❌'}</div>
              <div>Pools: {connectionMonitor.stats.poolCount}</div>
              <div>Subscribers: {connectionMonitor.stats.subscriberCount}</div>
              <div>Callbacks: {connectionMonitor.stats.callbackCount}</div>
              <div>Has Leaks: {connectionMonitor.leaks.hasLeaks ? '❌' : '✅'}</div>
            </div>
          </div>
          <div>
            <h4 className="font-medium mb-2">Browser WebSocket Count:</h4>
            <div className="space-y-1">
              <div>Active Connections: {browserMonitor.webSocketCount}</div>
              <div>Multiple Connections: {browserMonitor.isMultipleConnections ? '❌' : '✅'}</div>
              <div>Expected: 1 connection</div>
            </div>
          </div>
        </div>

        {connectionMonitor.leaks.hasLeaks && (
          <div className="mt-4 p-3 bg-red-900/20 border border-red-500 rounded">
            <h4 className="font-medium text-red-400 mb-2">⚠️ Connection Leaks Detected:</h4>
            <ul className="text-sm text-red-300 space-y-1">
              {connectionMonitor.leaks.issues.map((issue, index) => (
                <li key={index}>• {issue}</li>
              ))}
            </ul>
          </div>
        )}

        <div className="mt-4 flex gap-2">
          <button
            onClick={connectionMonitor.forceRefresh}
            className="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm"
          >
            🔄 Refresh Stats
          </button>
          <button
            onClick={connectionMonitor.forceCleanup}
            className="px-3 py-1 bg-red-600 hover:bg-red-700 rounded text-sm"
          >
            🧹 Force Cleanup
          </button>
        </div>
      </div>

      {/* Connection Monitor Component */}
      <ConnectionMonitor />
    </div>
  );
};

interface TradeDataInstanceProps {
  title: string;
  poolAddress: string;
  instanceId: string;
}

const TradeDataInstance: React.FC<TradeDataInstanceProps> = ({ title, poolAddress, instanceId }) => {
  const { trades, isLoading, isConnected, error, lastUpdate, latestRawTrade } = useGlobalTradeData(poolAddress);

  return (
    <div className="border border-gray-600 rounded-lg p-4">
      <h2 className="text-xl font-semibold mb-4">{title}</h2>
      
      {/* Connection Status */}
      <div className="mb-4 p-3 bg-gray-800 rounded">
        <h3 className="font-medium mb-2">Connection Status</h3>
        <div className="space-y-1 text-sm">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
            <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
          </div>
          <div>Loading: {isLoading ? 'Yes' : 'No'}</div>
          <div>Error: {error || 'None'}</div>
          <div>Last Update: {lastUpdate ? new Date(lastUpdate).toLocaleTimeString() : 'Never'}</div>
          <div>Pool: {poolAddress}</div>
        </div>
      </div>

      {/* Trade Data */}
      <div className="mb-4 p-3 bg-gray-800 rounded">
        <h3 className="font-medium mb-2">Trade Data</h3>
        <div className="space-y-1 text-sm">
          <div>Trades Count: {trades.length}</div>
          <div>Latest Raw Trade: {latestRawTrade ? 'Available' : 'None'}</div>
          {latestRawTrade && (
            <div className="mt-2 p-2 bg-gray-700 rounded text-xs">
              <div>Latest Trade ID: {latestRawTrade.id}</div>
              <div>Type: {latestRawTrade.type}</div>
              <div>Amount: {latestRawTrade.amount}</div>
              <div>Has Pair Data: {latestRawTrade.pairData ? 'Yes' : 'No'}</div>
              {latestRawTrade.pairData?.liquidity && (
                <div>Liquidity: ${latestRawTrade.pairData.liquidity.toLocaleString()}</div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Recent Trades */}
      <div className="p-3 bg-gray-800 rounded">
        <h3 className="font-medium mb-2">Recent Trades ({trades.length})</h3>
        <div className="max-h-40 overflow-y-auto">
          {trades.length === 0 ? (
            <div className="text-gray-400 text-sm">No trades available</div>
          ) : (
            <div className="space-y-1">
              {trades.slice(0, 5).map((trade, index) => (
                <div key={trade.id || index} className="text-xs p-2 bg-gray-700 rounded">
                  <div className="flex justify-between">
                    <span className={trade.type === 'buy' ? 'text-green-400' : 'text-red-400'}>
                      {trade.type.toUpperCase()}
                    </span>
                    <span>{trade.amount}</span>
                  </div>
                  <div className="text-gray-400">
                    {trade.trader} • {trade.age}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GlobalTradeDataTest;
