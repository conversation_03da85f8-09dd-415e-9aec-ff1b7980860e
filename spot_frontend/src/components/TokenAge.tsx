import React, { useState, useEffect } from 'react';
import { calculateTokenAge, getAgeDisplayClasses, shouldUpdateLive, TokenAgeInfo } from '@/utils/tokenAge';

interface TokenAgeProps {
  createdAt: string | null | undefined;
  className?: string;
}

/**
 * TokenAge Component
 * Displays token age with live countdown for new tokens (< 1 minute)
 * and static age display for older tokens
 */
const TokenAge: React.FC<TokenAgeProps> = ({ createdAt, className = '' }) => {
  const [ageInfo, setAgeInfo] = useState<TokenAgeInfo>(() => calculateTokenAge(createdAt));

  useEffect(() => {
    // Initial calculation
    setAgeInfo(calculateTokenAge(createdAt));

    // Set up interval for live updates if needed
    if (shouldUpdateLive(createdAt)) {
      const interval = setInterval(() => {
        const newAgeInfo = calculateTokenAge(createdAt);
        setAgeInfo(newAgeInfo);

        // Stop updating once token reaches 1 minute old
        if (!newAgeInfo.isLive) {
          clearInterval(interval);
        }
      }, 1000); // Update every second

      return () => clearInterval(interval);
    }
  }, [createdAt]);

  // Don't render anything if no age info
  if (!ageInfo.displayText) {
    return null;
  }

  const displayClasses = getAgeDisplayClasses(ageInfo);

  return (
    <div className={`text-base ${displayClasses} ${className}`}>
      {ageInfo.displayText}
    </div>
  );
};

export default TokenAge;
