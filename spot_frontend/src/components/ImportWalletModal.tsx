import React, { useState } from 'react';
import { usePrivy, useImportWallet } from '@privy-io/react-auth';
import { useImportWallet as useSolanaImportWallet } from '@privy-io/react-auth/solana';
import { IoClose, IoEye, IoEyeOff } from 'react-icons/io5';
import { useSessionSigners } from '../utils/sessionSigners';

interface ImportWalletModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ImportWalletModal: React.FC<ImportWalletModalProps> = ({ isOpen, onClose }) => {
  const { ready, authenticated } = usePrivy();
  const { importWallet: importEVMWallet } = useImportWallet();
  const { importWallet: importSolanaWallet } = useSolanaImportWallet();
  const { delegateEvmWallet, delegateSolanaWallet } = useSessionSigners();

  const [privateKey, setPrivateKey] = useState('');
  const [walletType, setWalletType] = useState<'evm' | 'solana'>('evm');
  const [isImporting, setIsImporting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showPrivateKey, setShowPrivateKey] = useState(false);
  const [sessionSigningStatus, setSessionSigningStatus] = useState<string | null>(null);

  if (!isOpen) return null;

  const isAuthenticated = ready && authenticated;

  /**
   * Enable session signing for the newly imported wallet
   * @param walletAddress The address of the imported wallet
   * @param type The wallet type ('evm' or 'solana')
   */
  const enableSessionSigning = async (walletAddress: string, type: 'evm' | 'solana'): Promise<void> => {
    try {
      setSessionSigningStatus('Enabling session signing...');
      console.log(`Enabling session signing for imported ${type} wallet:`, walletAddress);

      let delegationSuccess = false;
      if (type === 'evm') {
        delegationSuccess = await delegateEvmWallet(walletAddress);
      } else {
        delegationSuccess = await delegateSolanaWallet(walletAddress);
      }

      if (delegationSuccess) {
        setSessionSigningStatus('Session signing enabled successfully!');
        console.log(`Session signing enabled for ${type} wallet:`, walletAddress);

        // Dispatch event to notify other components
        window.dispatchEvent(new CustomEvent('wallet-session-signing-enabled', {
          detail: {
            address: walletAddress,
            type: type,
            delegated: true
          }
        }));

        // Also trigger general session signer initialization to catch any missed wallets
        window.dispatchEvent(new CustomEvent('initialize-session-signers', {
          detail: {
            address: walletAddress,
            type: type,
            source: 'wallet-import'
          }
        }));
      } else {
        setSessionSigningStatus('Session signing setup failed, but wallet imported successfully');
        console.warn(`Failed to enable session signing for ${type} wallet:`, walletAddress);
      }
    } catch (error) {
      console.error('Error enabling session signing for imported wallet:', error);
      setSessionSigningStatus('Session signing setup failed, but wallet imported successfully');
    }
  };

  const validatePrivateKey = (key: string, type: 'evm' | 'solana'): boolean => {
    if (!key.trim()) return false;
    
    if (type === 'evm') {
      // EVM private key validation (64 hex characters, optionally prefixed with 0x)
      const cleanKey = key.startsWith('0x') ? key.slice(2) : key;
      return /^[a-fA-F0-9]{64}$/.test(cleanKey);
    } else {
      // Solana private key validation (base58 encoded, typically 88 characters)
      // This is a basic validation - Privy will do the actual validation
      return key.length >= 80 && key.length <= 90 && /^[1-9A-HJ-NP-Za-km-z]+$/.test(key);
    }
  };

  const handleImportWallet = async () => {
    if (!isAuthenticated) {
      setError('Please authenticate first');
      return;
    }

    if (!validatePrivateKey(privateKey, walletType)) {
      setError(`Invalid ${walletType.toUpperCase()} private key format`);
      return;
    }

    setIsImporting(true);
    setError(null);
    setSuccess(null);

    try {
      let result;
      if (walletType === 'evm') {
        result = await importEVMWallet({ privateKey });
      } else {
        result = await importSolanaWallet({ privateKey });
      }

      console.log('Wallet imported successfully:', result);
      setSuccess(`${walletType.toUpperCase()} wallet imported successfully!`);

      // Extract wallet address from result
      let walletAddress: string | null = null;
      if (result && typeof result === 'object') {
        // Try different possible address fields
        walletAddress = (result as any).address ||
                      (result as any).publicKey ||
                      (result as any).wallet?.address ||
                      null;
      }

      // Enable session signing for the imported wallet
      if (walletAddress) {
        console.log(`Attempting to enable session signing for imported wallet: ${walletAddress}`);
        await enableSessionSigning(walletAddress, walletType);
      } else {
        console.warn('Could not extract wallet address from import result, session signing may not be enabled');
        setSessionSigningStatus('Could not enable session signing - address not found');
      }

      setPrivateKey('');

      // Auto-close after success with longer delay to show session signing status
      setTimeout(() => {
        handleClose();
      }, 3000);
      
    } catch (err) {
      console.error('Import wallet error:', err);
      if (err instanceof Error) {
        if (err.message.includes('already imported')) {
          setError('You have already imported a wallet. Only one wallet can be imported per account.');
        } else if (err.message.includes('invalid')) {
          setError('Invalid private key. Please check the format and try again.');
        } else {
          setError(err.message);
        }
      } else {
        setError('Failed to import wallet. Please try again.');
      }
    } finally {
      setIsImporting(false);
    }
  };

  const handleClose = () => {
    setPrivateKey('');
    setError(null);
    setSuccess(null);
    setSessionSigningStatus(null);
    setShowPrivateKey(false);
    onClose();
  };

  const getPlaceholderText = () => {
    if (walletType === 'evm') {
      return 'Enter your Ethereum private key (64 hex characters)';
    } else {
      return 'Enter your Solana private key (base58 encoded)';
    }
  };

  return (
    <div className="fixed inset-0 flex justify-center items-center bg-black/80 z-50">
      <div className="bg-[#181C20] border border-gray-600 max-w-lg w-full p-6 rounded-xl shadow-2xl relative">
        {/* Close Button */}
        <button 
          onClick={handleClose} 
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
        >
          <IoClose size={24} />
        </button>

        {/* Modal Title */}
        <div className="flex items-center space-x-3 text-white text-2xl font-bold mb-6">
          <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
            <svg 
              width="20" 
              height="20" 
              viewBox="0 0 24 24" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" 
                stroke="#3B82F6" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
              <path 
                d="M14 2V8H20" 
                stroke="#3B82F6" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
              <path 
                d="M12 18V12" 
                stroke="#3B82F6" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
              <path 
                d="M9 15L12 12L15 15" 
                stroke="#3B82F6" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <h2>Import a Wallet</h2>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {/* Description */}
          <div className="text-gray-300">
            <p className="mb-4">
              Import an existing wallet by entering its private key. This will add the wallet to your account.
            </p>
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <svg 
                  width="20" 
                  height="20" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  className="text-red-500 mt-0.5 flex-shrink-0"
                >
                  <path 
                    d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                </svg>
                <div>
                  <p className="text-red-500 font-medium text-sm">Security Warning</p>
                  <p className="text-red-400 text-sm mt-1">
                    Only import wallets you own. Never enter private keys from untrusted sources.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Success Message */}
          {success && (
            <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <svg 
                  width="20" 
                  height="20" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  className="text-green-500"
                >
                  <path 
                    d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                </svg>
                <p className="text-green-400 text-sm">{success}</p>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  className="text-red-500"
                >
                  <path
                    d="M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            </div>
          )}

          {/* Session Signing Status */}
          {sessionSigningStatus && (
            <div className={`rounded-lg p-4 ${
              sessionSigningStatus.includes('successfully')
                ? 'bg-blue-500/10 border border-blue-500/30'
                : sessionSigningStatus.includes('failed')
                  ? 'bg-yellow-500/10 border border-yellow-500/30'
                  : 'bg-blue-500/10 border border-blue-500/30'
            }`}>
              <div className="flex items-center space-x-2">
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  className={
                    sessionSigningStatus.includes('successfully')
                      ? 'text-blue-500'
                      : sessionSigningStatus.includes('failed')
                        ? 'text-yellow-500'
                        : 'text-blue-500'
                  }
                >
                  <path
                    d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2579 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.01127 9.77251C4.28054 9.5799 4.48571 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <p className={`text-sm ${
                  sessionSigningStatus.includes('successfully')
                    ? 'text-blue-400'
                    : sessionSigningStatus.includes('failed')
                      ? 'text-yellow-400'
                      : 'text-blue-400'
                }`}>
                  {sessionSigningStatus}
                </p>
              </div>
            </div>
          )}

          {/* Import Form */}
          {isAuthenticated ? (
            <div className="space-y-4">
              {/* Wallet Type Selection */}
              <div>
                <p className="text-gray-400 text-lg mb-3">Select wallet type:</p>
                <div className="flex bg-[#1D2226] rounded-lg p-1">
                  <button
                    className={`flex-1 px-4 py-2 rounded-md text-lg transition-colors ${
                      walletType === 'evm' ? "bg-white text-[#1D2226]" : "text-white"
                    }`}
                    onClick={() => setWalletType('evm')}
                  >
                    Ethereum
                  </button>
                  <button
                    className={`flex-1 px-4 py-2 rounded-md text-lg transition-colors ${
                      walletType === 'solana' ? "bg-white text-[#1D2226]" : "text-white"
                    }`}
                    onClick={() => setWalletType('solana')}
                  >
                    Solana
                  </button>
                </div>
              </div>

              {/* Private Key Input */}
              <div>
                <p className="text-gray-400 text-lg mb-3">Private key:</p>
                <div className="relative">
                  <textarea
                    value={privateKey}
                    onChange={(e) => setPrivateKey(e.target.value)}
                    placeholder={getPlaceholderText()}
                    className="bg-[#1D2226] text-white w-full p-4 rounded-lg outline-none border border-gray-600 focus:border-gray-400 transition-colors resize-none"
                    rows={3}
                    type={showPrivateKey ? 'text' : 'password'}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPrivateKey(!showPrivateKey)}
                    className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
                  >
                    {showPrivateKey ? <IoEyeOff size={20} /> : <IoEye size={20} />}
                  </button>
                </div>
              </div>

              {/* Import Button */}
              <button
                onClick={handleImportWallet}
                disabled={isImporting || !privateKey.trim() || !validatePrivateKey(privateKey, walletType)}
                className="bg-[#14FFA2] hover:bg-[#12E892] text-black w-full py-4 rounded-2xl text-xl font-bold transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isImporting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black"></div>
                    <span>Importing...</span>
                  </div>
                ) : (
                  'Import Wallet'
                )}
              </button>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-400">Please authenticate to import a wallet.</p>
            </div>
          )}
        </div>

        {/* Cancel Button */}
        <button
          className="bg-gray-600 hover:bg-gray-500 text-white w-full py-4 mt-4 rounded-2xl text-xl font-bold transition-colors"
          onClick={handleClose}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default ImportWalletModal;
