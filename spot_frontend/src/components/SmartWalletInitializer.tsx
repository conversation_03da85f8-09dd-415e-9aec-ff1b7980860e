import React, { useEffect, useState, useRef } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { useSmartWallets } from '@privy-io/react-auth/smart-wallets';
import { isRealSmartWallet } from '../utils/smartWalletUtils';

// Extend Window interface to include our custom properties
declare global {
  interface Window {
    walletInitializationComplete: boolean;
    walletRequestsInProgress: () => boolean;
    DISABLE_AUTO_CONNECT: boolean;
  }
}

/**
 * The SmartWalletInitializer component ensures that smart wallets are properly
 * created and initialized after login. It handles the case where a user logs in
 * but a smart wallet isn't immediately created.
 */
const SmartWalletInitializer: React.FC = () => {
  // Access Privy and SmartWallets hooks
  const { authenticated, ready, user } = usePrivy();
  const smartWallets = useSmartWallets();
  const client = smartWallets.client;
  const [creationAttempted, setCreationAttempted] = useState(false);
  const [initializationComplete, setInitializationComplete] = useState(false);
  const creationAttemptRef = useRef(0);
  const creationInProgressRef = useRef(false);

  // Track wallet requests in progress to prevent overlap
  const walletRequestsInProgressRef = useRef(0);

  // createSmartWallet might exist but not be in the TypeScript interface
  // @ts-ignore - Access createSmartWallet via bracket notation to avoid type errors
  const createSmartWallet = smartWallets['createSmartWallet'];

  // Check if user already has a wallet to prevent unnecessary connections
  const hasExistingWallet = () => {
    // Check if there's a stored smart wallet address
    const storedAddress = localStorage.getItem('smart_wallet_address');

    // Check if we already have a smart wallet client
    const hasClient = client && isRealSmartWallet(client);

    // Check if creation was previously attempted this session
    const previousAttempt = localStorage.getItem('smart_wallet_creation_attempted') === 'true';

    return !!storedAddress || hasClient || previousAttempt || creationAttempted;
  };

  // Set wallet initialization complete once we have a client
  useEffect(() => {
    if (client && isRealSmartWallet(client)) {
      console.log("Valid smart wallet client found, marking initialization complete", client);
      setInitializationComplete(true);

      // Store the wallet address for reference
      if (client.account?.address) {
        localStorage.setItem('smart_wallet_address', client.account.address);
        console.log("Smart wallet address stored:", client.account.address);
      }

      // Notify app that wallet setup is complete
      window.dispatchEvent(new CustomEvent('wallet-setup-complete', {
        detail: { address: client.account?.address }
      }));

      // Also trigger session signer initialization
      window.dispatchEvent(new CustomEvent('initialize-session-signers', {
        detail: { address: client.account?.address }
      }));
    }
  }, [client]);

  // Listen for the force-smart-wallet-creation event - ONLY TRIGGERED BY EXPLICIT USER ACTION
  useEffect(() => {
    const handleForceCreation = async (event: any) => {
      // Check if auto-connect is disabled
      if (window.DISABLE_AUTO_CONNECT === true) {
        console.log("Automatic wallet creation disabled via DISABLE_AUTO_CONNECT flag");
        return;
      }

      console.log("Force smart wallet creation event received");

      // If creation is in progress, don't trigger again
      if (creationInProgressRef.current) {
        console.log("Skipping wallet creation - creation already in progress");
        return;
      }

      // If we already attempted creation multiple times or have a wallet, don't trigger again
      if (creationAttemptRef.current >= 2 || hasExistingWallet()) {
        console.log("Skipping wallet creation - wallet already exists or maximum creation attempts reached");
        return;
      }

      // Increment creation attempt counter
      creationAttemptRef.current++;
      creationInProgressRef.current = true;

      // If no client exists but we're authenticated, create a smart wallet
      if (authenticated && !isRealSmartWallet(client)) {
        console.log("No smart wallet client found, creating one");

        // Mark that we've attempted creation to prevent multiple attempts
        setCreationAttempted(true);
        localStorage.setItem('smart_wallet_creation_attempted', 'true');

        // Increment wallet requests counter
        walletRequestsInProgressRef.current++;

        try {
          // @ts-ignore - createSmartWallet may not be in the type definition but should be available
          if (typeof createSmartWallet === 'function') {
            console.log("Creating smart wallet via createSmartWallet function");
            // @ts-ignore
            await createSmartWallet();
            console.log("Smart wallet creation completed");
          } else {
            console.warn("createSmartWallet function not available");
          }
        } catch (error) {
          console.error("Error creating smart wallet:", error);
        } finally {
          // Decrement wallet requests counter
          walletRequestsInProgressRef.current--;
          creationInProgressRef.current = false;
        }
      } else {
        creationInProgressRef.current = false;
      }
    };

    window.addEventListener('force-smart-wallet-creation', handleForceCreation);

    return () => {
      window.removeEventListener('force-smart-wallet-creation', handleForceCreation);
    };
  }, [authenticated, client, createSmartWallet, creationAttempted]);

  // Check for missing smart wallet on authentication
  useEffect(() => {
    // Exit if auto-connect is disabled
    if (window.DISABLE_AUTO_CONNECT === true) {
      console.log("Auto wallet creation disabled - not checking for missing wallets");

      // Even though we're not creating wallets, we should still track the existing ones
      if (client && isRealSmartWallet(client)) {
        console.log("Valid client exists - marking initialization complete without creation attempts");
        setInitializationComplete(true);

        if (client.account?.address) {
          localStorage.setItem('smart_wallet_address', client.account.address);
        }
      }

      return;
    }

    // Wait for Privy to be ready and user to be authenticated
    if (!ready || !authenticated) return;

    const checkWallet = async () => {
      // If we already have a wallet or initialization is complete, don't trigger again
      if (initializationComplete || hasExistingWallet()) {
        console.log("Smart wallet check - wallet exists or initialization complete, skipping");
        return;
      }

      // If we've already tried multiple times, don't trigger again
      if (creationAttemptRef.current >= 2) {
        console.log("Smart wallet check - maximum creation attempts reached, skipping");
        return;
      }

      // If creation is already in progress, don't trigger again
      if (creationInProgressRef.current) {
        console.log("Smart wallet check - creation already in progress, skipping");
        return;
      }

      creationInProgressRef.current = true;

      // If no client exists or it's not a valid smart wallet
      if (!client || !isRealSmartWallet(client)) {
        console.log("Smart wallet check - no valid client found");

        // Wait a moment for initialization to complete
        setTimeout(async () => {
          // Check again if wallet was created
          if (!client || !isRealSmartWallet(client)) {
            console.log("Still no smart wallet after initial delay, forcing creation");

            // Mark that we've attempted creation
            setCreationAttempted(true);
            localStorage.setItem('smart_wallet_creation_attempted', 'true');

            // Increment wallet requests counter
            walletRequestsInProgressRef.current++;
            creationAttemptRef.current++;

            try {
              // @ts-ignore - Try to use createSmartWallet if available
              if (typeof createSmartWallet === 'function') {
                console.log("Creating smart wallet via createSmartWallet function");
                // @ts-ignore
                await createSmartWallet();
                console.log("Smart wallet creation completed");
              } else {
                console.warn("createSmartWallet function not available");
                // For debugging only - don't actually dispatch
                console.log("Would have triggered smart wallet creation event");
              }
            } catch (error) {
              console.error("Error during manual smart wallet creation:", error);
            } finally {
              // Decrement wallet requests counter
              walletRequestsInProgressRef.current--;
              creationInProgressRef.current = false;
            }
          } else {
            console.log("Smart wallet client found after delay", client);
            creationInProgressRef.current = false;
          }
        }, 3000);
      } else {
        console.log("Valid smart wallet client found", client);
        creationInProgressRef.current = false;

        // Store the wallet address for reference
        if (client.account?.address) {
          localStorage.setItem('smart_wallet_address', client.account.address);
          console.log("Smart wallet address stored:", client.account.address);

          // Mark initialization as complete
          setInitializationComplete(true);

          // Notify app that wallet setup is complete
          window.dispatchEvent(new CustomEvent('wallet-setup-complete', {
            detail: { address: client.account.address }
          }));

          // Also trigger session signer initialization
          window.dispatchEvent(new CustomEvent('initialize-session-signers', {
            detail: { address: client.account.address }
          }));
        }
      }
    };

    checkWallet();
  }, [ready, authenticated, client, user, creationAttempted]);

  // Expose wallet initialization status
  useEffect(() => {
    // Make initialization status available globally
    window.walletInitializationComplete = initializationComplete;
    window.walletRequestsInProgress = () => walletRequestsInProgressRef.current > 0;

    // Log wallet initialization status when it changes
    if (initializationComplete) {
      console.log("Smart wallet initialization complete");
    }
  }, [initializationComplete]);

  // This component doesn't render anything visible
  return null;
};

export default SmartWalletInitializer;