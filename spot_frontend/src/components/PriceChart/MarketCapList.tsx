import React, { useState, useEffect } from 'react';
import { homeAPI } from '../../utils/api';

interface TokenData {
  id: string;
  symbol: string;
  name: string;
  price: number;
  marketCap: number;
  change24h: number;
  volume24h: number;
}

interface MarketCapListProps {
  isVisible: boolean;
  onToggle: () => void;
}



const MarketCapList: React.FC<MarketCapListProps> = ({ isVisible, onToggle }) => {
  const [tokens, setTokens] = useState<TokenData[]>([]);
  const [sortBy, setSortBy] = useState<'marketCap' | 'price' | 'change24h'>('marketCap');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch real market data
  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch connect coins data which includes market information
        const connectCoins = await homeAPI.getConnectCoins();

        if (connectCoins && connectCoins.length > 0) {
          const formattedTokens: TokenData[] = connectCoins.map((coin, index) => ({
            id: coin.id || index.toString(),
            symbol: coin.symbol || '',
            name: coin.name || '',
            price: coin.current_price || 0,
            marketCap: coin.market_cap || 0,
            change24h: coin.market_cap_change_percentage_24h || 0,
            volume24h: coin.total_volume || 0
          }));

          setTokens(formattedTokens);
        } else {
          setTokens([]);
        }
      } catch (err) {
        console.error('Error fetching market data:', err);
        setError('Failed to load market data');
        setTokens([]);
      } finally {
        setLoading(false);
      }
    };

    if (isVisible) {
      fetchMarketData();
    }
  }, [isVisible]);

  const formatNumber = (num: number): string => {
    if (num >= 1e9) {
      return `$${(num / 1e9).toFixed(2)}B`;
    }
    if (num >= 1e6) {
      return `$${(num / 1e6).toFixed(2)}M`;
    }
    if (num >= 1e3) {
      return `$${(num / 1e3).toFixed(2)}K`;
    }
    return `$${num.toFixed(2)}`;
  };

  const formatPrice = (price: number): string => {
    if (price < 0.01) {
      return `$${price.toFixed(6)}`;
    }
    return `$${price.toFixed(2)}`;
  };

  const sortedTokens = [...tokens].sort((a, b) => {
    switch (sortBy) {
      case 'marketCap':
        return b.marketCap - a.marketCap;
      case 'price':
        return b.price - a.price;
      case 'change24h':
        return b.change24h - a.change24h;
      default:
        return 0;
    }
  });

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed right-4 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white p-2 rounded-l-lg border border-gray-700 hover:bg-gray-700 transition-colors z-10"
        title="Show Market Cap List"
      >
        ◀
      </button>
    );
  }

  return (
    <div className="w-80 bg-black border-l border-gray-800 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <h3 className="text-white font-semibold">Market Cap</h3>
          <button
            onClick={onToggle}
            className="text-gray-400 hover:text-white transition-colors"
            title="Hide Market Cap List"
          >
            ▶
          </button>
        </div>
        
        {/* Sort Options */}
        <div className="flex space-x-2 mt-3">
          {[
            { key: 'marketCap', label: 'Cap' },
            { key: 'price', label: 'Price' },
            { key: 'change24h', label: '24h' }
          ].map((option) => (
            <button
              key={option.key}
              onClick={() => setSortBy(option.key as any)}
              className={`px-2 py-1 text-xs rounded transition-colors ${
                sortBy === option.key
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Token List */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          <div className="p-4 text-center text-gray-400">
            Loading market data...
          </div>
        ) : error ? (
          <div className="p-4 text-center text-red-400">
            {error}
          </div>
        ) : tokens.length === 0 ? (
          <div className="p-4 text-center text-gray-400">
            No market data available
          </div>
        ) : (
          sortedTokens.map((token, index) => (
            <div
              key={token.id}
              className="p-3 border-b border-gray-900 hover:bg-gray-900 transition-colors cursor-pointer"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">#{index + 1}</span>
                    <span className="text-white font-medium">{token.symbol}</span>
                  </div>
                  <div className="text-xs text-gray-400 mt-1">{token.name}</div>
                </div>

                <div className="text-right">
                  <div className="text-white font-mono text-sm">
                    {formatPrice(token.price)}
                  </div>
                  <div className="text-xs text-gray-400">
                    {formatNumber(token.marketCap)}
                  </div>
                  <div className={`text-xs font-mono ${
                    token.change24h >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {token.change24h >= 0 ? '+' : ''}{token.change24h.toFixed(2)}%
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-gray-800 text-center">
        <button className="text-xs text-gray-400 hover:text-white transition-colors">
          View All Markets
        </button>
      </div>
    </div>
  );
};

export default MarketCapList;
