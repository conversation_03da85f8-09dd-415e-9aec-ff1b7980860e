import React, { useState } from 'react';

interface TradingToolsSidebarProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

interface TradingTool {
  id: string;
  name: string;
  icon: React.ReactNode;
  category: 'drawing' | 'analysis' | 'annotation';
  hotkey?: string;
}

const tradingTools: TradingTool[] = [
  // Drawing Tools - Modern TradingView style icons
  {
    id: 'cursor',
    name: '<PERSON><PERSON><PERSON>',
    hotkey: 'Esc',
    icon: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path d="M3 3l11 4-4 1-2 4-5-9z" fill="currentColor"/>
      </svg>
    ),
    category: 'drawing'
  },
  {
    id: 'crosshair',
    name: '<PERSON>hair',
    hotkey: 'Alt+C',
    icon: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path d="M9 1v7m0 2v7M1 9h7m2 0h7"/>
        <circle cx="9" cy="9" r="2" fill="none"/>
      </svg>
    ),
    category: 'drawing'
  },
  {
    id: 'trendline',
    name: 'Trend Line',
    hotkey: 'Alt+T',
    icon: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="currentColor" strokeWidth="1.8">
        <path d="M3 15L15 3"/>
        <circle cx="3" cy="15" r="1.5" fill="currentColor"/>
        <circle cx="15" cy="3" r="1.5" fill="currentColor"/>
      </svg>
    ),
    category: 'drawing'
  },
  {
    id: 'horizontal',
    name: 'Horizontal Line',
    hotkey: 'Alt+H',
    icon: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="currentColor" strokeWidth="1.8">
        <path d="M2 9h14"/>
        <circle cx="2" cy="9" r="1.5" fill="currentColor"/>
        <circle cx="16" cy="9" r="1.5" fill="currentColor"/>
      </svg>
    ),
    category: 'drawing'
  },
  {
    id: 'vertical',
    name: 'Vertical Line',
    hotkey: 'Alt+V',
    icon: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="currentColor" strokeWidth="1.8">
        <path d="M9 2v14"/>
        <circle cx="9" cy="2" r="1.5" fill="currentColor"/>
        <circle cx="9" cy="16" r="1.5" fill="currentColor"/>
      </svg>
    ),
    category: 'drawing'
  },
  {
    id: 'rectangle',
    name: 'Rectangle',
    hotkey: 'Alt+R',
    icon: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="currentColor" strokeWidth="1.5">
        <rect x="3" y="5" width="12" height="8" rx="1"/>
      </svg>
    ),
    category: 'drawing'
  },
  {
    id: 'ellipse',
    name: 'Ellipse',
    hotkey: 'Alt+E',
    icon: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="currentColor" strokeWidth="1.5">
        <ellipse cx="9" cy="9" rx="6" ry="4"/>
      </svg>
    ),
    category: 'drawing'
  },

  // Analysis Tools
  {
    id: 'fibonacci',
    name: 'Fibonacci Retracement',
    hotkey: 'Alt+F',
    icon: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="currentColor" strokeWidth="1.2">
        <path d="M3 15L15 3"/>
        <path d="M3 12h12" opacity="0.8"/>
        <path d="M3 9h12" opacity="0.6"/>
        <path d="M3 6h12" opacity="0.4"/>
      </svg>
    ),
    category: 'analysis'
  },
  {
    id: 'parallel',
    name: 'Parallel Channel',
    hotkey: 'Alt+P',
    icon: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path d="M3 13L15 5"/>
        <path d="M3 9L15 1"/>
      </svg>
    ),
    category: 'analysis'
  },
  {
    id: 'pitchfork',
    name: 'Pitchfork',
    hotkey: 'Alt+I',
    icon: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="currentColor" strokeWidth="1.2">
        <path d="M9 2L9 16"/>
        <path d="M4 7L14 11"/>
        <path d="M4 11L14 7"/>
      </svg>
    ),
    category: 'analysis'
  },

  // Annotation Tools
  {
    id: 'text',
    name: 'Text',
    hotkey: 'Alt+X',
    icon: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path d="M5 4h8M9 4v10"/>
        <path d="M7 14h4"/>
      </svg>
    ),
    category: 'annotation'
  },
  {
    id: 'note',
    name: 'Note',
    hotkey: 'Alt+N',
    icon: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" stroke="currentColor" strokeWidth="1.5">
        <path d="M14 2H4a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h10l4 4V4a2 2 0 0 0-2-2z"/>
        <path d="M6 7h6M6 10h4"/>
      </svg>
    ),
    category: 'annotation'
  },
];

const TradingToolsSidebar: React.FC<TradingToolsSidebarProps> = ({
  isCollapsed,
  onToggleCollapse
}) => {
  const [selectedTool, setSelectedTool] = useState<string>('cursor');

  const handleToolSelect = (toolId: string) => {
    setSelectedTool(toolId);
    // Here you would implement the actual tool selection logic
    console.log(`Selected tool: ${toolId}`);
  };

  return (
    <div className={`bg-[#0d1421] border-r border-gray-700/50 transition-all duration-300 ${
      isCollapsed ? 'w-12' : 'w-16'
    } flex flex-col shadow-lg`}>
      {/* Toggle Button */}
      <button
        onClick={onToggleCollapse}
        className="p-3 text-gray-400 hover:text-white hover:bg-gray-700/30 transition-all duration-200 border-b border-gray-700/30 group"
        title={isCollapsed ? 'Expand Tools' : 'Collapse Tools'}
      >
        <svg width="14" height="14" viewBox="0 0 14 14" fill="currentColor" className="group-hover:scale-110 transition-transform">
          <path d={isCollapsed ? "M4 2l6 5-6 5V2z" : "M10 2L4 7l6 5V2z"} />
        </svg>
      </button>

      {/* Tools List */}
      <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent">
        {tradingTools.map((tool, index) => (
          <div key={tool.id} className="relative group">
            <button
              onClick={() => handleToolSelect(tool.id)}
              className={`w-full p-3 flex flex-col items-center justify-center transition-all duration-200 border-b border-gray-800/30 relative ${
                selectedTool === tool.id
                  ? 'bg-blue-600/20 text-blue-400 border-blue-500/30'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700/20'
              }`}
              title={`${tool.name}${tool.hotkey ? ` (${tool.hotkey})` : ''}`}
            >
              {/* Active indicator */}
              {selectedTool === tool.id && (
                <div className="absolute left-0 top-0 bottom-0 w-0.5 bg-blue-500"></div>
              )}

              <div className="flex items-center justify-center transform group-hover:scale-110 transition-transform duration-200">
                {tool.icon}
              </div>

              {!isCollapsed && (
                <div className="text-[10px] mt-1 truncate max-w-full font-medium opacity-80">
                  {tool.name}
                </div>
              )}
            </button>

            {/* Tooltip for collapsed state */}
            {isCollapsed && (
              <div className="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-nowrap">
                {tool.name}
                {tool.hotkey && <span className="text-gray-400 ml-1">({tool.hotkey})</span>}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Additional Controls */}
      <div className="border-t border-gray-700/30 bg-gray-900/20">
        <button
          className="w-full p-3 text-gray-400 hover:text-white hover:bg-gray-700/30 transition-all duration-200 flex items-center justify-center group"
          title="Chart Settings"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" stroke="currentColor" strokeWidth="1.5" className="group-hover:rotate-90 transition-transform duration-300">
            <circle cx="8" cy="8" r="3"/>
            <path d="M12 8a4 4 0 0 0-.5-1.9l1.4-1.4a6 6 0 0 0-2.1-2.1L9.4 4.1A4 4 0 0 0 8 4a4 4 0 0 0-1.4.1L5.2 2.7a6 6 0 0 0-2.1 2.1L4.5 6.1A4 4 0 0 0 4 8a4 4 0 0 0 .5 1.9l-1.4 1.4a6 6 0 0 0 2.1 2.1l1.4-1.4A4 4 0 0 0 8 12a4 4 0 0 0 1.4-.1l1.4 1.4a6 6 0 0 0 2.1-2.1l-1.4-1.4A4 4 0 0 0 12 8z"/>
          </svg>
        </button>
        <button
          className="w-full p-3 text-gray-400 hover:text-white hover:bg-gray-700/30 transition-all duration-200 flex items-center justify-center group"
          title="Keyboard Shortcuts"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" stroke="currentColor" strokeWidth="1.5" className="group-hover:scale-110 transition-transform duration-200">
            <circle cx="8" cy="8" r="6"/>
            <path d="M8 6v4M6 8h4"/>
          </svg>
        </button>
      </div>
    </div>
  );
};

export default TradingToolsSidebar;
