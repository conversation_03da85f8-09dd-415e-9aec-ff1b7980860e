import React, { useState, useEffect } from 'react';
import { usePrivy, useWallets } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import { useSmartWallet } from '../hooks/useSmartWallet';
import { IoClose, IoCheckmarkCircle } from 'react-icons/io5';

interface ManageWalletsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface WalletInfo {
  id: string;
  address: string;
  type: 'ethereum' | 'solana';
  walletClientType: string;
  isEmbedded: boolean;
  isSmartWallet: boolean;
  displayName: string;
}

interface DefaultWallets {
  ethereum?: string;
  solana?: string;
}

const ManageWalletsModal: React.FC<ManageWalletsModalProps> = ({ isOpen, onClose }) => {
  const { ready, authenticated, user } = usePrivy();
  const { wallets } = useWallets();
  const { wallets: solanaWallets } = useSolanaWallets();
  const { smartWalletAddress, isValid: isSmartWalletValid } = useSmartWallet();

  const [allWallets, setAllWallets] = useState<WalletInfo[]>([]);
  const [defaultWallets, setDefaultWallets] = useState<DefaultWallets>({});
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const isAuthenticated = ready && authenticated;

  // Load default wallets from localStorage
  useEffect(() => {
    const stored = localStorage.getItem('defaultWallets');
    if (stored) {
      try {
        setDefaultWallets(JSON.parse(stored));
      } catch (e) {
        console.error('Error parsing stored default wallets:', e);
      }
    }
  }, []);

  // Collect all wallets from different sources
  useEffect(() => {
    if (!isAuthenticated) return;

    const walletList: WalletInfo[] = [];
    const seenAddresses = new Set<string>();

    // Add smart wallet from useSmartWallet hook
    if (smartWalletAddress && isSmartWalletValid) {
      walletList.push({
        id: `smart-wallet-${smartWalletAddress}`,
        address: smartWalletAddress,
        type: 'ethereum',
        walletClientType: 'smart-wallet',
        isEmbedded: true,
        isSmartWallet: true,
        displayName: 'Smart Wallet'
      });
      seenAddresses.add(smartWalletAddress.toLowerCase());
    }

    // Add smart wallet from user object (fallback)
    if (user?.smartWallet?.address && !seenAddresses.has(user.smartWallet.address.toLowerCase())) {
      walletList.push({
        id: `user-smart-wallet-${user.smartWallet.address}`,
        address: user.smartWallet.address,
        type: 'ethereum',
        walletClientType: 'smart-wallet',
        isEmbedded: true,
        isSmartWallet: true,
        displayName: 'Smart Wallet'
      });
      seenAddresses.add(user.smartWallet.address.toLowerCase());
    }

    // Add wallets from linkedAccounts
    if (user?.linkedAccounts) {
      user.linkedAccounts.forEach((account: any) => {
        if (!account || 
            !['wallet', 'smart_wallet', 'eoa_wallet'].includes(account.type) || 
            !account.address || 
            typeof account.address !== 'string' || 
            account.address.includes('@') ||
            seenAddresses.has(account.address.toLowerCase())) {
          return;
        }

        const isEthereum = account.address.startsWith('0x');
        const isEmbedded = account.connectorType === 'embedded' || account.walletClientType === 'privy';
        
        walletList.push({
          id: `linked-${account.address}`,
          address: account.address,
          type: isEthereum ? 'ethereum' : 'solana',
          walletClientType: account.walletClientType || 'unknown',
          isEmbedded,
          isSmartWallet: account.type === 'smart_wallet',
          displayName: isEthereum ? 'Ethereum Wallet' : 'Solana Wallet'
        });
        seenAddresses.add(account.address.toLowerCase());
      });
    }

    // Add wallets from useWallets hook
    if (wallets) {
      wallets.forEach((wallet: any) => {
        if (!wallet || !wallet.address || seenAddresses.has(wallet.address.toLowerCase())) {
          return;
        }

        const isEthereum = wallet.address.startsWith('0x');
        const isEmbedded = (wallet as any).embedded || wallet.walletClientType === 'embedded' || (wallet as any).connectorType === 'embedded';
        
        walletList.push({
          id: `hook-${wallet.address}`,
          address: wallet.address,
          type: isEthereum ? 'ethereum' : 'solana',
          walletClientType: wallet.walletClientType || 'unknown',
          isEmbedded,
          isSmartWallet: wallet.walletClientType === 'smart-wallet',
          displayName: isEthereum ? 'Ethereum Wallet' : 'Solana Wallet'
        });
        seenAddresses.add(wallet.address.toLowerCase());
      });
    }

    // Add Solana wallets from useSolanaWallets hook
    if (solanaWallets) {
      solanaWallets.forEach((wallet: any) => {
        if (!wallet || !wallet.address || seenAddresses.has(wallet.address.toLowerCase())) {
          return;
        }

        const isEmbedded = wallet.walletClientType === 'privy' || wallet.embedded === true;
        
        walletList.push({
          id: `solana-hook-${wallet.address}`,
          address: wallet.address,
          type: 'solana',
          walletClientType: wallet.walletClientType || 'unknown',
          isEmbedded,
          isSmartWallet: false,
          displayName: 'Solana Wallet'
        });
        seenAddresses.add(wallet.address.toLowerCase());
      });
    }

    // Update canDelete property based on default wallet status
    // Default wallets cannot be deleted
    const storedDefaults = localStorage.getItem('defaultWallets');
    let currentDefaults: DefaultWallets = {};
    if (storedDefaults) {
      try {
        currentDefaults = JSON.parse(storedDefaults);
      } catch (e) {
        console.error('Error parsing stored defaults:', e);
      }
    }

    // Auto-select single wallets as default
    const ethereumWalletsList = walletList.filter(w => w.type === 'ethereum');
    const solanaWalletsList = walletList.filter(w => w.type === 'solana');

    // Auto-select if only one wallet of each type exists
    if (ethereumWalletsList.length === 1 && !currentDefaults.ethereum) {
      currentDefaults.ethereum = ethereumWalletsList[0].address;
    }
    if (solanaWalletsList.length === 1 && !currentDefaults.solana) {
      currentDefaults.solana = solanaWalletsList[0].address;
    }

    setAllWallets(walletList);

    // Auto-set defaults if none exist (check localStorage to avoid infinite loop)
    const hasStoredDefaults = storedDefaults && Object.keys(currentDefaults).length > 0;

    if (walletList.length > 0 && !hasStoredDefaults) {
      const newDefaults: DefaultWallets = { ...currentDefaults };

      // Set first Solana wallet as default if none selected
      if (!newDefaults.solana) {
        const solanaWallet = walletList.find(w => w.type === 'solana');
        if (solanaWallet) {
          newDefaults.solana = solanaWallet.address;
        }
      }

      // Set smart wallet as default Ethereum wallet, or first Ethereum wallet if none selected
      if (!newDefaults.ethereum) {
        const smartWallet = walletList.find(w => w.isSmartWallet);
        const ethWallet = walletList.find(w => w.type === 'ethereum');
        if (smartWallet) {
          newDefaults.ethereum = smartWallet.address;
        } else if (ethWallet) {
          newDefaults.ethereum = ethWallet.address;
        }
      }

      if (Object.keys(newDefaults).length > 0) {
        setDefaultWallets(newDefaults);
        localStorage.setItem('defaultWallets', JSON.stringify(newDefaults));
      }
    }
  }, [isAuthenticated, user, wallets, solanaWallets, smartWalletAddress, isSmartWalletValid]);

  // Early return after all hooks to avoid violating Rules of Hooks
  if (!isOpen) return null;

  const handleSetDefault = (address: string, type: 'ethereum' | 'solana') => {
    const newDefaults = { ...defaultWallets, [type]: address };
    setDefaultWallets(newDefaults);
    localStorage.setItem('defaultWallets', JSON.stringify(newDefaults));

    setSuccess(`Default ${type} wallet updated successfully!`);
    setTimeout(() => setSuccess(null), 3000);
  };



  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const getWalletIcon = (wallet: WalletInfo) => {
    if (wallet.isSmartWallet) return 'SW';
    if (wallet.type === 'solana') return 'SOL';
    return 'ETH';
  };

  const getWalletColor = (wallet: WalletInfo) => {
    if (wallet.isSmartWallet) return 'bg-[#14FFA2]';
    if (wallet.type === 'solana') return 'bg-[#9945FF]';
    return 'bg-[#627EEA]';
  };

  const handleClose = () => {
    setError(null);
    setSuccess(null);
    onClose();
  };

  const ethereumWallets = allWallets.filter(w => w.type === 'ethereum');
  const solanaWalletsList = allWallets.filter(w => w.type === 'solana');

  return (
    <div className="fixed inset-0 flex justify-center items-center bg-black/80 z-50">
      <div className="bg-[#181C20] border border-gray-600 max-w-2xl w-full p-6 rounded-xl shadow-2xl relative max-h-[90vh] overflow-y-auto">
        {/* Close Button */}
        <button 
          onClick={handleClose} 
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
        >
          <IoClose size={24} />
        </button>

        {/* Modal Title */}
        <div className="flex items-center space-x-3 text-white text-2xl font-bold mb-6">
          <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
            <svg 
              width="20" 
              height="20" 
              viewBox="0 0 24 24" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                d="M21 16V8C21 6.89543 20.1046 6 19 6H5C3.89543 6 3 6.89543 3 8V16C3 17.1046 3.89543 18 5 18H19C20.1046 18 21 17.1046 21 16Z" 
                stroke="#8B5CF6" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
              <path 
                d="M7 10H17" 
                stroke="#8B5CF6" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
              <path 
                d="M7 14H11" 
                stroke="#8B5CF6" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
              <path 
                d="M15 14H17" 
                stroke="#8B5CF6" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <h2>Manage Wallets</h2>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {/* Success Message */}
          {success && (
            <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <IoCheckmarkCircle className="text-green-500" size={20} />
                <p className="text-green-400 text-sm">{success}</p>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <svg 
                  width="20" 
                  height="20" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  className="text-red-500"
                >
                  <path 
                    d="M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                </svg>
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            </div>
          )}

          {isAuthenticated ? (
            <div className="space-y-6">
              {/* Ethereum Wallets Section */}
              <div>
                <h3 className="text-white text-lg font-semibold mb-4 flex items-center space-x-2">
                  <div className="w-6 h-6 bg-[#627EEA] rounded-full flex items-center justify-center text-white text-xs font-bold">
                    ETH
                  </div>
                  <span>Ethereum Wallets ({ethereumWallets.length})</span>
                </h3>

                {ethereumWallets.length > 0 ? (
                  <div className="space-y-3">
                    {ethereumWallets.map((wallet) => (
                      <div key={wallet.id} className="bg-[#1D2226] border border-gray-600 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={`w-8 h-8 ${getWalletColor(wallet)} rounded-full flex items-center justify-center text-white text-xs font-bold`}>
                              {getWalletIcon(wallet)}
                            </div>
                            <div>
                              <div className="text-white font-medium">{formatAddress(wallet.address)}</div>
                              <div className="text-gray-400 text-sm">
                                {wallet.displayName} • {wallet.isEmbedded ? 'Embedded' : 'External'}
                              </div>
                            </div>
                            {defaultWallets.ethereum === wallet.address && (
                              <div className="bg-[#14FFA2] text-black text-xs px-2 py-1 rounded-full font-medium">
                                Default
                              </div>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            {defaultWallets.ethereum !== wallet.address && ethereumWallets.length > 1 && (
                              <button
                                onClick={() => handleSetDefault(wallet.address, 'ethereum')}
                                className="bg-gray-600 hover:bg-gray-500 text-white text-sm px-3 py-1 rounded-lg transition-colors"
                              >
                                Set Default
                              </button>
                            )}
                            {ethereumWallets.length === 1 && (
                              <div className="text-green-400 text-xs px-2 py-1">
                                Auto-selected as default
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-400">
                    No Ethereum wallets connected
                  </div>
                )}
              </div>

              {/* Solana Wallets Section */}
              <div>
                <h3 className="text-white text-lg font-semibold mb-4 flex items-center space-x-2">
                  <div className="w-6 h-6 bg-[#9945FF] rounded-full flex items-center justify-center text-white text-xs font-bold">
                    SOL
                  </div>
                  <span>Solana Wallets ({solanaWalletsList.length})</span>
                </h3>

                {solanaWalletsList.length > 0 ? (
                  <div className="space-y-3">
                    {solanaWalletsList.map((wallet) => (
                      <div key={wallet.id} className="bg-[#1D2226] border border-gray-600 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className={`w-8 h-8 ${getWalletColor(wallet)} rounded-full flex items-center justify-center text-white text-xs font-bold`}>
                              {getWalletIcon(wallet)}
                            </div>
                            <div>
                              <div className="text-white font-medium">{formatAddress(wallet.address)}</div>
                              <div className="text-gray-400 text-sm">
                                {wallet.displayName} • {wallet.isEmbedded ? 'Embedded' : 'External'}
                              </div>
                            </div>
                            {defaultWallets.solana === wallet.address && (
                              <div className="bg-[#14FFA2] text-black text-xs px-2 py-1 rounded-full font-medium">
                                Default
                              </div>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            {defaultWallets.solana !== wallet.address && solanaWalletsList.length > 1 && (
                              <button
                                onClick={() => handleSetDefault(wallet.address, 'solana')}
                                className="bg-gray-600 hover:bg-gray-500 text-white text-sm px-3 py-1 rounded-lg transition-colors"
                              >
                                Set Default
                              </button>
                            )}
                            {solanaWalletsList.length === 1 && (
                              <div className="text-green-400 text-xs px-2 py-1">
                                Auto-selected as default
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-400">
                    No Solana wallets connected
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-400">Please authenticate to manage your wallets.</p>
            </div>
          )}
        </div>



        {/* Close Button */}
        <button
          className="bg-gray-600 hover:bg-gray-500 text-white w-full py-4 mt-8 rounded-2xl text-xl font-bold transition-colors"
          onClick={handleClose}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default ManageWalletsModal;
