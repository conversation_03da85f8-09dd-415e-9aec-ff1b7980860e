import React from 'react';
import { showSwapSuccessToast, showSwapErrorToast, showSwapInfoToast } from '../utils/swapToast';

/**
 * Test component for swap toast notifications
 * This component can be temporarily added to test the toast functionality
 */
const SwapToastTest: React.FC = () => {
  const testSuccessToast = () => {
    showSwapSuccessToast(
      'buy',
      'BONK',
      '5KJp7KyuGxaNGhYGqd6wDHn8QjMH1CYjRqeQzpJ8FxMvN2Hs3kL9mP4qR7sT1uV6wX8yZ',
      'https://solscan.io/tx/5KJp7KyuGxaNGhYGqd6wDHn8QjMH1CYjRqeQzpJ8FxMvN2Hs3kL9mP4qR7sT1uV6wX8yZ'
    );
  };

  const testSellSuccessToast = () => {
    showSwapSuccessToast(
      'sell',
      'PEPE',
      '3FGh8iJ9kL2mN5oP6qR7sT4uV8wX1yZ2aB3cD4eF5gH6iJ7kL8mN9oP0qR1sT2uV3wX',
    );
  };

  const testErrorToast = () => {
    showSwapErrorToast('Insufficient SOL balance for transaction');
  };

  const testInfoToast = () => {
    showSwapInfoToast('Transaction submitted to blockchain');
  };

  return (
    <div className="p-4 space-y-4 bg-gray-800 rounded-lg">
      <h3 className="text-white text-lg font-semibold">Toast Notification Tests</h3>
      
      <div className="grid grid-cols-2 gap-4">
        <button
          onClick={testSuccessToast}
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors"
        >
          Test Buy Success Toast
        </button>
        
        <button
          onClick={testSellSuccessToast}
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors"
        >
          Test Sell Success Toast
        </button>
        
        <button
          onClick={testErrorToast}
          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors"
        >
          Test Error Toast
        </button>
        
        <button
          onClick={testInfoToast}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
        >
          Test Info Toast
        </button>
      </div>
      
      <div className="text-sm text-gray-400 mt-4">
        <p>Click the buttons above to test different toast notification types.</p>
        <p>Success toasts include clickable Solscan links.</p>
      </div>
    </div>
  );
};

export default SwapToastTest;
