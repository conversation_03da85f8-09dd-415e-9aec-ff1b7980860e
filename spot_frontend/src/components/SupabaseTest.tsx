import React, { useState } from 'react';
import { testSupabaseConnection } from '../utils/test-supabase';

/**
 * A simple component to test the Supabase connection
 */
const SupabaseTest: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const runTest = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const result = await testSupabaseConnection();
      setTestResult(result);
      
      // Log to console for developers
      console.log('Supabase connection test result:', result);
    } catch (err: any) {
      setError(err.message || 'An unknown error occurred');
      console.error('Failed to test Supabase connection:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-[#181C20] p-4 rounded-xl text-white">
      <h2 className="text-xl font-bold mb-4">Supabase Connection Test</h2>
      
      <button
        onClick={runTest}
        disabled={isLoading}
        className={`px-4 py-2 rounded-md ${isLoading ? 'bg-gray-500' : 'bg-blue-600 hover:bg-blue-700'} transition-colors mb-4`}
      >
        {isLoading ? 'Testing...' : 'Test Connection'}
      </button>
      
      {error && (
        <div className="p-3 bg-red-900/50 border border-red-700 rounded-md mb-4">
          <p className="text-red-400 font-medium">Error: {error}</p>
        </div>
      )}
      
      {testResult && (
        <div className="mt-4">
          <div className="mb-2 flex items-center">
            <div className={`w-3 h-3 rounded-full mr-2 ${testResult.success ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <p className="font-medium">{testResult.success ? 'Connection Successful' : 'Connection Failed'}</p>
          </div>
          
          {testResult.message && (
            <p className="text-gray-300 mb-4">{testResult.message}</p>
          )}
          
          {testResult.tables && (
            <div className="mt-4">
              <h3 className="text-lg font-medium mb-2">Tables Status:</h3>
              <div className="space-y-2">
                {Object.entries(testResult.tables).map(([tableName, status]: [string, any]) => (
                  <div key={tableName} className="p-3 bg-gray-800 rounded-md">
                    <div className="flex items-center mb-1">
                      <div className={`w-2 h-2 rounded-full mr-2 ${status.exists ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <p className="font-medium">{tableName}</p>
                    </div>
                    {status.exists ? (
                      <p className="text-sm text-gray-400">Rows found: {status.rowCount}</p>
                    ) : (
                      <p className="text-sm text-red-400">{status.error}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SupabaseTest;
