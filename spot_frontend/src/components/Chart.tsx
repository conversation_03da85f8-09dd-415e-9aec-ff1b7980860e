import React, { useState, useEffect, useMemo } from "react";
import { AreaChart, Area, XAxis, YAxis, Tooltip, ResponsiveContainer } from "recharts";

const Chart = ({ data, timeframe, isLoading }) => {
  // State for error handling
  const [error, setError] = useState<null>(null);
  
  // Process the data when it changes
  useEffect(() => {
    try {
      if (!data || data.length === 0) {
        setError("No price data available for this timeframe");
        return;
      }
      
      if (!Array.isArray(data)) {
        setError("Invalid data format received");
        return;
      }
      
      // Data is valid, clear any errors
      setError(null);
      
    } catch (err) {
      console.error("Error processing chart data:", err);
      setError("Error processing chart data");
    }
  }, [data]);
  
  // Determine if the price has gone up or down
  const priceUp = useMemo(() => {
    if (!data || data.length < 2) return true;
    return data[data.length - 1].value >= data[0].value;
  }, [data]);
  
  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-[#1a1d22] p-2 border border-gray-700 rounded shadow-md">
          <p className="text-[#ffffff]">{`${new Date(label).toLocaleDateString()} ${new Date(label).toLocaleTimeString()}`}</p>
          <p className="text-[#ffffff]">{`Price: $${parseFloat(payload[0].value).toFixed(6)}`}</p>
        </div>
      );
    }
    return null;
  };
  
  // If loading
  if (isLoading) {
    return (
      <div className="h-[300px] flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  // If error
  if (error) {
    return (
      <div className="h-[300px] flex justify-center items-center flex-col">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <p className="text-gray-500 text-center">{error}</p>
      </div>
    );
  }
  
  // If no data
  if (!data || data.length === 0) {
    return (
      <div className="h-[300px] flex justify-center items-center flex-col">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        <p className="text-gray-500 text-center">No price chart data available</p>
      </div>
    );
  }
  
  // Calculate min and max for domain
  const priceValues = data.map(item => item.value);
  const minPrice = Math.min(...priceValues) * 0.99; // Give some padding
  const maxPrice = Math.max(...priceValues) * 1.01;
  
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={data}
          margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
        >
          <defs>
            <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset="5%"
                stopColor={priceUp ? "#22c55e" : "#ef4444"}
                stopOpacity={0.8}
              />
              <stop
                offset="95%"
                stopColor={priceUp ? "#22c55e" : "#ef4444"}
                stopOpacity={0}
              />
            </linearGradient>
          </defs>
          <XAxis
            dataKey="time"
            tickFormatter={(time) => {
              const date = new Date(time);
              // Different format based on timeframe
              if (timeframe === "1d") {
                return date.toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                });
              }
              return date.toLocaleDateString([], {
                month: "short",
                day: "numeric",
              });
            }}
            tick={{ fill: "#9ca3af" }}
          />
          <YAxis
            domain={[minPrice, maxPrice]}
            tickFormatter={(value) => `$${value.toFixed(2)}`}
            width={80}
            tick={{ fill: "#9ca3af" }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Area
            type="monotone"
            dataKey="value"
            stroke={priceUp ? "#22c55e" : "#ef4444"}
            fillOpacity={1}
            fill="url(#colorPrice)"
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

export default Chart; 