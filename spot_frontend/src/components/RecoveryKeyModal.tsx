import React, { useState } from 'react';
import { usePrivy, useWallets } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import { useSmartWallet } from '../hooks/useSmartWallet';
import { IoClose } from 'react-icons/io5';

interface RecoveryKeyModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const RecoveryKeyModal: React.FC<RecoveryKeyModalProps> = ({ isOpen, onClose }) => {
  const { ready, authenticated, user, exportWallet: exportEVMWallet } = usePrivy();
  const { wallets } = useWallets();
  const { wallets: solanaWallets, exportWallet: exportSolanaWallet } = useSolanaWallets();
  const { smartWalletAddress, isValid: isSmartWalletValid } = useSmartWallet();
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [exportType, setExportType] = useState<'evm' | 'solana' | null>(null);

  if (!isOpen) return null;

  // Check authentication
  const isAuthenticated = ready && authenticated;

  // Debug: Log essential wallet information
  console.log("RecoveryKeyModal: User authenticated:", isAuthenticated);
  console.log("RecoveryKeyModal: LinkedAccounts count:", user?.linkedAccounts?.length || 0);
  console.log("RecoveryKeyModal: Wallets from useWallets count:", wallets?.length || 0);
  console.log("RecoveryKeyModal: Solana wallets count:", solanaWallets?.length || 0);
  console.log("RecoveryKeyModal: Smart wallet from user object:", user?.smartWallet?.address || 'none');
  console.log("RecoveryKeyModal: Smart wallet from useSmartWallet hook:", smartWalletAddress || 'none');
  console.log("RecoveryKeyModal: Smart wallet client valid:", isSmartWalletValid);

  // Check for embedded wallets - Updated logic with improved smart wallet detection
  // STEP 1: Check for smart wallet (highest priority for EVM) - use multiple sources
  const hasSmartWalletFromUser = !!(user?.smartWallet?.address);
  const hasSmartWalletFromHook = !!(smartWalletAddress && isSmartWalletValid);
  const hasSmartWallet = hasSmartWalletFromUser || hasSmartWalletFromHook;

  console.log("RecoveryKeyModal: Smart wallet detection results:", {
    fromUser: hasSmartWalletFromUser,
    fromHook: hasSmartWalletFromHook,
    combined: hasSmartWallet
  });

  // STEP 2: Check linkedAccounts for embedded wallets (same logic as TradingPanel)
  const hasEVMEmbeddedWalletFromAccounts = !!user?.linkedAccounts?.find(
    (account) => {
      const acc = account as any;
      // Skip if not a wallet type or missing key data
      if (!acc ||
          !['wallet', 'smart_wallet', 'eoa_wallet'].includes(acc.type) ||
          !acc.address ||
          typeof acc.address !== 'string' ||
          acc.address.includes('@')) {
        return false;
      }

      // Check for EVM addresses (start with 0x)
      const isEVMAddress = acc.address.startsWith('0x');
      // Check if it's embedded (same conditions as TradingPanel)
      const isEmbedded = acc.connectorType === 'embedded' || acc.walletClientType === 'privy';

      if (isEVMAddress && isEmbedded) {
        console.log(`RecoveryKeyModal: Found EVM embedded wallet in linkedAccounts: ${acc.address.slice(0,6)}...${acc.address.slice(-4)}`);
      }

      return isEVMAddress && isEmbedded;
    }
  );

  const hasSolanaEmbeddedWalletFromAccounts = !!user?.linkedAccounts?.find(
    (account) => {
      const acc = account as any;
      // Skip if not a wallet type or missing key data
      if (!acc ||
          !['wallet', 'smart_wallet', 'eoa_wallet'].includes(acc.type) ||
          !acc.address ||
          typeof acc.address !== 'string' ||
          acc.address.includes('@')) {
        return false;
      }

      // Check for Solana addresses (don't start with 0x and are long)
      const isSolanaAddress = !acc.address.startsWith('0x') && acc.address.length > 30;
      // Check if it's embedded (same conditions as TradingPanel)
      const isEmbedded = acc.connectorType === 'embedded' || acc.walletClientType === 'privy';

      if (isSolanaAddress && isEmbedded) {
        console.log(`RecoveryKeyModal: Found Solana embedded wallet in linkedAccounts: ${acc.address.slice(0,6)}...${acc.address.slice(-4)}`);
      }

      return isSolanaAddress && isEmbedded;
    }
  );

  // STEP 3: Check useWallets hook for embedded EVM wallets (same logic as TradingPanel)
  const hasEVMEmbeddedWalletFromHook = !!wallets?.find(
    (wallet) => {
      if (!wallet || !wallet.address || typeof wallet.address !== 'string' || wallet.address.includes('@')) {
        return false;
      }

      const isEVM = wallet.address.startsWith('0x');
      const isEmbedded = (wallet as any).embedded || wallet.walletClientType === 'embedded' || (wallet as any).connectorType === 'embedded';

      if (isEVM && isEmbedded) {
        console.log(`RecoveryKeyModal: Found EVM embedded wallet in useWallets: ${wallet.address.slice(0,6)}...${wallet.address.slice(-4)}`);
      }

      return isEVM && isEmbedded;
    }
  );

  // STEP 4: Check useSolanaWallets hook for embedded Solana wallets
  const hasSolanaEmbeddedWalletFromHook = !!solanaWallets?.find(
    (wallet) => {
      if (!wallet || !(wallet as any).address) {
        return false;
      }

      const isEmbedded = (wallet as any).walletClientType === 'privy' || (wallet as any).embedded === true;

      if (isEmbedded) {
        console.log(`RecoveryKeyModal: Found Solana embedded wallet in useSolanaWallets: ${(wallet as any).address.slice(0,6)}...${(wallet as any).address.slice(-4)}`);
      }

      return isEmbedded;
    }
  );

  // STEP 5: Combine results from all sources (including smart wallet)
  const hasEVMEmbeddedWallet = hasSmartWallet || hasEVMEmbeddedWalletFromAccounts || hasEVMEmbeddedWalletFromHook;
  const hasSolanaEmbeddedWallet = hasSolanaEmbeddedWalletFromAccounts || hasSolanaEmbeddedWalletFromHook;

  console.log("RecoveryKeyModal: hasEVMEmbeddedWallet:", hasEVMEmbeddedWallet);
  console.log("RecoveryKeyModal: hasSolanaEmbeddedWallet:", hasSolanaEmbeddedWallet);

  const handleExportWallet = async (type: 'evm' | 'solana') => {
    if (!isAuthenticated) {
      setError('Please authenticate first');
      return;
    }

    setIsExporting(true);
    setError(null);
    setExportType(type);

    try {
      if (type === 'evm' && hasEVMEmbeddedWallet) {
        await exportEVMWallet();
      } else if (type === 'solana' && hasSolanaEmbeddedWallet) {
        await exportSolanaWallet();
      } else {
        setError(`No ${type.toUpperCase()} embedded wallet found`);
        return;
      }
    } catch (err) {
      console.error('Export wallet error:', err);
      setError(err instanceof Error ? err.message : 'Failed to export wallet');
    } finally {
      setIsExporting(false);
      setExportType(null);
    }
  };

  const handleClose = () => {
    setError(null);
    setExportType(null);
    onClose();
  };

  return (
    <div className="fixed inset-0 flex justify-center items-center bg-black/80 z-50">
      <div className="bg-[#181C20] border border-gray-600 max-w-lg w-full p-6 rounded-xl shadow-2xl relative">
        {/* Close Button */}
        <button 
          onClick={handleClose} 
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
        >
          <IoClose size={24} />
        </button>

        {/* Modal Title */}
        <div className="flex items-center space-x-3 text-white text-2xl font-bold mb-6">
          <div className="w-8 h-8 bg-[#14FFA2]/20 rounded-lg flex items-center justify-center">
            <svg 
              width="20" 
              height="20" 
              viewBox="0 0 24 24" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" 
                stroke="#14FFA2" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
              <path 
                d="M9 12L11 14L15 10" 
                stroke="#14FFA2" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <h2>Recovery Key</h2>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {/* Description */}
          <div className="text-gray-300">
            <p className="mb-4">
              Export your wallet's private key to use it with other wallet applications like MetaMask or Phantom.
            </p>
            <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <svg 
                  width="20" 
                  height="20" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  className="text-yellow-500 mt-0.5 flex-shrink-0"
                >
                  <path 
                    d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                </svg>
                <div>
                  <p className="text-yellow-500 font-medium text-sm">Important Security Notice</p>
                  <p className="text-yellow-400 text-sm mt-1">
                    Never share your private key with anyone. Anyone with access to your private key can control your wallet and funds.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <svg 
                  width="20" 
                  height="20" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  className="text-red-500"
                >
                  <path 
                    d="M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                </svg>
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            </div>
          )}

          {/* Wallet Export Options */}
          {isAuthenticated ? (
            <div className="space-y-4">
              <p className="text-gray-400 text-lg">Available wallets:</p>

              {/* EVM Wallet Export */}
              {hasEVMEmbeddedWallet && (
                <button
                  onClick={() => handleExportWallet('evm')}
                  disabled={isExporting}
                  className="w-full bg-[#1D2226] hover:bg-[#23262F] border border-gray-600 rounded-lg p-4 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-[#627EEA] rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-bold">ETH</span>
                      </div>
                      <div className="text-left">
                        <div className="text-white font-medium">
                          {hasSmartWallet ? 'Smart Wallet' : 'Ethereum Wallet'}
                        </div>
                        <div className="text-gray-400 text-sm">
                          {hasSmartWallet ? 'Export smart wallet private key' : 'Export EVM private key'}
                        </div>
                      </div>
                    </div>
                    {isExporting && exportType === 'evm' && (
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    )}
                  </div>
                </button>
              )}

              {/* Solana Wallet Export */}
              {hasSolanaEmbeddedWallet && (
                <button
                  onClick={() => handleExportWallet('solana')}
                  disabled={isExporting}
                  className="w-full bg-[#1D2226] hover:bg-[#23262F] border border-gray-600 rounded-lg p-4 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-[#9945FF] rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-bold">SOL</span>
                      </div>
                      <div className="text-left">
                        <div className="text-white font-medium">Solana Wallet</div>
                        <div className="text-gray-400 text-sm">Export Solana private key</div>
                      </div>
                    </div>
                    {isExporting && exportType === 'solana' && (
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    )}
                  </div>
                </button>
              )}

              {/* Display all connected wallets (non-exportable) */}
              {(wallets?.length > 0 || solanaWallets?.length > 0) && (
                <div className="space-y-3">
                  <p className="text-gray-500 text-sm">Connected wallets (view only):</p>

                  {/* Show all EVM wallets */}
                  {wallets?.map((wallet: any, index: number) => {
                    if (!wallet?.address || !wallet.address.startsWith('0x')) return null;
                    const isEmbedded = (wallet as any).embedded || wallet.walletClientType === 'embedded' || (wallet as any).connectorType === 'embedded';
                    const isSmartWallet = wallet.walletClientType === 'smart-wallet';

                    return (
                      <div key={`evm-${index}`} className="bg-[#1D2226] border border-gray-600 rounded-lg p-3 opacity-75">
                        <div className="flex items-center space-x-3">
                          <div className={`w-6 h-6 ${isSmartWallet ? 'bg-[#14FFA2]' : 'bg-[#627EEA]'} rounded-full flex items-center justify-center`}>
                            <span className="text-white text-xs font-bold">{isSmartWallet ? 'SW' : 'ETH'}</span>
                          </div>
                          <div>
                            <div className="text-white text-sm font-medium">
                              {`${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)}`}
                            </div>
                            <div className="text-gray-400 text-xs">
                              {isSmartWallet ? 'Smart Wallet' : 'Ethereum Wallet'} • {isEmbedded ? 'Embedded' : 'External'}
                              {!isEmbedded && ' (Cannot export)'}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}

                  {/* Show all Solana wallets */}
                  {solanaWallets?.map((wallet: any, index: number) => {
                    if (!wallet?.address) return null;
                    const isEmbedded = wallet.walletClientType === 'privy' || wallet.embedded === true;

                    return (
                      <div key={`sol-${index}`} className="bg-[#1D2226] border border-gray-600 rounded-lg p-3 opacity-75">
                        <div className="flex items-center space-x-3">
                          <div className="w-6 h-6 bg-[#9945FF] rounded-full flex items-center justify-center">
                            <span className="text-white text-xs font-bold">SOL</span>
                          </div>
                          <div>
                            <div className="text-white text-sm font-medium">
                              {`${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)}`}
                            </div>
                            <div className="text-gray-400 text-xs">
                              Solana Wallet • {isEmbedded ? 'Embedded' : 'External'}
                              {!isEmbedded && ' (Cannot export)'}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}

              {/* No Wallets Message */}
              {!hasEVMEmbeddedWallet && !hasSolanaEmbeddedWallet && (!wallets?.length && !solanaWallets?.length) && (
                <div className="text-center py-8">
                  <p className="text-gray-400">No wallets found.</p>
                  <p className="text-gray-500 text-sm mt-2">
                    Connect a wallet to view and export your private keys.
                  </p>
                </div>
              )}

              {/* Show message if only external wallets */}
              {!hasEVMEmbeddedWallet && !hasSolanaEmbeddedWallet && (wallets?.length > 0 || solanaWallets?.length > 0) && (
                <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                  <div className="flex items-start space-x-2">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      className="text-blue-500 mt-0.5 flex-shrink-0"
                    >
                      <path
                        d="M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    <div>
                      <p className="text-blue-400 font-medium text-sm">Export Not Available</p>
                      <p className="text-blue-300 text-sm mt-1">
                        Only embedded wallets can be exported. External wallets are managed by their respective wallet applications.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-400">Please authenticate to export your wallet.</p>
            </div>
          )}
        </div>

        {/* Close Button */}
        <button
          className="bg-gray-600 hover:bg-gray-500 text-white w-full py-4 mt-8 rounded-2xl text-xl font-bold transition-colors"
          onClick={handleClose}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default RecoveryKeyModal;
