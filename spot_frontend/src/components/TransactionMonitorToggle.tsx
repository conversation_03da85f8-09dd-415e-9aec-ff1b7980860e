import React from 'react';
import { FaExchangeAlt, FaReceipt } from 'react-icons/fa';

interface TransactionMonitorToggleProps {
  showMonitor: boolean;
  onToggle: () => void;
  pendingTransactions?: number;
}

const TransactionMonitorToggle: React.FC<TransactionMonitorToggleProps> = ({ 
  showMonitor,
  onToggle,
  pendingTransactions = 0
}) => {
  return (
    <button
      onClick={onToggle}
      className={`fixed bottom-4 right-4 z-50 flex items-center gap-2 px-3 py-2 rounded-full shadow-lg transition-all ${
        showMonitor 
          ? 'bg-red-600 hover:bg-red-700' 
          : 'bg-[#1D2226] hover:bg-[#2A3038] border border-[#14FFA2]'
      }`}
      title={showMonitor ? 'Hide transaction monitor' : 'Show transaction monitor'}
    >
      <div className="flex items-center justify-center text-white">
        <FaReceipt className={`${showMonitor ? 'text-white' : 'text-[#14FFA2]'}`} />
      </div>
      
      <span className={`text-sm font-medium ${showMonitor ? 'text-white' : 'text-[#14FFA2]'}`}>
        {showMonitor ? 'Hide Transactions' : 'Transactions'}
      </span>
      
      {pendingTransactions > 0 && (
        <div className="bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
          {pendingTransactions > 9 ? '9+' : pendingTransactions}
        </div>
      )}
    </button>
  );
};

export default TransactionMonitorToggle; 