import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { homeAPI } from '../utils/api';

// Define types for the trending coin data
interface TrendingCoin {
  id: string;
  name: string;
  symbol?: string;
  current_price?: number;
  price?: number;
  network?: string;
  price_change_percentage_24h?: number;
  price_change_percentage?: number;
  image?: string;
  [key: string]: any; // For any additional properties
}

// Define the formatted trending coin data
interface FormattedTrendingCoin {
  id: string;
  name: string;
  symbol: string;
  price: string;
  change: string;
  network: string;
  changeColor: string;
  icon: string;
  raw: TrendingCoin;
}

// Define the context value type
interface TrendingContextType {
  trendingData: TrendingCoin[];
  formattedTrendingData: FormattedTrendingCoin[];
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  fetchTrendingData: (network?: string) => Promise<void>;
}

// Define props for the provider component
interface TrendingProviderProps {
  children: ReactNode;
}

// Create the context with a default value
export const TrendingContext = createContext<TrendingContextType | null>(null);

// Custom hook to use the context
export const useTrending = (): TrendingContextType => {
  const context = useContext(TrendingContext);
  if (context === null) {
    throw new Error('useTrending must be used within a TrendingProvider');
  }
  return context;
};

// Provider component
export const TrendingProvider = ({ children }: TrendingProviderProps) => {
  const [trendingData, setTrendingData] = useState<TrendingCoin[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchTrendingData = async (network = 'universal') => {
    try {
      setLoading(true);
      
      // Get data from the API
      const response = await homeAPI.getNetworkHighlights(network);
      
      // Handle nested response structure - API returns {data: {...}, status: 'success'}
      const highlights = response?.data ? response.data : response;
      
      // Check if we got trending data - correctly access nested structure
      if (highlights && Array.isArray(highlights.trending)) {
        setTrendingData(highlights.trending);
        setLastUpdated(new Date());
      } else {
        console.error('Invalid trending data format:', response);
        setError('Failed to load trending data: Invalid format');
      }
    } catch (err) {
      console.error('Error fetching trending data:', err);
      setError('Failed to load trending data');
    } finally {
      setLoading(false);
    }
  };

  // Initialize data
  useEffect(() => {
    fetchTrendingData();
    
    // Set up refresh interval (every 5 minutes)
    const intervalId = setInterval(() => {
      fetchTrendingData();
    }, 300000);
    
    return () => clearInterval(intervalId);
  }, []);

  // Format data for display
  const getFormattedTrendingData = (): FormattedTrendingCoin[] => {
    return trendingData.map((coin) => ({
      id: coin.id,
      name: coin.name,
      network: coin.network || 'unknown', // Provide default value to satisfy the type requirement
      symbol: coin.symbol?.toUpperCase() || '',
      price: `$${(coin.current_price || coin.price || 0).toFixed(2)}`,
      change: `${(coin.price_change_percentage_24h || coin.price_change_percentage || 0).toFixed(2)}%`,
      changeColor: (coin.price_change_percentage_24h || coin.price_change_percentage || 0) >= 0 
        ? "text-green-400" : "text-red-400",
      icon: coin.image || "",
      raw: coin // Keep the original data
    }));
  };

  const contextValue: TrendingContextType = {
    trendingData,
    formattedTrendingData: getFormattedTrendingData(),
    loading,
    error,
    lastUpdated,
    fetchTrendingData
  };

  return (
    <TrendingContext.Provider value={contextValue}>
      {children}
    </TrendingContext.Provider>
  );
};