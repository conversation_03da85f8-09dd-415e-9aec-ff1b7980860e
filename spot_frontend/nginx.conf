server {
    listen 80 default_server;
    server_name _; # Traefik handles the actual host matching

    root /usr/share/nginx/html;
    index index.html index.htm;

    # Handle frontend routes (e.g., React Router, Vue Router)
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Optional: Cache static assets
    # This can improve performance if <PERSON><PERSON><PERSON><PERSON> isn't already caching these.
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Optional: Basic health check endpoint for Nginx itself
    # location /nginx_health {
    #     access_log off;
    #     return 200 "healthy\n";
    #     add_header Content-Type text/plain;
    # }
}
