# Use an official Node.js runtime as a parent image
FROM node:18-alpine as builder

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock.json (or yarn.lock)
COPY package*.json ./
# If using yarn, uncomment the next line and comment out the npm install line
# COPY yarn.lock ./

# Install dependencies
RUN npm install --legacy-peer-deps
# If using yarn, uncomment the next line and comment out the npm install line
# RUN yarn install

# Copy the rest of the application code
COPY . .

# Set NODE_ENV to production and copy production environment file
ENV NODE_ENV=production

# Remove the default .env file and use .env.production
RUN rm -f .env && cp .env.production .env

# Build the React app for production
# Replace 'build' with your actual build script if different (e.g., 'vite build')
RUN npm run build

# Use a lighter image for serving the static files (nginx)
FROM nginx:stable-alpine

# Copy the build output from the builder stage
COPY --from=builder /app/dist /usr/share/nginx/html 
# Adjust /app/dist if your build output directory is different (e.g., /app/build)

# Copy a custom nginx configuration if needed (optional)
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80 for nginx
EXPOSE 80 
# The docker-compose file maps host 4000 to container 80 if using nginx, 
# or directly to the node server port (e.g., 4000) if serving with node.
# If you are using Node to serve (e.g., `npm run start` or `serve -s dist`), 
# adjust the base image, expose the correct port (e.g., 4000), and change the CMD.

# Start nginx
CMD ["nginx", "-g", "daemon off;"] 