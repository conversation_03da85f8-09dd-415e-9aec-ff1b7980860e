import * as React from 'react';
import { useEffect, useState, useCallback, ReactNode, ErrorInfo } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import Home from './Home/Home';
import Trade from './Home/Trade';
import { usePrivy, useWallets } from '@privy-io/react-auth';
import './App.css';

// Add Phantom type definition to Window interface
declare global {
  interface Window {
    phantom?: {
      solana?: {
        isConnected?: boolean;
        connect: (options?: { onlyIfTrusted?: boolean }) => Promise<{ publicKey: { toString: () => string } }>;
      };
    };
    getWalletInfo?: () => any;
  }
}

// Import Solana-specific hooks - compatible with Privy 2.0
let useSendSolanaTransaction: any;
try {
  // This is the proper import path for Solana functions in Privy 2.0
  const solanaModule = require('@privy-io/react-auth/solana');
  useSendSolanaTransaction = solanaModule.useSendTransaction;
} catch (e: any) {
  console.warn("Couldn't import Solana-specific hooks:", e.message);
  // Create a dummy implementation as fallback
  useSendSolanaTransaction = () => ({
    sendTransaction: async () => { 
      console.error("Solana transaction sending not available");
      throw new Error("Solana transaction sending not available");
    }
  });
}

// Define interfaces for the error boundary
interface ErrorBoundaryProps {
  children: React.ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

// Simple error boundary component to catch Privy errors
class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    console.error("Error caught by boundary:", error, errorInfo);
  }

  render(): React.ReactNode {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-[#141416] text-white flex-col">
          <h2 className="text-xl mb-4">Something went wrong</h2>
          <p className="text-md text-red-400 mb-6">
            {this.state.error?.message || "An unexpected error occurred in the authentication system"}
          </p>
          <button 
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
          >
            Reload Application
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Define interfaces for wallet data
interface WalletAddress {
  address: string;
  isEthereum: boolean;
  isSolana: boolean;
}

function App(): JSX.Element {
  // Basic Privy hooks without extra options
  const { ready, authenticated, login, user } = usePrivy();
  const { wallets } = useWallets();
  // Using any type for connectWallet to avoid TypeScript errors with Privy package
  const walletsMethods = useWallets() as any;
  const connectWallet = walletsMethods?.connectWallet;
  const [privyError, setPrivyError] = useState<Error | null>(null);
  const [hasPhantomWallet, setHasPhantomWallet] = useState<boolean>(false);
  const [hasSolanaAddress, setHasSolanaAddress] = useState<boolean>(false);
  const [connectedWallets, setConnectedWallets] = useState<WalletAddress[]>([]);
  
  // Check if we have Phantom wallet available
  useEffect(() => {
    if (window.phantom?.solana) {
      console.log("Phantom wallet detected in browser");
      setHasPhantomWallet(true);
    }
  }, []);
  
  // Check for Solana addresses in wallets
  useEffect(() => {
    if (wallets && wallets.length > 0) {
      console.log("App: Checking wallets for Solana addresses:", wallets.length);
      
      const walletAddresses: WalletAddress[] = [];
      let foundSolana = false;
      
      wallets.forEach(wallet => {
        if (wallet && wallet.address) {
          // Track in our connected wallets list
          walletAddresses.push({
            address: wallet.address,
            isEthereum: wallet.address.startsWith('0x'),
            isSolana: !wallet.address.startsWith('0x')
          });
          
          // Check if this is a Solana address
          if (!wallet.address.startsWith('0x')) {
            console.log("Found Solana address:", wallet.address);
            foundSolana = true;
          }
        }
      });
      
      setConnectedWallets(walletAddresses);
      setHasSolanaAddress(foundSolana);
      
      // If we have Phantom but no Solana address, try direct connection
      if (hasPhantomWallet && !foundSolana && authenticated) {
        tryConnectPhantomDirectly();
      }
    }
  }, [wallets, authenticated, hasPhantomWallet]);
  
  // Function to try connecting Phantom wallet directly
  const tryConnectPhantomDirectly = useCallback(async () => {
    if (!window.phantom?.solana) return;
    
    try {
      console.log("Attempting direct Phantom connection");
      const phantomProvider = window.phantom.solana;
      
      // Check if already connected
      if (phantomProvider.isConnected) {
        console.log("Phantom is already connected, getting public key");
        try {
          const { publicKey } = await phantomProvider.connect({ onlyIfTrusted: true });
          if (publicKey) {
            const solanaAddress = publicKey.toString();
            console.log("Got Solana address from Phantom:", solanaAddress);
            setHasSolanaAddress(true);
            
            // Dispatch an event that our components can listen for
            window.dispatchEvent(new CustomEvent('solana-wallet-connected', { 
              detail: { address: solanaAddress } 
            }));
            
            // Also store in localStorage for persistence
            const phantomData = { 
              address: solanaAddress, 
              type: 'solana', 
              timestamp: Date.now() 
            };
            localStorage.setItem('phantom_wallet', JSON.stringify(phantomData));
          }
        } catch (e) {
          console.log("Error getting connected wallet, will try regular connect:", e);
          return tryConnectPhantomFull();
        }
      } else {
        console.log("Phantom not yet connected, requesting connection");
        return tryConnectPhantomFull();
      }
    } catch (err) {
      console.error("Error connecting to Phantom directly:", err);
    }
  }, []);
  
  // Full Phantom connection flow
  const tryConnectPhantomFull = async () => {
    if (!window.phantom?.solana) return;
    
    try {
      // Try to connect through Privy first using updated methods for Solana
      try {
        console.log("Trying to connect Phantom via Privy for Solana...");
        
        // Force Solana chain with Phantom - explicitly specify details
        if (connectWallet) {
          await connectWallet({
            name: "phantom", 
            chain: "solana",
            chainType: "solana"
          });
          
          console.log("Connected to Phantom via Privy");
          
          // Check if we have wallets after connection
          setTimeout(async () => {
            if (wallets && wallets.length > 0) {
              // Look specifically for Solana addresses (non-0x)
              const solanaWallet = wallets.find(w => 
                w && w.address && !w.address.startsWith('0x')
              );
              
              if (solanaWallet) {
                console.log("Successfully got Solana wallet via Privy:", solanaWallet.address);
                setHasSolanaAddress(true);
                
                // Store in localStorage
                const phantomData = { 
                  address: solanaWallet.address, 
                  type: 'solana', 
                  timestamp: Date.now() 
                };
                localStorage.setItem('phantom_wallet', JSON.stringify(phantomData));
              } else {
                console.log("Connected to Phantom but didn't get a Solana address, falling back to direct connect");
                // If we didn't get a Solana address, try direct connection
                await connectPhantomDirectly();
              }
            }
          }, 500);
        }
      } catch (e) {
        console.log("Could not connect via Privy, trying direct connect:", e);
        await connectPhantomDirectly();
      }
    } catch (err) {
      console.error("Error in tryConnectPhantomFull:", err);
    }
  };
  
  // Connect directly to Phantom for Solana
  const connectPhantomDirectly = async () => {
    try {
      // Use the direct Phantom API to get the Solana address
      if (!window.phantom?.solana) return;
      const phantomProvider = window.phantom.solana;
      const { publicKey } = await phantomProvider.connect();
      
      if (publicKey) {
        const solanaAddress = publicKey.toString();
        console.log("Got Solana address from direct Phantom connection:", solanaAddress);
        setHasSolanaAddress(true);
        
        // Dispatch an event that our components can listen for
        window.dispatchEvent(new CustomEvent('solana-wallet-connected', { 
          detail: { address: solanaAddress } 
        }));
        
        // Store in localStorage
        const phantomData = { 
          address: solanaAddress, 
          type: 'solana', 
          timestamp: Date.now() 
        };
        localStorage.setItem('phantom_wallet', JSON.stringify(phantomData));
      }
    } catch (error) {
      console.error("Error connecting to Phantom directly:", error);
      setPrivyError(error instanceof Error ? error : new Error('Failed to connect to Phantom wallet'));
    }
  };
  
  // Ensure wallets info is added to window for debugging
  useEffect(() => {
    // Handle errors in authentication system
    const handleError = (event: ErrorEvent) => {
      console.error("JS Error caught:", event.error);
      
      // Only handle errors that seem related to wallet connections
      if (event.error && typeof event.error.message === 'string' && 
          (event.error.message.includes('wallet') || 
           event.error.message.includes('Wallet') || 
           event.error.message.includes('Privy') || 
           event.error.message.includes('privy'))) {
        setPrivyError(event.error);
      }
    };
    
    window.addEventListener('error', handleError);
    
    // For debugging only - add wallet info to window
    window.getWalletInfo = () => {
      return {
        authenticated,
        wallets: wallets?.map(w => {
          // Using any type to avoid ConnectedWallet property errors
          const wallet = w as any;
          return {
            address: wallet.address,
            chainId: wallet.chainId,
            // Use only properties that are definitely available
            walletClientType: wallet.walletClientType
          };
        }),
        connectedWallets,
        hasPhantomWallet,
        hasSolanaAddress
      };
    };
    
    return () => {
      window.removeEventListener('error', handleError);
      if (window.getWalletInfo) {
        delete window.getWalletInfo;
      }
    };
  }, [authenticated, wallets, connectedWallets, hasPhantomWallet, hasSolanaAddress]);
  
  // Handle logout with cleanup
  const handleCompleteLogout = () => {
    // Clear localStorage wallet data
    localStorage.removeItem('phantom_wallet');
    localStorage.removeItem('selected_wallet');
    
    // Reset state
    setHasSolanaAddress(false);
    setConnectedWallets([]);
    
    // Reload the page to completely reset application state
    window.location.reload();
  };
  
  // Show loading state when we're waiting for Privy to initialize
  if (!ready) {
    return <div className="min-h-screen flex items-center justify-center bg-[#141416] text-white">Loading...</div>;
  }
  
  // Show authentication error if we encountered a Privy problem
  if (privyError) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#141416] text-white flex-col">
        <h2 className="text-xl mb-4">Authentication System Error</h2>
        <p className="text-md text-red-400 mb-6">
          {privyError.message || "An unexpected error occurred in the authentication system"}
        </p>
        <button 
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
        >
          Reload Application
        </button>
      </div>
    );
  }
  
  // Show login button if not authenticated
  if (!authenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#141416]">
        <button
          onClick={login}
          className="px-6 py-3 text-lg font-semibold text-white bg-[#214638] rounded-lg hover:bg-[#14FFA2] hover:text-black transition-colors"
        >
          Connect Wallet
        </button>
      </div>
    );
  }
  
  // Main application with routing
  return (
    <ErrorBoundary>
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/trade" element={<Trade />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </BrowserRouter>
    </ErrorBoundary>
  );
}

export default App;
