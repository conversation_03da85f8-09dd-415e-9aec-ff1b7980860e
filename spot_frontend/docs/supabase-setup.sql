-- Create trades table for completed trades
CREATE TABLE public.trades (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_wallet TEXT NOT NULL,
  ticker TEXT NOT NULL,
  qty NUMERIC NOT NULL,
  entry_price NUMERIC NOT NULL,
  exit_price NUMERIC NOT NULL,
  entry_value NUMERIC NOT NULL,
  exit_value NUMERIC NOT NULL,
  closed_pnl NUMERIC NOT NULL,
  dex TEXT,
  tx_hash TEXT,
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  
  -- Create an index for faster queries by user wallet
  CONSTRAINT fk_user_wallet FOREIGN KEY (user_wallet) REFERENCES auth.users(id) ON DELETE CASCADE
);

CREATE INDEX idx_trades_user_wallet ON public.trades(user_wallet);

-- Create orders table for pending/active orders
CREATE TABLE public.orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_wallet TEXT NOT NULL,
  ticker TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('Market', 'Limit')),
  qty NUMERIC NOT NULL,
  share NUMERIC NOT NULL,
  value NUMERIC NOT NULL,
  target_price NUMERIC,
  target_mktcap NUMERIC,
  target_pnl NUMERIC,
  status TEXT NOT NULL CHECK (status IN ('Open', 'Filled', 'Canceled')),
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  
  -- Create an index for faster queries by user wallet and status
  CONSTRAINT fk_user_wallet FOREIGN KEY (user_wallet) REFERENCES auth.users(id) ON DELETE CASCADE
);

CREATE INDEX idx_orders_user_wallet ON public.orders(user_wallet);
CREATE INDEX idx_orders_status ON public.orders(status);

-- Create trade_history table for all trading activities
CREATE TABLE public.trade_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_wallet TEXT NOT NULL,
  ticker TEXT NOT NULL,
  trade_type TEXT NOT NULL CHECK (trade_type IN ('Buy', 'Sell')),
  qty NUMERIC NOT NULL,
  value NUMERIC NOT NULL,
  filled_price NUMERIC NOT NULL,
  tx_hash TEXT NOT NULL,
  dex TEXT NOT NULL,
  token_in_symbol TEXT NOT NULL,
  token_out_symbol TEXT NOT NULL,
  token_in_address TEXT NOT NULL,
  token_out_address TEXT NOT NULL,
  amount_in TEXT NOT NULL,
  amount_out TEXT NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  
  -- Create an index for faster queries by user wallet
  CONSTRAINT fk_user_wallet FOREIGN KEY (user_wallet) REFERENCES auth.users(id) ON DELETE CASCADE
);

CREATE INDEX idx_trade_history_user_wallet ON public.trade_history(user_wallet);
CREATE INDEX idx_trade_history_timestamp ON public.trade_history(timestamp);

-- RLS (Row Level Security) Policies
-- These ensure users can only access their own data

-- Enable RLS on all tables
ALTER TABLE public.trades ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trade_history ENABLE ROW LEVEL SECURITY;

-- Create policies for trades table
CREATE POLICY "Users can view their own trades"
  ON public.trades FOR SELECT
  USING (auth.uid() = user_wallet);

CREATE POLICY "Users can insert their own trades"
  ON public.trades FOR INSERT
  WITH CHECK (auth.uid() = user_wallet);

-- Create policies for orders table
CREATE POLICY "Users can view their own orders"
  ON public.orders FOR SELECT
  USING (auth.uid() = user_wallet);

CREATE POLICY "Users can insert their own orders"
  ON public.orders FOR INSERT
  WITH CHECK (auth.uid() = user_wallet);

CREATE POLICY "Users can update their own orders"
  ON public.orders FOR UPDATE
  USING (auth.uid() = user_wallet);

-- Create policies for trade_history table
CREATE POLICY "Users can view their own trade history"
  ON public.trade_history FOR SELECT
  USING (auth.uid() = user_wallet);

CREATE POLICY "Users can insert their own trade history"
  ON public.trade_history FOR INSERT
  WITH CHECK (auth.uid() = user_wallet);
