<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
</head>
<body>
    <h1>Frontend WebSocket Connection Test</h1>
    <div id="status">Connecting...</div>
    <div id="messages"></div>
    
    <script>
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        
        function addMessage(message) {
            const div = document.createElement('div');
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            messagesDiv.appendChild(div);
            console.log(message);
        }
        
        addMessage('🔌 Starting WebSocket connection test...');
        
        // Test connection to backend
        const socket = io('http://localhost:5001', {
            transports: ['websocket', 'polling'],
            timeout: 15000,
            reconnection: false,
            forceNew: true,
            upgrade: true,
            rememberUpgrade: false
        });
        
        socket.on('connect', () => {
            statusDiv.textContent = '✅ Connected';
            statusDiv.style.color = 'green';
            addMessage('✅ Connected to backend WebSocket');
            addMessage(`🔗 Socket ID: ${socket.id}`);
            
            // Register as a user
            const testUserId = `test_user_${Date.now()}`;
            const testSessionId = `test_session_${Date.now()}`;
            
            addMessage('📝 Registering user...');
            socket.emit('register', {
                userId: testUserId,
                sessionId: testSessionId
            });
            
            // Join pulse room after a short delay
            setTimeout(() => {
                addMessage('🏠 Joining pulse room...');
                socket.emit('join-pulse', {
                    userId: testUserId,
                    sessionId: testSessionId
                });
            }, 1000);
        });
        
        socket.on('connection-status', (status) => {
            addMessage(`📡 Connection status: ${JSON.stringify(status)}`);
        });
        
        socket.on('pulse-data', (update) => {
            addMessage(`📥 Pulse data received: source=${update.source}, timestamp=${new Date(update.timestamp).toLocaleTimeString()}`);
            if (update.data) {
                addMessage(`   - New: ${update.data.new?.length || 0}, Bonding: ${update.data.bonding?.length || 0}, Bonded: ${update.data.bonded?.length || 0}`);
            }
        });
        
        socket.on('room-stats', (stats) => {
            addMessage(`📊 Room stats: ${JSON.stringify(stats)}`);
        });
        
        socket.on('connect_error', (error) => {
            statusDiv.textContent = '❌ Connection Error';
            statusDiv.style.color = 'red';
            addMessage(`❌ Connection error: ${error.message}`);
        });
        
        socket.on('error', (error) => {
            addMessage(`❌ Socket error: ${JSON.stringify(error)}`);
        });
        
        socket.on('disconnect', (reason) => {
            statusDiv.textContent = '🔌 Disconnected';
            statusDiv.style.color = 'orange';
            addMessage(`🔌 Disconnected: ${reason}`);
        });
        
        // Auto-disconnect after 30 seconds
        setTimeout(() => {
            addMessage('🔚 Test completed - disconnecting...');
            socket.disconnect();
        }, 30000);
    </script>
</body>
</html>
