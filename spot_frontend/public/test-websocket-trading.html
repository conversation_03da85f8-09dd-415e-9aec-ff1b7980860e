<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Panel WebSocket Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .info { background: #e3f2fd; color: #1565c0; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Trading Panel WebSocket Connection Test</h1>
    
    <div>
        <button onclick="testDirectConnection()">Test Direct Connection (ws://localhost:5003)</button>
        <button onclick="testProxyConnection()">Test Proxy Connection (ws://localhost:4001/trading-panel-ws)</button>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>
    
    <div id="logs"></div>

    <script>
        let ws = null;
        
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logsDiv.appendChild(logDiv);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        function closeExistingConnection() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }
        
        function testDirectConnection() {
            closeExistingConnection();
            log('🔄 Testing direct connection to ws://localhost:5003', 'info');
            
            try {
                ws = new WebSocket('ws://localhost:5003');
                
                ws.onopen = () => {
                    log('✅ Direct connection established successfully!', 'success');
                    
                    // Test subscription
                    const subscribeMessage = {
                        action: 'subscribe',
                        poolAddress: '66Vnm942tWCgeGXpfQDjFASewQfjogpVHDS55PHypump'
                    };
                    
                    ws.send(JSON.stringify(subscribeMessage));
                    log('📤 Sent subscription message: ' + JSON.stringify(subscribeMessage), 'info');
                };
                
                ws.onmessage = (event) => {
                    log('📥 Received message: ' + event.data.substring(0, 200) + (event.data.length > 200 ? '...' : ''), 'success');
                };
                
                ws.onclose = (event) => {
                    log(`🔌 Direct connection closed. Code: ${event.code}, Reason: ${event.reason}`, 'info');
                };
                
                ws.onerror = (error) => {
                    log('❌ Direct connection error: ' + error, 'error');
                };
                
            } catch (error) {
                log('❌ Failed to create direct connection: ' + error.message, 'error');
            }
        }
        
        function testProxyConnection() {
            closeExistingConnection();
            log('🔄 Testing proxy connection to ws://localhost:4001/trading-panel-ws', 'info');
            
            try {
                ws = new WebSocket('ws://localhost:4001/trading-panel-ws');
                
                ws.onopen = () => {
                    log('✅ Proxy connection established successfully!', 'success');
                    
                    // Test subscription
                    const subscribeMessage = {
                        action: 'subscribe',
                        poolAddress: '66Vnm942tWCgeGXpfQDjFASewQfjogpVHDS55PHypump'
                    };
                    
                    ws.send(JSON.stringify(subscribeMessage));
                    log('📤 Sent subscription message: ' + JSON.stringify(subscribeMessage), 'info');
                };
                
                ws.onmessage = (event) => {
                    log('📥 Received message: ' + event.data.substring(0, 200) + (event.data.length > 200 ? '...' : ''), 'success');
                };
                
                ws.onclose = (event) => {
                    log(`🔌 Proxy connection closed. Code: ${event.code}, Reason: ${event.reason}`, 'info');
                };
                
                ws.onerror = (error) => {
                    log('❌ Proxy connection error: ' + error, 'error');
                };
                
            } catch (error) {
                log('❌ Failed to create proxy connection: ' + error.message, 'error');
            }
        }
        
        // Auto-test on page load
        window.onload = () => {
            log('🚀 WebSocket test page loaded', 'info');
            log('Click the buttons above to test different connection methods', 'info');
        };
    </script>
</body>
</html>
