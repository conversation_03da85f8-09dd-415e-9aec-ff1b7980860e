import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const sourceDir = path.join(__dirname, 'src');

// Common import patterns to add
const fixPatterns = [
  // Add React import to TSX files
  {
    test: /\.tsx$/,
    pattern: /^(?!import React)/,
    replacement: (match, file) => {
      if (!file.includes('import React')) {
        return 'import React from "react";\n';
      }
      return '';
    }
  },
  // Fix component props
  {
    test: /\.tsx$/,
    pattern: /function\s+([A-Z][A-Za-z0-9_]*)\s*\(\s*\{([^}]*)\}\s*\)/g,
    replacement: (match, name, props) => {
      if (!match.includes(': React.FC')) {
        return `interface ${name}Props {${props}}\n\nfunction ${name}({ ${props} }: ${name}Props)`;
      }
      return match;
    }
  },
  // Add type annotations to useState
  {
    test: /\.tsx$/,
    pattern: /useState\(([^)]*)\)/g,
    replacement: (match, value) => {
      if (!match.includes('useState<')) {
        if (value.trim() === 'false' || value.trim() === 'true') {
          return `useState<boolean>(${value})`;
        } else if (value.trim() === '[]') {
          return `useState<any[]>(${value})`;
        } else if (value.trim() === '{}') {
          return `useState<Record<string, any>>(${value})`;
        } else if (value.trim() === 'null') {
          return `useState<null>(${value})`;
        } else if (value.trim() === '""' || value.includes('"') || value.includes("'")) {
          return `useState<string>(${value})`;
        } else if (!isNaN(value.trim()) && value.trim() !== '') {
          return `useState<number>(${value})`;
        }
      }
      return match;
    }
  },
  // Add type annotations to useRef
  {
    test: /\.tsx$/,
    pattern: /useRef\(([^)]*)\)/g,
    replacement: (match, value) => {
      if (!match.includes('useRef<')) {
        if (value.trim() === 'null') {
          return `useRef<HTMLDivElement | null>(${value})`;
        }
      }
      return match;
    }
  },
  // Add type annotations to event handlers
  {
    test: /\.tsx$/,
    pattern: /(function |const )([a-zA-Z0-9_]+)\s*=\s*\(([^)]*?e(vent)?\s*[,)])/g,
    replacement: (match, prefix, funcName, params) => {
      if (funcName.startsWith('handle') || funcName.includes('Handler') || funcName.includes('onClick')) {
        if (!params.includes(': React.')) {
          const updatedParams = params.replace('e,', 'e: React.MouseEvent,')
            .replace('event,', 'event: React.MouseEvent,')
            .replace('e)', 'e: React.MouseEvent)')
            .replace('event)', 'event: React.MouseEvent)');
          return `${prefix}${funcName} = (${updatedParams}`;
        }
      }
      return match;
    }
  },
  // Add React.ReactNode type to children prop
  {
    test: /\.tsx$/,
    pattern: /children(\s*)[,:]/g,
    replacement: (match, space) => {
      if (!match.includes('ReactNode')) {
        return `children${space}: React.ReactNode,`;
      }
      return match;
    }
  },
  // Fix import statements for TS/TSX files
  {
    test: /\.(ts|tsx)$/,
    pattern: /import\s+.*\s+from\s+['"](.+?)(\.jsx?)?['"];/g,
    replacement: (match, importPath) => {
      if (match.includes('.js') || match.includes('.jsx')) {
        return match.replace('.jsx', '.tsx').replace('.js', '.ts');
      }
      return match;
    }
  }
];

// Function to get all TS/TSX files in a directory
function getTSFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      getTSFiles(filePath, fileList);
    } else {
      const ext = path.extname(file);
      if (ext === '.ts' || ext === '.tsx') {
        fileList.push(filePath);
      }
    }
  });
  
  return fileList;
}

// Function to apply fixes to a file
function applyFixes(file) {
  try {
    let content = fs.readFileSync(file, 'utf8');
    let modified = false;
    const relativePath = path.relative(process.cwd(), file);
    const ext = path.extname(file);
    
    fixPatterns.forEach(fix => {
      if (fix.test.test(ext)) {
        const originalContent = content;
        
        if (typeof fix.pattern.test === 'function') {
          if (fix.pattern.test(content)) {
            content = content.replace(fix.pattern, (match, ...args) => {
              return fix.replacement(match, ...args, content);
            });
            
            if (content !== originalContent) {
              modified = true;
            }
          }
        }
      }
    });
    
    if (modified) {
      fs.writeFileSync(file, content);
      console.log(`Fixed issues in: ${relativePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${file}:`, error);
    return false;
  }
}

// Main function
function fixTypeScriptErrors() {
  const tsFiles = getTSFiles(sourceDir);
  console.log(`Found ${tsFiles.length} TypeScript files`);
  
  let fixedCount = 0;
  tsFiles.forEach(file => {
    if (applyFixes(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed issues in ${fixedCount} files`);
}

// Run the script
console.log('Fixing common TypeScript errors...');
fixTypeScriptErrors(); 