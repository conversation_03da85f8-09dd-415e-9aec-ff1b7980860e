// Type definitions for global objects and third-party libraries
/// <reference types="vite/client" />

// Import.meta type extension for additional env variables
interface ImportMetaEnv extends Record<string, string> {
  // Add specific environment variables if needed
  VITE_PRIVY_APP_ID: string;
}

// Window extension for Phantom wallet
interface PhantomWallet {
  isConnected: boolean;
  connect: (options?: { onlyIfTrusted?: boolean }) => Promise<{ publicKey: { toString: () => string } }>;
  // Add other methods as needed
}

interface PhantomProvider {
  solana?: PhantomWallet;
  // Add other blockchain providers if needed
}

// Extend Window interface
interface Window {
  phantom?: PhantomProvider;
  indexedDB: {
    databases: () => Promise<Array<{ name?: string }>>;
    deleteDatabase: (name: string) => IDBOpenDBRequest;
  };
}

// Add any module declarations for libraries without types
declare module '@privy-io/react-auth/solana' {
  export function useSendTransaction(): {
    sendTransaction: (transaction: any) => Promise<any>;
  };

  export function useSolanaWallets(): {
    wallets: any[];
    exportWallet: (options?: { address?: string }) => Promise<void>;
  };

  export function useImportWallet(): {
    importWallet: (options: { privateKey: string }) => Promise<any>;
  };

  // Add other exports as needed
}
