#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Config
const SRC_DIR = path.join(__dirname, 'src');
const IGNORE_DIRS = ['node_modules', 'dist', 'build', '.git'];

// Common TypeScript fixes
const COMMON_FIXES = [
  {
    // Add React import for TSX files
    pattern: /^(import .* from .*;\n)/,
    replacement: (match, p1, file) => {
      if (file.endsWith('.tsx') && !match.includes("import React")) {
        return `import React from 'react';\n${p1}`;
      }
      return match;
    },
    test: file => file.endsWith('.tsx')
  },
  {
    // Fix useState without type
    pattern: /useState\(([^)]*)\)/g,
    replacement: (match, p1) => {
      // Skip if it already has a type parameter or is an empty state
      if (match.includes('useState<') || p1.trim() === '') {
        return match;
      }
      
      // Try to infer the type
      if (p1.includes('"') || p1.includes("'")) {
        return `useState<string>(${p1})`;
      } else if (p1 === 'true' || p1 === 'false') {
        return `useState<boolean>(${p1})`;
      } else if (!isNaN(Number(p1)) && p1.trim() !== '') {
        return `useState<number>(${p1})`;
      } else if (p1.includes('[')) {
        return `useState<any[]>(${p1})`;
      } else if (p1.includes('{')) {
        return `useState<Record<string, any>>(${p1})`;
      }
      
      // Default to any
      return `useState<any>(${p1})`;
    }
  },
  {
    // Fix useRef without type
    pattern: /useRef\(([^)]*)\)/g,
    replacement: (match, p1) => {
      // Skip if it already has a type parameter
      if (match.includes('useRef<')) {
        return match;
      }
      
      // Common ref patterns
      if (p1.trim() === 'null') {
        return `useRef<HTMLElement | null>(${p1})`;
      }
      
      // Default to any
      return `useRef<any>(${p1})`;
    }
  },
  {
    // Fix props without type in functional components
    pattern: /function\s+([A-Z][A-Za-z0-9_]*)\s*\(\s*(\{[^}]*\}|\w+)\s*\)/g,
    replacement: (match, name, props) => {
      const isDestructured = props.startsWith('{');
      const propsName = isDestructured ? 'Props' : 'props';
      
      return `function ${name}(${props}: ${name}${propsName})`;
    }
  },
  {
    // Add Props interface for components
    pattern: /^(import .+;\n+)(function|const)\s+([A-Z][A-Za-z0-9_]*)\s*(\(|=)/m,
    replacement: (match, imports, type, name, bracket) => {
      return `${imports}\ninterface ${name}Props {\n  // TODO: Add proper props types\n  [key: string]: any;\n}\n\n${type} ${name}${bracket}`;
    },
    test: file => file.endsWith('.tsx') && !fs.readFileSync(file, 'utf8').includes('interface') && !fs.readFileSync(file, 'utf8').includes('type Props =')
  },
  {
    // Fix event handlers
    pattern: /(function|const)\s+handle([A-Z][a-zA-Z0-9_]*)\s*=?\s*(\(|=>)\s*(\(?)([^)]*?)(\)?)\s*(=>|\{)/g,
    replacement: (match, type, name, arrow1, openParen, params, closeParen, arrow2) => {
      let eventType = 'any';
      
      // Try to infer the event type
      if (name.includes('Click')) {
        eventType = 'React.MouseEvent<HTMLElement>';
      } else if (name.includes('Change')) {
        eventType = 'React.ChangeEvent<HTMLInputElement>';
      } else if (name.includes('Submit')) {
        eventType = 'React.FormEvent<HTMLFormElement>';
      } else if (name.includes('Key')) {
        eventType = 'React.KeyboardEvent<HTMLElement>';
      }
      
      // Format parameters
      const formattedParams = params.split(',').map(p => {
        const paramTrim = p.trim();
        if (paramTrim === 'e' || paramTrim === 'event') {
          return `${paramTrim}: ${eventType}`;
        }
        return paramTrim ? `${paramTrim}: any` : '';
      }).join(', ');
      
      return `${type} handle${name} = ${openParen}${formattedParams}${closeParen} ${arrow2}`;
    }
  }
];

// Process a TypeScript file
function processFile(filePath) {
  try {
    // Read content
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Apply fixes
    for (const fix of COMMON_FIXES) {
      if (fix.test && !fix.test(filePath)) {
        continue;
      }
      
      const originalContent = content;
      content = content.replace(fix.pattern, (match, ...args) => fix.replacement(match, ...args, filePath));
      
      if (content !== originalContent) {
        modified = true;
      }
    }
    
    // Save changes if modified
    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed TypeScript issues in: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

// Get all TypeScript files
function getTypeScriptFiles(directory) {
  let results = [];
  
  const items = fs.readdirSync(directory);
  
  for (const item of items) {
    const itemPath = path.join(directory, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      if (!IGNORE_DIRS.includes(item)) {
        results = results.concat(getTypeScriptFiles(itemPath));
      }
    } else {
      const ext = path.extname(itemPath);
      if (ext === '.ts' || ext === '.tsx') {
        results.push(itemPath);
      }
    }
  }
  
  return results;
}

// Main function
async function main() {
  console.log('Fixing common TypeScript issues...');
  
  // Get TS files
  const files = getTypeScriptFiles(SRC_DIR);
  console.log(`Found ${files.length} TypeScript files to process`);
  
  // Process each file
  for (const file of files) {
    processFile(file);
  }
  
  console.log('\nFixed common TypeScript issues!');
  console.log(`
Next steps:
1. Run 'npm run dev' to check for remaining TypeScript errors
2. Fix any specific type errors that arise by hand
3. Consider adding more specific types than 'any'
  `);
}

// Run the script
main().catch(err => {
  console.error('Fix TypeScript errors failed:', err);
  process.exit(1);
}); 