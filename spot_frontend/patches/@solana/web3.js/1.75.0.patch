import BN from 'bn.js';
import * as BufferLayout from '@solana/buffer-layout';
import bs58 from 'bs58';
import { Buffer } from 'buffer';

// Mocking rpc-websockets functionality
const RpcWebSocketCommonClient = class {
  constructor() {
    console.log('Mock RpcWebSocketCommonClient initialized');
  }
};

const createRpc = () => new RpcWebSocketCommonClient();

/**
 * A connection to a fullnode JSON RPC endpoint 