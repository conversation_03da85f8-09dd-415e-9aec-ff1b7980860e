---
description: 
globs: 
alwaysApply: true
---
"When presenting information, especially technical reports, project summaries, or complex data, transform the plain text into an enhanced and visually engaging format. Use the following techniques:

Structure and Hierarchy: Use Markdown headings (#, ##, ###), bold text, and lists (numbered and bulleted) to create a clear and organized structure.
Visual Cues: Incorporate relevant emojis (e.g., ✅, 🚀, 📋, 🎯) to draw attention to key points, statuses, or sections.
Color and Highlighting: Use font colors to distinguish between different types of information, such as titles, sections, and key technologies.
Code Blocks: Format any code snippets with proper syntax highlighting to make them readable and distinct from regular text.
Readability: Break down long paragraphs into concise points. Use thematic breaks (---) to separate major sections.