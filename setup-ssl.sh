#!/bin/bash

# SSL Certificate Setup Script for redfyn.crypfi.io
# This script sets up Let's Encrypt SSL certificate

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="redfyn.crypfi.io"
EMAIL="<EMAIL>"  # Change this to your email
SERVER_IP="*************"

echo -e "${BLUE}=== SSL Certificate Setup for ${DOMAIN} ===${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on the server
if [ "$1" = "server" ]; then
    print_status "Running SSL setup on server..."
    
    # Install certbot
    print_status "Installing certbot..."
    apt-get update
    apt-get install -y certbot python3-certbot-nginx
    
    # Stop nginx temporarily
    print_status "Stopping nginx temporarily..."
    docker-compose -f docker-compose.production.yml stop frontend || true
    
    # Obtain certificate
    print_status "Obtaining SSL certificate for ${DOMAIN}..."
    certbot certonly --standalone \
        --email ${EMAIL} \
        --agree-tos \
        --no-eff-email \
        --domains ${DOMAIN},www.${DOMAIN}
    
    # Create SSL directory
    mkdir -p ssl
    
    # Copy certificates
    print_status "Copying certificates..."
    cp /etc/letsencrypt/live/${DOMAIN}/fullchain.pem ssl/${DOMAIN}.crt
    cp /etc/letsencrypt/live/${DOMAIN}/privkey.pem ssl/${DOMAIN}.key
    
    # Set permissions
    chmod 644 ssl/${DOMAIN}.crt
    chmod 600 ssl/${DOMAIN}.key
    
    # Restart services
    print_status "Restarting services..."
    docker-compose -f docker-compose.production.yml up -d
    
    # Setup auto-renewal
    print_status "Setting up auto-renewal..."
    (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet --post-hook 'cd /opt/redfyn-spot && docker-compose -f docker-compose.production.yml restart frontend'") | crontab -
    
    print_status "SSL certificate setup completed!"
    
else
    # Run on local machine to execute on server
    print_status "Executing SSL setup on server ${SERVER_IP}..."
    
    # Copy this script to server and execute
    # Using SSH key named 'prashanth'
    SSH_KEY_OPTION="-i ~/.ssh/prashanth"
    scp ${SSH_KEY_OPTION} $0 root@${SERVER_IP}:/tmp/setup-ssl.sh
    ssh ${SSH_KEY_OPTION} root@${SERVER_IP} << EOF
        chmod +x /tmp/setup-ssl.sh
        cd /opt/redfyn-spot
        /tmp/setup-ssl.sh server
        rm /tmp/setup-ssl.sh
EOF
    
    print_status "SSL setup completed on server!"
fi

print_status "SSL Certificate Information:"
echo "Domain: ${DOMAIN}"
echo "Certificate will auto-renew every 90 days"
echo "Check certificate status: https://www.ssllabs.com/ssltest/analyze.html?d=${DOMAIN}"
