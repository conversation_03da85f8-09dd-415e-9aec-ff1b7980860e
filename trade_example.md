<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobula Trade Feed</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        #status {
            text-align: center;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        #trades {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        #trades th, #trades td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        #trades th {
            background-color: #4CAF50;
            color: white;
        }
        #trades tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        #trades tr:hover {
            background-color: #ddd;
        }
        .buy {
            color: green;
        }
        .sell {
            color: red;
        }
        /* Card styles */
        .metrics-container {
            display: flex;
            justify-content: space-between;
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            flex: 1;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 15px;
            text-align: center;
        }
        .metric-title {
            color: #555;
            font-size: 14px;
            margin-bottom: 8px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        /* Price card specific styling */
        .price-card {
            background-color: #f8f9fa;
            border-left: 4px solid #4CAF50;
        }
        .price-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .price-value.large {
            font-size: 24px;
        }
        .price-value.medium {
            font-size: 22px;
        }
        .price-value.small {
            font-size: 20px;
        }
        .price-value.micro {
            font-size: 18px;
        }
        /* Price display styling */
        .price-display .integer-part {
            font-size: 24px;
            font-weight: bold;
        }
        .price-display .decimal-part {
            font-size: 16px;
            vertical-align: sub;
        }
        .price-display.micro-price {
            font-size: 18px;
        }
        .price-display.micro-price .integer-part {
            font-size: 18px;
        }
        .price-display.micro-price .decimal-part {
            font-size: 14px;
        }
        /* Amount formatting */
        .amount-format .subscript {
            font-size: 70%;
            vertical-align: sub;
            position: relative;
            bottom: -1px;
        }
    </style>
</head>
<body>
    <h1>Mobula Trade Feed</h1>
    <div id="status" class="disconnected">Disconnected</div>
    
    <div style="margin: 20px 0; padding: 15px; background-color: #f0f0f0; border-radius: 5px;">
        <form id="poolForm" onsubmit="subscribeToPool(event)">
            <div style="display: flex; gap: 10px; align-items: center;">
                <div style="flex-grow: 1;">
                    <label for="blockchain" style="display: block; margin-bottom: 5px; font-weight: bold;">Blockchain:</label>
                    <select id="blockchain" style="width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #ccc;">
                        <option value="solana">Solana</option>
                        <option value="ethereum">Ethereum</option>
                        <option value="bsc">Binance Smart Chain</option>
                        <option value="polygon">Polygon</option>
                        <option value="avalanche">Avalanche</option>
                    </select>
                </div>
                <div style="flex-grow: 3;">
                    <label for="poolAddress" style="display: block; margin-bottom: 5px; font-weight: bold;">Pool Address:</label>
                    <input type="text" id="poolAddress" placeholder="Enter pool address" 
                           value="9zG56JRfnJRru7k3kmY4gajvZQVwfWv2vMnbbccA4c6p"
                           style="width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #ccc;">
                </div>
                <div style="align-self: flex-end;">
                    <button type="submit" style="padding: 8px 16px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">Subscribe</button>
                    <button type="button" id="unsubscribeBtn" onclick="unsubscribeFromPool()" style="padding: 8px 16px; background-color: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">Unsubscribe</button>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Metrics Cards -->
    <div class="metrics-container">
        <div class="metric-card">
            <div class="metric-title">MARKET CAP</div>
            <div id="marketCap" class="metric-value">$0</div>
        </div>
        <div class="metric-card price-card">
            <div class="metric-title">PRICE</div>
            <div id="price" class="price-value">$0.00</div>
        </div>
        <div class="metric-card">
            <div class="metric-title">LIQUIDITY</div>
            <div id="liquidity" class="metric-value">$0</div>
        </div>
        <div class="metric-card">
            <div class="metric-title">SUPPLY</div>
            <div id="supply" class="metric-value">0</div>
        </div>
    </div>
    
    <table id="trades">
        <thead>
            <tr>
                <th>Age</th>
                <th>Type</th>
                <th>Market Cap</th>
                <th>Amount</th>
                <th>USD Value</th>
                <th>Hash</th>
            </tr>
        </thead>
        <tbody id="tradesBody">
            <!-- Trade data will be inserted here -->
        </tbody>
    </table>

    <script>
        // DOM elements
        const statusElement = document.getElementById('status');
        const tradesBody = document.getElementById('tradesBody');
        
        // Metric elements
        const marketCapElement = document.getElementById('marketCap');
        const priceElement = document.getElementById('price');
        const liquidityElement = document.getElementById('liquidity');
        const supplyElement = document.getElementById('supply');
        
        // Maximum number of trades to display
        const MAX_TRADES = 50;
        
        // Keep track of addresses we've already loaded historical data for
        const loadedHistoricalAddresses = new Set();
        
        // Keep track of trade IDs to prevent duplicates
        const processedTradeIds = new Set();
        
        // Mobula WebSocket connection
        let mobulaWs = null;
        let reconnectAttempts = 0;
        const MAX_RECONNECTS = 3;
        const RECONNECT_DELAY = 5000; // 5 seconds
        
        // Current subscription info
        let currentBlockchain = 'solana';
        let currentAddress = '9zG56JRfnJRru7k3kmY4gajvZQVwfWv2vMnbbccA4c6p';
        
        // Function to connect to Mobula WebSocket API
        function connectToMobula(blockchain, address) {
            // Close existing connection if any
            if (mobulaWs) {
                try {
                    mobulaWs.close(1000, 'New connection');
                } catch (err) {
                    console.error('Error closing existing connection:', err);
                }
                mobulaWs = null;
            }
            
            // Store current subscription
            currentBlockchain = blockchain;
            currentAddress = address;
            
            // Update status
            statusElement.textContent = 'Connecting to Mobula...';
            statusElement.className = 'disconnected';
            
            // Create new WebSocket connection
            try {
                mobulaWs = new WebSocket('wss://api.mobula.io');
                
                // Handle WebSocket open
                mobulaWs.onopen = function() {
                    console.log(`Connected to Mobula API for ${blockchain}:${address}`);
                    reconnectAttempts = 0;
                    
                    // Send subscription message
                    const subscriptionMessage = {
                        type: 'pair',
                        authorization: 'fffa68cd-6bde-4ac5-909d-eb627d8baca0', // Public API key
                        payload: { 
                            blockchain: blockchain, 
                            address: address,
                        }
                    };
                    
                    try {
                        mobulaWs.send(JSON.stringify(subscriptionMessage));
                        console.log(`Subscribed to Mobula feed for ${blockchain}:${address}`);
                        
                        // Update status
                        statusElement.textContent = `Connected to ${blockchain}:${address}`;
                        statusElement.className = 'connected';
                        
                        // Fetch historical trades
                        fetchHistoricalTrades(blockchain, address);
                    } catch (e) {
                        console.error('Subscribe failed:', e.message);
                        statusElement.textContent = `Failed to subscribe: ${e.message}`;
                        statusElement.className = 'disconnected';
                    }
                };
                
                // Handle WebSocket messages
                mobulaWs.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        const processTrade = function(trade) {
                            console.log('Received trade:', trade);
                            
                            // Update metrics with latest trade data
                            updateMetrics(trade);
                            
                            // Generate a unique ID for this trade
                            const tradeId = generateTradeId(trade);
                            
                            // Only add the trade if we haven't seen it before
                            if (!processedTradeIds.has(tradeId)) {
                                processedTradeIds.add(tradeId);
                                addTradeToTable(trade);
                            } else {
                                console.log('Skipping duplicate trade:', tradeId);
                            }
                        };
                        
                        // Process single trade or array of trades
                        Array.isArray(data) ? data.forEach(processTrade) : processTrade(data);
                    } catch (e) {
                        console.error('Message error:', e.message);
                    }
                };
                
                // Handle WebSocket close
                mobulaWs.onclose = function(e) {
                    console.log(`Mobula connection closed. Code: ${e.code}${e.reason ? ' Reason: ' + e.reason : ''}`);
                    
                    // Update status
                    statusElement.textContent = 'Disconnected';
                    statusElement.className = 'disconnected';
                    
                    // Try to reconnect if not a normal closure and not manually disconnected
                    if (reconnectAttempts < MAX_RECONNECTS && e.code !== 1000) {
                        reconnectAttempts++;
                        console.log(`Reconnect attempt ${reconnectAttempts}/${MAX_RECONNECTS} in ${RECONNECT_DELAY / 1000}s...`);
                        
                        // Update status
                        statusElement.textContent = `Reconnecting (${reconnectAttempts}/${MAX_RECONNECTS})...`;
                        
                        // Try to reconnect after delay
                        setTimeout(function() {
                            connectToMobula(currentBlockchain, currentAddress);
                        }, RECONNECT_DELAY);
                    } else if (e.code !== 1000) {
                        console.error('Max reconnects or unrecoverable. Not retrying.');
                        statusElement.textContent = 'Connection failed. Please try again.';
                    }
                };
                
                // Handle WebSocket errors
                mobulaWs.onerror = function(err) {
                    console.error('Mobula WS Error:', err.message || 'Unknown');
                    statusElement.textContent = `Error: ${err.message || 'Unknown'}`;
                    statusElement.className = 'disconnected';
                };
            } catch (err) {
                console.error('Failed to create WebSocket connection:', err);
                statusElement.textContent = `Connection error: ${err.message}`;
                statusElement.className = 'disconnected';
            }
        }
        
        // Function to fetch historical trades
        function fetchHistoricalTrades(blockchain, address) {
            console.log(`Fetching historical trades for ${blockchain}:${address}...`);
            
            // Update status
            statusElement.textContent = 'Loading historical trades...';
            
            // Construct API URL
            const apiUrl = `https://api.mobula.io/api/1/market/trades/pair?sortOrder=desc&mode=pair&blockchain=${blockchain}&address=${address}&limit=50`;
            
            // Make HTTP request
            fetch(apiUrl)
                .then(response => response.json())
                .then(response => {
                    if (response.data && Array.isArray(response.data)) {
                        console.log(`Received ${response.data.length} historical trades`);
                        
                        // Send trades in chronological order (oldest first)
                        const trades = response.data.reverse();
                        
                        trades.forEach(trade => {
                            // Update metrics with latest trade data
                            updateMetrics(trade);
                            
                            // Generate a unique ID for this trade
                            const tradeId = generateTradeId(trade);
                            
                            // Only add the trade if we haven't seen it before
                            if (!processedTradeIds.has(tradeId)) {
                                processedTradeIds.add(tradeId);
                                addTradeToTable(trade);
                            }
                        });
                        
                        // Update status
                        statusElement.textContent = `Loaded ${trades.length} historical trades`;
                        statusElement.className = 'connected';
                    } else {
                        console.error('Invalid response format:', response);
                        statusElement.textContent = 'Failed to load historical trades: Invalid response format';
                        statusElement.className = 'disconnected';
                    }
                })
                .catch(error => {
                    console.error('Error fetching historical trades:', error);
                    statusElement.textContent = `Failed to load historical trades: ${error.message}`;
                    statusElement.className = 'disconnected';
                });
        }
        
        // Function to subscribe to a pool
        function subscribeToPool(event) {
            event.preventDefault();
            
            // Clear existing trades
            tradesBody.innerHTML = '';
            
            // Clear processed trade IDs
            processedTradeIds.clear();
            
            // Reset metrics
            marketCapElement.textContent = '$0';
            priceElement.textContent = '$0.00';
            liquidityElement.textContent = '$0';
            supplyElement.textContent = '0';
            
            const blockchain = document.getElementById('blockchain').value;
            const poolAddress = document.getElementById('poolAddress').value.trim();
            
            if (!poolAddress) {
                alert('Please enter a pool address');
                return;
            }
            
            // Connect to Mobula with the new subscription
            connectToMobula(blockchain, poolAddress);
            
            return false;
        }
        
        // Function to unsubscribe from the current pool
        function unsubscribeFromPool() {
            if (mobulaWs) {
                try {
                    // Only try to close if the connection is still open
                    if (mobulaWs.readyState === WebSocket.OPEN || mobulaWs.readyState === WebSocket.CONNECTING) {
                        mobulaWs.close(1000, 'User unsubscribed');
                        console.log('WebSocket connection closed properly');
                    }
                    mobulaWs = null;
                } catch (err) {
                    console.error('Error during WebSocket cleanup:', err);
                }
                
                // Update status
                statusElement.textContent = 'Unsubscribed';
                statusElement.className = 'disconnected';
                
                // Clear the table
                tradesBody.innerHTML = '';
            }
            
            return false;
        }
        
        // Update metrics with latest trade data
        function updateMetrics(trade) {
            // Update Market Cap
            let marketCap = 0;
            if (trade.pairData && trade.pairData.token1 && trade.pairData.token1.marketCap) {
                marketCap = trade.pairData.token1.marketCap;
            } else if (trade.token1 && trade.token1.marketCap) {
                marketCap = trade.token1.marketCap;
            } else if (trade.marketCap) {
                marketCap = trade.marketCap;
            }
            
            if (marketCap > 0) {
                marketCapElement.textContent = formatCurrency(marketCap);
            }
            
            // Update Price
            let price = 0;
            if (trade.token_price) {
                price = trade.token_price;
            } else if (trade.pairData && trade.pairData.token0 && trade.pairData.token0.price) {
                price = trade.pairData.token0.price;
            }
            
            if (price > 0) {
                // Format price with appropriate decimal places based on the price magnitude
                formatPriceDisplay(price);
            }
            
            // Update Liquidity
            let liquidity = 0;
            if (trade.pairData && trade.pairData.liquidity) {
                liquidity = trade.pairData.liquidity;
            } else if (trade.liquidity) {
                liquidity = trade.liquidity;
            }
            
            if (liquidity > 0) {
                liquidityElement.textContent = formatCurrency(liquidity);
            }
            
            // Update Supply
            let supply = 0;
            if (trade.pairData && trade.pairData.token1 && trade.pairData.token1.totalSupply) {
                supply = trade.pairData.token1.totalSupply;
            } else if (trade.token1 && trade.token1.totalSupply) {
                supply = trade.token1.totalSupply;
            } else if (trade.totalSupply) {
                supply = trade.totalSupply;
            }
            
            if (supply > 0) {
                supplyElement.textContent = formatSupply(supply);
            }
        }
        
        // Format price display with appropriate decimal places
        function formatPriceDisplay(price) {
            // Get the price element
            const priceElement = document.getElementById('price');
            
            // Reset price element classes
            priceElement.className = 'price-value';
            
            let decimalPlaces;
            
            // Determine decimal places and class based on price magnitude
            if (price >= 1000) {
                // For large prices, show fewer decimals
                decimalPlaces = 2;
                priceElement.classList.add('large');
            } else if (price >= 1) {
                // For normal prices, show 4 decimals
                decimalPlaces = 4;
                priceElement.classList.add('medium');
            } else if (price >= 0.0001) {
                // For smaller prices, show 6 decimals
                decimalPlaces = 6;
                priceElement.classList.add('small');
            } else {
                // For micro prices, show scientific notation or more decimals
                if (price < 0.00000001) {
                    // Use scientific notation for extremely small prices
                    priceElement.textContent = '$' + price.toExponential(4);
                    priceElement.classList.add('micro');
                    return;
                } else {
                    // Show 8 decimals for very small prices
                    decimalPlaces = 8;
                    priceElement.classList.add('micro');
                }
            }
            
            // Format the price with the appropriate number of decimal places
            // and display it as plain text without any subscripts
            const formattedPrice = price.toFixed(decimalPlaces);
            priceElement.textContent = '$' + formattedPrice;
        }
        
        // Format supply with three digits before K/M/B/T suffix
        function formatSupply(value) {
            if (value >= 1e12) {
                return formatThreeDigits(value, 1e12) + 'T';
            } else if (value >= 1e9) {
                return formatThreeDigits(value, 1e9) + 'B';
            } else if (value >= 1e6) {
                return formatThreeDigits(value, 1e6) + 'M';
            } else if (value >= 1e3) {
                return formatThreeDigits(value, 1e3) + 'K';
            } else {
                return value.toFixed(0);
            }
        }
        
        // Format to show three significant digits before suffix
        function formatThreeDigits(value, divisor) {
            const scaled = value / divisor;
            if (scaled >= 100) {
                return scaled.toFixed(0);
            } else if (scaled >= 10) {
                return scaled.toFixed(1);
            } else {
                return scaled.toFixed(2);
            }
        }
        
        // Format currency with appropriate suffix (K, M, B, T)
        function formatCurrency(value) {
            if (value >= 1e12) {
                return '$' + (value / 1e12).toFixed(2) + 'T';
            } else if (value >= 1e9) {
                return '$' + (value / 1e9).toFixed(2) + 'B';
            } else if (value >= 1e6) {
                return '$' + (value / 1e6).toFixed(2) + 'M';
            } else if (value >= 1e3) {
                return '$' + (value / 1e3).toFixed(2) + 'K';
            } else {
                return '$' + formatNumber(value);
            }
        }
        
        // Generate a unique ID for a trade
        function generateTradeId(trade) {
            // Try to use existing ID if available
            if (trade.id) return `id-${trade.id}`;
            if (trade.hash || trade.transaction_hash || trade.tx_hash) {
                const hash = trade.hash || trade.transaction_hash || trade.tx_hash;
                return `hash-${hash}`;
            }
            
            // Otherwise generate an ID from trade properties
            const timestamp = trade.timestamp || Date.now();
            const price = trade.price || 0;
            const amount = trade.token_amount || trade.amount || 0;
            
            return `${timestamp}-${price}-${amount}`;
        }
        
        // Add a trade to the table
        function addTradeToTable(trade) {
            // Create a new row
            const row = document.createElement('tr');
            
            // Store the timestamp for age calculation
            const timestamp = trade.timestamp || Date.now();
            row.dataset.timestamp = timestamp;
            
            // Determine if buy or sell
            const type = trade.type || 'Unknown';
            const typeClass = type.toLowerCase() === 'buy' ? 'buy' : 'sell';
            
            // Create cells with trade data
            row.innerHTML = `
                <td class="age-cell">${formatAge(timestamp)}</td>
                <td class="${typeClass}">${type}</td>
                <td>$${formatMarketCap(trade)}</td>
                <td>${formatTokenAmount(trade)}</td>
                <td>$${formatTradeValue(trade)}</td>
                <td>${formatHash(trade)}</td>
            `;
            
            // Update ages periodically
            updateAges();
            
            // Add the row to the top of the table
            tradesBody.insertBefore(row, tradesBody.firstChild);
            
            // Remove old trades if we have too many
            while (tradesBody.children.length > MAX_TRADES) {
                tradesBody.removeChild(tradesBody.lastChild);
            }
        }
        
        // Format numbers to 6 decimal places maximum
        function formatNumber(num, minDecimals = 2, maxDecimals = 6) {
            if (!num) return '0';
            return parseFloat(num).toLocaleString(undefined, {
                minimumFractionDigits: minDecimals,
                maximumFractionDigits: maxDecimals
            });
        }
        
        // Format market cap from trade data
        function formatMarketCap(trade) {
            // Try to get market cap from different possible locations in the data structure
            let marketCap = 0;
            
            if (trade.pairData && trade.pairData.token1 && trade.pairData.token1.marketCap) {
                marketCap = trade.pairData.token1.marketCap;
            } else if (trade.token1 && trade.token1.marketCap) {
                marketCap = trade.token1.marketCap;
            } else if (trade.marketCap) {
                marketCap = trade.marketCap;
            }
            
            // Format market cap with appropriate suffix (K, M, B, T)
            if (marketCap >= 1e12) {
                return (marketCap / 1e12).toFixed(2) + 'T';
            } else if (marketCap >= 1e9) {
                return (marketCap / 1e9).toFixed(2) + 'B';
            } else if (marketCap >= 1e6) {
                return (marketCap / 1e6).toFixed(2) + 'M';
            } else if (marketCap >= 1e3) {
                return (marketCap / 1e3).toFixed(2) + 'K';
            } else {
                return formatNumber(marketCap);
            }
        }
        
        // Format token amount from trade data
        function formatTokenAmount(trade) {
            // Try to get token amount from different possible locations in the data structure
            let tokenAmount = 0;
            let symbol = '';
            
            if (trade.token_amount) {
                tokenAmount = trade.token_amount;
            } else if (trade.amount) {
                tokenAmount = trade.amount;
            }
            
            // Try to get token symbol
            if (trade.pairData && trade.pairData.token1 && trade.pairData.token1.symbol) {
                symbol = trade.pairData.token1.symbol;
            } else if (trade.token1 && trade.token1.symbol) {
                symbol = trade.token1.symbol;
            } else if (trade.symbol) {
                symbol = trade.symbol;
            }
            
            // Format amount with appropriate suffix (K, M, B, T)
            let formattedAmount = '';
            if (tokenAmount >= 1e12) {
                formattedAmount = (tokenAmount / 1e12).toFixed(2) + 'T';
            } else if (tokenAmount >= 1e9) {
                formattedAmount = (tokenAmount / 1e9).toFixed(2) + 'B';
            } else if (tokenAmount >= 1e6) {
                formattedAmount = (tokenAmount / 1e6).toFixed(2) + 'M';
            } else if (tokenAmount >= 1e3) {
                formattedAmount = (tokenAmount / 1e3).toFixed(2) + 'K';
            } else {
                // For small numbers, check for zeros after decimal point
                const amountStr = tokenAmount.toString();
                if (amountStr.includes('.')) {
                    const parts = amountStr.split('.');
                    const integerPart = parts[0];
                    const decimalPart = parts[1];
                    
                    // Count leading zeros in decimal part
                    let leadingZeros = 0;
                    for (let i = 0; i < decimalPart.length; i++) {
                        if (decimalPart[i] === '0') {
                            leadingZeros++;
                        } else {
                            break;
                        }
                    }
                    
                    // If we have 3 or more leading zeros, format with subscript
                    if (leadingZeros >= 3) {
                        // Format: integer part + "0." + subscript(number of zeros) + remaining decimal digits (max 2)
                        const remainingDigits = decimalPart.substring(leadingZeros, leadingZeros + 2);
                        formattedAmount = `<span class="amount-format">${integerPart}.0<span class="subscript">${leadingZeros}</span>${remainingDigits}</span>`;
                    } else {
                        // Regular formatting for numbers with fewer leading zeros
                        formattedAmount = parseFloat(tokenAmount).toFixed(2);
                    }
                } else {
                    formattedAmount = parseFloat(tokenAmount).toFixed(2);
                }
            }
            
            return `${formattedAmount} ${symbol}`;
        }
        
        // Format trade value from trade data
        function formatTradeValue(trade) {
            // Try to get USD value from different possible locations
            let usdValue = 0;
            
            if (trade.token_amount_usd) {
                usdValue = trade.token_amount_usd;
            } else if (trade.usd_amount) {
                usdValue = trade.usd_amount;
            } else if (trade.value_usd) {
                usdValue = trade.value_usd;
            } else {
                // Calculate from price and amount if USD value not available
                let price = trade.price || 0;
                let amount = trade.token_amount || trade.amount || 0;
                usdValue = price * amount;
            }
            
            return formatNumber(usdValue);
        }
        
        // Format transaction hash
        function formatHash(trade) {
            // Try to get hash from different possible locations
            let hash = '';
            let blockchain = '';
            
            if (trade.hash) {
                hash = trade.hash;
            } else if (trade.transaction_hash) {
                hash = trade.transaction_hash;
            } else if (trade.tx_hash) {
                hash = trade.tx_hash;
            }
            
            // Try to get blockchain
            if (trade.pairData && trade.pairData.token1 && trade.pairData.token1.chainId) {
                blockchain = trade.pairData.token1.chainId.split(':')[0];
            } else if (trade.blockchain) {
                blockchain = trade.blockchain;
            } else if (trade.chain) {
                blockchain = trade.chain;
            }
            
            // If no hash found
            if (!hash) {
                return 'N/A';
            }
            
            // Format hash to show first 4 and last 4 characters
            const shortHash = `${hash.substring(0, 4)}...${hash.substring(hash.length - 4)}`;
            
            // Create explorer link based on blockchain
            let explorerUrl = '';
            if (blockchain === 'solana') {
                explorerUrl = `https://solscan.io/tx/${hash}`;
            } else if (blockchain === 'ethereum') {
                explorerUrl = `https://etherscan.io/tx/${hash}`;
            } else if (blockchain === 'bsc') {
                explorerUrl = `https://bscscan.com/tx/${hash}`;
            } else if (blockchain === 'polygon') {
                explorerUrl = `https://polygonscan.com/tx/${hash}`;
            } else if (blockchain === 'avalanche') {
                explorerUrl = `https://snowtrace.io/tx/${hash}`;
            } else {
                // Default to Solscan if blockchain not recognized
                explorerUrl = `https://solscan.io/tx/${hash}`;
            }
            
            return `<a href="${explorerUrl}" target="_blank" style="color: #3498db; text-decoration: none;">${shortHash}</a>`;
        }
        
        // Format timestamp as human-readable age
        function formatAge(timestamp) {
            const now = Date.now();
            const diffSeconds = Math.floor((now - timestamp) / 1000);
            
            if (diffSeconds < 5) {
                return 'just now';
            } else if (diffSeconds < 60) {
                return `${diffSeconds}s ago`;
            } else if (diffSeconds < 3600) {
                return `${Math.floor(diffSeconds / 60)}m ago`;
            } else if (diffSeconds < 86400) {
                return `${Math.floor(diffSeconds / 3600)}h ago`;
            } else {
                return `${Math.floor(diffSeconds / 86400)}d ago`;
            }
        }
        
        // Update all age cells
        function updateAges() {
            const ageCells = document.querySelectorAll('.age-cell');
            ageCells.forEach(cell => {
                const row = cell.parentElement;
                const timestamp = parseInt(row.dataset.timestamp);
                if (timestamp) {
                    cell.textContent = formatAge(timestamp);
                }
            });
        }
        
        // Update ages every 10 seconds
        setInterval(updateAges, 10000);
        
        // Add event listener for page unload to clean up WebSocket connection
        window.addEventListener('beforeunload', function() {
            if (mobulaWs) {
                try {
                    // Only try to close if the connection is still open
                    if (mobulaWs.readyState === WebSocket.OPEN || mobulaWs.readyState === WebSocket.CONNECTING) {
                        mobulaWs.close(1000, 'Page unload');
                    }
                } catch (err) {
                    console.error('Error closing WebSocket on page unload:', err);
                }
            }
        });
        
        // Connect to Mobula on page load with default values
        window.addEventListener('DOMContentLoaded', function() {
            const blockchain = document.getElementById('blockchain').value;
            const poolAddress = document.getElementById('poolAddress').value.trim();
            
            if (poolAddress) {
                // Delay the connection slightly to ensure the page is fully loaded
                setTimeout(function() {
                    connectToMobula(blockchain, poolAddress);
                }, 500);
            }
        });
    </script>
</body>
</html> 