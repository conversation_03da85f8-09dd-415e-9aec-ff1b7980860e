#!/bin/bash

# start-services.sh - Start Redfyn services with port checking

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Define service ports
FRONTEND_PORT=4001
BACKEND_PORT=5001
LIQUIDITY_POOL_PORT=3047

# Function to start a service with port checking
start_service() {
  local name=$1
  local port=$2
  local dir=$3
  local cmd=$4
  
  echo "Checking $name service (port $port)..."
  cd "$PROJECT_ROOT/$dir"
  
  $SCRIPT_DIR/check-port.sh $port $name "$cmd"
}

# Start services with their respective port checks
start_frontend() {
  start_service "frontend" $FRONTEND_PORT "spot_frontend" "PORT=$FRONTEND_PORT npm start"
}

start_backend() {
  start_service "backend" $BACKEND_PORT "spot_backend" "PORT=$BACKEND_PORT npm run start"
}

start_liquidity_pool() {
  start_service "liquidity-pool" $LIQUIDITY_POOL_PORT "liquidity_pool_v1" "PORT=$LIQUIDITY_POOL_PORT npm run start"
}

# Handle command line arguments
if [ $# -eq 0 ]; then
  # No arguments, start all services
  start_frontend
  start_backend
  start_liquidity_pool
else
  # Start specific services based on arguments
  for arg in "$@"; do
    case $arg in
      frontend)
        start_frontend
        ;;
      backend)
        start_backend
        ;;
      liquidity-pool)
        start_liquidity_pool
        ;;
      *)
        echo "Unknown service: $arg"
        echo "Valid services: frontend, backend, liquidity-pool"
        exit 1
        ;;
    esac
  done
fi 