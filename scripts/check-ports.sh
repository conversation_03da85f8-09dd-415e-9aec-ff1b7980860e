#!/bin/bash

# Script to check which ports are in use for RedFyn services
# Author: Auto-generated for RedFyn multi-service setup

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== RedFyn Service Port Status ===${NC}"
echo ""

# Define service ports
declare -A SERVICES=(
    ["Frontend"]="4001"
    ["Spot Backend"]="5001"
    ["Liquidity Pool"]="3047"
    ["Solana Service"]="6000"
    ["Solana Service Alt"]="6001"
    ["Redis"]="6379"
)

# Function to check if port is in use
check_port() {
    local port=$1
    local service=$2
    
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        echo -e "${GREEN}✓${NC} $service (Port $port): ${GREEN}RUNNING${NC}"
        # Try to get process info
        local pid=$(lsof -ti:$port 2>/dev/null | head -1)
        if [ ! -z "$pid" ]; then
            local process=$(ps -p $pid -o comm= 2>/dev/null)
            echo -e "  └─ Process: $process (PID: $pid)"
        fi
    else
        echo -e "${RED}✗${NC} $service (Port $port): ${RED}NOT RUNNING${NC}"
    fi
}

# Check each service
for service in "${!SERVICES[@]}"; do
    check_port "${SERVICES[$service]}" "$service"
done

echo ""
echo -e "${BLUE}=== All Active Network Connections ===${NC}"
echo "Listening ports:"
netstat -tuln 2>/dev/null | grep LISTEN | sort

echo ""
echo -e "${BLUE}=== Quick Commands ===${NC}"
echo "Start all services: npm run dev"
echo "Start individual services:"
echo "  Frontend: npm run dev:frontend"
echo "  Spot Backend: npm run dev:spot-backend"
echo "  Liquidity Pool: npm run dev:liquidity-pool"
echo "  Solana Service: npm run dev:solana"
