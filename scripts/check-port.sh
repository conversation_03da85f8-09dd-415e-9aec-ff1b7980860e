#!/bin/bash

# check-port.sh - Utility to check if a port is already in use
# Usage: ./check-port.sh PORT_NUMBER SERVICE_NAME [COMMAND_TO_RUN_IF_FREE]

if [ $# -lt 2 ]; then
  echo "Usage: ./check-port.sh PORT_NUMBER SERVICE_NAME [COMMAND_TO_RUN_IF_FREE]"
  echo "Example: ./check-port.sh 4001 frontend 'npm start'"
  exit 1
fi

PORT=$1
SERVICE=$2
COMMAND="${@:3}"

# Check if the port is already in use
if netstat -tuln | grep -q ":$PORT "; then
  echo "Port $PORT is already in use by $SERVICE or another service."
  echo "Not starting a new instance."
  exit 0
else
  echo "Port $PORT is free. Starting $SERVICE..."
  
  # If a command was provided, run it
  if [ -n "$COMMAND" ]; then
    echo "Running: $COMMAND"
    eval $COMMAND
  fi
fi 