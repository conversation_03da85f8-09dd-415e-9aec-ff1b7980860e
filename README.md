# RedFyn - Multi-Chain DEX and Wallet Management Platform

A comprehensive cryptocurrency platform for traders and DeFi users with multi-chain support, advanced trading features, and rich wallet management capabilities.

![Trading Panel Screenshot](screenshot.png)

## Project Overview

RedFyn is a full-stack cryptocurrency platform consisting of:
1. **Frontend Trading Panel** - Modern React-based UI for trading and token management
2. **Spot Backend Service** - Core trading and wallet management API
3. **Liquidity Pool Service** - Multi-chain DEX aggregation and liquidity management
4. **Solana Service** - Specialized Solana blockchain integration service
5. **Memory Bank** - Project documentation and context store

## Architecture

RedFyn follows a microservices architecture with four main services:

### Frontend Service
- **Location**: `spot_frontend/`
- **Port**: 4001
- **Technology**: React + TypeScript + Vite
- **Purpose**: User interface for trading, wallet management, and DeFi interactions

### Backend Services

#### 1. Spot Backend Service
- **Location**: `backend/spot_backend/`
- **Port**: 5001
- **Technology**: Node.js + Express + TypeScript
- **Purpose**: Core trading API, wallet balance management, token metadata

#### 2. Liquidity Pool Service
- **Location**: `backend/liquidity_pool/`
- **Port**: 3047
- **Technology**: Node.js + Express + TypeScript
- **Purpose**: Multi-chain DEX aggregation, liquidity pool management, price quotes

#### 3. Solana Service
- **Location**: `backend/solana/`
- **Port**: 6000
- **Technology**: Node.js + Express + TypeScript
- **Purpose**: Specialized Solana blockchain interactions, Pump.fun integration

## Project Structure

```
redfyn-spot/
├── spot_frontend/            # React frontend application (Port: 4001)
│   ├── src/
│   │   ├── components/       # Reusable UI components
│   │   │   ├── Trading/      # Trading panel components
│   │   │   ├── Wallet/       # Wallet connection components
│   │   │   └── UI/           # Generic UI elements
│   │   ├── hooks/            # Custom React hooks
│   │   ├── pages/            # Application pages/routes
│   │   ├── services/         # API services and external integrations
│   │   ├── store/            # State management (Redux/Context)
│   │   ├── utils/            # Helper functions and utilities
│   │   └── App.tsx           # Main application component
│   ├── public/               # Static assets
│   ├── .env                  # Environment variables
│   └── package.json          # Dependencies and scripts
├── backend/                  # Backend services directory
│   ├── spot_backend/         # Core trading backend (Port: 5001)
│   │   ├── src/
│   │   │   ├── controllers/  # API endpoint controllers
│   │   │   ├── routes/       # API route definitions
│   │   │   ├── services/     # Business logic and external integrations
│   │   │   ├── utils/        # Helper functions and utilities
│   │   │   ├── middleware/   # Express middleware
│   │   │   ├── config/       # Configuration files
│   │   │   └── index.ts      # Application entry point
│   │   ├── dist/             # Compiled TypeScript
│   │   ├── .env              # Environment variables
│   │   └── tsconfig.json     # TypeScript configuration
│   ├── liquidity_pool/       # Liquidity pool service (Port: 3047)
│   │   ├── src/
│   │   │   ├── api/          # API endpoints for liquidity pools
│   │   │   ├── services/     # DEX integration services
│   │   │   ├── protocols/    # Protocol-specific implementations
│   │   │   ├── utils/        # Helper functions and utilities
│   │   │   ├── config/       # Configuration files
│   │   │   └── index.ts      # Application entry point
│   │   ├── docs/             # API documentation
│   │   ├── tests/            # Test suites
│   │   └── config/           # Configuration for different networks
│   └── solana/               # Solana blockchain service (Port: 6000)
│       ├── src/
│       │   ├── controllers/  # Solana-specific controllers
│       │   ├── services/     # Solana blockchain services
│       │   ├── utils/        # Solana utilities
│       │   └── index.ts      # Application entry point
│       ├── idl/              # Anchor IDL files
│       └── test/             # Solana-specific tests
├── scripts/                  # Utility scripts
│   ├── start-services.sh     # Service startup script
│   └── check-port.sh         # Port checking utility
├── memory-bank/              # Project documentation and context
├── nginx/                    # Nginx configuration for production
├── docker-compose.yml        # Development Docker setup
├── docker-compose.prod.yml   # Production Docker setup
├── package.json              # Root package.json with unified scripts
└── README.md                 # Project documentation
```

## Core Features

### Frontend (Trading Panel)
- **Multi-Chain Support**: Trade across Ethereum, Solana, Polygon, Base, Arbitrum, Optimism, and more
- **Wallet Integration**: Seamless connection with multiple wallet types (Privy integration)
- **DEX Aggregation**: Finds the best prices across multiple DEXs
- **Real-Time Quotes**: Fetches best available prices with minimal latency (100ms debounce)
- **Advanced Trading Features**: Market & Limit orders, Take Profit/Stop Loss, Slippage control
- **Responsive Design**: Modern UI with intuitive controls

#### Frontend Architecture
- **Component-Based Structure**: Modular components for maintainability and reusability
- **Responsive Layout**: Mobile-first design with Tailwind CSS
- **State Management**: Context API with reducers for global state
- **API Integration**: Custom hooks for data fetching with caching and invalidation
- **Websocket Integration**: Real-time price updates and order book data
- **Modular Routing**: Dynamic routing with code splitting for improved performance

#### Key UI Components
- **Trading Panel**: The core component for executing trades
  - Token selector with search and balance display
  - Order type selector (Market/Limit/Stop)
  - Price input with real-time validation
  - Slippage tolerance configuration
  - Transaction confirmation modal
- **Wallet Dashboard**: Interface for viewing and managing assets
  - Multi-chain balance overview
  - Token list with sorting and filtering
  - Transaction history with status indicators
- **Charts**: Interactive price charts with multiple timeframe options
  - TradingView integration
  - Custom indicators and drawing tools
  - Volume display and market depth visualization

### Backend (Wallet & Token Services)
- **Multi-Chain Wallet Balance API**: 
  - Fetch balances across ETH, BSC, Solana networks
  - Automatic token discovery with comprehensive metadata
  - Transaction history analysis for complete token detection
- **Token Metadata Service**: 
  - Dynamic token metadata retrieval from multiple sources
  - Token image, name and symbol fetching with smart caching
  - Support for newly created tokens via on-chain data
- **DEX Integration**:
  - Price and liquidity data from multiple DEXs
  - Trading pair information and token swap capabilities
  - Real-time quote aggregation

### Liquidity Pool (DeFi Components)
- **Multi-Chain Liquidity Provision**:
  - Support for multiple EVM-compatible chains
  - Cross-chain liquidity aggregation
  - MEV protection mechanisms

#### Liquidity Pool Architecture
- **Smart Contract Layer**: Solidity contracts for pool management
  - Automated Market Maker (AMM) implementation
  - Fee collection and distribution
  - Flash loan protection
  - Optimized gas usage for lower transaction costs
- **Protocol Layer**: Business logic for LP interactions
  - Position management and tracking
  - Impermanent loss calculation
  - APY/APR projections
  - Reward distribution mechanics
- **API Layer**: RESTful endpoints for frontend interaction
  - Pool statistics and analytics
  - User position data
  - Historical performance metrics

#### Key Features
- **Yield Farming**:
  - Token rewards for liquidity providers
  - Time-weighted position tracking
  - Compound and claim options
  - Reward boosting mechanisms
- **Staking**:
  - Single-sided staking options
  - Dynamic rewards based on pool utilization
  - Lockup periods with APY multipliers
  - Early exit penalties to encourage stability
- **Analytics Dashboard**:
  - Pool performance metrics
  - Volume and TVL tracking
  - Fee generation statistics
  - Individual position performance
- **Governance Integration**:
  - Voting on fee parameters
  - Protocol upgrade proposals
  - Emergency controls and circuit breakers

#### Technology Stack
- **Solidity**: Smart contract development
- **Hardhat/Truffle**: Development and testing framework
- **Web3.js**: Blockchain interaction
- **OpenZeppelin**: Security-focused contract libraries
- **The Graph**: Indexing and querying blockchain data

## Technology Stack

### Frontend
- **React** - UI framework
- **TypeScript** - Type-safe code
- **Ethers.js** - Ethereum interaction
- **Privy** - Wallet connection
- **HeadlessUI** - UI components
- **Tailwind CSS** - Styling
- **React Query** - API data fetching and caching

### Backend
- **Node.js** - Runtime environment
- **Express** - Web framework
- **TypeScript** - Type-safe code
- **Ethers.js** - Blockchain interaction
- **Axios** - HTTP client
- **Solana Web3.js** - Solana blockchain integration
- **Redis** - Caching (optional)

## API Endpoints

### Wallet Routes
- `POST /api/wallet/all-balances` - Get balances across all supported networks
- `POST /api/wallet/discover-tokens` - Comprehensive token discovery
- `POST /api/wallet/balances` - Get wallet balances by token pairs
- `POST /api/wallet/identify` - Identify wallet type
- `POST /api/wallet/token-pairs` - Get token pairs from DexScreener
- `POST /api/wallet/scan-all-tokens` - Scan for all tokens in a wallet

### Trading Routes
- `/api/quotes` - Fetch quotes across DEXs
- `/api/pairs` - Get trading pair information
- `/api/swap` - Execute token swaps

## Getting Started

### Quick Start (All Services)

Install dependencies and start all services with a single command:

```bash
# Install dependencies for all services
npm install

# Start all services concurrently
npm run dev
```

This will start:
- Frontend on http://localhost:4001
- Spot Backend on http://localhost:5001
- Liquidity Pool Service on http://localhost:3047
- Solana Service on http://localhost:6000

### Individual Service Development

#### Frontend Development
```bash
cd spot_frontend
npm install
npm run dev
# Runs on http://localhost:4001
```

#### Spot Backend Development
```bash
cd backend/spot_backend
npm install
npm run dev
# Runs on http://localhost:5001
```

#### Liquidity Pool Service Development
```bash
cd backend/liquidity_pool
npm install
npm run dev
# Runs on http://localhost:3047
```

#### Solana Service Development
```bash
cd backend/solana
npm install
npm run dev
# Runs on http://localhost:6000
```

### Available Scripts

From the root directory:

```bash
# Development
npm run dev                    # Start all services concurrently
npm run dev:frontend          # Start only frontend
npm run dev:spot-backend      # Start only spot backend
npm run dev:liquidity-pool    # Start only liquidity pool service
npm run dev:solana            # Start only solana service

# Installation
npm run install:all           # Install dependencies for all services
npm run install:frontend      # Install frontend dependencies
npm run install:backends      # Install all backend dependencies

# Building
npm run build:all             # Build all services
npm run build:frontend        # Build frontend
npm run build:backends        # Build all backend services

# Testing
npm run test:all              # Run tests for all services
npm run test:frontend         # Run frontend tests
npm run test:backends         # Run backend tests

# Utilities
npm run clean                 # Clean all node_modules and dist folders
npm run ports                 # Check which ports are in use
```

### Troubleshooting

#### Common Issues

1. **Permission Denied Errors**
   ```bash
   # Fix node_modules binary permissions
   chmod -R +x */node_modules/.bin/*
   ```

2. **Port Already in Use**
   ```bash
   # Check which services are running
   npm run ports

   # Kill processes on specific ports
   sudo lsof -ti:4001 | xargs kill -9  # Frontend
   sudo lsof -ti:5001 | xargs kill -9  # Spot Backend
   sudo lsof -ti:3047 | xargs kill -9  # Liquidity Pool
   sudo lsof -ti:6000 | xargs kill -9  # Solana Service
   ```

3. **Redis Connection Errors**
   ```bash
   # Install and start Redis (optional)
   sudo apt-get install redis-server
   sudo systemctl start redis-server
   ```

4. **Missing Dependencies**
   ```bash
   # Reinstall all dependencies
   npm run clean
   npm run install:all
   ```

#### Service Health Check

Use the built-in port checker to verify all services are running:
```bash
npm run ports
```

Expected output when all services are running:
```
✓ Frontend (Port 4001): RUNNING
✓ Spot Backend (Port 5001): RUNNING
✓ Liquidity Pool (Port 3047): RUNNING
✓ Solana Service (Port 6000): RUNNING
```

### Environment Setup

Each service requires its own environment configuration:

#### Spot Backend (.env in backend/spot_backend/)
```
PORT=5001
ALCHEMY_API_KEY_ETH=your_alchemy_key
BSCSCAN_API_KEY=your_bscscan_key
ETHERSCAN_API_KEY=your_etherscan_key
SOLANA_RPC_URL=your_solana_rpc
REDIS_URL=redis://localhost:6379
```

#### Liquidity Pool Service (.env in backend/liquidity_pool/)
```
PORT=3047
NODE_ENV=development
REDIS_URL=redis://localhost:6379
ETH_RPC_URL=https://mainnet.infura.io/v3/your_key
BSC_RPC_URL=https://bsc-dataseed.binance.org/
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
```

#### Solana Service (.env in backend/solana/)
```
PORT=6000
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_DEVNET_RPC_URL=https://api.devnet.solana.com
```

#### Frontend (.env in spot_frontend/)
```
VITE_PRIVY_APP_ID=your_privy_app_id
VITE_API_URL=http://localhost:5001/api
VITE_LIQUIDITY_API_URL=http://localhost:3047/api
VITE_SOLANA_API_URL=http://localhost:6000/api
```

## Security Features
- Real-time balance checking
- Insufficient funds detection
- High price impact warnings
- Transaction fee transparency
- Input validation and comprehensive error handling
- Multiple fallback mechanisms for API failures

## License

MIT 