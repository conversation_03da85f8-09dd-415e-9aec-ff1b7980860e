#!/bin/bash

# Simple Deployment Preparation Script for RedFyn Spot Trading Platform
# This script only prepares files locally - no SSH required

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== RedFyn Deployment Preparation ===${NC}"
echo -e "${YELLOW}This script prepares deployment files locally${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "spot_frontend" ] || [ ! -d "backend" ]; then
    echo -e "${RED}[ERROR]${NC} Please run this script from the project root directory"
    exit 1
fi

# Step 1: Build all services locally
print_status "Step 1: Building all services locally..."

# Install dependencies and build all services
print_status "Installing dependencies for all services..."
npm install

# Build frontend
print_status "Building frontend..."
cd spot_frontend
npm install --legacy-peer-deps
npm run build
cd ..

# Build backend services
print_status "Building backend services..."
cd backend/spot_backend
npm install
npm run build
cd ../..

cd backend/liquidity_pool
npm install
npm run build
cd ../..

cd backend/solana
npm install
npm run build
cd ../..

print_status "Local build completed successfully!"

# Step 2: Create deployment package
print_status "Step 2: Creating deployment package..."

# Create deployment directory
DEPLOY_DIR="redfyn-production-$(date +%Y%m%d-%H%M%S)"
mkdir -p $DEPLOY_DIR

# Copy necessary files
print_status "Copying files to deployment package..."
cp -r backend $DEPLOY_DIR/
cp -r spot_frontend $DEPLOY_DIR/
cp docker-compose.production.yml $DEPLOY_DIR/
cp nginx.conf $DEPLOY_DIR/
cp package.json $DEPLOY_DIR/
cp README.md $DEPLOY_DIR/ 2>/dev/null || true

# Copy production environment files
print_status "Setting up production environment files..."
cp backend/spot_backend/.env.production $DEPLOY_DIR/backend/spot_backend/.env
cp backend/liquidity_pool/.env.production $DEPLOY_DIR/backend/liquidity_pool/.env
cp backend/solana/.env.production $DEPLOY_DIR/backend/solana/.env
cp spot_frontend/.env.production $DEPLOY_DIR/spot_frontend/.env

# Create archive
print_status "Creating deployment archive..."
tar -czf ${DEPLOY_DIR}.tar.gz $DEPLOY_DIR

print_status "Deployment package created successfully!"
echo ""
echo -e "${GREEN}=== Deployment Package Ready ===${NC}"
echo -e "${YELLOW}Package location:${NC} ${DEPLOY_DIR}.tar.gz"
echo -e "${YELLOW}Package size:${NC} $(du -h ${DEPLOY_DIR}.tar.gz | cut -f1)"
echo ""

# Step 3: Provide manual deployment instructions
echo -e "${BLUE}=== Manual Deployment Instructions ===${NC}"
echo ""
echo "1. Upload the deployment package to your server:"
echo "   - Use your hosting provider's file manager"
echo "   - Or use: scp ${DEPLOY_DIR}.tar.gz root@*************:/tmp/"
echo ""
echo "2. On the server, extract and deploy:"
echo "   cd /tmp"
echo "   tar -xzf ${DEPLOY_DIR}.tar.gz"
echo "   sudo mv ${DEPLOY_DIR} /opt/redfyn-spot"
echo "   cd /opt/redfyn-spot"
echo ""
echo "3. Install Docker (if not installed):"
echo "   curl -fsSL https://get.docker.com -o get-docker.sh"
echo "   sudo sh get-docker.sh"
echo ""
echo "4. Start the services:"
echo "   sudo docker-compose -f docker-compose.production.yml up --build -d"
echo ""
echo "5. Check service status:"
echo "   sudo docker-compose -f docker-compose.production.yml ps"
echo ""
echo -e "${GREEN}=== Service URLs (after deployment) ===${NC}"
echo "Frontend: https://redfyn.crypfi.io"
echo "Backend API: http://*************:5001"
echo "Liquidity Pool API: http://*************:3047"
echo "Solana API: http://*************:6001"
echo ""

# Cleanup
print_status "Cleaning up temporary files..."
rm -rf $DEPLOY_DIR

print_status "Deployment preparation completed!"
echo -e "${YELLOW}Next: Upload ${DEPLOY_DIR}.tar.gz to your server and follow the manual instructions above.${NC}"
