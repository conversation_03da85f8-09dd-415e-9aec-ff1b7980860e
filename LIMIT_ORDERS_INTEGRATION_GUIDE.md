# Limit Orders System Integration Guide

This guide provides step-by-step instructions for integrating the comprehensive limit orders system into your existing trading platform.

## 🏗️ System Overview

The limit orders system consists of:

1. **Database Layer**: Supabase with comprehensive schema and RLS
2. **Backend API**: Express.js service with full CRUD operations
3. **Frontend Service**: TypeScript service for API communication
4. **React Hook**: Custom hook for state management
5. **Validation**: Multi-layer validation system

## 📋 Prerequisites

- Supabase account and project
- Node.js backend with Express.js
- React frontend with TypeScript
- Existing Privy authentication system

## 🚀 Installation Steps

### 1. Backend Setup

#### Install Dependencies
```bash
cd backend/spot_backend
npm install @supabase/supabase-js
```

#### Environment Variables
Add to your `.env` file:
```env
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

#### Database Schema
1. Open Supabase SQL Editor
2. Execute the SQL from `backend/spot_backend/src/database/supabase.sql`

#### Start Backend
```bash
npm run dev
```

### 2. Frontend Setup

#### Install Dependencies (if needed)
```bash
cd spot_frontend
npm install axios
```

#### Import Services
The limit order service and hook are ready to use:
```typescript
import { limitOrderService } from '../services/limitOrderService';
import { useLimitOrders } from '../hooks/useLimitOrders';
```

## 🔧 Integration with Existing Pulse Trade Page

### 1. Update the Limit Tab Component

Replace the existing `LimitTab` component in `TradingPanel.tsx`:

```typescript
// Enhanced Limit Tab Component with actual limit order functionality
const LimitTab: React.FC<TabProps> = ({ amount, setAmount, isBuy, onQuoteUpdate, balanceData }) => {
  const [targetPrice, setTargetPrice] = useState<string>('');
  const [expirationHours, setExpirationHours] = useState<number>(24);
  const { createOrder, isCreating } = useLimitOrders();
  
  // Calculate target price based on current price and percentage
  const handlePriceAdjustment = (percentage: number) => {
    const activePulseToken = JSON.parse(localStorage.getItem('activePulseToken') || '{}');
    const currentPrice = activePulseToken.current_price || 0;
    
    if (currentPrice > 0) {
      const adjustedPrice = currentPrice * (1 + percentage / 100);
      setTargetPrice(adjustedPrice.toFixed(8));
    }
  };

  return (
    <div>
      <AmountInput amount={amount} setAmount={setAmount} isBuy={isBuy} onQuoteUpdate={onQuoteUpdate} balanceData={balanceData} />
      
      {/* Target Price Input */}
      <div className="mb-4">
        <label className="block text-sm text-gray-400 mb-2">Target Price</label>
        <input
          type="number"
          value={targetPrice}
          onChange={(e) => setTargetPrice(e.target.value)}
          placeholder="Enter target price"
          className="w-full bg-[#2A2D35] border border-gray-700 text-white rounded-md py-2 px-3 text-sm"
        />
      </div>

      {/* Price Adjustment Buttons */}
      <div className="grid grid-cols-5 gap-2 mb-4">
        {[-10, -5, 0, +5, +10].map((percentage) => (
          <button
            key={percentage}
            onClick={() => handlePriceAdjustment(percentage)}
            className="border border-gray-700 hover:bg-gray-700 text-gray-300 py-2 px-2 text-xs"
          >
            {percentage > 0 ? '+' : ''}{percentage}%
          </button>
        ))}
      </div>

      {/* Expiration */}
      <div className="mb-4">
        <label className="block text-sm text-gray-400 mb-2">Expires in (hours)</label>
        <select
          value={expirationHours}
          onChange={(e) => setExpirationHours(Number(e.target.value))}
          className="w-full bg-[#2A2D35] border border-gray-700 text-white rounded-md py-2 px-3 text-sm"
        >
          <option value={1}>1 hour</option>
          <option value={6}>6 hours</option>
          <option value={24}>24 hours</option>
          <option value={72}>3 days</option>
          <option value={168}>1 week</option>
        </select>
      </div>

      <SlippageSettings isBuy={isBuy} />
    </div>
  );
};
```

### 2. Update the Buy Button for Limit Orders

Modify the `getButtonText()` function in `BuyButton` component:

```typescript
const getButtonText = () => {
  if (isExecuting) {
    if (executionStage) {
      return `${executionStage} (${executionProgress}%)`;
    }
    return `${isBuy ? 'Buying' : 'Selling'}...`;
  }

  if (activeTab === 'Limit') {
    return `Create ${isBuy ? 'Buy' : 'Sell'} Limit Order`;
  }

  // ... rest of existing logic
};
```

### 3. Add Limit Order Execution Logic

Add this to the `executeSwap` function in `BuyButton` component:

```typescript
// Add at the beginning of executeSwap function
if (activeTab === 'Limit') {
  return await executeLimitOrder();
}

// Add this new function
const executeLimitOrder = async () => {
  // Get limit order specific data (target price, expiration, etc.)
  // This would come from the LimitTab component state
  
  const orderData: CreateLimitOrderRequest = {
    user_id: user?.id || '',
    token_address: activePulseToken.address,
    token_name: activePulseToken.name,
    token_symbol: activePulseToken.symbol,
    pool_address: activePulseToken.pool_address || '',
    dex_type: activePulseToken.exchange_name.toLowerCase(),
    direction: isBuy ? 'buy' : 'sell',
    amount: parseFloat(amount),
    target_price: parseFloat(targetPrice), // From LimitTab
    current_price: activePulseToken.current_price || 0,
    slippage: currentPresetSettings.slippage / 100,
    wallet_address: walletInfo.address,
    wallet_id: walletInfo.id,
    expires_at: new Date(Date.now() + expirationHours * 60 * 60 * 1000).toISOString()
  };

  const result = await limitOrderService.createLimitOrder(orderData);
  
  if (result.success) {
    showSwapSuccessToast('Limit order created successfully');
    onResetUI?.();
  } else {
    showSwapErrorToast(result.error || 'Failed to create limit order');
  }
};
```

## 📱 Creating a Limit Orders Management Component

Create a new component to display and manage limit orders:

```typescript
// components/LimitOrdersManager.tsx
import React from 'react';
import { useLimitOrders } from '../hooks/useLimitOrders';

export const LimitOrdersManager: React.FC = () => {
  const {
    orders,
    stats,
    isLoading,
    cancelOrder,
    refreshOrders,
    filters,
    setFilters
  } = useLimitOrders();

  return (
    <div className="bg-gray-800 rounded-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-white text-lg font-medium">Limit Orders</h3>
        <button
          onClick={refreshOrders}
          className="text-blue-400 hover:text-blue-300 text-sm"
        >
          Refresh
        </button>
      </div>

      {/* Stats */}
      {stats && (
        <div className="grid grid-cols-4 gap-4 mb-4 text-center">
          <div>
            <div className="text-yellow-400 text-lg font-bold">{stats.pending}</div>
            <div className="text-gray-400 text-xs">Pending</div>
          </div>
          <div>
            <div className="text-green-400 text-lg font-bold">{stats.executed}</div>
            <div className="text-gray-400 text-xs">Executed</div>
          </div>
          <div>
            <div className="text-red-400 text-lg font-bold">{stats.cancelled}</div>
            <div className="text-gray-400 text-xs">Cancelled</div>
          </div>
          <div>
            <div className="text-gray-400 text-lg font-bold">{stats.expired}</div>
            <div className="text-gray-400 text-xs">Expired</div>
          </div>
        </div>
      )}

      {/* Orders List */}
      <div className="space-y-2">
        {orders.map((order) => (
          <div key={order.id} className="bg-gray-700 rounded p-3">
            <div className="flex justify-between items-start">
              <div>
                <div className="text-white font-medium">
                  {order.direction.toUpperCase()} {order.token_symbol}
                </div>
                <div className="text-gray-400 text-sm">
                  Amount: {order.amount} | Target: ${order.target_price}
                </div>
                <div className="text-gray-500 text-xs">
                  Created: {new Date(order.created_at).toLocaleDateString()}
                </div>
              </div>
              <div className="text-right">
                <div className={`text-sm font-medium ${
                  order.status === 'pending' ? 'text-yellow-400' :
                  order.status === 'executed' ? 'text-green-400' :
                  order.status === 'cancelled' ? 'text-red-400' :
                  'text-gray-400'
                }`}>
                  {order.status.toUpperCase()}
                </div>
                {order.status === 'pending' && (
                  <button
                    onClick={() => cancelOrder(order.id)}
                    className="text-red-400 hover:text-red-300 text-xs mt-1"
                  >
                    Cancel
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {isLoading && (
        <div className="text-center text-gray-400 py-4">Loading...</div>
      )}
    </div>
  );
};
```

## 🔄 Future Extensions

### 1. Price Monitoring Service

Create a background service to monitor token prices and execute orders:

```typescript
// services/priceMonitoringService.ts
export class PriceMonitoringService {
  async monitorPrices() {
    // Get all pending orders
    // Check current prices
    // Execute orders when conditions are met
  }
}
```

### 2. Real-time Updates

Integrate with Supabase real-time subscriptions:

```typescript
// In useLimitOrders hook
useEffect(() => {
  const subscription = supabase
    .channel('limit_orders')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'limit_orders' },
      (payload) => {
        // Update orders in real-time
      }
    )
    .subscribe();

  return () => subscription.unsubscribe();
}, []);
```

### 3. Advanced Order Types

Extend the system to support:
- Stop-loss orders
- Take-profit orders
- Trailing stop orders
- OCO (One-Cancels-Other) orders

## 🧪 Testing

### Backend Testing
```bash
# Test order creation
curl -X POST http://localhost:3001/api/limit-orders \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "test_user",
    "token_address": "So11111111111111111111111111111111111111112",
    "token_name": "Solana",
    "token_symbol": "SOL",
    "pool_address": "test_pool",
    "dex_type": "pumpfun",
    "direction": "buy",
    "amount": 1.5,
    "target_price": 100.50,
    "current_price": 105.00,
    "wallet_address": "test_wallet",
    "wallet_id": "test_wallet_id"
  }'
```

### Frontend Testing
```typescript
// Test the hook
const TestComponent = () => {
  const { createOrder, orders } = useLimitOrders();
  
  const testOrder = async () => {
    const result = await createOrder({
      // ... order data
    });
    console.log('Order created:', result);
  };

  return <button onClick={testOrder}>Test Order</button>;
};
```

## 🔒 Security Considerations

1. **Row Level Security**: Enabled by default in Supabase
2. **Input Validation**: Multi-layer validation on frontend and backend
3. **Rate Limiting**: Applied to all API endpoints
4. **Authentication**: Integrated with existing Privy system

## 📊 Monitoring

Monitor the system using:
- Supabase dashboard for database metrics
- Backend performance endpoints
- Frontend error tracking
- Order execution success rates

## 🆘 Troubleshooting

Common issues and solutions:

1. **Database Connection Issues**: Check Supabase credentials
2. **RLS Policy Errors**: Verify user context is set correctly
3. **Validation Errors**: Check order data format and types
4. **API Timeout**: Increase timeout values for slow networks

This comprehensive system provides a solid foundation for limit orders that can be extended with price monitoring and automatic execution capabilities.
